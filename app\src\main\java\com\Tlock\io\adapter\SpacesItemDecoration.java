package com.Tlock.io.adapter;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

public class SpacesItemDecoration extends RecyclerView.ItemDecoration {
    private final int space;
    private final int spanCount;

    public SpacesItemDecoration(int space, int spanCount) {
        this.space = space;
        this.spanCount = spanCount;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        outRect.left = space;
        outRect.right = space;
        outRect.bottom = space;
        outRect.top = space;

        // 根据列数调整边距
        if (position % spanCount == 0) {
            outRect.left = 0; // 第一列无左边距
        }
        if (position % spanCount == spanCount - 1) {
            outRect.right = 0; // 最后一列无右边距
        }
    }
}