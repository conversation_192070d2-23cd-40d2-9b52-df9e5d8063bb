package com.Tlock.io.base;

import java.io.Serializable;

/**
 * @Describe 数据解析基类
 */
public class BaseBean<T> implements Serializable {
    private static final long serialVersionUID = 1876345352L;
    private int code;
    private String msg;
    private T body;
    private T data;
    private T result;
    private int type;
    private long checkTime;

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public long getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(long checkTime) {
        this.checkTime = checkTime;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return msg;
    }

    public void setMessage(String message) {
        this.msg = message;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {

        this.body = body;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "BaseBean{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", body=" + body +
                ", data=" + data +
                ", result=" + result +
                ", type=" + type +
                ", checkTime=" + checkTime +
                '}';
    }
}
