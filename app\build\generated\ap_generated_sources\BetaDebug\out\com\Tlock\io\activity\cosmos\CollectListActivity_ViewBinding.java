// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CollectListActivity_ViewBinding implements Unbinder {
  private CollectListActivity target;

  @UiThread
  public CollectListActivity_ViewBinding(CollectListActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public CollectListActivity_ViewBinding(CollectListActivity target, View source) {
    this.target = target;

    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mRecyclerView = Utils.findRequiredViewAsType(source, R.id.recyler_view, "field 'mRecyclerView'", RecyclerView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    CollectListActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mRecyclerView = null;
  }
}
