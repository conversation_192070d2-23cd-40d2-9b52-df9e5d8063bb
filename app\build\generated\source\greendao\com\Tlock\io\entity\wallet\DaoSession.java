package com.Tlock.io.entity.wallet;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.OperationBean;
import com.Tlock.io.entity.wallet.TokenInfo;
import com.Tlock.io.entity.wallet.Transaction;
import com.Tlock.io.entity.wallet.Transfer;

import com.Tlock.io.entity.wallet.ETHWalletDao;
import com.Tlock.io.entity.wallet.OperationBeanDao;
import com.Tlock.io.entity.wallet.TokenInfoDao;
import com.Tlock.io.entity.wallet.TransactionDao;
import com.Tlock.io.entity.wallet.TransferDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig eTHWalletDaoConfig;
    private final DaoConfig operationBeanDaoConfig;
    private final DaoConfig tokenInfoDaoConfig;
    private final DaoConfig transactionDaoConfig;
    private final DaoConfig transferDaoConfig;

    private final ETHWalletDao eTHWalletDao;
    private final OperationBeanDao operationBeanDao;
    private final TokenInfoDao tokenInfoDao;
    private final TransactionDao transactionDao;
    private final TransferDao transferDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        eTHWalletDaoConfig = daoConfigMap.get(ETHWalletDao.class).clone();
        eTHWalletDaoConfig.initIdentityScope(type);

        operationBeanDaoConfig = daoConfigMap.get(OperationBeanDao.class).clone();
        operationBeanDaoConfig.initIdentityScope(type);

        tokenInfoDaoConfig = daoConfigMap.get(TokenInfoDao.class).clone();
        tokenInfoDaoConfig.initIdentityScope(type);

        transactionDaoConfig = daoConfigMap.get(TransactionDao.class).clone();
        transactionDaoConfig.initIdentityScope(type);

        transferDaoConfig = daoConfigMap.get(TransferDao.class).clone();
        transferDaoConfig.initIdentityScope(type);

        eTHWalletDao = new ETHWalletDao(eTHWalletDaoConfig, this);
        operationBeanDao = new OperationBeanDao(operationBeanDaoConfig, this);
        tokenInfoDao = new TokenInfoDao(tokenInfoDaoConfig, this);
        transactionDao = new TransactionDao(transactionDaoConfig, this);
        transferDao = new TransferDao(transferDaoConfig, this);

        registerDao(ETHWallet.class, eTHWalletDao);
        registerDao(OperationBean.class, operationBeanDao);
        registerDao(TokenInfo.class, tokenInfoDao);
        registerDao(Transaction.class, transactionDao);
        registerDao(Transfer.class, transferDao);
    }
    
    public void clear() {
        eTHWalletDaoConfig.clearIdentityScope();
        operationBeanDaoConfig.clearIdentityScope();
        tokenInfoDaoConfig.clearIdentityScope();
        transactionDaoConfig.clearIdentityScope();
        transferDaoConfig.clearIdentityScope();
    }

    public ETHWalletDao getETHWalletDao() {
        return eTHWalletDao;
    }

    public OperationBeanDao getOperationBeanDao() {
        return operationBeanDao;
    }

    public TokenInfoDao getTokenInfoDao() {
        return tokenInfoDao;
    }

    public TransactionDao getTransactionDao() {
        return transactionDao;
    }

    public TransferDao getTransferDao() {
        return transferDao;
    }

}
