<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_5"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_12">

    <ImageView
        android:id="@+id/iv_heard"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginRight="@dimen/dp_8"
        android:src="@drawable/shape_light_gray_60" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/iv_heard"
        android:layout_alignBottom="@id/iv_heard"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/dp_10"
        android:layout_toLeftOf="@id/iv_more"
        android:layout_toRightOf="@id/iv_heard"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <com.Tlock.io.widget.FontTextView
            android:id="@+id/tv_account_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:maxWidth="@dimen/dp_150"
            android:singleLine="true"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_16"
            app:fontType="name" />

        <TextView
            android:id="@+id/tv_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_account_name"
            android:layout_toRightOf="@id/iv_heard"
            android:includeFontPadding="false"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/dp_14" />
    </LinearLayout>


    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="@dimen/dp_20"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/dp_16"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_gray_more" />

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_heard"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_10"
        android:orientation="vertical">

        <com.Tlock.io.widget.FontTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_heard"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginRight="@dimen/dp_16"
            android:layout_toRightOf="@id/iv_heard"
            android:lineHeight="@dimen/dp_19"
            android:maxLines="2"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_17"
            app:fontFamily="@font/font_family_bold"
            app:fontType="name" />

        <com.Tlock.io.widget.FontTextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_title"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginRight="@dimen/dp_16"
            android:layout_toRightOf="@id/iv_heard"
            android:ellipsize="end"
            android:lineHeight="@dimen/dp_19"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_17"
            app:fontFamily="@font/font_family"
            app:fontType="content"
            app:lineHeight="@dimen/dp_22" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_image"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp_6" />

        <LinearLayout
            android:id="@+id/ll_content_quote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/rv_image"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_10"
            android:layout_toRightOf="@id/iv_heard"
            android:background="@drawable/bg_gray_13"
            android:minHeight="@dimen/dp_60"
            android:orientation="vertical"
            android:padding="@dimen/dp_10"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_quote_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_quote_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/sp_13" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_vote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_voteTime"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_12"
            android:text="1 votes · 2025/2/11 12:00"
            android:visibility="gone" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_content"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_10"
        android:textColor="@color/cosmos_default"
        android:textSize="@dimen/sp_14" />

    <View
        android:id="@+id/line3"
        style="@style/gray_horizontal_line_view"
        android:layout_below="@id/tv_time"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/line3"
        android:layout_marginLeft="@dimen/dp_16"
        android:orientation="horizontal"
        android:visibility="gone">


        <RelativeLayout
            android:id="@+id/ll_praise"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/ll_quote"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_praise"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_like" />

            <TextView
                android:id="@+id/tv_praise"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_praise"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_quote"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/ll_praise"
            app:layout_constraintRight_toLeftOf="@id/ll_review"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_quote"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_forward" />

            <TextView
                android:id="@+id/tv_quote"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_toRightOf="@id/iv_quote"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_review"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/ll_quote"
            app:layout_constraintRight_toLeftOf="@id/ll_Collect"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_review"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_alignParentLeft="true"

                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_comment" />

            <TextView
                android:id="@+id/tv_review"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_review"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/ll_Collect"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/ll_review"
            app:layout_constraintRight_toLeftOf="@id/ll_share"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_Collect"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_save" />

            <TextView
                android:id="@+id/tv_Collect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_Collect"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_share"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_16"
            android:paddingTop="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="0.5"
            app:layout_constraintLeft_toRightOf="@id/ll_Collect"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_share"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_share" />

            <TextView
                android:id="@+id/tv_share"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_share"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line1"
        style="@style/deep_gray_horizontal_line_view"
        android:layout_below="@id/constraint"
        android:visibility="gone" />
</RelativeLayout>

