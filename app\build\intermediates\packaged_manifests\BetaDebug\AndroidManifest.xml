<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.Tlock.io"
    android:versionCode="148"
    android:versionName="0.0.1" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <uses-permission
        android:name="android.permission.BROADCAST_PACKAGE_ADDED"
        android:required="false" />
    <uses-permission
        android:name="android.permission.BROADCAST_PACKAGE_CHANGED"
        android:required="false" />
    <uses-permission
        android:name="android.permission.BROADCAST_PACKAGE_INSTALL"
        android:required="false" />
    <uses-permission
        android:name="android.permission.BROADCAST_PACKAGE_REPLACED"
        android:required="false" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission
        android:name="android.permission.WAKE_LOCK"
        android:required="false" />
    <uses-permission
        android:name="android.permission.REORDER_TASKS"
        android:required="false" />
    <uses-permission
        android:name="android.permission.DISABLE_KEYGUARD"
        android:required="false" />
    <uses-permission
        android:name="android.permission.SYSTEM_ALERT_WINDOW"
        android:required="false" />
    <uses-permission
        android:name="android.permission.SYSTEM_OVERLAY_WINDOW"
        android:required="false" />
    <uses-permission
        android:name="android.permission.INTERNET"
        android:required="false" />
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        android:required="false" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.REQUEST_DELETE_PACKAGES"
        android:required="false" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission
        android:name="android.permission.ACCESS_WIFI_STATE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.READ_SETTINGS"
        android:required="false" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        android:required="false" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        android:required="false" />

    <permission
        android:name="com.Tlock.io.openadsdk.permission.TT_PANGOLIN"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.Tlock.io.openadsdk.permission.TT_PANGOLIN" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        android:required="true" /> <!-- 硬件加速对X5视频播放非常重要，建议开启 -->
    <uses-permission
        android:name="android.permission.GET_TASKS"
        android:required="false" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        android:required="false" />
    <uses-permission
        android:name="android.permission.VIBRATE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        android:required="false" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:required="false" />

    <uses-feature android:name="android.hardware.Camera" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />

    <uses-permission
        android:name="android.permission.CHANGE_NETWORK_STATE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        android:required="false" />
    <uses-permission
        android:name="android.permission.CHANGE_WIFI_STATE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.RECEIVE_BOOT_COMPLETED"
        android:required="false" />
    <uses-permission
        android:name="com.android.launcher.permission.READ_SETTINGS"
        android:required="false" />
    <uses-permission
        android:name="android.permission.BROADCAST_STICKY"
        android:required="false" />
    <uses-permission
        android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"
        android:required="false" /> <!-- 悬浮窗权限 -->
    <uses-permission
        android:name="android.permission.SYSTEM_ALERT_WINDOW"
        android:required="false" /> <!-- 魅族推送配置 start -->
    <!-- 兼容 flyme5.0 以下版本，魅族内部集成 pushSDK 必填，不然无法收到消息 -->
    <uses-permission
        android:name="com.meizu.flyme.push.permission.RECEIVE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.VIBRATE"
        android:required="false" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <permission
        android:name="com.Tlock.io.push.permission.MESSAGE"
        android:protectionLevel="signature"
        android:required="false" />

    <uses-permission
        android:name="com.Tlock.io.push.permission.MESSAGE"
        android:required="false" /> <!-- 兼容 flyme3.0 配置权限 -->
    <uses-permission
        android:name="com.meizu.c2dm.permission.RECEIVE"
        android:required="false" />

    <permission
        android:name="com.Tlock.io.permission.C2D_MESSAGE"
        android:protectionLevel="signature"
        android:required="false" />

    <uses-permission
        android:name="com.Tlock.io.permission.C2D_MESSAGE"
        android:required="false" /> <!-- 魅族推送配置 end -->
    <!-- Oppo推送配置 start -->
    <uses-permission
        android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE"
        android:required="false" /> <!-- Oppo推送配置 end -->
    <!-- Mi推送配置 start -->
    <permission
        android:name="com.Tlock.io.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature"
        android:required="false" />

    <uses-permission
        android:name="com.Tlock.io.permission.MIPUSH_RECEIVE"
        android:required="false" /> <!-- Mi推送配置 end -->
    <!-- 桌面角标权限start -->
    <!-- 华为 -->
    <uses-permission
        android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"
        android:required="false" /> <!-- 三星 -->
    <uses-permission
        android:name="com.sec.android.provider.badge.permission.READ"
        android:required="false" />
    <uses-permission
        android:name="com.sec.android.provider.badge.permission.WRITE"
        android:required="false" /> <!-- OPPO -->
    <uses-permission
        android:name="com.oppo.launcher.permission.READ_SETTINGS"
        android:required="false" />
    <uses-permission
        android:name="com.oppo.launcher.permission.WRITE_SETTINGS"
        android:required="false" /> <!-- 联想ZUK -->
    <uses-permission
        android:name="android.permission.READ_APP_BADGE"
        android:required="false" /> <!-- HTC -->
    <uses-permission
        android:name="com.htc.launcher.permission.READ_SETTINGS"
        android:required="false" />
    <uses-permission
        android:name="com.htc.launcher.permission.UPDATE_SHORTCUT"
        android:required="false" /> <!-- 用于申请调用A-GPS模块 -->
    <uses-permission
        android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"
        android:required="false" />
    <uses-permission
        android:name="android.permission.USE_FINGERPRINT"
        android:required="false" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE"
        android:required="false" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        android:required="false" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.PICK" />
        </intent>
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />
        </intent>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="com.android.camera.action.CROP" />
        </intent>
    </queries>

    <permission
        android:name="com.Tlock.io.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.Tlock.io.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <uses-feature android:name="android.hardware.camera" />

    <application
        android:name="com.Tlock.io.app.AppApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/app_icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/app_icon"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.Tlock.io.activity.cosmos.Test1Activity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.AllowanceActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.EditTopicActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.MangerActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TransactionListActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TransactionInfoActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TransferInfoActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.ReceiveActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TransferActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TransferHistoryActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.WalletInfoActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.SearchActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.EditProfileActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.UserInfoActivity"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.MTaskDetailActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.MTaskListActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.MTopicDetailActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.MTopicSearchActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.FollowingListActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.CollectListActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.wallet.WalletOrderActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.MMemberListActivity"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TopicActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.PostQuoteActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.ContentInfoActivity"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="com.Tlock.io.activity.cosmos.TopicDetailActivity"
            android:exported="false" />
        <activity
            android:name="com.Tlock.io.activity.wallet.WalletSettingActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.NewWalletActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme"
            android:windowSoftInputMode="adjustPan" >
        </activity>
        <activity
            android:name="com.Tlock.io.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme"
            android:windowSoftInputMode="adjustPan" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.Tlock.io.activity.cosmos.PostActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- <activity -->
        <!-- android:name=".widget.web.DappBrowerActivity" -->
        <!-- android:screenOrientation="portrait" -->
        <!-- android:theme="@style/AppTheme" -->
        <!-- android:usesCleartextTraffic="true" /> -->

        <activity
            android:name="com.Tlock.io.activity.wallet.AssetDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.InputWalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.BackupActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.ToBackupMnemonicsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.BackupPrivateKeyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.CheckMnemonicsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.BackupMnemonicsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.CreateWalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.Tlock.io.activity.wallet.ShareAddressActivity"
            android:screenOrientation="portrait" />

        <!-- <activity -->
        <!-- android:name=".activity.H5Activity" -->
        <!-- android:screenOrientation="portrait" /> -->


        <!-- <service -->
        <!-- android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService" -->
        <!-- android:label="dexopt" /> -->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.Tlock.io.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <meta-data
            android:name="design_width_in_dp"
            android:value="375" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="812" />
        <meta-data
            android:name="com.Tlock.io.widget.QiNiuModule"
            android:value="GlideModule" />
        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- PermissonActivity -->
        <activity
            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.sl.utakephoto.TakePhotoProvider"
            android:authorities="com.Tlock.io.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/take_file_path" />
        </provider>

        <activity
            android:name="com.sl.utakephoto.crop.CropActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:theme="@style/Theme.Crop" />

        <meta-data
            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
            android:value="GlideModule" />

        <provider
            android:name="com.squareup.picasso.PicassoProvider"
            android:authorities="com.Tlock.io.com.squareup.picasso"
            android:exported="false" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.Tlock.io.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.yzq.zxinglibrary.android.CaptureActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <provider
            android:name="me.jessyan.autosize.InitProvider"
            android:authorities="com.Tlock.io.autosize-init-provider"
            android:exported="false"
            android:multiprocess="true" />

        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
        <service
            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
            android:process=":filedownloader" />
    </application>

</manifest>