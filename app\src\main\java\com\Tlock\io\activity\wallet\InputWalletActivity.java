package com.Tlock.io.activity.wallet;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.cosmos.CosmosWalletUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomInputBox;
import com.yzq.zxinglibrary.android.CaptureActivity;
import com.yzq.zxinglibrary.common.Constant;

import org.greenrobot.eventbus.EventBus;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.OnClick;


public class InputWalletActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.iv_scan)
    ImageView mIvScan;
    @BindView(R.id.rl_toolbar)
    RelativeLayout mRlToolbar;
    @BindView(R.id.tv_info)
    TextView mTvInfo;
    @BindView(R.id.tv_mnemonics)
    TextView mTvMnemonics;
    @BindView(R.id.tv_key)
    TextView mTvKey;
    @BindView(R.id.ll_type)
    LinearLayout mLlType;
    @BindView(R.id.ed_input)
    EditText mEdInput;
    @BindView(R.id.et_wallet_name)
    CustomInputBox mEtWalletName;
    @BindView(R.id.et_pwd)
    CustomInputBox mEtPwd;
    @BindView(R.id.et_pwd_confirm)
    CustomInputBox mEtPwdConfirm;

    @BindView(R.id.tv_input)
    TextView mTvInput;
    @BindView(R.id.tv_error)
    TextView mTvError;
    @BindView(R.id.tv_already)
    TextView mTvAlready;
    @BindView(R.id.tv_pwd_error)
    TextView mTvPwdError;
    private int REQUEST_CODE_SCAN;
    private boolean isMnemonic = true;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, InputWalletActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_input_wallet;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        mEdInput.setHint(getString(R.string.with_space));
        mIvScan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                scanQRCode();
            }
        });
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });


        mEdInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().isEmpty()) {
                    mTvError.setVisibility(View.GONE);
                }
                String str = checkMonica();
                if (isMnemonic)
                    if (!TextUtils.isEmpty(str)) {
                        mTvInfo.setVisibility(View.VISIBLE);
                        mTvInfo.setText(getResources().getString(R.string.mnemonics) + " " + str + " " + getResources().getString(R.string.mnemonics_error));
                    } else {
                        mTvInfo.setVisibility(View.GONE);
                    }
            }
        });

        mEtPwd.setCallback(new CustomInputBox.Callback() {
            @Override
            public void onTextChange(String text) {
                if (text.isEmpty()) {
                    mTvPwdError.setVisibility(View.GONE);

                }
            }
        });
    }

    @Override
    protected void loadData() {

    }


    @OnClick({R.id.tv_mnemonics, R.id.tv_key, R.id.tv_input})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_mnemonics:
                isMnemonic = true;
                setTextChange(true);
                mEdInput.setHint(getString(R.string.with_space));
                break;
            case R.id.tv_key:
                isMnemonic = false;
                setTextChange(false);
                mEdInput.setHint(getString(R.string.no_space));
                break;
            case R.id.tv_input:
                String check = mEtPwd.getEditText();
                String recheck = mEtPwdConfirm.getEditText();
                if (check.equals(recheck)) {
                    if (check.length() < 6) {
                        showToast(getString(R.string.digits_needed));
                        mTvPwdError.setText(getString(R.string.digits_needed));
                        mTvPwdError.setVisibility(View.VISIBLE);
                        return;
                    }
                } else {
                    showToast("Passwords don't match.");
                    mTvPwdError.setText("Passwords don't match.");
                    mTvPwdError.setVisibility(View.VISIBLE);
                    return;
                }
                ClipboardManager cm = (ClipboardManager) getActivity().getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData mClipData = ClipData.newPlainText("Label", "");
                cm.setPrimaryClip(mClipData);
                if (isMnemonic) {
                    if (WalletDaoUtils.checkRepeatByMnemonic(mEdInput.getText().toString().trim())) {
                        mTvAlready.setVisibility(View.VISIBLE);
                        return;
                    }
                    if (mEdInput.getText().toString().contains(" ")) {
                        ETHWallet walletInfo = CosmosWalletUtils.getWallet(mEdInput.getText().toString().trim(), 2);
                        walletInfo.setType(2);
                        walletInfo.setPassword(check);
                        walletInfo.setName(walletInfo.getAddress().substring(walletInfo.getAddress().length()-6));

                        walletInfo.setChainId("10889");
                        WalletDaoUtils.insertNewWallet(walletInfo);
                        //删除观察钱包
                        WalletDaoUtils.removeObserve(walletInfo.getAddress());
                        EventBus.getDefault().postSticky(new Event(EventConstant.WALLET));
                        finish();
                    } else {
                        showToast(getString(R.string.with_space));
                        mTvError.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (mEdInput.getText().toString().length() < 60 || mEdInput.getText().toString().length() > 66) {
                        showToast(getString(R.string.private_key_error));
                        mTvError.setVisibility(View.VISIBLE);
                        return;
                    }
                    if (!isLegality(mEdInput.getText().toString())) {
                        showToast(getString(R.string.Contain_illegal_character));
                        mTvError.setVisibility(View.VISIBLE);
                        return;
                    }
                    if (WalletDaoUtils.checkRepeatByPrivateKey(mEdInput.getText().toString().trim())) {
                        mTvAlready.setVisibility(View.VISIBLE);
                        return;
                    }
                    if (mEdInput.getText().toString().contains(" ")) {
                        showToast(getString(R.string.no_space));
                        mTvError.setVisibility(View.VISIBLE);
                    } else {
                        try {
                            ETHWallet ethWallet = CosmosWalletUtils.getWallet(mEdInput.getText().toString().trim(), 1);
                            ethWallet.setType(1);
                            ethWallet.setPrivateKey(mEdInput.getText().toString().trim());
                            ethWallet.setPassword(check);
                            ethWallet.setName(ethWallet.getAddress().substring(ethWallet.getAddress().length()-6));
                            ethWallet.setChainId("10889");
                            WalletDaoUtils.insertNewWallet(ethWallet);
                            //删除观察钱包
                            EventBus.getDefault().postSticky(new Event(EventConstant.WALLET));
                            finish();
                        } catch (Exception e) {
                            mTvAlready.setVisibility(View.VISIBLE);
                        }
                    }
                }
        }
    }


    /**
     * 检查助记词
     *
     * @return true所有单词拼写正确
     */
    private String checkMonica() {
        String[] s = mEdInput.getText().toString().trim().split(" ");
        InputStream inputStream = getResources().openRawResource(R.raw.word_list);
        String string = getString(inputStream);
        String[] s2 = string.split(" ");
        List<String> strings = Arrays.asList(s2);
        for (String s1 : s) {
            if (!strings.contains(s1)) {
                return s1;
            }

        }
        return "";
    }

    public static String getString(InputStream inputStream) {
        InputStreamReader inputStreamReader = null;
        try {
            inputStreamReader = new InputStreamReader(inputStream, "gbk");
        } catch (UnsupportedEncodingException e1) {
            e1.printStackTrace();
        }
        BufferedReader reader = new BufferedReader(inputStreamReader);
        StringBuffer sb = new StringBuffer("");
        String line;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line);
                sb.append(" ");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    private void setTextChange(boolean isMnemonic) {
        if (isMnemonic) {
            //助记词
            mTvMnemonics.setTextColor(getResources().getColor(R.color.cosmos_black));
            mTvKey.setTextColor(getResources().getColor(R.color.cosmos_default));

        } else {
            //私钥
            mTvKey.setTextColor(getResources().getColor(R.color.cosmos_black));
            mTvMnemonics.setTextColor(getResources().getColor(R.color.cosmos_default));

        }

    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            //扫描返回
            if (requestCode == REQUEST_CODE_SCAN && resultCode == RESULT_OK) {
                String content = data.getStringExtra(Constant.CODED_CONTENT);
                mEdInput.setText(content);
            }
        }
    }

    public void scanQRCode() {
        Intent intent = new Intent(getActivity(), CaptureActivity.class);
        startActivityForResult(intent, REQUEST_CODE_SCAN);
    }


    /**
     * 是否只包含英文和数字
     *
     * @param str
     * @return
     */
    public static boolean isLegality(String str) {
        String regex = "^[a-zA-Z0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }
}