package com.Tlock.io.activity.cosmos;

import static android.Manifest.permission.READ_MEDIA_IMAGES;
import static android.Manifest.permission.READ_MEDIA_VIDEO;
import static com.Tlock.io.utils.FilePathUtil.convertUriToFile;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.R;
import com.Tlock.io.adapter.SpacesItemDecoration;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseListBean;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.ImageViewBean;
import com.Tlock.io.network.OKHttpManager;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.ImageViewerPopupView;
import com.lxj.xpopup.interfaces.OnSrcViewUpdateListener;
import com.lxj.xpopup.util.SmartGlideImageLoader;

import java.io.File;
import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;

public class Test1Activity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_avatar)
    ImageView mIvAvatar;
    @BindView(R.id.iv_avatar1)
    ImageView mIvAvatar1;
    @BindView(R.id.smallLabel1)
    ImageView mSmallLabel1;
    @BindView(R.id.smallLabel2)
    ImageView mSmallLabel2;
    @BindView(R.id.tv_save)
    TextView mTvSave;
    @BindView(R.id.rv_images)
    RecyclerView mRvImages;
    @BindView(R.id.pager2)
    ViewPager2 mPager2;
    private BaseRecyclerViewAdapter<File> imageAdapter;
    private ArrayList<File> files = new ArrayList<>();

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context) {
        Intent intent = new Intent(context, Test1Activity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_test1;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initAdapter();
    }

    @Override
    protected void loadData() {
    }


    @OnClick({R.id.tv_save, R.id.iv_avatar})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_save:
                finish();
                uploadAvatar();
                break;
            case R.id.iv_avatar:
                break;

        }
    }


    private static final int REQUEST_CODE_PERMISSIONS = 100;
    private final String[] REQUIRED_PERMISSIONS = {
            READ_MEDIA_IMAGES,
            READ_MEDIA_VIDEO,
            Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
    };

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, REQUIRED_PERMISSIONS[0]) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS);
        } else {
            selectMultipleImages(); // 已有权限，直接打开相册
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    selectMultipleImages();
                }
            } else {
                selectMultipleImages();
            }

        }
    }

    private ActivityResultLauncher<Intent> galleryLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            this::onActivityResult);

    private void uploadAvatar() {
//        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
//            @Override
//            public void run() {
//                params.put("files", files);
//                Log.e(TAG, "这个是上传的地址 : 开始执行" + files.size());
//
//                OKHttpManager.postAsynUploadFiles("http://************:8080/api/s3/uploadImages", params, "files", files, new OKHttpManager.ResultCallback<BaseListBean<String>>() {
//                    @Override
//                    public void onError(int code, String result, String message) {
//                        Log.e(TAG, "这个是上传的地址 : 错误" + message);
//                    }
//
//                    @Override
//                    public void onResponse(BaseListBean<String> response) {
//                        Log.e(TAG, "这个是上传的地址 : " + response);
//                        PostTXProto.MsgCreateFreePost.Builder builder = PostTXProto.MsgCreateFreePost.newBuilder();
//                        ETHWallet current = WalletDaoUtils.getCurrent();
//                        builder.setCreator(current.getAddress());
//                        builder.setContent("测试图片内容");
//                        builder.addAllImagesUrl(response.getData());
//                        CosmosUtils.sendPostBody(builder.build());
//                        Log.e(TAG, "这个是上传的地址 : 成功" + JsonUtils.objectToJson(response));
//
//
//                    }
//                });
//            }
//        });


    }

    private void selectMultipleImages() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        galleryLauncher.launch(Intent.createChooser(intent, "选择图片"));

    }

    private void onActivityResult(ActivityResult result) {
        if (result.getResultCode() == RESULT_OK && result.getData() != null) {
            Intent data = result.getData();
            // 处理多张图片
            if (data.getClipData() != null) {
                // 多选图片的情况
                for (int i = 0; i < data.getClipData().getItemCount(); i++) {
                    Uri imageUri = data.getClipData().getItemAt(i).getUri();
                    File file = convertUriToFile(getActivity(), imageUri);
                    files.add(file);
                }
            } else if (data.getData() != null) {
                // 单选图片的情况
                File file = convertUriToFile(getActivity(), data.getData());
                files.add(file);
            }
            // 显示选择图片数量
            showToast("已选择 " + files.size() + " 张图片");
            ArrayList<File> files1 = new ArrayList<>(files);
            if (files1.size() < 3) {
                files1.add(null);
            }
            imageAdapter.setList(files1);
        }
    }


    private void initAdapter() {
        mRvImages.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        ArrayList<File> files1 = new ArrayList<>(files);
        if (files1.size() < 3) {
            files1.add(null);
        }

        int spanCount = files1.size() == 1 ? 1 : files1.size();
        mRvImages.setLayoutManager(new GridLayoutManager(getActivity(), spanCount));
        int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
        mRvImages.addItemDecoration(new SpacesItemDecoration(spacingInPixels, spanCount));

        imageAdapter = new BaseRecyclerViewAdapter<>(getActivity(), files1, new BaseRecyclerViewAdapter.Delegate<File>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new ImageViewBean(getActivity(), 0);
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, File file, View view) {
                ImageViewBean viewBean = (ImageViewBean) view;
                viewBean.setData(file);
                viewBean.setCallback(new ImageViewBean.Callback() {
                    @Override
                    public void onDelete(File file) {
                        files.remove(file);
                        ArrayList<File> files1 = new ArrayList<>(files);
                        if (files1.size() < 3) {
                            files1.add(null);
                        }
                        imageAdapter.setList(files1);
                    }

                    @Override
                    public void onAdd() {
                        checkPermissions();

                    }

                    @Override
                    public void onSelect(ImageView mIvImage) {
                        ArrayList<Object> list = new ArrayList<>();
                        for (File file1 : files) {
                            list.add(file1);
                        }
                        new XPopup.Builder(getActivity()).asImageViewer(mIvImage, position, list,
                                        new OnSrcViewUpdateListener() {
                                            @Override
                                            public void onSrcViewUpdate(final ImageViewerPopupView popupView, final int position) {

                                            }
                                        }, new SmartGlideImageLoader())
                                .show();
                    }
                });

            }
        });

        mRvImages.setAdapter(imageAdapter);

    }


}