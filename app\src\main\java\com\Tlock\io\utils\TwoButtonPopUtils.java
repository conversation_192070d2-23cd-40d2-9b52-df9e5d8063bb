package com.Tlock.io.utils;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;


import com.Tlock.io.R;
import com.Tlock.io.base.BasePop;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 本类的主要功能是 :  两个按钮 确定取消按钮;
 * 可以隐藏取消按钮
 */
public class TwoButtonPopUtils extends BasePop {


    @BindView(R.id.tv_pop_title)
    TextView mTvPopTitle;
    @BindView(R.id.tv_pop_content)
    TextView mTvPopContent;
    @BindView(R.id.tv_cancel)
    TextView mTvCancel;
    @BindView(R.id.view_line)
    View mViewLine;
    @BindView(R.id.tv_sure)
    TextView mTvSure;
    @BindView(R.id.ll_container)
    LinearLayout mLlContainer;
    @BindView(R.id.ll_root_pop)
    LinearLayout mLlRootPop;

    public TwoButtonPopUtils(Context context) {
        super(context);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.pop_two_button_tip;
    }

    @Override
    public void initData(View layout, Context context) {
        startScaleInAnimate(mLlContainer);
    }

    public TwoButtonPopUtils setContent(String s) {
        mTvPopContent.setText(s);
        return this;
    }

    public TwoButtonPopUtils setTitle(String s) {
        mTvPopTitle.setText(s);
        return this;
    }

    /**
     * 设置确定和取消字体的颜色
     *
     * @param sureColor   确定按钮颜色
     * @param cancleColor 取消按钮颜色
     */
    public TwoButtonPopUtils setButtonColor(int sureColor, int cancleColor) {
        mTvSure.setTextColor(sureColor);
        mTvCancel.setTextColor(cancleColor);
        return this;
    }

    /**
     * 设置按钮名字
     *
     * @param sureBtn   确定按钮
     * @param cancleBtn 取消按钮
     */
    public TwoButtonPopUtils setButtonName(String sureBtn, String cancleBtn) {
        mTvSure.setText(sureBtn);
        mTvCancel.setText(cancleBtn);
        return this;

    }

    /**
     * 隐藏取消按钮
     */
    public TwoButtonPopUtils setCancelButtonGone() {
        mViewLine.setVisibility(View.GONE);
        mTvCancel.setVisibility(View.GONE);
        return this;
    }

    @OnClick({R.id.tv_cancel, R.id.tv_sure})
    public void onViewClicked(View view) {

        switch (view.getId()) {
            case R.id.tv_sure:
                if (onClickSureListener != null) {
                    onClickSureListener.onClick(view);
                }

                break;
            case R.id.tv_cancel:
                if (onClickCancelListener != null) {
                    onClickCancelListener.onClick(view);
                }
                break;
        }
        startScaleoutAnimate(mLlContainer);
    }
}
