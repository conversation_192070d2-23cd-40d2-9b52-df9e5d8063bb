package com.Tlock.io.itemBean.wallet;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.TokenInfo;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.wallet.WalletDaoUtils;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;


/**
 * @ClassName WalletViewBean
 * <AUTHOR>
 * @Data 2021/11/9 12:54
 * @Desc
 */

public class WalletViewBean extends BaseView {

    @BindView(R.id.tv_name)
    TextView mTvName;
    @BindView(R.id.tv_address)
    TextView mTvAddress;
    @BindView(R.id.iv_select)
    ImageView mIvSelect;
    @BindView(R.id.Tv_balance)
    TextView mTvBalance;
    ETHWallet ethWallet;
    @BindView(R.id.rl_root)
    RelativeLayout mRlRoot;

    public WalletViewBean(Context context) {
        super(context);
    }

    public WalletViewBean(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public WalletViewBean(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_wallet;
    }

    /**
     * 设置数据
     *
     * @param ethWallet
     * @param type      1钱包展示  2排序
     */
    @SuppressLint("SetTextI18n")
    public void setData(ETHWallet ethWallet, int type) {
        this.ethWallet = ethWallet;
        mTvName.setText(ethWallet.getName());
        mTvAddress.setText(ethWallet.getAddress());
        setSelect(ethWallet.getIsCurrent());
        TokenInfo token = WalletDaoUtils.getTokenByWalletId(ethWallet.getId());
        mTvBalance.setText(BigDecimalUtils.saveDecimals(BigDecimalUtils.division(String.valueOf(token.getBalanceOf()), "1000000"), 2)+" TOK");
    }

    public void setSelect(boolean b) {
        if (mIvSelect != null) mIvSelect.setVisibility(b ? VISIBLE : INVISIBLE);
//        mRlRoot.setBackground(b ? getResources().getDrawable(R.drawable.shape_yellow_shade_10) : getResources().getDrawable(R.drawable.shape_gray_shade_10));
    }

    private CallBack callBack;

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }
    @OnClick({R.id.tv_address})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_address:
                CopyUtils.copyToClipboard(ethWallet.getAddress());
                break;
        }
    }


    public interface CallBack {
        void edit(ETHWallet ethWallet);

        void click(ETHWallet ethWallet);
    }
}
