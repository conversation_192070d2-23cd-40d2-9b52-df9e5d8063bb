// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WarnTipView_ViewBinding implements Unbinder {
  private WarnTipView target;

  @UiThread
  public WarnTipView_ViewBinding(WarnTipView target) {
    this(target, target);
  }

  @UiThread
  public WarnTipView_ViewBinding(WarnTipView target, View source) {
    this.target = target;

    target.mTvTip = Utils.findRequiredViewAsType(source, R.id.tv_tip, "field 'mTvTip'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    WarnTipView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTip = null;
  }
}
