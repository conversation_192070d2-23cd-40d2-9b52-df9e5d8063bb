// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.custom.LoadErrorView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TopicDetailActivity_ViewBinding implements Unbinder {
  private TopicDetailActivity target;

  private View view7f090135;

  private View view7f090325;

  private View view7f09018f;

  private View view7f09031e;

  @UiThread
  public TopicDetailActivity_ViewBinding(TopicDetailActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public TopicDetailActivity_ViewBinding(final TopicDetailActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_back, "field 'mIvBack' and method 'onBindClick'");
    target.mIvBack = Utils.castView(view, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    view7f090135 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_follow, "field 'mTvFollow' and method 'onBindClick'");
    target.mTvFollow = Utils.castView(view, R.id.tv_follow, "field 'mTvFollow'", TextView.class);
    view7f090325 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvTopicAvatar = Utils.findRequiredViewAsType(source, R.id.iv_topic_avatar, "field 'mIvTopicAvatar'", ImageView.class);
    target.mTvTopicName = Utils.findRequiredViewAsType(source, R.id.tv_topic_name, "field 'mTvTopicName'", TextView.class);
    target.mTvTopicHotness = Utils.findRequiredViewAsType(source, R.id.tv_topic_hotness, "field 'mTvTopicHotness'", TextView.class);
    target.mTvTopicFollow = Utils.findRequiredViewAsType(source, R.id.tv_topic_follow, "field 'mTvTopicFollow'", TextView.class);
    target.mLlTitle = Utils.findRequiredViewAsType(source, R.id.ll_title, "field 'mLlTitle'", LinearLayout.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mRecyclerView = Utils.findRequiredViewAsType(source, R.id.recyclerView, "field 'mRecyclerView'", RecyclerView.class);
    target.mLoadError = Utils.findRequiredViewAsType(source, R.id.load_error, "field 'mLoadError'", LoadErrorView.class);
    target.mRefreshLayout = Utils.findRequiredViewAsType(source, R.id.refresh_layout, "field 'mRefreshLayout'", SmartRefreshLayout.class);
    target.mLine2 = Utils.findRequiredView(source, R.id.line2, "field 'mLine2'");
    target.mIvUserHeard = Utils.findRequiredViewAsType(source, R.id.iv_user_heard, "field 'mIvUserHeard'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_comment, "field 'mLlComment' and method 'onBindClick'");
    target.mLlComment = Utils.castView(view, R.id.ll_comment, "field 'mLlComment'", RelativeLayout.class);
    view7f09018f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_edit, "field 'mTvEdit' and method 'onBindClick'");
    target.mTvEdit = Utils.castView(view, R.id.tv_edit, "field 'mTvEdit'", TextView.class);
    view7f09031e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    TopicDetailActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mTvFollow = null;
    target.mIvTopicAvatar = null;
    target.mTvTopicName = null;
    target.mTvTopicHotness = null;
    target.mTvTopicFollow = null;
    target.mLlTitle = null;
    target.mLine1 = null;
    target.mRecyclerView = null;
    target.mLoadError = null;
    target.mRefreshLayout = null;
    target.mLine2 = null;
    target.mIvUserHeard = null;
    target.mLlComment = null;
    target.mMain = null;
    target.mTvEdit = null;

    view7f090135.setOnClickListener(null);
    view7f090135 = null;
    view7f090325.setOnClickListener(null);
    view7f090325 = null;
    view7f09018f.setOnClickListener(null);
    view7f09018f = null;
    view7f09031e.setOnClickListener(null);
    view7f09031e = null;
  }
}
