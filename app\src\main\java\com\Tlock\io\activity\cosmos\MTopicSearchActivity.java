package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.itemBean.cosmos.TopicListItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.SearchView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import java.util.ArrayList;

import butterknife.BindView;


public class MTopicSearchActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.search_view)
    SearchView mSearchView;
    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    @BindView(R.id.main)
    RelativeLayout mMain;
    private BaseRecyclerViewAdapter<PostQueryProto.TopicResponse> adapter;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context
    ) {
        Intent intent = new Intent(context, MTopicSearchActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_topic_search;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRecycleView();
        mSearchView.setClickTextListner(new SearchView.TextListener() {
            @Override
            public void clickSearch(String searchText) {
                getTopicData(searchText);
            }
        });

    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        adapter = new BaseRecyclerViewAdapter<>(getActivity(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.TopicResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                TopicListItemView itemView = new TopicListItemView(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.TopicResponse data, View view) {
                ((TopicListItemView) view).setData(data);
                ((TopicListItemView) view).setCallback(new TopicListItemView.Callback() {
                    @Override
                    public void resetProfile(PostQueryProto.TopicResponse data) {
                        adapter.getList().set(position, data);
                    }
                });
            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.TopicResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.TopicResponse data, View view) {
                MTopicDetailActivity.start(getActivity(), data.getId(),"",data.getName());
            }
        });
    }

    public void getTopicData(String math) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.TopicResponse> topic = CosmosUtils.getTopic(math);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (topic.size() != 0) {
                            adapter.setList(topic);

                        } else {
                            PostQueryProto.TopicResponse build = PostQueryProto.TopicResponse.newBuilder().setName("#" + math).build();
                            ArrayList<PostQueryProto.TopicResponse> objects = new ArrayList<>();
                            objects.add(build);
                            adapter.setList(objects);
                        }
                    }
                });
            }
        });
    }

    @Override
    protected void loadData() {

    }

}