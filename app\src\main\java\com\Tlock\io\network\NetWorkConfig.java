package com.Tlock.io.network;

public class NetWorkConfig {


    public static final String HTTP_CONNECT_ERROR = "error";
    public static final String HTTP_DATA_ERROR = "数据错误";
    public static final String HTTP_SERVER_EXCEPTION = "error";
    public static final String HTTP_DOWNLOAD_ERROR = "下载失败";
    public static final String HTTP_NO_DATA = "not data";
    public static final String COMMING = "comming";

    public static final int NET_NO_NET = 0;
    public static final int NET_4G = 1;
    public static final int NET_WIFI = 2;
}
