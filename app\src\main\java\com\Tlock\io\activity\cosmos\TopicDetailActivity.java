package com.Tlock.io.activity.cosmos;

import static com.Tlock.io.utils.UserUtil.dip2px;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.PostItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.pop.PopPostForward;
import com.Tlock.io.widget.pop.PopPostMore;
import com.Tlock.io.widget.pop.PopPostShare;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;

public class TopicDetailActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.tv_follow)
    TextView mTvFollow;
    @BindView(R.id.iv_topic_avatar)
    ImageView mIvTopicAvatar;
    @BindView(R.id.tv_topic_name)
    TextView mTvTopicName;
    @BindView(R.id.tv_topic_hotness)
    TextView mTvTopicHotness;
    @BindView(R.id.tv_topic_follow)
    TextView mTvTopicFollow;
    @BindView(R.id.ll_title)
    LinearLayout mLlTitle;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    @BindView(R.id.line2)
    View mLine2;
    @BindView(R.id.iv_user_heard)
    ImageView mIvUserHeard;
    @BindView(R.id.ll_comment)
    RelativeLayout mLlComment;
    @BindView(R.id.main)
    RelativeLayout mMain;
    @BindView(R.id.tv_edit)
    TextView mTvEdit;
    private BaseRecyclerViewAdapter<PostQueryProto.PostResponse> adapter;
    private ETHWallet current;
    private boolean isFollow = false;
    private PostQueryProto.TopicResponse data;
    private String dataStr;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context, String data) {
        Intent intent = new Intent(context, TopicDetailActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_topic_detail;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
        dataStr = getIntent().getStringExtra("data");
        data = JsonUtils.jsonToObject(dataStr, PostQueryProto.TopicResponse.class);
        mTvTopicName.setText(data.getName());
        current = WalletDaoUtils.getCurrent();
        mTvTopicHotness.setText(BigDecimalUtils.saveDecimals(data.getScore() + "", 0));
        initRecycleView();
        if (data.getImage().isEmpty()) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String authStr = CosmosUtils.getTopicHeard(data.getId());

                    mIvTopicAvatar.post(new Runnable() {
                        @Override
                        public void run() {
                            mIvTopicAvatar.setVisibility(View.VISIBLE);
                            int radiusPx = dip2px(getActivity(), 6);
                            RequestOptions options = new RequestOptions()
                                    .bitmapTransform(new RoundedCorners(radiusPx)) // 统一设置圆角
                                    .placeholder(getResources().getDrawable(R.mipmap.topic_heard_default));
                            if (!TextUtils.isEmpty(authStr)) {
                                PostQueryProto.TopicResponse build = data.toBuilder().setImage(authStr).build();
                                data = build;
                                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authStr);
                                Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                .centerCrop()
                                                .dontTransform())
                                        .apply(options)
                                        .into(mIvTopicAvatar);
                            } else {
                                mIvTopicAvatar.setVisibility(View.GONE);

                            }
                        }
                    });
                }
            });

        } else {
            mIvTopicAvatar.setVisibility(View.VISIBLE);
            Bitmap bitmap1 = BitmapUtils.base64ToBitmap(data.getImage());

            int radiusPx = dip2px(getActivity(), 6);
            RequestOptions options = new RequestOptions()
                    .bitmapTransform(new RoundedCorners(radiusPx)) // 统一设置圆角
                    .placeholder(getResources().getDrawable(R.mipmap.topic_heard_default));
            Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                            .centerCrop()
                            .dontTransform())
                    .apply(options)
                    .into(mIvTopicAvatar);
        }

        try {
            String userInfo = SpUtil.getUserInfo();
            ProfileProto.Profile profile = JsonUtils.jsonToObject(userInfo, ProfileProto.Profile.class);
            if (TextUtils.isEmpty(profile.getAvatar())) {
                TextAvatarDrawable a = new TextAvatarDrawable(profile.getUserHandle().substring(0, 1));
                mIvUserHeard.setImageDrawable(a);
            } else {
                if (profile.getAvatar().startsWith("http")) {
                    Glide.with(getActivity()).load(profile.getAvatar()).apply(new RequestOptions()
                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                    .signature(new ObjectKey(profile.getAvatar()))
                                    .centerCrop()
                                    .format(DecodeFormat.PREFER_RGB_565)
                                    .dontTransform())
                            .apply(RequestOptions.circleCropTransform().circleCrop())
                            .into(mIvUserHeard);
                } else {
                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(profile.getAvatar());
                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                    .centerCrop()
                                    .dontTransform())
                            .apply(RequestOptions.circleCropTransform().circleCrop())
                            .into(mIvUserHeard);
                }
            }
        } catch (Exception e) {
//            Log.e(TAG, "initView: " + e.getMessage());
        }
        mTvTopicName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                clickCount++;
                if (clickCount == 5) {
                    EditTopicActivity.start(getActivity(), dataStr);
                }
            }
        });
    }
private int clickCount = 0;
    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        adapter = new BaseRecyclerViewAdapter<>(getActivity(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.PostResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                PostItemView itemView = new PostItemView(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.PostResponse data, View view) {
                ((PostItemView) view).setData(data);
                ((PostItemView) view).setCallback(new PostItemView.Callback() {

                    @Override
                    public void quote() {
                        PopPostForward popPostMore = new PopPostForward(getActivity(), data.getPost().getId(), "");
                        popPostMore.setCallBack(new PopPostForward.CallBack() {

                            @Override
                            public void repost(String id) {
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.postRepost(WalletDaoUtils.getCurrent().getAddress(), id);

                                    }
                                });
                            }

                            @Override
                            public void quote(String id) {
                                PostQuoteActivity.start(getActivity(), id);
                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
//                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
//                            @Override
//                            public void run() {
//                                PostTXProto.CastVoteOnPollRequest build = PostTXProto.CastVoteOnPollRequest.newBuilder()
//                                        .setCreator(WalletDaoUtils.getCurrent().getAddress())
//                                        .setId(data.getPost().getId())
//                                        .setOptionId(0)
//                                        .build();
//                                CosmosUtils.sendPoll(build);
//                            }
//                        });

                    }

                    @Override
                    public void share() {
                        PopPostShare popPostMore = new PopPostShare(getActivity());
                        popPostMore.setCallBack(new PopPostShare.CallBack() {

                            @Override
                            public void follow(String handle) {

                            }

                            @Override
                            public void report(String id) {

                            }

                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();

                    }

                    @Override
                    public void review(String id, String handle) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), true);
                    }


                    @Override
                    public void praise(String id) {
                        //点赞
                        if (current != null) {
                            showToast("Liked");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    CosmosUtils.postLike(current.getAddress(), id);
                                }
                            });
                        }

                    }

                    @Override
                    public void collect(String id) {
                        //收藏
                        if (current != null) {
                            showToast("Added to bookmarks");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    CosmosUtils.savePost(current.getAddress(), id);
                                }
                            });
                        }


                    }

                    @Override
                    public void more(String data, String handle, String address) {
                        PopPostMore popPostMore = new PopPostMore(getActivity(), data, handle);
                        popPostMore.setCallBack(new PopPostMore.CallBack() {
                            @Override
                            public void follow(String handle) {
                                showToast("Following");
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.follow(current.getAddress(), address);
                                    }
                                });
                            }

                            @Override
                            public void report(String id) {
                                //TODO 新页面
                                showToast("Not Nvailable");

                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
                    }

                    @Override
                    public void info(PostQueryProto.PostResponse data) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
                    }

                    @Override
                    public void resetProfile(PostQueryProto.PostResponse postResponse) {
                        adapter.getList().set(position, postResponse);
                    }
                });

            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.PostResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.PostResponse data, View view) {
                ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
            }
        });
    }

    @Override
    protected void loadData() {
        getRecycleData();
    }

    @Override
    protected void getData() {
        super.getData();
        getRecycleData();
    }

    private void getRecycleData() {
        if (data == null) return;
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.PostResponse> postsByTopics = CosmosUtils.getPostsByTopics(data.getId(), page);
                boolean isFollowingTopic = CosmosUtils.queryIsFollowingTopic(data.getId());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.addListNoChange(postsByTopics, page);
                        mRefreshLayout.finishRefresh();
                        mRefreshLayout.finishLoadMore();
                        if (isFollowingTopic) {
                            mTvFollow.setText("Leave");
                            mTvFollow.setTextColor(getResources().getColor(R.color.cosmos_black));
                            mTvFollow.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
                            isFollow = true;
                        } else {
                            mTvFollow.setText("Join");
                            mTvFollow.setTextColor(getColor(R.color.white));
                            mTvFollow.setBackground(getResources().getDrawable(R.drawable.btn_black_60));
                            isFollow = false;
                        }
                    }
                });
            }
        });
    }


    @OnClick({R.id.iv_back, R.id.tv_follow, R.id.tv_edit, R.id.ll_comment})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.ll_comment:
                PostActivity.start(getActivity(), 1, data.getName());
                break;
            case R.id.tv_edit:
                EditTopicActivity.start(getActivity(), dataStr);
                break;
            case R.id.tv_follow:
                if (isFollow) {
                    setUnFollow();
                } else {
                    setFollow();
                }
                break;
        }
    }


    private void setFollow() {
        isFollow = true;
        mTvFollow.setText("Leave");
        mTvFollow.setTextColor(getColor(R.color.cosmos_black));
        mTvFollow.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
        showToast("Join success");
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.followTopic(data.getId());
            }
        });
    }

    @Override
    public void onDestroy() {
        EventBus.getDefault().unregister(this);
        super.onDestroy();

    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(Event event) {
        if (event.getMessgae().equalsIgnoreCase("refresh_topic")) {
//            Log.e(TAG, "onEventMainThread: 刷新");
            getRecycleData();
        }
    }

    private boolean isFirst = true;

    private void setUnFollow() {
        isFollow = false;
        mTvFollow.setText("Join");
        mTvFollow.setTextColor(getColor(R.color.white));
        mTvFollow.setBackground(getResources().getDrawable(R.drawable.btn_black_60));
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.unFollowTopic(data.getId());
                mTvFollow.post(new Runnable() {
                    @Override
                    public void run() {
                        showToast("Leave success");

                    }

                });
            }
        });
    }
}