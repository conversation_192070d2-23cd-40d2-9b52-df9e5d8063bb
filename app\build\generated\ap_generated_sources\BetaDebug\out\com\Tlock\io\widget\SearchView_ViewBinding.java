// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class SearchView_ViewBinding implements Unbinder {
  private SearchView target;

  @UiThread
  public SearchView_ViewBinding(SearchView target) {
    this(target, target);
  }

  @UiThread
  public SearchView_ViewBinding(SearchView target, View source) {
    this.target = target;

    target.mEdSearchText = Utils.findRequiredViewAsType(source, R.id.ed_search_text, "field 'mEdSearchText'", EditText.class);
    target.mIvClear = Utils.findRequiredViewAsType(source, R.id.iv_clear, "field 'mIvClear'", ImageView.class);
    target.mTvClear = Utils.findRequiredViewAsType(source, R.id.tv_clear, "field 'mTvClear'", TextView.class);
    target.mLlClear = Utils.findRequiredViewAsType(source, R.id.ll_clear, "field 'mLlClear'", LinearLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    SearchView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mEdSearchText = null;
    target.mIvClear = null;
    target.mTvClear = null;
    target.mLlClear = null;
  }
}
