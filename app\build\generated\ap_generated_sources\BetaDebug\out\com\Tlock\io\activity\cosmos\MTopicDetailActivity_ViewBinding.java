// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import com.zhy.view.flowlayout.TagFlowLayout;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MTopicDetailActivity_ViewBinding implements Unbinder {
  private MTopicDetailActivity target;

  private View view7f09008e;

  @UiThread
  public MTopicDetailActivity_ViewBinding(MTopicDetailActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MTopicDetailActivity_ViewBinding(final MTopicDetailActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mTvTopicName = Utils.findRequiredViewAsType(source, R.id.tv_topic_name, "field 'mTvTopicName'", TextView.class);
    target.mFlowlayoutMnemonic = Utils.findRequiredViewAsType(source, R.id.flowlayout_mnemonic, "field 'mFlowlayoutMnemonic'", TagFlowLayout.class);
    view = Utils.findRequiredView(source, R.id.confirm_button, "field 'mConfirmButton' and method 'onBindClick'");
    target.mConfirmButton = Utils.castView(view, R.id.confirm_button, "field 'mConfirmButton'", TextView.class);
    view7f09008e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    MTopicDetailActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mTvTopicName = null;
    target.mFlowlayoutMnemonic = null;
    target.mConfirmButton = null;

    view7f09008e.setOnClickListener(null);
    view7f09008e = null;
  }
}
