package com.Tlock.io.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.Tlock.io.app.AppApplication;
import com.Tlock.io.entity.cosmos.SearchHistoryBean;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;


/**
 * 本类的主要功能是 :  SharePreference缓存工具类
 */
public class SpUtil {
    private static final Context mcontext;

    static {
        AppApplication instance = AppApplication.getInstance();
        mcontext = instance.getApplicationContext();
    }

    /**
     * 该配置文件被自己的应用程序访问
     */
    private static SharedPreferences getPreferences() {
        if (mcontext != null) {
            return mcontext.getSharedPreferences(
                    Share_Preferences_Name_Mode_Private,
                    Context.MODE_PRIVATE);
        }
        return null;
    }

    public static final String Share_Preferences_Name_Mode_Private = "sp_private";

    public static final String START_FAN_TAG = "START_FAN_TAG";//是否展示fan页面
    public static final String MESSAGE_COUNT = "MESSAGE_COUNT";//未读消息数量
    public static final String SEARCH_HISTORY = "SEARCH_HISTORY";//搜索历史
    public static final String USER_INFO = "USER_INFO";//用户信息";
    public static final String TOPIC_CLASSIFY = "TOPIC_CLASSIFY";//话题分类";


    /**
     * 设置用户信息
     */
    public static boolean setUserInfo(String userInfo) {
        if (getPreferences() != null) {
            return getPreferences().edit().putString(USER_INFO, userInfo).commit();
        }
        return false;
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    public static String getUserInfo() {
        if (getPreferences() != null) {
            String string = getPreferences().getString(USER_INFO, "");
            return string;
        }
        return "";
    }


    /**
     * 启动展示页码
     */
    public static boolean setStartFlag(int showTag) {
        if (getPreferences() != null) {
            return getPreferences().edit().putInt(START_FAN_TAG, showTag).commit();
        }
        return false;
    }

    /**
     * 启动展示
     *
     * @return
     */
    public static int getStartFlag() {
        if (getPreferences() != null) {
            int isShow = getPreferences().getInt(START_FAN_TAG, 0);
            return isShow;
        }
        return 0;
    }

    /**
     * 设置消息数量
     */
    public static boolean setMessageCount(int isShowTag) {
        if (getPreferences() != null) {
            return getPreferences().edit().putInt(MESSAGE_COUNT+WalletDaoUtils.getCurrent().getAddress(), isShowTag).commit();
        }
        return false;
    }

    /**
     * 获取消息数量
     *
     * @return
     */
    public static int getMessageCount() {
        if (getPreferences() != null) {
            int isShow = getPreferences().getInt(MESSAGE_COUNT+WalletDaoUtils.getCurrent().getAddress(), 0);
            return isShow;
        }
        return 0;
    }


    /**
     * 设置历史
     */
    public static boolean setSearchHistory(ArrayList<SearchHistoryBean> data) {
        if (getPreferences() != null) {
            return getPreferences().edit().putString(SEARCH_HISTORY, JsonUtils.listToJson(data)).commit();
        }
        return false;
    }

    /**
     * 获取历史
     *
     * @return
     */
    public static ArrayList<SearchHistoryBean> getSearchHistory() {
        if (getPreferences() != null) {
            String str = getPreferences().getString(SEARCH_HISTORY, "");
            if (str.isEmpty()) {
                return new ArrayList<>();
            } else {
                Type type = new TypeToken<List<SearchHistoryBean>>() {
                }.getType();
                ArrayList<SearchHistoryBean> list = new Gson().fromJson(str, type);
                return list;
            }
        }
        return new ArrayList<>();
    }
    /**
     * 设置历史
     */
    public static boolean setTopicClassify( ArrayList<PostQueryProto.CategoryResponse> data) {
        if (getPreferences() != null) {
            return getPreferences().edit().putString(TOPIC_CLASSIFY, JsonUtils.listToJson(data)).commit();
        }
        return false;
    }

    /**
     * 获取历史
     *
     * @return
     */
    public static  ArrayList<PostQueryProto.CategoryResponse> getTopicClassify() {
        if (getPreferences() != null) {
            String str = getPreferences().getString(TOPIC_CLASSIFY, "");
            if (str.isEmpty()) {
                return new ArrayList<>();
            } else {
                Type type = new TypeToken<List<PostQueryProto.CategoryResponse>>() {
                }.getType();
                ArrayList<PostQueryProto.CategoryResponse> list = new Gson().fromJson(str, type);
                return list;
            }
        }
        return new ArrayList<>();
    }


}
