<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/toast_blue_60"
    android:maxWidth="@dimen/dp_320">

    <TextView
        android:id="@+id/toast_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginRight="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_8"
        android:textColor="@color/white"
        android:textSize="16sp" />
</RelativeLayout>