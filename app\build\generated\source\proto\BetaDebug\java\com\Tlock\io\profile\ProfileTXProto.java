// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: profile/profile_tx.proto
// Protobuf Java Version: 4.28.3

package com.Tlock.io.profile;

public final class ProfileTXProto {
  private ProfileTXProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      ProfileTXProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ManageOptionsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.ManageOptions)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string line_manager = 1;</code>
     * @return The lineManager.
     */
    java.lang.String getLineManager();
    /**
     * <code>string line_manager = 1;</code>
     * @return The bytes for lineManager.
     */
    com.google.protobuf.ByteString
        getLineManagerBytes();

    /**
     * <code>string admin_address = 2;</code>
     * @return The adminAddress.
     */
    java.lang.String getAdminAddress();
    /**
     * <code>string admin_address = 2;</code>
     * @return The bytes for adminAddress.
     */
    com.google.protobuf.ByteString
        getAdminAddressBytes();

    /**
     * <code>uint64 admin_level = 3;</code>
     * @return The adminLevel.
     */
    long getAdminLevel();

    /**
     * <code>bool editable = 4;</code>
     * @return The editable.
     */
    boolean getEditable();
  }
  /**
   * Protobuf type {@code profile.v1.ManageOptions}
   */
  public static final class ManageOptions extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.ManageOptions)
      ManageOptionsOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        ManageOptions.class.getName());
    }
    // Use ManageOptions.newBuilder() to construct.
    private ManageOptions(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ManageOptions() {
      lineManager_ = "";
      adminAddress_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ManageOptions_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ManageOptions_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.ManageOptions.class, com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder.class);
    }

    public static final int LINE_MANAGER_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object lineManager_ = "";
    /**
     * <code>string line_manager = 1;</code>
     * @return The lineManager.
     */
    @java.lang.Override
    public java.lang.String getLineManager() {
      java.lang.Object ref = lineManager_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lineManager_ = s;
        return s;
      }
    }
    /**
     * <code>string line_manager = 1;</code>
     * @return The bytes for lineManager.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLineManagerBytes() {
      java.lang.Object ref = lineManager_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lineManager_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ADMIN_ADDRESS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object adminAddress_ = "";
    /**
     * <code>string admin_address = 2;</code>
     * @return The adminAddress.
     */
    @java.lang.Override
    public java.lang.String getAdminAddress() {
      java.lang.Object ref = adminAddress_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        adminAddress_ = s;
        return s;
      }
    }
    /**
     * <code>string admin_address = 2;</code>
     * @return The bytes for adminAddress.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAdminAddressBytes() {
      java.lang.Object ref = adminAddress_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        adminAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ADMIN_LEVEL_FIELD_NUMBER = 3;
    private long adminLevel_ = 0L;
    /**
     * <code>uint64 admin_level = 3;</code>
     * @return The adminLevel.
     */
    @java.lang.Override
    public long getAdminLevel() {
      return adminLevel_;
    }

    public static final int EDITABLE_FIELD_NUMBER = 4;
    private boolean editable_ = false;
    /**
     * <code>bool editable = 4;</code>
     * @return The editable.
     */
    @java.lang.Override
    public boolean getEditable() {
      return editable_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lineManager_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, lineManager_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adminAddress_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, adminAddress_);
      }
      if (adminLevel_ != 0L) {
        output.writeUInt64(3, adminLevel_);
      }
      if (editable_ != false) {
        output.writeBool(4, editable_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lineManager_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, lineManager_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(adminAddress_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, adminAddress_);
      }
      if (adminLevel_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, adminLevel_);
      }
      if (editable_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, editable_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.ManageOptions)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.ManageOptions other = (com.Tlock.io.profile.ProfileTXProto.ManageOptions) obj;

      if (!getLineManager()
          .equals(other.getLineManager())) return false;
      if (!getAdminAddress()
          .equals(other.getAdminAddress())) return false;
      if (getAdminLevel()
          != other.getAdminLevel()) return false;
      if (getEditable()
          != other.getEditable()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LINE_MANAGER_FIELD_NUMBER;
      hash = (53 * hash) + getLineManager().hashCode();
      hash = (37 * hash) + ADMIN_ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAdminAddress().hashCode();
      hash = (37 * hash) + ADMIN_LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAdminLevel());
      hash = (37 * hash) + EDITABLE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getEditable());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.ManageOptions prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.ManageOptions}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.ManageOptions)
        com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ManageOptions_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ManageOptions_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.ManageOptions.class, com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.ManageOptions.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        lineManager_ = "";
        adminAddress_ = "";
        adminLevel_ = 0L;
        editable_ = false;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ManageOptions_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.ManageOptions getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.ManageOptions build() {
        com.Tlock.io.profile.ProfileTXProto.ManageOptions result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.ManageOptions buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.ManageOptions result = new com.Tlock.io.profile.ProfileTXProto.ManageOptions(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.ManageOptions result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.lineManager_ = lineManager_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.adminAddress_ = adminAddress_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.adminLevel_ = adminLevel_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.editable_ = editable_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.ManageOptions) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.ManageOptions)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.ManageOptions other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance()) return this;
        if (!other.getLineManager().isEmpty()) {
          lineManager_ = other.lineManager_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getAdminAddress().isEmpty()) {
          adminAddress_ = other.adminAddress_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.getAdminLevel() != 0L) {
          setAdminLevel(other.getAdminLevel());
        }
        if (other.getEditable() != false) {
          setEditable(other.getEditable());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                lineManager_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                adminAddress_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                adminLevel_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                editable_ = input.readBool();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object lineManager_ = "";
      /**
       * <code>string line_manager = 1;</code>
       * @return The lineManager.
       */
      public java.lang.String getLineManager() {
        java.lang.Object ref = lineManager_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          lineManager_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string line_manager = 1;</code>
       * @return The bytes for lineManager.
       */
      public com.google.protobuf.ByteString
          getLineManagerBytes() {
        java.lang.Object ref = lineManager_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lineManager_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string line_manager = 1;</code>
       * @param value The lineManager to set.
       * @return This builder for chaining.
       */
      public Builder setLineManager(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        lineManager_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string line_manager = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineManager() {
        lineManager_ = getDefaultInstance().getLineManager();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string line_manager = 1;</code>
       * @param value The bytes for lineManager to set.
       * @return This builder for chaining.
       */
      public Builder setLineManagerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        lineManager_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object adminAddress_ = "";
      /**
       * <code>string admin_address = 2;</code>
       * @return The adminAddress.
       */
      public java.lang.String getAdminAddress() {
        java.lang.Object ref = adminAddress_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          adminAddress_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string admin_address = 2;</code>
       * @return The bytes for adminAddress.
       */
      public com.google.protobuf.ByteString
          getAdminAddressBytes() {
        java.lang.Object ref = adminAddress_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          adminAddress_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string admin_address = 2;</code>
       * @param value The adminAddress to set.
       * @return This builder for chaining.
       */
      public Builder setAdminAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        adminAddress_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string admin_address = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAdminAddress() {
        adminAddress_ = getDefaultInstance().getAdminAddress();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string admin_address = 2;</code>
       * @param value The bytes for adminAddress to set.
       * @return This builder for chaining.
       */
      public Builder setAdminAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        adminAddress_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private long adminLevel_ ;
      /**
       * <code>uint64 admin_level = 3;</code>
       * @return The adminLevel.
       */
      @java.lang.Override
      public long getAdminLevel() {
        return adminLevel_;
      }
      /**
       * <code>uint64 admin_level = 3;</code>
       * @param value The adminLevel to set.
       * @return This builder for chaining.
       */
      public Builder setAdminLevel(long value) {

        adminLevel_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 admin_level = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAdminLevel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        adminLevel_ = 0L;
        onChanged();
        return this;
      }

      private boolean editable_ ;
      /**
       * <code>bool editable = 4;</code>
       * @return The editable.
       */
      @java.lang.Override
      public boolean getEditable() {
        return editable_;
      }
      /**
       * <code>bool editable = 4;</code>
       * @param value The editable to set.
       * @return This builder for chaining.
       */
      public Builder setEditable(boolean value) {

        editable_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>bool editable = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearEditable() {
        bitField0_ = (bitField0_ & ~0x00000008);
        editable_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.ManageOptions)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.ManageOptions)
    private static final com.Tlock.io.profile.ProfileTXProto.ManageOptions DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.ManageOptions();
    }

    public static com.Tlock.io.profile.ProfileTXProto.ManageOptions getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ManageOptions>
        PARSER = new com.google.protobuf.AbstractParser<ManageOptions>() {
      @java.lang.Override
      public ManageOptions parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ManageOptions> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ManageOptions> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.ManageOptions getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgManageAdminRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgManageAdminRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    java.lang.String getCreator();
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    com.google.protobuf.ByteString
        getCreatorBytes();

    /**
     * <code>.profile.v1.ManageOptions manage_json = 2;</code>
     * @return Whether the manageJson field is set.
     */
    boolean hasManageJson();
    /**
     * <code>.profile.v1.ManageOptions manage_json = 2;</code>
     * @return The manageJson.
     */
    com.Tlock.io.profile.ProfileTXProto.ManageOptions getManageJson();
    /**
     * <code>.profile.v1.ManageOptions manage_json = 2;</code>
     */
    com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder getManageJsonOrBuilder();

    /**
     * <code>string action = 3;</code>
     * @return The action.
     */
    java.lang.String getAction();
    /**
     * <code>string action = 3;</code>
     * @return The bytes for action.
     */
    com.google.protobuf.ByteString
        getActionBytes();
  }
  /**
   * Protobuf type {@code profile.v1.MsgManageAdminRequest}
   */
  public static final class MsgManageAdminRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgManageAdminRequest)
      MsgManageAdminRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgManageAdminRequest.class.getName());
    }
    // Use MsgManageAdminRequest.newBuilder() to construct.
    private MsgManageAdminRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgManageAdminRequest() {
      creator_ = "";
      action_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.Builder.class);
    }

    private int bitField0_;
    public static final int CREATOR_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object creator_ = "";
    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    @java.lang.Override
    public java.lang.String getCreator() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creator_ = s;
        return s;
      }
    }
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCreatorBytes() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MANAGE_JSON_FIELD_NUMBER = 2;
    private com.Tlock.io.profile.ProfileTXProto.ManageOptions manageJson_;
    /**
     * <code>.profile.v1.ManageOptions manage_json = 2;</code>
     * @return Whether the manageJson field is set.
     */
    @java.lang.Override
    public boolean hasManageJson() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.profile.v1.ManageOptions manage_json = 2;</code>
     * @return The manageJson.
     */
    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.ManageOptions getManageJson() {
      return manageJson_ == null ? com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance() : manageJson_;
    }
    /**
     * <code>.profile.v1.ManageOptions manage_json = 2;</code>
     */
    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder getManageJsonOrBuilder() {
      return manageJson_ == null ? com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance() : manageJson_;
    }

    public static final int ACTION_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object action_ = "";
    /**
     * <code>string action = 3;</code>
     * @return The action.
     */
    @java.lang.Override
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      }
    }
    /**
     * <code>string action = 3;</code>
     * @return The bytes for action.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, creator_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getManageJson());
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(action_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, action_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, creator_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getManageJson());
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(action_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, action_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest other = (com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest) obj;

      if (!getCreator()
          .equals(other.getCreator())) return false;
      if (hasManageJson() != other.hasManageJson()) return false;
      if (hasManageJson()) {
        if (!getManageJson()
            .equals(other.getManageJson())) return false;
      }
      if (!getAction()
          .equals(other.getAction())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CREATOR_FIELD_NUMBER;
      hash = (53 * hash) + getCreator().hashCode();
      if (hasManageJson()) {
        hash = (37 * hash) + MANAGE_JSON_FIELD_NUMBER;
        hash = (53 * hash) + getManageJson().hashCode();
      }
      hash = (37 * hash) + ACTION_FIELD_NUMBER;
      hash = (53 * hash) + getAction().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgManageAdminRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgManageAdminRequest)
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getManageJsonFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        creator_ = "";
        manageJson_ = null;
        if (manageJsonBuilder_ != null) {
          manageJsonBuilder_.dispose();
          manageJsonBuilder_ = null;
        }
        action_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest build() {
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest result = new com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.creator_ = creator_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.manageJson_ = manageJsonBuilder_ == null
              ? manageJson_
              : manageJsonBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.action_ = action_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.getDefaultInstance()) return this;
        if (!other.getCreator().isEmpty()) {
          creator_ = other.creator_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasManageJson()) {
          mergeManageJson(other.getManageJson());
        }
        if (!other.getAction().isEmpty()) {
          action_ = other.action_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                creator_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getManageJsonFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                action_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object creator_ = "";
      /**
       * <code>string creator = 1;</code>
       * @return The creator.
       */
      public java.lang.String getCreator() {
        java.lang.Object ref = creator_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @return The bytes for creator.
       */
      public com.google.protobuf.ByteString
          getCreatorBytes() {
        java.lang.Object ref = creator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreator(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreator() {
        creator_ = getDefaultInstance().getCreator();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The bytes for creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private com.Tlock.io.profile.ProfileTXProto.ManageOptions manageJson_;
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.profile.ProfileTXProto.ManageOptions, com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder, com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder> manageJsonBuilder_;
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       * @return Whether the manageJson field is set.
       */
      public boolean hasManageJson() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       * @return The manageJson.
       */
      public com.Tlock.io.profile.ProfileTXProto.ManageOptions getManageJson() {
        if (manageJsonBuilder_ == null) {
          return manageJson_ == null ? com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance() : manageJson_;
        } else {
          return manageJsonBuilder_.getMessage();
        }
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      public Builder setManageJson(com.Tlock.io.profile.ProfileTXProto.ManageOptions value) {
        if (manageJsonBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          manageJson_ = value;
        } else {
          manageJsonBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      public Builder setManageJson(
          com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder builderForValue) {
        if (manageJsonBuilder_ == null) {
          manageJson_ = builderForValue.build();
        } else {
          manageJsonBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      public Builder mergeManageJson(com.Tlock.io.profile.ProfileTXProto.ManageOptions value) {
        if (manageJsonBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            manageJson_ != null &&
            manageJson_ != com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance()) {
            getManageJsonBuilder().mergeFrom(value);
          } else {
            manageJson_ = value;
          }
        } else {
          manageJsonBuilder_.mergeFrom(value);
        }
        if (manageJson_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      public Builder clearManageJson() {
        bitField0_ = (bitField0_ & ~0x00000002);
        manageJson_ = null;
        if (manageJsonBuilder_ != null) {
          manageJsonBuilder_.dispose();
          manageJsonBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      public com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder getManageJsonBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getManageJsonFieldBuilder().getBuilder();
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      public com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder getManageJsonOrBuilder() {
        if (manageJsonBuilder_ != null) {
          return manageJsonBuilder_.getMessageOrBuilder();
        } else {
          return manageJson_ == null ?
              com.Tlock.io.profile.ProfileTXProto.ManageOptions.getDefaultInstance() : manageJson_;
        }
      }
      /**
       * <code>.profile.v1.ManageOptions manage_json = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.profile.ProfileTXProto.ManageOptions, com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder, com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder> 
          getManageJsonFieldBuilder() {
        if (manageJsonBuilder_ == null) {
          manageJsonBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.Tlock.io.profile.ProfileTXProto.ManageOptions, com.Tlock.io.profile.ProfileTXProto.ManageOptions.Builder, com.Tlock.io.profile.ProfileTXProto.ManageOptionsOrBuilder>(
                  getManageJson(),
                  getParentForChildren(),
                  isClean());
          manageJson_ = null;
        }
        return manageJsonBuilder_;
      }

      private java.lang.Object action_ = "";
      /**
       * <code>string action = 3;</code>
       * @return The action.
       */
      public java.lang.String getAction() {
        java.lang.Object ref = action_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          action_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string action = 3;</code>
       * @return The bytes for action.
       */
      public com.google.protobuf.ByteString
          getActionBytes() {
        java.lang.Object ref = action_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          action_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string action = 3;</code>
       * @param value The action to set.
       * @return This builder for chaining.
       */
      public Builder setAction(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        action_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string action = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAction() {
        action_ = getDefaultInstance().getAction();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string action = 3;</code>
       * @param value The bytes for action to set.
       * @return This builder for chaining.
       */
      public Builder setActionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        action_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgManageAdminRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgManageAdminRequest)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgManageAdminRequest>
        PARSER = new com.google.protobuf.AbstractParser<MsgManageAdminRequest>() {
      @java.lang.Override
      public MsgManageAdminRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgManageAdminRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgManageAdminRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgManageAdminResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgManageAdminResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bool status = 1;</code>
     * @return The status.
     */
    boolean getStatus();
  }
  /**
   * Protobuf type {@code profile.v1.MsgManageAdminResponse}
   */
  public static final class MsgManageAdminResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgManageAdminResponse)
      MsgManageAdminResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgManageAdminResponse.class.getName());
    }
    // Use MsgManageAdminResponse.newBuilder() to construct.
    private MsgManageAdminResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgManageAdminResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.Builder.class);
    }

    public static final int STATUS_FIELD_NUMBER = 1;
    private boolean status_ = false;
    /**
     * <code>bool status = 1;</code>
     * @return The status.
     */
    @java.lang.Override
    public boolean getStatus() {
      return status_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (status_ != false) {
        output.writeBool(1, status_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (status_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, status_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse other = (com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse) obj;

      if (getStatus()
          != other.getStatus()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getStatus());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgManageAdminResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgManageAdminResponse)
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        status_ = false;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgManageAdminResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse build() {
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse result = new com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.status_ = status_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.getDefaultInstance()) return this;
        if (other.getStatus() != false) {
          setStatus(other.getStatus());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                status_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean status_ ;
      /**
       * <code>bool status = 1;</code>
       * @return The status.
       */
      @java.lang.Override
      public boolean getStatus() {
        return status_;
      }
      /**
       * <code>bool status = 1;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(boolean value) {

        status_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>bool status = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000001);
        status_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgManageAdminResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgManageAdminResponse)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgManageAdminResponse>
        PARSER = new com.google.protobuf.AbstractParser<MsgManageAdminResponse>() {
      @java.lang.Override
      public MsgManageAdminResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgManageAdminResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgManageAdminResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ProfileOptionsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.ProfileOptions)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string nickname = 1;</code>
     * @return The nickname.
     */
    java.lang.String getNickname();
    /**
     * <code>string nickname = 1;</code>
     * @return The bytes for nickname.
     */
    com.google.protobuf.ByteString
        getNicknameBytes();

    /**
     * <code>string user_handle = 2;</code>
     * @return The userHandle.
     */
    java.lang.String getUserHandle();
    /**
     * <code>string user_handle = 2;</code>
     * @return The bytes for userHandle.
     */
    com.google.protobuf.ByteString
        getUserHandleBytes();

    /**
     * <code>string avatar = 3;</code>
     * @return The avatar.
     */
    java.lang.String getAvatar();
    /**
     * <code>string avatar = 3;</code>
     * @return The bytes for avatar.
     */
    com.google.protobuf.ByteString
        getAvatarBytes();

    /**
     * <code>string bio = 4;</code>
     * @return The bio.
     */
    java.lang.String getBio();
    /**
     * <code>string bio = 4;</code>
     * @return The bytes for bio.
     */
    com.google.protobuf.ByteString
        getBioBytes();

    /**
     * <code>string location = 5;</code>
     * @return The location.
     */
    java.lang.String getLocation();
    /**
     * <code>string location = 5;</code>
     * @return The bytes for location.
     */
    com.google.protobuf.ByteString
        getLocationBytes();

    /**
     * <code>string website = 6;</code>
     * @return The website.
     */
    java.lang.String getWebsite();
    /**
     * <code>string website = 6;</code>
     * @return The bytes for website.
     */
    com.google.protobuf.ByteString
        getWebsiteBytes();
  }
  /**
   * Protobuf type {@code profile.v1.ProfileOptions}
   */
  public static final class ProfileOptions extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.ProfileOptions)
      ProfileOptionsOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        ProfileOptions.class.getName());
    }
    // Use ProfileOptions.newBuilder() to construct.
    private ProfileOptions(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ProfileOptions() {
      nickname_ = "";
      userHandle_ = "";
      avatar_ = "";
      bio_ = "";
      location_ = "";
      website_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ProfileOptions_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ProfileOptions_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.ProfileOptions.class, com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder.class);
    }

    public static final int NICKNAME_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object nickname_ = "";
    /**
     * <code>string nickname = 1;</code>
     * @return The nickname.
     */
    @java.lang.Override
    public java.lang.String getNickname() {
      java.lang.Object ref = nickname_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickname_ = s;
        return s;
      }
    }
    /**
     * <code>string nickname = 1;</code>
     * @return The bytes for nickname.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNicknameBytes() {
      java.lang.Object ref = nickname_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USER_HANDLE_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object userHandle_ = "";
    /**
     * <code>string user_handle = 2;</code>
     * @return The userHandle.
     */
    @java.lang.Override
    public java.lang.String getUserHandle() {
      java.lang.Object ref = userHandle_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userHandle_ = s;
        return s;
      }
    }
    /**
     * <code>string user_handle = 2;</code>
     * @return The bytes for userHandle.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserHandleBytes() {
      java.lang.Object ref = userHandle_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userHandle_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AVATAR_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object avatar_ = "";
    /**
     * <code>string avatar = 3;</code>
     * @return The avatar.
     */
    @java.lang.Override
    public java.lang.String getAvatar() {
      java.lang.Object ref = avatar_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        avatar_ = s;
        return s;
      }
    }
    /**
     * <code>string avatar = 3;</code>
     * @return The bytes for avatar.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAvatarBytes() {
      java.lang.Object ref = avatar_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        avatar_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BIO_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object bio_ = "";
    /**
     * <code>string bio = 4;</code>
     * @return The bio.
     */
    @java.lang.Override
    public java.lang.String getBio() {
      java.lang.Object ref = bio_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bio_ = s;
        return s;
      }
    }
    /**
     * <code>string bio = 4;</code>
     * @return The bytes for bio.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBioBytes() {
      java.lang.Object ref = bio_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bio_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LOCATION_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object location_ = "";
    /**
     * <code>string location = 5;</code>
     * @return The location.
     */
    @java.lang.Override
    public java.lang.String getLocation() {
      java.lang.Object ref = location_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        location_ = s;
        return s;
      }
    }
    /**
     * <code>string location = 5;</code>
     * @return The bytes for location.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLocationBytes() {
      java.lang.Object ref = location_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        location_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int WEBSITE_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object website_ = "";
    /**
     * <code>string website = 6;</code>
     * @return The website.
     */
    @java.lang.Override
    public java.lang.String getWebsite() {
      java.lang.Object ref = website_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        website_ = s;
        return s;
      }
    }
    /**
     * <code>string website = 6;</code>
     * @return The bytes for website.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getWebsiteBytes() {
      java.lang.Object ref = website_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        website_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickname_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, nickname_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(userHandle_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, userHandle_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(avatar_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, avatar_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bio_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, bio_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(location_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, location_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(website_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, website_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickname_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, nickname_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(userHandle_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, userHandle_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(avatar_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, avatar_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bio_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, bio_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(location_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, location_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(website_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, website_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.ProfileOptions)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.ProfileOptions other = (com.Tlock.io.profile.ProfileTXProto.ProfileOptions) obj;

      if (!getNickname()
          .equals(other.getNickname())) return false;
      if (!getUserHandle()
          .equals(other.getUserHandle())) return false;
      if (!getAvatar()
          .equals(other.getAvatar())) return false;
      if (!getBio()
          .equals(other.getBio())) return false;
      if (!getLocation()
          .equals(other.getLocation())) return false;
      if (!getWebsite()
          .equals(other.getWebsite())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickname().hashCode();
      hash = (37 * hash) + USER_HANDLE_FIELD_NUMBER;
      hash = (53 * hash) + getUserHandle().hashCode();
      hash = (37 * hash) + AVATAR_FIELD_NUMBER;
      hash = (53 * hash) + getAvatar().hashCode();
      hash = (37 * hash) + BIO_FIELD_NUMBER;
      hash = (53 * hash) + getBio().hashCode();
      hash = (37 * hash) + LOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getLocation().hashCode();
      hash = (37 * hash) + WEBSITE_FIELD_NUMBER;
      hash = (53 * hash) + getWebsite().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.ProfileOptions prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.ProfileOptions}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.ProfileOptions)
        com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ProfileOptions_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ProfileOptions_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.ProfileOptions.class, com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.ProfileOptions.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        nickname_ = "";
        userHandle_ = "";
        avatar_ = "";
        bio_ = "";
        location_ = "";
        website_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_ProfileOptions_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.ProfileOptions getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.ProfileOptions build() {
        com.Tlock.io.profile.ProfileTXProto.ProfileOptions result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.ProfileOptions buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.ProfileOptions result = new com.Tlock.io.profile.ProfileTXProto.ProfileOptions(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.ProfileOptions result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.nickname_ = nickname_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userHandle_ = userHandle_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.avatar_ = avatar_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.bio_ = bio_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.location_ = location_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.website_ = website_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.ProfileOptions) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.ProfileOptions)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.ProfileOptions other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance()) return this;
        if (!other.getNickname().isEmpty()) {
          nickname_ = other.nickname_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getUserHandle().isEmpty()) {
          userHandle_ = other.userHandle_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.getAvatar().isEmpty()) {
          avatar_ = other.avatar_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getBio().isEmpty()) {
          bio_ = other.bio_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getLocation().isEmpty()) {
          location_ = other.location_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getWebsite().isEmpty()) {
          website_ = other.website_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                nickname_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                userHandle_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                avatar_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                bio_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                location_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                website_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object nickname_ = "";
      /**
       * <code>string nickname = 1;</code>
       * @return The nickname.
       */
      public java.lang.String getNickname() {
        java.lang.Object ref = nickname_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          nickname_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string nickname = 1;</code>
       * @return The bytes for nickname.
       */
      public com.google.protobuf.ByteString
          getNicknameBytes() {
        java.lang.Object ref = nickname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          nickname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string nickname = 1;</code>
       * @param value The nickname to set.
       * @return This builder for chaining.
       */
      public Builder setNickname(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        nickname_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string nickname = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNickname() {
        nickname_ = getDefaultInstance().getNickname();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string nickname = 1;</code>
       * @param value The bytes for nickname to set.
       * @return This builder for chaining.
       */
      public Builder setNicknameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        nickname_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object userHandle_ = "";
      /**
       * <code>string user_handle = 2;</code>
       * @return The userHandle.
       */
      public java.lang.String getUserHandle() {
        java.lang.Object ref = userHandle_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          userHandle_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string user_handle = 2;</code>
       * @return The bytes for userHandle.
       */
      public com.google.protobuf.ByteString
          getUserHandleBytes() {
        java.lang.Object ref = userHandle_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userHandle_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string user_handle = 2;</code>
       * @param value The userHandle to set.
       * @return This builder for chaining.
       */
      public Builder setUserHandle(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        userHandle_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string user_handle = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserHandle() {
        userHandle_ = getDefaultInstance().getUserHandle();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string user_handle = 2;</code>
       * @param value The bytes for userHandle to set.
       * @return This builder for chaining.
       */
      public Builder setUserHandleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        userHandle_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object avatar_ = "";
      /**
       * <code>string avatar = 3;</code>
       * @return The avatar.
       */
      public java.lang.String getAvatar() {
        java.lang.Object ref = avatar_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          avatar_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string avatar = 3;</code>
       * @return The bytes for avatar.
       */
      public com.google.protobuf.ByteString
          getAvatarBytes() {
        java.lang.Object ref = avatar_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          avatar_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string avatar = 3;</code>
       * @param value The avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatar(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        avatar_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string avatar = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatar() {
        avatar_ = getDefaultInstance().getAvatar();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string avatar = 3;</code>
       * @param value The bytes for avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatarBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        avatar_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object bio_ = "";
      /**
       * <code>string bio = 4;</code>
       * @return The bio.
       */
      public java.lang.String getBio() {
        java.lang.Object ref = bio_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bio_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string bio = 4;</code>
       * @return The bytes for bio.
       */
      public com.google.protobuf.ByteString
          getBioBytes() {
        java.lang.Object ref = bio_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bio_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string bio = 4;</code>
       * @param value The bio to set.
       * @return This builder for chaining.
       */
      public Builder setBio(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        bio_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>string bio = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBio() {
        bio_ = getDefaultInstance().getBio();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>string bio = 4;</code>
       * @param value The bytes for bio to set.
       * @return This builder for chaining.
       */
      public Builder setBioBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        bio_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object location_ = "";
      /**
       * <code>string location = 5;</code>
       * @return The location.
       */
      public java.lang.String getLocation() {
        java.lang.Object ref = location_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          location_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string location = 5;</code>
       * @return The bytes for location.
       */
      public com.google.protobuf.ByteString
          getLocationBytes() {
        java.lang.Object ref = location_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          location_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string location = 5;</code>
       * @param value The location to set.
       * @return This builder for chaining.
       */
      public Builder setLocation(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        location_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>string location = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLocation() {
        location_ = getDefaultInstance().getLocation();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <code>string location = 5;</code>
       * @param value The bytes for location to set.
       * @return This builder for chaining.
       */
      public Builder setLocationBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        location_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object website_ = "";
      /**
       * <code>string website = 6;</code>
       * @return The website.
       */
      public java.lang.String getWebsite() {
        java.lang.Object ref = website_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          website_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string website = 6;</code>
       * @return The bytes for website.
       */
      public com.google.protobuf.ByteString
          getWebsiteBytes() {
        java.lang.Object ref = website_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          website_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string website = 6;</code>
       * @param value The website to set.
       * @return This builder for chaining.
       */
      public Builder setWebsite(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        website_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>string website = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearWebsite() {
        website_ = getDefaultInstance().getWebsite();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <code>string website = 6;</code>
       * @param value The bytes for website to set.
       * @return This builder for chaining.
       */
      public Builder setWebsiteBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        website_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.ProfileOptions)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.ProfileOptions)
    private static final com.Tlock.io.profile.ProfileTXProto.ProfileOptions DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.ProfileOptions();
    }

    public static com.Tlock.io.profile.ProfileTXProto.ProfileOptions getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ProfileOptions>
        PARSER = new com.google.protobuf.AbstractParser<ProfileOptions>() {
      @java.lang.Override
      public ProfileOptions parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ProfileOptions> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ProfileOptions> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.ProfileOptions getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgAddProfileRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgAddProfileRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    java.lang.String getCreator();
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    com.google.protobuf.ByteString
        getCreatorBytes();

    /**
     * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
     * @return Whether the profileJson field is set.
     */
    boolean hasProfileJson();
    /**
     * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
     * @return The profileJson.
     */
    com.Tlock.io.profile.ProfileTXProto.ProfileOptions getProfileJson();
    /**
     * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
     */
    com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder getProfileJsonOrBuilder();
  }
  /**
   * Protobuf type {@code profile.v1.MsgAddProfileRequest}
   */
  public static final class MsgAddProfileRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgAddProfileRequest)
      MsgAddProfileRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgAddProfileRequest.class.getName());
    }
    // Use MsgAddProfileRequest.newBuilder() to construct.
    private MsgAddProfileRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgAddProfileRequest() {
      creator_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.Builder.class);
    }

    private int bitField0_;
    public static final int CREATOR_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object creator_ = "";
    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    @java.lang.Override
    public java.lang.String getCreator() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creator_ = s;
        return s;
      }
    }
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCreatorBytes() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROFILE_JSON_FIELD_NUMBER = 2;
    private com.Tlock.io.profile.ProfileTXProto.ProfileOptions profileJson_;
    /**
     * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
     * @return Whether the profileJson field is set.
     */
    @java.lang.Override
    public boolean hasProfileJson() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
     * @return The profileJson.
     */
    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.ProfileOptions getProfileJson() {
      return profileJson_ == null ? com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance() : profileJson_;
    }
    /**
     * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
     */
    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder getProfileJsonOrBuilder() {
      return profileJson_ == null ? com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance() : profileJson_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, creator_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getProfileJson());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, creator_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getProfileJson());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest other = (com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest) obj;

      if (!getCreator()
          .equals(other.getCreator())) return false;
      if (hasProfileJson() != other.hasProfileJson()) return false;
      if (hasProfileJson()) {
        if (!getProfileJson()
            .equals(other.getProfileJson())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CREATOR_FIELD_NUMBER;
      hash = (53 * hash) + getCreator().hashCode();
      if (hasProfileJson()) {
        hash = (37 * hash) + PROFILE_JSON_FIELD_NUMBER;
        hash = (53 * hash) + getProfileJson().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgAddProfileRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgAddProfileRequest)
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getProfileJsonFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        creator_ = "";
        profileJson_ = null;
        if (profileJsonBuilder_ != null) {
          profileJsonBuilder_.dispose();
          profileJsonBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest build() {
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest result = new com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.creator_ = creator_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.profileJson_ = profileJsonBuilder_ == null
              ? profileJson_
              : profileJsonBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.getDefaultInstance()) return this;
        if (!other.getCreator().isEmpty()) {
          creator_ = other.creator_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasProfileJson()) {
          mergeProfileJson(other.getProfileJson());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                creator_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getProfileJsonFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object creator_ = "";
      /**
       * <code>string creator = 1;</code>
       * @return The creator.
       */
      public java.lang.String getCreator() {
        java.lang.Object ref = creator_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @return The bytes for creator.
       */
      public com.google.protobuf.ByteString
          getCreatorBytes() {
        java.lang.Object ref = creator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreator(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreator() {
        creator_ = getDefaultInstance().getCreator();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The bytes for creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private com.Tlock.io.profile.ProfileTXProto.ProfileOptions profileJson_;
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.profile.ProfileTXProto.ProfileOptions, com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder, com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder> profileJsonBuilder_;
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       * @return Whether the profileJson field is set.
       */
      public boolean hasProfileJson() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       * @return The profileJson.
       */
      public com.Tlock.io.profile.ProfileTXProto.ProfileOptions getProfileJson() {
        if (profileJsonBuilder_ == null) {
          return profileJson_ == null ? com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance() : profileJson_;
        } else {
          return profileJsonBuilder_.getMessage();
        }
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      public Builder setProfileJson(com.Tlock.io.profile.ProfileTXProto.ProfileOptions value) {
        if (profileJsonBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          profileJson_ = value;
        } else {
          profileJsonBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      public Builder setProfileJson(
          com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder builderForValue) {
        if (profileJsonBuilder_ == null) {
          profileJson_ = builderForValue.build();
        } else {
          profileJsonBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      public Builder mergeProfileJson(com.Tlock.io.profile.ProfileTXProto.ProfileOptions value) {
        if (profileJsonBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            profileJson_ != null &&
            profileJson_ != com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance()) {
            getProfileJsonBuilder().mergeFrom(value);
          } else {
            profileJson_ = value;
          }
        } else {
          profileJsonBuilder_.mergeFrom(value);
        }
        if (profileJson_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      public Builder clearProfileJson() {
        bitField0_ = (bitField0_ & ~0x00000002);
        profileJson_ = null;
        if (profileJsonBuilder_ != null) {
          profileJsonBuilder_.dispose();
          profileJsonBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      public com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder getProfileJsonBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getProfileJsonFieldBuilder().getBuilder();
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      public com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder getProfileJsonOrBuilder() {
        if (profileJsonBuilder_ != null) {
          return profileJsonBuilder_.getMessageOrBuilder();
        } else {
          return profileJson_ == null ?
              com.Tlock.io.profile.ProfileTXProto.ProfileOptions.getDefaultInstance() : profileJson_;
        }
      }
      /**
       * <code>.profile.v1.ProfileOptions profile_json = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.profile.ProfileTXProto.ProfileOptions, com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder, com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder> 
          getProfileJsonFieldBuilder() {
        if (profileJsonBuilder_ == null) {
          profileJsonBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.Tlock.io.profile.ProfileTXProto.ProfileOptions, com.Tlock.io.profile.ProfileTXProto.ProfileOptions.Builder, com.Tlock.io.profile.ProfileTXProto.ProfileOptionsOrBuilder>(
                  getProfileJson(),
                  getParentForChildren(),
                  isClean());
          profileJson_ = null;
        }
        return profileJsonBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgAddProfileRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgAddProfileRequest)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgAddProfileRequest>
        PARSER = new com.google.protobuf.AbstractParser<MsgAddProfileRequest>() {
      @java.lang.Override
      public MsgAddProfileRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgAddProfileRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgAddProfileRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgAddProfileResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgAddProfileResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Profile profile = 1;</code>
     * @return Whether the profile field is set.
     */
    boolean hasProfile();
    /**
     * <code>.Profile profile = 1;</code>
     * @return The profile.
     */
    com.Tlock.io.entity.profile.ProfileProto.Profile getProfile();
    /**
     * <code>.Profile profile = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfileOrBuilder();
  }
  /**
   * Protobuf type {@code profile.v1.MsgAddProfileResponse}
   */
  public static final class MsgAddProfileResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgAddProfileResponse)
      MsgAddProfileResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgAddProfileResponse.class.getName());
    }
    // Use MsgAddProfileResponse.newBuilder() to construct.
    private MsgAddProfileResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgAddProfileResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.Builder.class);
    }

    private int bitField0_;
    public static final int PROFILE_FIELD_NUMBER = 1;
    private com.Tlock.io.entity.profile.ProfileProto.Profile profile_;
    /**
     * <code>.Profile profile = 1;</code>
     * @return Whether the profile field is set.
     */
    @java.lang.Override
    public boolean hasProfile() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Profile profile = 1;</code>
     * @return The profile.
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getProfile() {
      return profile_ == null ? com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
    }
    /**
     * <code>.Profile profile = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfileOrBuilder() {
      return profile_ == null ? com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getProfile());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getProfile());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse other = (com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse) obj;

      if (hasProfile() != other.hasProfile()) return false;
      if (hasProfile()) {
        if (!getProfile()
            .equals(other.getProfile())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasProfile()) {
        hash = (37 * hash) + PROFILE_FIELD_NUMBER;
        hash = (53 * hash) + getProfile().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgAddProfileResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgAddProfileResponse)
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getProfileFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        profile_ = null;
        if (profileBuilder_ != null) {
          profileBuilder_.dispose();
          profileBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgAddProfileResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse build() {
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse result = new com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.profile_ = profileBuilder_ == null
              ? profile_
              : profileBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.getDefaultInstance()) return this;
        if (other.hasProfile()) {
          mergeProfile(other.getProfile());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getProfileFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.Tlock.io.entity.profile.ProfileProto.Profile profile_;
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> profileBuilder_;
      /**
       * <code>.Profile profile = 1;</code>
       * @return Whether the profile field is set.
       */
      public boolean hasProfile() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Profile profile = 1;</code>
       * @return The profile.
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile getProfile() {
        if (profileBuilder_ == null) {
          return profile_ == null ? com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
        } else {
          return profileBuilder_.getMessage();
        }
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder setProfile(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profileBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          profile_ = value;
        } else {
          profileBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder setProfile(
          com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profileBuilder_ == null) {
          profile_ = builderForValue.build();
        } else {
          profileBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder mergeProfile(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profileBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            profile_ != null &&
            profile_ != com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance()) {
            getProfileBuilder().mergeFrom(value);
          } else {
            profile_ = value;
          }
        } else {
          profileBuilder_.mergeFrom(value);
        }
        if (profile_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder clearProfile() {
        bitField0_ = (bitField0_ & ~0x00000001);
        profile_ = null;
        if (profileBuilder_ != null) {
          profileBuilder_.dispose();
          profileBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder getProfileBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getProfileFieldBuilder().getBuilder();
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfileOrBuilder() {
        if (profileBuilder_ != null) {
          return profileBuilder_.getMessageOrBuilder();
        } else {
          return profile_ == null ?
              com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
        }
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
          getProfileFieldBuilder() {
        if (profileBuilder_ == null) {
          profileBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder>(
                  getProfile(),
                  getParentForChildren(),
                  isClean());
          profile_ = null;
        }
        return profileBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgAddProfileResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgAddProfileResponse)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgAddProfileResponse>
        PARSER = new com.google.protobuf.AbstractParser<MsgAddProfileResponse>() {
      @java.lang.Override
      public MsgAddProfileResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgAddProfileResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgAddProfileResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgFollowRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgFollowRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    java.lang.String getCreator();
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    com.google.protobuf.ByteString
        getCreatorBytes();

    /**
     * <code>string targetAddr = 2;</code>
     * @return The targetAddr.
     */
    java.lang.String getTargetAddr();
    /**
     * <code>string targetAddr = 2;</code>
     * @return The bytes for targetAddr.
     */
    com.google.protobuf.ByteString
        getTargetAddrBytes();
  }
  /**
   * Protobuf type {@code profile.v1.MsgFollowRequest}
   */
  public static final class MsgFollowRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgFollowRequest)
      MsgFollowRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgFollowRequest.class.getName());
    }
    // Use MsgFollowRequest.newBuilder() to construct.
    private MsgFollowRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgFollowRequest() {
      creator_ = "";
      targetAddr_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.Builder.class);
    }

    public static final int CREATOR_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object creator_ = "";
    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    @java.lang.Override
    public java.lang.String getCreator() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creator_ = s;
        return s;
      }
    }
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCreatorBytes() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TARGETADDR_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object targetAddr_ = "";
    /**
     * <code>string targetAddr = 2;</code>
     * @return The targetAddr.
     */
    @java.lang.Override
    public java.lang.String getTargetAddr() {
      java.lang.Object ref = targetAddr_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetAddr_ = s;
        return s;
      }
    }
    /**
     * <code>string targetAddr = 2;</code>
     * @return The bytes for targetAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTargetAddrBytes() {
      java.lang.Object ref = targetAddr_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetAddr_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, creator_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(targetAddr_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, targetAddr_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, creator_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(targetAddr_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, targetAddr_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest other = (com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest) obj;

      if (!getCreator()
          .equals(other.getCreator())) return false;
      if (!getTargetAddr()
          .equals(other.getTargetAddr())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CREATOR_FIELD_NUMBER;
      hash = (53 * hash) + getCreator().hashCode();
      hash = (37 * hash) + TARGETADDR_FIELD_NUMBER;
      hash = (53 * hash) + getTargetAddr().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgFollowRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgFollowRequest)
        com.Tlock.io.profile.ProfileTXProto.MsgFollowRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        creator_ = "";
        targetAddr_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest build() {
        com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest result = new com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.creator_ = creator_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetAddr_ = targetAddr_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.getDefaultInstance()) return this;
        if (!other.getCreator().isEmpty()) {
          creator_ = other.creator_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getTargetAddr().isEmpty()) {
          targetAddr_ = other.targetAddr_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                creator_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                targetAddr_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object creator_ = "";
      /**
       * <code>string creator = 1;</code>
       * @return The creator.
       */
      public java.lang.String getCreator() {
        java.lang.Object ref = creator_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @return The bytes for creator.
       */
      public com.google.protobuf.ByteString
          getCreatorBytes() {
        java.lang.Object ref = creator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreator(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreator() {
        creator_ = getDefaultInstance().getCreator();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The bytes for creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object targetAddr_ = "";
      /**
       * <code>string targetAddr = 2;</code>
       * @return The targetAddr.
       */
      public java.lang.String getTargetAddr() {
        java.lang.Object ref = targetAddr_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          targetAddr_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @return The bytes for targetAddr.
       */
      public com.google.protobuf.ByteString
          getTargetAddrBytes() {
        java.lang.Object ref = targetAddr_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          targetAddr_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @param value The targetAddr to set.
       * @return This builder for chaining.
       */
      public Builder setTargetAddr(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        targetAddr_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetAddr() {
        targetAddr_ = getDefaultInstance().getTargetAddr();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @param value The bytes for targetAddr to set.
       * @return This builder for chaining.
       */
      public Builder setTargetAddrBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        targetAddr_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgFollowRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgFollowRequest)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgFollowRequest>
        PARSER = new com.google.protobuf.AbstractParser<MsgFollowRequest>() {
      @java.lang.Override
      public MsgFollowRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgFollowRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgFollowRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgFollowResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgFollowResponse)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code profile.v1.MsgFollowResponse}
   */
  public static final class MsgFollowResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgFollowResponse)
      MsgFollowResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgFollowResponse.class.getName());
    }
    // Use MsgFollowResponse.newBuilder() to construct.
    private MsgFollowResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgFollowResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse other = (com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgFollowResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgFollowResponse)
        com.Tlock.io.profile.ProfileTXProto.MsgFollowResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgFollowResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse build() {
        com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse result = new com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgFollowResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgFollowResponse)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgFollowResponse>
        PARSER = new com.google.protobuf.AbstractParser<MsgFollowResponse>() {
      @java.lang.Override
      public MsgFollowResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgFollowResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgFollowResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgUnfollowRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgUnfollowRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    java.lang.String getCreator();
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    com.google.protobuf.ByteString
        getCreatorBytes();

    /**
     * <code>string targetAddr = 2;</code>
     * @return The targetAddr.
     */
    java.lang.String getTargetAddr();
    /**
     * <code>string targetAddr = 2;</code>
     * @return The bytes for targetAddr.
     */
    com.google.protobuf.ByteString
        getTargetAddrBytes();
  }
  /**
   * Protobuf type {@code profile.v1.MsgUnfollowRequest}
   */
  public static final class MsgUnfollowRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgUnfollowRequest)
      MsgUnfollowRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgUnfollowRequest.class.getName());
    }
    // Use MsgUnfollowRequest.newBuilder() to construct.
    private MsgUnfollowRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgUnfollowRequest() {
      creator_ = "";
      targetAddr_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.Builder.class);
    }

    public static final int CREATOR_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object creator_ = "";
    /**
     * <code>string creator = 1;</code>
     * @return The creator.
     */
    @java.lang.Override
    public java.lang.String getCreator() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creator_ = s;
        return s;
      }
    }
    /**
     * <code>string creator = 1;</code>
     * @return The bytes for creator.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCreatorBytes() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TARGETADDR_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object targetAddr_ = "";
    /**
     * <code>string targetAddr = 2;</code>
     * @return The targetAddr.
     */
    @java.lang.Override
    public java.lang.String getTargetAddr() {
      java.lang.Object ref = targetAddr_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetAddr_ = s;
        return s;
      }
    }
    /**
     * <code>string targetAddr = 2;</code>
     * @return The bytes for targetAddr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTargetAddrBytes() {
      java.lang.Object ref = targetAddr_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetAddr_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, creator_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(targetAddr_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, targetAddr_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, creator_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(targetAddr_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, targetAddr_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest other = (com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest) obj;

      if (!getCreator()
          .equals(other.getCreator())) return false;
      if (!getTargetAddr()
          .equals(other.getTargetAddr())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CREATOR_FIELD_NUMBER;
      hash = (53 * hash) + getCreator().hashCode();
      hash = (37 * hash) + TARGETADDR_FIELD_NUMBER;
      hash = (53 * hash) + getTargetAddr().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgUnfollowRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgUnfollowRequest)
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.class, com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        creator_ = "";
        targetAddr_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest build() {
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest result = new com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.creator_ = creator_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetAddr_ = targetAddr_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.getDefaultInstance()) return this;
        if (!other.getCreator().isEmpty()) {
          creator_ = other.creator_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getTargetAddr().isEmpty()) {
          targetAddr_ = other.targetAddr_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                creator_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                targetAddr_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object creator_ = "";
      /**
       * <code>string creator = 1;</code>
       * @return The creator.
       */
      public java.lang.String getCreator() {
        java.lang.Object ref = creator_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @return The bytes for creator.
       */
      public com.google.protobuf.ByteString
          getCreatorBytes() {
        java.lang.Object ref = creator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreator(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreator() {
        creator_ = getDefaultInstance().getCreator();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 1;</code>
       * @param value The bytes for creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        creator_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object targetAddr_ = "";
      /**
       * <code>string targetAddr = 2;</code>
       * @return The targetAddr.
       */
      public java.lang.String getTargetAddr() {
        java.lang.Object ref = targetAddr_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          targetAddr_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @return The bytes for targetAddr.
       */
      public com.google.protobuf.ByteString
          getTargetAddrBytes() {
        java.lang.Object ref = targetAddr_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          targetAddr_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @param value The targetAddr to set.
       * @return This builder for chaining.
       */
      public Builder setTargetAddr(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        targetAddr_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetAddr() {
        targetAddr_ = getDefaultInstance().getTargetAddr();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string targetAddr = 2;</code>
       * @param value The bytes for targetAddr to set.
       * @return This builder for chaining.
       */
      public Builder setTargetAddrBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        targetAddr_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgUnfollowRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgUnfollowRequest)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgUnfollowRequest>
        PARSER = new com.google.protobuf.AbstractParser<MsgUnfollowRequest>() {
      @java.lang.Override
      public MsgUnfollowRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgUnfollowRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgUnfollowRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgUnfollowResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.MsgUnfollowResponse)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code profile.v1.MsgUnfollowResponse}
   */
  public static final class MsgUnfollowResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.MsgUnfollowResponse)
      MsgUnfollowResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        MsgUnfollowResponse.class.getName());
    }
    // Use MsgUnfollowResponse.newBuilder() to construct.
    private MsgUnfollowResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MsgUnfollowResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse other = (com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.MsgUnfollowResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.MsgUnfollowResponse)
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.class, com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileTXProto.internal_static_profile_v1_MsgUnfollowResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse build() {
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse buildPartial() {
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse result = new com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse other) {
        if (other == com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.MsgUnfollowResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.MsgUnfollowResponse)
    private static final com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse();
    }

    public static com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgUnfollowResponse>
        PARSER = new com.google.protobuf.AbstractParser<MsgUnfollowResponse>() {
      @java.lang.Override
      public MsgUnfollowResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MsgUnfollowResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgUnfollowResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_ManageOptions_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_ManageOptions_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgManageAdminRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgManageAdminRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgManageAdminResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgManageAdminResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_ProfileOptions_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_ProfileOptions_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgAddProfileRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgAddProfileRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgAddProfileResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgAddProfileResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgFollowRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgFollowRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgFollowResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgFollowResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgUnfollowRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgUnfollowRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_MsgUnfollowResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_MsgUnfollowResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030profile/profile_tx.proto\022\nprofile.v1\032\033" +
      "profile/profile_query.proto\032\021entity/post" +
      ".proto\032\024entity/profile.proto\"c\n\rManageOp" +
      "tions\022\024\n\014line_manager\030\001 \001(\t\022\025\n\radmin_add" +
      "ress\030\002 \001(\t\022\023\n\013admin_level\030\003 \001(\004\022\020\n\010edita" +
      "ble\030\004 \001(\010\"h\n\025MsgManageAdminRequest\022\017\n\007cr" +
      "eator\030\001 \001(\t\022.\n\013manage_json\030\002 \001(\0132\031.profi" +
      "le.v1.ManageOptions\022\016\n\006action\030\003 \001(\t\"(\n\026M" +
      "sgManageAdminResponse\022\016\n\006status\030\001 \001(\010\"w\n" +
      "\016ProfileOptions\022\020\n\010nickname\030\001 \001(\t\022\023\n\013use" +
      "r_handle\030\002 \001(\t\022\016\n\006avatar\030\003 \001(\t\022\013\n\003bio\030\004 " +
      "\001(\t\022\020\n\010location\030\005 \001(\t\022\017\n\007website\030\006 \001(\t\"Y" +
      "\n\024MsgAddProfileRequest\022\017\n\007creator\030\001 \001(\t\022" +
      "0\n\014profile_json\030\002 \001(\0132\032.profile.v1.Profi" +
      "leOptions\"2\n\025MsgAddProfileResponse\022\031\n\007pr" +
      "ofile\030\001 \001(\0132\010.Profile\"7\n\020MsgFollowReques" +
      "t\022\017\n\007creator\030\001 \001(\t\022\022\n\ntargetAddr\030\002 \001(\t\"\023" +
      "\n\021MsgFollowResponse\"9\n\022MsgUnfollowReques" +
      "t\022\017\n\007creator\030\001 \001(\t\022\022\n\ntargetAddr\030\002 \001(\t\"\025" +
      "\n\023MsgUnfollowResponse2\302\002\n\003Msg\022Q\n\nAddProf" +
      "ile\022 .profile.v1.MsgAddProfileRequest\032!." +
      "profile.v1.MsgAddProfileResponse\022E\n\006Foll" +
      "ow\022\034.profile.v1.MsgFollowRequest\032\035.profi" +
      "le.v1.MsgFollowResponse\022K\n\010Unfollow\022\036.pr" +
      "ofile.v1.MsgUnfollowRequest\032\037.profile.v1" +
      ".MsgUnfollowResponse\022T\n\013ManageAdmin\022!.pr" +
      "ofile.v1.MsgManageAdminRequest\032\".profile" +
      ".v1.MsgManageAdminResponseBS\n\024com.Tlock." +
      "io.profileB\016ProfileTXProtoZ+github.com/r" +
      "ollchains/tlock/x/profile/typesb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.Tlock.io.profile.ProfileQueryProto.getDescriptor(),
          com.Tlock.io.entity.post.PostProto.getDescriptor(),
          com.Tlock.io.entity.profile.ProfileProto.getDescriptor(),
        });
    internal_static_profile_v1_ManageOptions_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_profile_v1_ManageOptions_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_ManageOptions_descriptor,
        new java.lang.String[] { "LineManager", "AdminAddress", "AdminLevel", "Editable", });
    internal_static_profile_v1_MsgManageAdminRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_profile_v1_MsgManageAdminRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgManageAdminRequest_descriptor,
        new java.lang.String[] { "Creator", "ManageJson", "Action", });
    internal_static_profile_v1_MsgManageAdminResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_profile_v1_MsgManageAdminResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgManageAdminResponse_descriptor,
        new java.lang.String[] { "Status", });
    internal_static_profile_v1_ProfileOptions_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_profile_v1_ProfileOptions_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_ProfileOptions_descriptor,
        new java.lang.String[] { "Nickname", "UserHandle", "Avatar", "Bio", "Location", "Website", });
    internal_static_profile_v1_MsgAddProfileRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_profile_v1_MsgAddProfileRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgAddProfileRequest_descriptor,
        new java.lang.String[] { "Creator", "ProfileJson", });
    internal_static_profile_v1_MsgAddProfileResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_profile_v1_MsgAddProfileResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgAddProfileResponse_descriptor,
        new java.lang.String[] { "Profile", });
    internal_static_profile_v1_MsgFollowRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_profile_v1_MsgFollowRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgFollowRequest_descriptor,
        new java.lang.String[] { "Creator", "TargetAddr", });
    internal_static_profile_v1_MsgFollowResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_profile_v1_MsgFollowResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgFollowResponse_descriptor,
        new java.lang.String[] { });
    internal_static_profile_v1_MsgUnfollowRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_profile_v1_MsgUnfollowRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgUnfollowRequest_descriptor,
        new java.lang.String[] { "Creator", "TargetAddr", });
    internal_static_profile_v1_MsgUnfollowResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_profile_v1_MsgUnfollowResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_MsgUnfollowResponse_descriptor,
        new java.lang.String[] { });
    descriptor.resolveAllFeaturesImmutable();
    com.Tlock.io.profile.ProfileQueryProto.getDescriptor();
    com.Tlock.io.entity.post.PostProto.getDescriptor();
    com.Tlock.io.entity.profile.ProfileProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
