<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".activity.wallet.BackupMnemonicsActivity">

    <RelativeLayout
        android:id="@+id/rl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_45"
        android:layout_marginBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/Backup_mnemonics"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dp_4"
            android:adjustViewBounds="true"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/icon_back_black" />
    </RelativeLayout>


    <LinearLayout
        android:id="@+id/ll_check"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_toolbar"
        android:layout_margin="@dimen/dp_20"
        android:background="@drawable/btn_gray_6"
        android:gravity="fill_horizontal"
        android:orientation="vertical"
        android:padding="@dimen/dp_20">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv1_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="#1"
                    android:textColor="#CC536471"
                    android:textSize="@dimen/sp_15" />

                <TextView
                    android:id="@+id/tv1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_toRightOf="@id/tv1_title"
                    android:textColor="@color/cosmos_black"
                    android:textSize="@dimen/sp_15"
                    android:textStyle="bold" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv2_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="#1"
                    android:textColor="#CC536471"
                    android:textSize="@dimen/sp_15"
              />

                <TextView
                    android:id="@+id/tv2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_toRightOf="@id/tv2_title"
                    android:textColor="@color/cosmos_black"
                    android:textSize="@dimen/sp_15" />


            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_10"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv3_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="#3"
                    android:textColor="#CC536471"
                    android:textSize="@dimen/sp_15" />

                <TextView
                    android:id="@+id/tv3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_toRightOf="@id/tv3_title"

                    android:textColor="@color/cosmos_black"
                    android:textSize="@dimen/sp_15"
                    android:textStyle="bold" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"

                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv4_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="#1"
                    android:textColor="#CC536471"
                    android:textSize="@dimen/sp_15" />

                <TextView
                    android:id="@+id/tv4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_toRightOf="@id/tv4_title"
                    android:textColor="@color/cosmos_black"
                    android:textSize="@dimen/sp_15"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_check"
        android:layout_marginLeft="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_20"
        android:padding="10dp" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/recyler_view"
        android:layout_marginLeft="@dimen/dp_22"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginRight="@dimen/dp_22"
        android:background="@drawable/shape_light_red_2"
        android:paddingLeft="@dimen/dp_20"
        android:paddingTop="@dimen/dp_9"
        android:paddingRight="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_9"
        android:text="The current mnemonic phrase is incorrect. Please try again."
        android:textColor="@color/error_red"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/dp_24"
        android:background="@drawable/btn_blue_6"
        android:gravity="center"
        android:padding="@dimen/dp_15"
        android:textStyle="bold"
        android:text="Continue"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />
</RelativeLayout>