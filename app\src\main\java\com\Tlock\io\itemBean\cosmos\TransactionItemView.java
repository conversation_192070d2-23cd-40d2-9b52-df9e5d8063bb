package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.wallet.Transaction;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.DateUtil;
import com.Tlock.io.utils.TimeUtils;
import com.Tlock.io.utils.ToastUtil;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TransactionItemView extends BaseView {


    @BindView(R.id.tv_method)
    TextView mTvMethod;
    @BindView(R.id.tv_hash)
    TextView mTvHash;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.ll_root)
    RelativeLayout mLlRoot;

    public TransactionItemView(Context context) {
        super(context);
    }

    public TransactionItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TransactionItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.item_transaction;
    }

    public void setData(Transaction data) {
        mTvMethod.setText(data.getFunction());
        mTvHash.setText(data.getHash());
        mTvHash.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                CopyUtils.copyToClipboard(data.getHash());
                ToastUtil.toastView("Copy success");
            }
        });
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(TimeUtils.timestampToLocalDate(data.getTime()));
        }

    }

}
