package com.Tlock.io.utils.wallet;




import android.util.Log;

import androidx.annotation.NonNull;

import com.Tlock.io.entity.wallet.ETHWallet;
import com.google.common.collect.ImmutableList;

import org.bitcoinj.core.Bech32;
import org.bitcoinj.crypto.ChildNumber;
import org.bitcoinj.crypto.DeterministicHierarchy;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.HDKeyDerivation;

import org.web3j.crypto.Credentials;
import org.web3j.crypto.ECKeyPair;
import org.web3j.crypto.Keys;

import java.security.MessageDigest;
import java.security.Security;
import java.util.Arrays;

import wallet.core.jni.HDWallet;

/**
 * @ClassName WalletUtils
 * <AUTHOR>
 * @Data 2021/11/4 10:22
 * @Desc
 */

public class WalletUtils {


    /**
     * 通用的以太坊基于bip44协议的助记词路径 （imtoken jaxx Metamask myetherwallet）
     */
    private static String ETH_TYPE = "m/44'/60'/0'/0/0";
    private static String COSMOS_TYPE = "m/44'/118'/0'/0/0";

    public static ImmutableList<ChildNumber> BIP44_ETH_ACCOUNT_ZERO_PATH = ImmutableList.of(new ChildNumber(44, true), new ChildNumber(60, true), ChildNumber.ZERO_HARDENED, ChildNumber.ZERO);
    public static ImmutableList<ChildNumber> BIP44_COSMOS_ACCOUNT_ZERO_PATH = ImmutableList.of(new ChildNumber(44, true), new ChildNumber(118, true), ChildNumber.ZERO_HARDENED, ChildNumber.ZERO);

    public static ETHWallet getWalletInfo(@NonNull HDWallet hdWallet) {
        Log.i("TAG", "getWalletInfo--------------------------------------");

        //秘钥种子
        byte[] seed = hdWallet.seed();
        Log.i("TAG", "seed===" + Arrays.toString(seed));

        //生成私钥
        DeterministicKey masterPrivateKey = HDKeyDerivation.createMasterPrivateKey(seed);

        DeterministicHierarchy deterministicHierarchy = new DeterministicHierarchy(masterPrivateKey);
        DeterministicKey deterministicKey = deterministicHierarchy.deriveChild(BIP44_ETH_ACCOUNT_ZERO_PATH, false, true, new ChildNumber(0));
        byte[] bytes = deterministicKey.getPrivKeyBytes();
        ECKeyPair keyPair = ECKeyPair.create(bytes);
        //通过公钥生成钱包地址
        String address = Keys.getAddress(keyPair.getPublicKey());
        ETHWallet ethWallet = new ETHWallet();
        ethWallet.setAddress(address);
        ethWallet.setMnemonic(hdWallet.mnemonic());
        ethWallet.setAddress("0x" + address);
        ethWallet.setPrivateKey("0x" + keyPair.getPrivateKey().toString(16));
        ethWallet.setPublicKey("0x" + keyPair.getPublicKey().toString(16));
        ethWallet.setType(2);

        Log.i("TAG", "privateKey====" + "0x" + keyPair.getPrivateKey().toString(16));
        Log.i("TAG", "publicKey===" + keyPair.getPublicKey().toString(16));
        Log.i("TAG", "address===" + "0x" + address);
        Log.i("TAG", "mnemonic===" + hdWallet.mnemonic());
        Log.i("TAG", "getWalletInfo--------------------------------------");
        return ethWallet;

    }

    public static ETHWallet loadWalletByPrivateKey(String privateKey) {
        Credentials credentials = Credentials.create(privateKey);

        ETHWallet ethWallet = new ETHWallet();
        try {
            ethWallet.setAddress(credentials.getAddress());
            ethWallet.setPrivateKey(privateKey);
            ethWallet.setType(1);
        } catch (Exception e) {
            e.printStackTrace();

        }
        return ethWallet;

    }



}