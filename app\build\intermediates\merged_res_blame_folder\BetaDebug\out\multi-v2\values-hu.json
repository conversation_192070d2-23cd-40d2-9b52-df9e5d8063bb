{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "268,376,468,583,667,782,905,982,1057,1148,1241,1336,1430,1530,1623,1718,1813,1904,1995,2078,2188,2298,2398,2509,2618,2737,2919,8112", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "371,463,578,662,777,900,977,1052,1143,1236,1331,1425,1525,1618,1713,1808,1899,1990,2073,2183,2293,2393,2504,2613,2732,2914,3017,8191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,295,394,514,597,661,760,835,894,1004,1073,1131,1203,1264,1319,1422,1479,1539,1594,1675,1795,1878,1966,2071,2154,2234,2328,2395,2461,2537,2619,2705,2782,2857,2936,3013,3109,3186,3278,3375,3449,3534,3631,3683,3750,3838,3925,3987,4051,4114,4212,4309,4403,4501", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,119,82,63,98,74,58,109,68,57,71,60,54,102,56,59,54,80,119,82,87,104,82,79,93,66,65,75,81,85,76,74,78,76,95,76,91,96,73,84,96,51,66,87,86,61,63,62,97,96,93,97,84", "endOffsets": "213,290,389,509,592,656,755,830,889,999,1068,1126,1198,1259,1314,1417,1474,1534,1589,1670,1790,1873,1961,2066,2149,2229,2323,2390,2456,2532,2614,2700,2777,2852,2931,3008,3104,3181,3273,3370,3444,3529,3626,3678,3745,3833,3920,3982,4046,4109,4207,4304,4398,4496,4581"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3022,3821,3920,4040,4123,4187,4286,4361,4420,4530,4599,4657,4729,4790,4845,4948,5005,5065,5120,5201,5321,5404,5492,5597,5680,5760,5854,5921,5987,6063,6145,6231,6308,6383,6462,6539,6635,6712,6804,6901,6975,7060,7157,7209,7276,7364,7451,7513,7577,7640,7738,7835,7929,8027", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,76,98,119,82,63,98,74,58,109,68,57,71,60,54,102,56,59,54,80,119,82,87,104,82,79,93,66,65,75,81,85,76,74,78,76,95,76,91,96,73,84,96,51,66,87,86,61,63,62,97,96,93,97,84", "endOffsets": "263,3094,3915,4035,4118,4182,4281,4356,4415,4525,4594,4652,4724,4785,4840,4943,5000,5060,5115,5196,5316,5399,5487,5592,5675,5755,5849,5916,5982,6058,6140,6226,6303,6378,6457,6534,6630,6707,6799,6896,6970,7055,7152,7204,7271,7359,7446,7508,7572,7635,7733,7830,7924,8022,8107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3099,3196,3298,3400,3501,3604,3711,8196", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3191,3293,3395,3496,3599,3706,3816,8292"}}]}]}