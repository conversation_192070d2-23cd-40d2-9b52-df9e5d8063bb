package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.wallet.MnemonicViewBean;
import com.Tlock.io.utils.JsonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class BackupMnemonicsActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.rl_toolbar)
    RelativeLayout mRlToolbar;
    @BindView(R.id.tv_1)
    TextView mTv1;
    @BindView(R.id.tv_tip)
    TextView mTvTip;
    @BindView(R.id.tv_next)
    TextView mTvNext;

    private ArrayList<String> valueBeans = new ArrayList<>();
    private BaseRecyclerViewAdapter<String> wordAdapter;
    private String data;
    private int type;
    private BaseRecyclerViewAdapter<String> adapter;

    /**
     * @param context
     * @param data    数据
     * @param from    来源 1创建钱包 2导出钱包
     */
    public static void start(Context context, String data, int from) {
        Intent intent = new Intent(context, BackupMnemonicsActivity.class);
        intent.putExtra("data", data);
        intent.putExtra("type", from);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_backup_mnemonics;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        data = getIntent().getStringExtra("data");
        type = getIntent().getIntExtra("type", 1);
        if (type == 1) {
            ETHWallet ethHDWallet = JsonUtils.jsonToObject(data, ETHWallet.class);
            String[] s = ethHDWallet.getMnemonic().split(" ");
            List<String> strings = Arrays.asList(s);
            valueBeans.addAll(strings);
        } else {
            ETHWallet ethWallet = JsonUtils.jsonToObject(data, ETHWallet.class);
            List<String> strings = Arrays.asList(ethWallet.getMnemonic().split(" "));
            mTvNext.setText("Done");
            valueBeans.addAll(strings);
        }

        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
        initRecyclerView();
    }

    private void initRecyclerView() {
        mRecylerView.setLayoutManager(new GridLayoutManager(getActivity(), 2));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), valueBeans, new BaseRecyclerViewAdapter.Delegate<String>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new MnemonicViewBean(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, String data, View view) {
                MnemonicViewBean viewBean = (MnemonicViewBean) view;
                viewBean.setData(data, position + 1, 1);
            }
        });

        mRecylerView.setAdapter(adapter);
    }


    @Override
    protected void loadData() {

    }

    @OnClick({R.id.tv_next})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_next:
                if (type == 1) {
                    CheckMnemonicsActivity.start(getActivity(), data);
                }else {
                    finish();
                }

                break;
        }
    }

}