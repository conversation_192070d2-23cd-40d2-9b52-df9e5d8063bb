// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.cy.tablayoutniubility.TabLayoutScroll;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MTaskListActivity_ViewBinding implements Unbinder {
  private MTaskListActivity target;

  private View view7f090135;

  @UiThread
  public MTaskListActivity_ViewBinding(MTaskListActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MTaskListActivity_ViewBinding(final MTaskListActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_back, "field 'mIvBack' and method 'onBindClick'");
    target.mIvBack = Utils.castView(view, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    view7f090135 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTablayout = Utils.findRequiredViewAsType(source, R.id.tablayout, "field 'mTablayout'", TabLayoutScroll.class);
    target.viewPager2 = Utils.findRequiredViewAsType(source, R.id.view_pager, "field 'viewPager2'", ViewPager2.class);
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    MTaskListActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mTablayout = null;
    target.viewPager2 = null;
    target.mMain = null;

    view7f090135.setOnClickListener(null);
    view7f090135 = null;
  }
}
