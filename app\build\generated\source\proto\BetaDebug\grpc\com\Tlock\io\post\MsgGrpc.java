package com.Tlock.io.post;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.57.0)",
    comments = "Source: post/post_tx.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class MsgGrpc {

  private MsgGrpc() {}

  public static final java.lang.String SERVICE_NAME = "post.v1.Msg";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgSetServiceName,
      com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse> getSetServiceNameMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SetServiceName",
      requestType = com.Tlock.io.post.PostTXProto.MsgSetServiceName.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgSetServiceName,
      com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse> getSetServiceNameMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgSetServiceName, com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse> getSetServiceNameMethod;
    if ((getSetServiceNameMethod = MsgGrpc.getSetServiceNameMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getSetServiceNameMethod = MsgGrpc.getSetServiceNameMethod) == null) {
          MsgGrpc.getSetServiceNameMethod = getSetServiceNameMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgSetServiceName, com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SetServiceName"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgSetServiceName.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("SetServiceName"))
              .build();
        }
      }
    }
    return getSetServiceNameMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest,
      com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse> getGrantAllowanceFromModuleMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GrantAllowanceFromModule",
      requestType = com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest,
      com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse> getGrantAllowanceFromModuleMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest, com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse> getGrantAllowanceFromModuleMethod;
    if ((getGrantAllowanceFromModuleMethod = MsgGrpc.getGrantAllowanceFromModuleMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getGrantAllowanceFromModuleMethod = MsgGrpc.getGrantAllowanceFromModuleMethod) == null) {
          MsgGrpc.getGrantAllowanceFromModuleMethod = getGrantAllowanceFromModuleMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest, com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GrantAllowanceFromModule"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("GrantAllowanceFromModule"))
              .build();
        }
      }
    }
    return getGrantAllowanceFromModuleMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgCreatePost,
      com.Tlock.io.post.PostTXProto.MsgCreatePostResponse> getCreatePostMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "CreatePost",
      requestType = com.Tlock.io.post.PostTXProto.MsgCreatePost.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgCreatePostResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgCreatePost,
      com.Tlock.io.post.PostTXProto.MsgCreatePostResponse> getCreatePostMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgCreatePost, com.Tlock.io.post.PostTXProto.MsgCreatePostResponse> getCreatePostMethod;
    if ((getCreatePostMethod = MsgGrpc.getCreatePostMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getCreatePostMethod = MsgGrpc.getCreatePostMethod) == null) {
          MsgGrpc.getCreatePostMethod = getCreatePostMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgCreatePost, com.Tlock.io.post.PostTXProto.MsgCreatePostResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "CreatePost"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgCreatePost.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgCreatePostResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("CreatePost"))
              .build();
        }
      }
    }
    return getCreatePostMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgQuotePostRequest,
      com.Tlock.io.post.PostTXProto.MsgQuotePostResponse> getQuotePostMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QuotePost",
      requestType = com.Tlock.io.post.PostTXProto.MsgQuotePostRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgQuotePostResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgQuotePostRequest,
      com.Tlock.io.post.PostTXProto.MsgQuotePostResponse> getQuotePostMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgQuotePostRequest, com.Tlock.io.post.PostTXProto.MsgQuotePostResponse> getQuotePostMethod;
    if ((getQuotePostMethod = MsgGrpc.getQuotePostMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getQuotePostMethod = MsgGrpc.getQuotePostMethod) == null) {
          MsgGrpc.getQuotePostMethod = getQuotePostMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgQuotePostRequest, com.Tlock.io.post.PostTXProto.MsgQuotePostResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QuotePost"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgQuotePostRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgQuotePostResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("QuotePost"))
              .build();
        }
      }
    }
    return getQuotePostMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgRepostRequest,
      com.Tlock.io.post.PostTXProto.MsgRepostResponse> getRepostMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Repost",
      requestType = com.Tlock.io.post.PostTXProto.MsgRepostRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgRepostResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgRepostRequest,
      com.Tlock.io.post.PostTXProto.MsgRepostResponse> getRepostMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgRepostRequest, com.Tlock.io.post.PostTXProto.MsgRepostResponse> getRepostMethod;
    if ((getRepostMethod = MsgGrpc.getRepostMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getRepostMethod = MsgGrpc.getRepostMethod) == null) {
          MsgGrpc.getRepostMethod = getRepostMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgRepostRequest, com.Tlock.io.post.PostTXProto.MsgRepostResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Repost"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgRepostRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgRepostResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("Repost"))
              .build();
        }
      }
    }
    return getRepostMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgLikeRequest,
      com.Tlock.io.post.PostTXProto.MsgLikeResponse> getLikeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Like",
      requestType = com.Tlock.io.post.PostTXProto.MsgLikeRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgLikeResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgLikeRequest,
      com.Tlock.io.post.PostTXProto.MsgLikeResponse> getLikeMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgLikeRequest, com.Tlock.io.post.PostTXProto.MsgLikeResponse> getLikeMethod;
    if ((getLikeMethod = MsgGrpc.getLikeMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getLikeMethod = MsgGrpc.getLikeMethod) == null) {
          MsgGrpc.getLikeMethod = getLikeMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgLikeRequest, com.Tlock.io.post.PostTXProto.MsgLikeResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Like"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgLikeRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgLikeResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("Like"))
              .build();
        }
      }
    }
    return getLikeMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnlikeRequest,
      com.Tlock.io.post.PostTXProto.MsgUnlikeResponse> getUnlikeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Unlike",
      requestType = com.Tlock.io.post.PostTXProto.MsgUnlikeRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgUnlikeResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnlikeRequest,
      com.Tlock.io.post.PostTXProto.MsgUnlikeResponse> getUnlikeMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnlikeRequest, com.Tlock.io.post.PostTXProto.MsgUnlikeResponse> getUnlikeMethod;
    if ((getUnlikeMethod = MsgGrpc.getUnlikeMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getUnlikeMethod = MsgGrpc.getUnlikeMethod) == null) {
          MsgGrpc.getUnlikeMethod = getUnlikeMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgUnlikeRequest, com.Tlock.io.post.PostTXProto.MsgUnlikeResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Unlike"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgUnlikeRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgUnlikeResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("Unlike"))
              .build();
        }
      }
    }
    return getUnlikeMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgSaveRequest,
      com.Tlock.io.post.PostTXProto.MsgSaveResponse> getSavePostMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SavePost",
      requestType = com.Tlock.io.post.PostTXProto.MsgSaveRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgSaveResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgSaveRequest,
      com.Tlock.io.post.PostTXProto.MsgSaveResponse> getSavePostMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgSaveRequest, com.Tlock.io.post.PostTXProto.MsgSaveResponse> getSavePostMethod;
    if ((getSavePostMethod = MsgGrpc.getSavePostMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getSavePostMethod = MsgGrpc.getSavePostMethod) == null) {
          MsgGrpc.getSavePostMethod = getSavePostMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgSaveRequest, com.Tlock.io.post.PostTXProto.MsgSaveResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SavePost"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgSaveRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgSaveResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("SavePost"))
              .build();
        }
      }
    }
    return getSavePostMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnsaveRequest,
      com.Tlock.io.post.PostTXProto.MsgUnsaveResponse> getUnsavePostMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UnsavePost",
      requestType = com.Tlock.io.post.PostTXProto.MsgUnsaveRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgUnsaveResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnsaveRequest,
      com.Tlock.io.post.PostTXProto.MsgUnsaveResponse> getUnsavePostMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnsaveRequest, com.Tlock.io.post.PostTXProto.MsgUnsaveResponse> getUnsavePostMethod;
    if ((getUnsavePostMethod = MsgGrpc.getUnsavePostMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getUnsavePostMethod = MsgGrpc.getUnsavePostMethod) == null) {
          MsgGrpc.getUnsavePostMethod = getUnsavePostMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgUnsaveRequest, com.Tlock.io.post.PostTXProto.MsgUnsaveResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UnsavePost"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgUnsaveRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgUnsaveResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("UnsavePost"))
              .build();
        }
      }
    }
    return getUnsavePostMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgCommentRequest,
      com.Tlock.io.post.PostTXProto.MsgCommentResponse> getCommentMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Comment",
      requestType = com.Tlock.io.post.PostTXProto.MsgCommentRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgCommentResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgCommentRequest,
      com.Tlock.io.post.PostTXProto.MsgCommentResponse> getCommentMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgCommentRequest, com.Tlock.io.post.PostTXProto.MsgCommentResponse> getCommentMethod;
    if ((getCommentMethod = MsgGrpc.getCommentMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getCommentMethod = MsgGrpc.getCommentMethod) == null) {
          MsgGrpc.getCommentMethod = getCommentMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgCommentRequest, com.Tlock.io.post.PostTXProto.MsgCommentResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Comment"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgCommentRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgCommentResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("Comment"))
              .build();
        }
      }
    }
    return getCommentMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest,
      com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse> getCastVoteOnPollMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "CastVoteOnPoll",
      requestType = com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest,
      com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse> getCastVoteOnPollMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest, com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse> getCastVoteOnPollMethod;
    if ((getCastVoteOnPollMethod = MsgGrpc.getCastVoteOnPollMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getCastVoteOnPollMethod = MsgGrpc.getCastVoteOnPollMethod) == null) {
          MsgGrpc.getCastVoteOnPollMethod = getCastVoteOnPollMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest, com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "CastVoteOnPoll"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("CastVoteOnPoll"))
              .build();
        }
      }
    }
    return getCastVoteOnPollMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.AddCategoryRequest,
      com.Tlock.io.post.PostTXProto.AddCategoryResponse> getAddCategoryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "AddCategory",
      requestType = com.Tlock.io.post.PostTXProto.AddCategoryRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.AddCategoryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.AddCategoryRequest,
      com.Tlock.io.post.PostTXProto.AddCategoryResponse> getAddCategoryMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.AddCategoryRequest, com.Tlock.io.post.PostTXProto.AddCategoryResponse> getAddCategoryMethod;
    if ((getAddCategoryMethod = MsgGrpc.getAddCategoryMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getAddCategoryMethod = MsgGrpc.getAddCategoryMethod) == null) {
          MsgGrpc.getAddCategoryMethod = getAddCategoryMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.AddCategoryRequest, com.Tlock.io.post.PostTXProto.AddCategoryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "AddCategory"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.AddCategoryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.AddCategoryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("AddCategory"))
              .build();
        }
      }
    }
    return getAddCategoryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.DeleteCategoryRequest,
      com.Tlock.io.post.PostTXProto.DeleteCategoryResponse> getDeleteCategoryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "DeleteCategory",
      requestType = com.Tlock.io.post.PostTXProto.DeleteCategoryRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.DeleteCategoryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.DeleteCategoryRequest,
      com.Tlock.io.post.PostTXProto.DeleteCategoryResponse> getDeleteCategoryMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.DeleteCategoryRequest, com.Tlock.io.post.PostTXProto.DeleteCategoryResponse> getDeleteCategoryMethod;
    if ((getDeleteCategoryMethod = MsgGrpc.getDeleteCategoryMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getDeleteCategoryMethod = MsgGrpc.getDeleteCategoryMethod) == null) {
          MsgGrpc.getDeleteCategoryMethod = getDeleteCategoryMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.DeleteCategoryRequest, com.Tlock.io.post.PostTXProto.DeleteCategoryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "DeleteCategory"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.DeleteCategoryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.DeleteCategoryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("DeleteCategory"))
              .build();
        }
      }
    }
    return getDeleteCategoryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.UpdateTopicRequest,
      com.Tlock.io.post.PostTXProto.UpdateTopicResponse> getUpdateTopicMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateTopic",
      requestType = com.Tlock.io.post.PostTXProto.UpdateTopicRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.UpdateTopicResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.UpdateTopicRequest,
      com.Tlock.io.post.PostTXProto.UpdateTopicResponse> getUpdateTopicMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.UpdateTopicRequest, com.Tlock.io.post.PostTXProto.UpdateTopicResponse> getUpdateTopicMethod;
    if ((getUpdateTopicMethod = MsgGrpc.getUpdateTopicMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getUpdateTopicMethod = MsgGrpc.getUpdateTopicMethod) == null) {
          MsgGrpc.getUpdateTopicMethod = getUpdateTopicMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.UpdateTopicRequest, com.Tlock.io.post.PostTXProto.UpdateTopicResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateTopic"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.UpdateTopicRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.UpdateTopicResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("UpdateTopic"))
              .build();
        }
      }
    }
    return getUpdateTopicMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest,
      com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse> getFollowTopicMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "FollowTopic",
      requestType = com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest,
      com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse> getFollowTopicMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest, com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse> getFollowTopicMethod;
    if ((getFollowTopicMethod = MsgGrpc.getFollowTopicMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getFollowTopicMethod = MsgGrpc.getFollowTopicMethod) == null) {
          MsgGrpc.getFollowTopicMethod = getFollowTopicMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest, com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "FollowTopic"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("FollowTopic"))
              .build();
        }
      }
    }
    return getFollowTopicMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest,
      com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse> getUnfollowTopicMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UnfollowTopic",
      requestType = com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest,
      com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse> getUnfollowTopicMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest, com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse> getUnfollowTopicMethod;
    if ((getUnfollowTopicMethod = MsgGrpc.getUnfollowTopicMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getUnfollowTopicMethod = MsgGrpc.getUnfollowTopicMethod) == null) {
          MsgGrpc.getUnfollowTopicMethod = getUnfollowTopicMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest, com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UnfollowTopic"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("UnfollowTopic"))
              .build();
        }
      }
    }
    return getUnfollowTopicMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest,
      com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse> getClassifyUncategorizedTopicMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ClassifyUncategorizedTopic",
      requestType = com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest.class,
      responseType = com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest,
      com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse> getClassifyUncategorizedTopicMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest, com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse> getClassifyUncategorizedTopicMethod;
    if ((getClassifyUncategorizedTopicMethod = MsgGrpc.getClassifyUncategorizedTopicMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getClassifyUncategorizedTopicMethod = MsgGrpc.getClassifyUncategorizedTopicMethod) == null) {
          MsgGrpc.getClassifyUncategorizedTopicMethod = getClassifyUncategorizedTopicMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest, com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ClassifyUncategorizedTopic"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("ClassifyUncategorizedTopic"))
              .build();
        }
      }
    }
    return getClassifyUncategorizedTopicMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static MsgStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MsgStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MsgStub>() {
        @java.lang.Override
        public MsgStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MsgStub(channel, callOptions);
        }
      };
    return MsgStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static MsgBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MsgBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MsgBlockingStub>() {
        @java.lang.Override
        public MsgBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MsgBlockingStub(channel, callOptions);
        }
      };
    return MsgBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static MsgFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MsgFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MsgFutureStub>() {
        @java.lang.Override
        public MsgFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MsgFutureStub(channel, callOptions);
        }
      };
    return MsgFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     * SetServiceName allows a user to set their accounts name.
     * </pre>
     */
    default void setServiceName(com.Tlock.io.post.PostTXProto.MsgSetServiceName request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetServiceNameMethod(), responseObserver);
    }

    /**
     */
    default void grantAllowanceFromModule(com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGrantAllowanceFromModuleMethod(), responseObserver);
    }

    /**
     */
    default void createPost(com.Tlock.io.post.PostTXProto.MsgCreatePost request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgCreatePostResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCreatePostMethod(), responseObserver);
    }

    /**
     */
    default void quotePost(com.Tlock.io.post.PostTXProto.MsgQuotePostRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgQuotePostResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQuotePostMethod(), responseObserver);
    }

    /**
     */
    default void repost(com.Tlock.io.post.PostTXProto.MsgRepostRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgRepostResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRepostMethod(), responseObserver);
    }

    /**
     */
    default void like(com.Tlock.io.post.PostTXProto.MsgLikeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgLikeResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLikeMethod(), responseObserver);
    }

    /**
     */
    default void unlike(com.Tlock.io.post.PostTXProto.MsgUnlikeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnlikeResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUnlikeMethod(), responseObserver);
    }

    /**
     */
    default void savePost(com.Tlock.io.post.PostTXProto.MsgSaveRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgSaveResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSavePostMethod(), responseObserver);
    }

    /**
     */
    default void unsavePost(com.Tlock.io.post.PostTXProto.MsgUnsaveRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnsaveResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUnsavePostMethod(), responseObserver);
    }

    /**
     */
    default void comment(com.Tlock.io.post.PostTXProto.MsgCommentRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgCommentResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCommentMethod(), responseObserver);
    }

    /**
     */
    default void castVoteOnPoll(com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCastVoteOnPollMethod(), responseObserver);
    }

    /**
     */
    default void addCategory(com.Tlock.io.post.PostTXProto.AddCategoryRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.AddCategoryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAddCategoryMethod(), responseObserver);
    }

    /**
     */
    default void deleteCategory(com.Tlock.io.post.PostTXProto.DeleteCategoryRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.DeleteCategoryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteCategoryMethod(), responseObserver);
    }

    /**
     */
    default void updateTopic(com.Tlock.io.post.PostTXProto.UpdateTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.UpdateTopicResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateTopicMethod(), responseObserver);
    }

    /**
     */
    default void followTopic(com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getFollowTopicMethod(), responseObserver);
    }

    /**
     */
    default void unfollowTopic(com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUnfollowTopicMethod(), responseObserver);
    }

    /**
     */
    default void classifyUncategorizedTopic(com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getClassifyUncategorizedTopicMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service Msg.
   */
  public static abstract class MsgImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return MsgGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service Msg.
   */
  public static final class MsgStub
      extends io.grpc.stub.AbstractAsyncStub<MsgStub> {
    private MsgStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MsgStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MsgStub(channel, callOptions);
    }

    /**
     * <pre>
     * SetServiceName allows a user to set their accounts name.
     * </pre>
     */
    public void setServiceName(com.Tlock.io.post.PostTXProto.MsgSetServiceName request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSetServiceNameMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void grantAllowanceFromModule(com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGrantAllowanceFromModuleMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void createPost(com.Tlock.io.post.PostTXProto.MsgCreatePost request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgCreatePostResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCreatePostMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void quotePost(com.Tlock.io.post.PostTXProto.MsgQuotePostRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgQuotePostResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQuotePostMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void repost(com.Tlock.io.post.PostTXProto.MsgRepostRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgRepostResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRepostMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void like(com.Tlock.io.post.PostTXProto.MsgLikeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgLikeResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLikeMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void unlike(com.Tlock.io.post.PostTXProto.MsgUnlikeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnlikeResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUnlikeMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void savePost(com.Tlock.io.post.PostTXProto.MsgSaveRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgSaveResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSavePostMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void unsavePost(com.Tlock.io.post.PostTXProto.MsgUnsaveRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnsaveResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUnsavePostMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void comment(com.Tlock.io.post.PostTXProto.MsgCommentRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgCommentResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCommentMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void castVoteOnPoll(com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCastVoteOnPollMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void addCategory(com.Tlock.io.post.PostTXProto.AddCategoryRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.AddCategoryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAddCategoryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void deleteCategory(com.Tlock.io.post.PostTXProto.DeleteCategoryRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.DeleteCategoryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteCategoryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void updateTopic(com.Tlock.io.post.PostTXProto.UpdateTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.UpdateTopicResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateTopicMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void followTopic(com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getFollowTopicMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void unfollowTopic(com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUnfollowTopicMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void classifyUncategorizedTopic(com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getClassifyUncategorizedTopicMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service Msg.
   */
  public static final class MsgBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<MsgBlockingStub> {
    private MsgBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MsgBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MsgBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * SetServiceName allows a user to set their accounts name.
     * </pre>
     */
    public com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse setServiceName(com.Tlock.io.post.PostTXProto.MsgSetServiceName request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSetServiceNameMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse grantAllowanceFromModule(com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGrantAllowanceFromModuleMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgCreatePostResponse createPost(com.Tlock.io.post.PostTXProto.MsgCreatePost request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCreatePostMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgQuotePostResponse quotePost(com.Tlock.io.post.PostTXProto.MsgQuotePostRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQuotePostMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgRepostResponse repost(com.Tlock.io.post.PostTXProto.MsgRepostRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRepostMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgLikeResponse like(com.Tlock.io.post.PostTXProto.MsgLikeRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLikeMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgUnlikeResponse unlike(com.Tlock.io.post.PostTXProto.MsgUnlikeRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUnlikeMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgSaveResponse savePost(com.Tlock.io.post.PostTXProto.MsgSaveRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSavePostMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgUnsaveResponse unsavePost(com.Tlock.io.post.PostTXProto.MsgUnsaveRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUnsavePostMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgCommentResponse comment(com.Tlock.io.post.PostTXProto.MsgCommentRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCommentMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse castVoteOnPoll(com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCastVoteOnPollMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.AddCategoryResponse addCategory(com.Tlock.io.post.PostTXProto.AddCategoryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAddCategoryMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.DeleteCategoryResponse deleteCategory(com.Tlock.io.post.PostTXProto.DeleteCategoryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteCategoryMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.UpdateTopicResponse updateTopic(com.Tlock.io.post.PostTXProto.UpdateTopicRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateTopicMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse followTopic(com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getFollowTopicMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse unfollowTopic(com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUnfollowTopicMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse classifyUncategorizedTopic(com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getClassifyUncategorizedTopicMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service Msg.
   */
  public static final class MsgFutureStub
      extends io.grpc.stub.AbstractFutureStub<MsgFutureStub> {
    private MsgFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MsgFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MsgFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * SetServiceName allows a user to set their accounts name.
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse> setServiceName(
        com.Tlock.io.post.PostTXProto.MsgSetServiceName request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSetServiceNameMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse> grantAllowanceFromModule(
        com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGrantAllowanceFromModuleMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgCreatePostResponse> createPost(
        com.Tlock.io.post.PostTXProto.MsgCreatePost request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCreatePostMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgQuotePostResponse> quotePost(
        com.Tlock.io.post.PostTXProto.MsgQuotePostRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQuotePostMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgRepostResponse> repost(
        com.Tlock.io.post.PostTXProto.MsgRepostRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRepostMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgLikeResponse> like(
        com.Tlock.io.post.PostTXProto.MsgLikeRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLikeMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgUnlikeResponse> unlike(
        com.Tlock.io.post.PostTXProto.MsgUnlikeRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUnlikeMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgSaveResponse> savePost(
        com.Tlock.io.post.PostTXProto.MsgSaveRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSavePostMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgUnsaveResponse> unsavePost(
        com.Tlock.io.post.PostTXProto.MsgUnsaveRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUnsavePostMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgCommentResponse> comment(
        com.Tlock.io.post.PostTXProto.MsgCommentRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCommentMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse> castVoteOnPoll(
        com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCastVoteOnPollMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.AddCategoryResponse> addCategory(
        com.Tlock.io.post.PostTXProto.AddCategoryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAddCategoryMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.DeleteCategoryResponse> deleteCategory(
        com.Tlock.io.post.PostTXProto.DeleteCategoryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteCategoryMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.UpdateTopicResponse> updateTopic(
        com.Tlock.io.post.PostTXProto.UpdateTopicRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateTopicMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse> followTopic(
        com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getFollowTopicMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse> unfollowTopic(
        com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUnfollowTopicMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse> classifyUncategorizedTopic(
        com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getClassifyUncategorizedTopicMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SET_SERVICE_NAME = 0;
  private static final int METHODID_GRANT_ALLOWANCE_FROM_MODULE = 1;
  private static final int METHODID_CREATE_POST = 2;
  private static final int METHODID_QUOTE_POST = 3;
  private static final int METHODID_REPOST = 4;
  private static final int METHODID_LIKE = 5;
  private static final int METHODID_UNLIKE = 6;
  private static final int METHODID_SAVE_POST = 7;
  private static final int METHODID_UNSAVE_POST = 8;
  private static final int METHODID_COMMENT = 9;
  private static final int METHODID_CAST_VOTE_ON_POLL = 10;
  private static final int METHODID_ADD_CATEGORY = 11;
  private static final int METHODID_DELETE_CATEGORY = 12;
  private static final int METHODID_UPDATE_TOPIC = 13;
  private static final int METHODID_FOLLOW_TOPIC = 14;
  private static final int METHODID_UNFOLLOW_TOPIC = 15;
  private static final int METHODID_CLASSIFY_UNCATEGORIZED_TOPIC = 16;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SET_SERVICE_NAME:
          serviceImpl.setServiceName((com.Tlock.io.post.PostTXProto.MsgSetServiceName) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse>) responseObserver);
          break;
        case METHODID_GRANT_ALLOWANCE_FROM_MODULE:
          serviceImpl.grantAllowanceFromModule((com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse>) responseObserver);
          break;
        case METHODID_CREATE_POST:
          serviceImpl.createPost((com.Tlock.io.post.PostTXProto.MsgCreatePost) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgCreatePostResponse>) responseObserver);
          break;
        case METHODID_QUOTE_POST:
          serviceImpl.quotePost((com.Tlock.io.post.PostTXProto.MsgQuotePostRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgQuotePostResponse>) responseObserver);
          break;
        case METHODID_REPOST:
          serviceImpl.repost((com.Tlock.io.post.PostTXProto.MsgRepostRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgRepostResponse>) responseObserver);
          break;
        case METHODID_LIKE:
          serviceImpl.like((com.Tlock.io.post.PostTXProto.MsgLikeRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgLikeResponse>) responseObserver);
          break;
        case METHODID_UNLIKE:
          serviceImpl.unlike((com.Tlock.io.post.PostTXProto.MsgUnlikeRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnlikeResponse>) responseObserver);
          break;
        case METHODID_SAVE_POST:
          serviceImpl.savePost((com.Tlock.io.post.PostTXProto.MsgSaveRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgSaveResponse>) responseObserver);
          break;
        case METHODID_UNSAVE_POST:
          serviceImpl.unsavePost((com.Tlock.io.post.PostTXProto.MsgUnsaveRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnsaveResponse>) responseObserver);
          break;
        case METHODID_COMMENT:
          serviceImpl.comment((com.Tlock.io.post.PostTXProto.MsgCommentRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgCommentResponse>) responseObserver);
          break;
        case METHODID_CAST_VOTE_ON_POLL:
          serviceImpl.castVoteOnPoll((com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse>) responseObserver);
          break;
        case METHODID_ADD_CATEGORY:
          serviceImpl.addCategory((com.Tlock.io.post.PostTXProto.AddCategoryRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.AddCategoryResponse>) responseObserver);
          break;
        case METHODID_DELETE_CATEGORY:
          serviceImpl.deleteCategory((com.Tlock.io.post.PostTXProto.DeleteCategoryRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.DeleteCategoryResponse>) responseObserver);
          break;
        case METHODID_UPDATE_TOPIC:
          serviceImpl.updateTopic((com.Tlock.io.post.PostTXProto.UpdateTopicRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.UpdateTopicResponse>) responseObserver);
          break;
        case METHODID_FOLLOW_TOPIC:
          serviceImpl.followTopic((com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse>) responseObserver);
          break;
        case METHODID_UNFOLLOW_TOPIC:
          serviceImpl.unfollowTopic((com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse>) responseObserver);
          break;
        case METHODID_CLASSIFY_UNCATEGORIZED_TOPIC:
          serviceImpl.classifyUncategorizedTopic((com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getSetServiceNameMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgSetServiceName,
              com.Tlock.io.post.PostTXProto.MsgSetServiceNameResponse>(
                service, METHODID_SET_SERVICE_NAME)))
        .addMethod(
          getGrantAllowanceFromModuleMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleRequest,
              com.Tlock.io.post.PostTXProto.MsgGrantAllowanceFromModuleResponse>(
                service, METHODID_GRANT_ALLOWANCE_FROM_MODULE)))
        .addMethod(
          getCreatePostMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgCreatePost,
              com.Tlock.io.post.PostTXProto.MsgCreatePostResponse>(
                service, METHODID_CREATE_POST)))
        .addMethod(
          getQuotePostMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgQuotePostRequest,
              com.Tlock.io.post.PostTXProto.MsgQuotePostResponse>(
                service, METHODID_QUOTE_POST)))
        .addMethod(
          getRepostMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgRepostRequest,
              com.Tlock.io.post.PostTXProto.MsgRepostResponse>(
                service, METHODID_REPOST)))
        .addMethod(
          getLikeMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgLikeRequest,
              com.Tlock.io.post.PostTXProto.MsgLikeResponse>(
                service, METHODID_LIKE)))
        .addMethod(
          getUnlikeMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgUnlikeRequest,
              com.Tlock.io.post.PostTXProto.MsgUnlikeResponse>(
                service, METHODID_UNLIKE)))
        .addMethod(
          getSavePostMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgSaveRequest,
              com.Tlock.io.post.PostTXProto.MsgSaveResponse>(
                service, METHODID_SAVE_POST)))
        .addMethod(
          getUnsavePostMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgUnsaveRequest,
              com.Tlock.io.post.PostTXProto.MsgUnsaveResponse>(
                service, METHODID_UNSAVE_POST)))
        .addMethod(
          getCommentMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgCommentRequest,
              com.Tlock.io.post.PostTXProto.MsgCommentResponse>(
                service, METHODID_COMMENT)))
        .addMethod(
          getCastVoteOnPollMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.CastVoteOnPollRequest,
              com.Tlock.io.post.PostTXProto.CastVoteOnPollResponse>(
                service, METHODID_CAST_VOTE_ON_POLL)))
        .addMethod(
          getAddCategoryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.AddCategoryRequest,
              com.Tlock.io.post.PostTXProto.AddCategoryResponse>(
                service, METHODID_ADD_CATEGORY)))
        .addMethod(
          getDeleteCategoryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.DeleteCategoryRequest,
              com.Tlock.io.post.PostTXProto.DeleteCategoryResponse>(
                service, METHODID_DELETE_CATEGORY)))
        .addMethod(
          getUpdateTopicMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.UpdateTopicRequest,
              com.Tlock.io.post.PostTXProto.UpdateTopicResponse>(
                service, METHODID_UPDATE_TOPIC)))
        .addMethod(
          getFollowTopicMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgFollowTopicRequest,
              com.Tlock.io.post.PostTXProto.MsgFollowTopicResponse>(
                service, METHODID_FOLLOW_TOPIC)))
        .addMethod(
          getUnfollowTopicMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.MsgUnfollowTopicRequest,
              com.Tlock.io.post.PostTXProto.MsgUnfollowTopicResponse>(
                service, METHODID_UNFOLLOW_TOPIC)))
        .addMethod(
          getClassifyUncategorizedTopicMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicRequest,
              com.Tlock.io.post.PostTXProto.ClassifyUncategorizedTopicResponse>(
                service, METHODID_CLASSIFY_UNCATEGORIZED_TOPIC)))
        .build();
  }

  private static abstract class MsgBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    MsgBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.Tlock.io.post.PostTXProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Msg");
    }
  }

  private static final class MsgFileDescriptorSupplier
      extends MsgBaseDescriptorSupplier {
    MsgFileDescriptorSupplier() {}
  }

  private static final class MsgMethodDescriptorSupplier
      extends MsgBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    MsgMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (MsgGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new MsgFileDescriptorSupplier())
              .addMethod(getSetServiceNameMethod())
              .addMethod(getGrantAllowanceFromModuleMethod())
              .addMethod(getCreatePostMethod())
              .addMethod(getQuotePostMethod())
              .addMethod(getRepostMethod())
              .addMethod(getLikeMethod())
              .addMethod(getUnlikeMethod())
              .addMethod(getSavePostMethod())
              .addMethod(getUnsavePostMethod())
              .addMethod(getCommentMethod())
              .addMethod(getCastVoteOnPollMethod())
              .addMethod(getAddCategoryMethod())
              .addMethod(getDeleteCategoryMethod())
              .addMethod(getUpdateTopicMethod())
              .addMethod(getFollowTopicMethod())
              .addMethod(getUnfollowTopicMethod())
              .addMethod(getClassifyUncategorizedTopicMethod())
              .build();
        }
      }
    }
    return result;
  }
}
