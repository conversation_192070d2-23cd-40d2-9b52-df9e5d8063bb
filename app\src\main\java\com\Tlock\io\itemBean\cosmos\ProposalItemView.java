package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.DateUtil.friendly_time_2;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.widget.FontTextView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;

import butterknife.BindView;
import butterknife.OnClick;
import io.noties.markwon.Markwon;
import io.noties.markwon.image.glide.GlideImagesPlugin;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class ProposalItemView extends BaseView {


    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.tv_account)
    TextView mTvAccount;
    @BindView(R.id.tv_content)
    FontTextView mTvContent;
    @BindView(R.id.tv_share)
    TextView mTvShare;
    @BindView(R.id.tv_review)
    TextView mTvReview;
    @BindView(R.id.tv_praise)
    TextView mTvPraise;
    @BindView(R.id.tv_Collect)
    TextView mTvCollect;
    @BindView(R.id.iv_post)
    ImageView mIvPost;
    @BindView(R.id.iv_share)
    ImageView mIvShare;
    @BindView(R.id.ll_share)
    RelativeLayout mLlShare;
    @BindView(R.id.iv_review)
    ImageView mIvReview;
    @BindView(R.id.ll_review)
    RelativeLayout mLlReview;
    @BindView(R.id.iv_praise)
    ImageView mIvPraise;
    @BindView(R.id.ll_praise)
    RelativeLayout mLlPraise;
    @BindView(R.id.iv_Collect)
    ImageView mIvCollect;
    @BindView(R.id.ll_Collect)
    RelativeLayout mLlCollect;


    public ProposalItemView(Context context) {
        super(context);
    }

    public ProposalItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ProposalItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public PostProto.Post post;
    public ProfileProto.Profile profile;
    public PostQueryProto.PostResponse data;
    public PostProto.Post data1;

    @Override
    protected int getLayoutId() {
        return R.layout.item_post;
    }

    public void setData(PostQueryProto.PostResponse data) {
        this.data = data;
        this.post = data.getPost();
        this.profile = data.getProfile();
        Markwon markdwon = Markwon.builder(mTvAccount.getContext())
                .usePlugin(GlideImagesPlugin.create(mTvAccount.getContext()))
                .build();
        markdwon.setMarkdown(mTvContent, post.getContent());

        if (TextUtils.isEmpty(profile.getAvatar())) {
            Glide.with(mTvCollect.getContext()).load(R.mipmap.tlk_round).apply(new RequestOptions()
                            .centerCrop()
                            .dontTransform())
                    .apply(RequestOptions.circleCropTransform().circleCrop())
                    .into(mIvHeard);
        } else {
            Glide.with(mTvCollect.getContext()).asGif().load(profile.getAvatar()).apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .signature(new ObjectKey(profile.getAvatar()))
                            .centerCrop()
                            .dontTransform())
                    .apply(RequestOptions.circleCropTransform().circleCrop())
                    .placeholder(getResources().getDrawable(R.mipmap.tlk_round)).into(mIvHeard);
        }


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_2(post.getTimestamp() + "000"));
        }
        mTvAccountName.setText(profile.getNickname());
        mTvAccount.setText("@" + profile.getUserHandle());
        mTvReview.setText(post.getCommentCount() != 0 ? post.getCommentCount() + "" : "Comments");
        mTvPraise.setText(post.getLikeCount() != 0 ? post.getLikeCount() + "" : "Likes");
//        mTvCollect.setText(post.getReportCount() != 0 ? post.getReportCount() + "" : "收藏");
        if (post.getImagesUrlCount() != 0 && !TextUtils.isEmpty(post.getImagesUrl(0))) {
            mIvPost.setVisibility(VISIBLE);
            Glide.with(mTvCollect.getContext()).load(post.getImagesUrl(0)).apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .centerCrop()
                            .signature(new ObjectKey(post.getImagesUrl(0)))
                            .dontTransform())
                    .into(mIvPost);

        } else {
            mIvPost.setVisibility(GONE);
        }

    }


    public void setData(PostQueryProto.CommentReceivedResponse data) {
        this.post = data.getComment();
        this.data1=data.getParent();
        Markwon markdwon = Markwon.builder(mContext)
                .usePlugin(GlideImagesPlugin.create(mContext))
                .build();
        markdwon.setMarkdown(mTvContent, post.getContent());

        if (TextUtils.isEmpty(profile.getAvatar())) {
            Glide.with(mTvCollect.getContext()).load(R.mipmap.tlk_round).apply(new RequestOptions()
                            .centerCrop()
                            .dontTransform())
                    .apply(RequestOptions.circleCropTransform().circleCrop())
                    .into(mIvHeard);
        } else {
            Glide.with(mTvCollect.getContext()).asGif().load(profile.getAvatar()).apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .signature(new ObjectKey(profile.getAvatar()))
                            .centerCrop()
                            .dontTransform())
                    .apply(RequestOptions.circleCropTransform().circleCrop())
                    .placeholder(getResources().getDrawable(R.mipmap.tlk_round)).into(mIvHeard);
        }


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_2(post.getTimestamp() + "000"));
        }
        mTvAccountName.setText(profile.getNickname());
        mTvAccount.setText("@" + profile.getUserHandle());
        mTvReview.setText(post.getCommentCount() != 0 ? post.getCommentCount() + "" : "Comments");
        mTvPraise.setText(post.getLikeCount() != 0 ? post.getLikeCount() + "" : "Likes");
//        mTvCollect.setText(post.getReportCount() != 0 ? post.getReportCount() + "" : "收藏");
        if (post.getImagesUrlCount() != 0 && !TextUtils.isEmpty(post.getImagesUrl(0))) {
            mIvPost.setVisibility(VISIBLE);
            Glide.with(mTvCollect.getContext()).load(post.getImagesUrl(0)).apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .centerCrop()
                            .signature(new ObjectKey(post.getImagesUrl(0)))
                            .dontTransform())
                    .into(mIvPost);

        } else {
            mIvPost.setVisibility(GONE);
        }

    }

    public void setCommentCount() {
        String string1 = mTvReview.getText().toString();
        if (TextUtils.isDigitsOnly(string1)) {
            int i = Integer.parseInt(string1);
            mTvReview.setText((i + 1) + "");
        } else {
            mTvReview.setText("1");
        }
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @OnClick({R.id.ll_share, R.id.ll_review, R.id.ll_praise, R.id.ll_Collect, R.id.tv_content})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.ll_share:
                if (callback != null)
                    callback.share();
                break;
            case R.id.tv_content:
                if (callback != null)
                    callback.info(data);
                break;
            case R.id.ll_review:
//                Glide.with(mTvCollect.getContext()).asGif().load(R.drawable.gif_comment)
//                        .addListener(new RequestListener<GifDrawable>() {
//                            @Override
//                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
//                                return false;
//                            }
//
//                            @Override
//                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
//                                resource.setLoopCount(1);
//                                return false;
//
//                            }
//                        })
//                        .into(mIvReview);
//                if (callback != null)
//                    callback.review(post.getId(),profile.getUserHandle());
                break;
            case R.id.ll_praise:
//                Glide.with(mLlPraise.getContext()).asGif().load(R.drawable.gif_like)
//                        .addListener(new RequestListener<GifDrawable>() {
//                            @Override
//                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
//                                return false;
//                            }
//
//                            @Override
//                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
//                                resource.setLoopCount(1);
//                                return false;
//                            }
//                        })
//                        .into(mIvPraise);
//                String string = mTvPraise.getText().toString();
//                if (TextUtils.isDigitsOnly(string)) {
//                    int i = Integer.parseInt(string);
//                    mTvPraise.setText((i + 1) + "");
//                } else {
//                    mTvPraise.setText("1");
//                }
//                if (callback != null)
//                    callback.praise(post.getId());
                break;
            case R.id.ll_Collect:
//                Glide.with(mTvCollect.getContext()).asGif().load(R.drawable.gif_start)
//                        .addListener(new RequestListener<GifDrawable>() {
//                            @Override
//                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
//                                return false;
//                            }
//
//                            @Override
//                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
//                                resource.setLoopCount(1);
//                                return false;
//
//                            }
//                        })
//                        .into(mIvCollect);
//                if (callback != null)
//                    callback.collect(post.getId());
                break;
        }
    }

    public interface Callback {
        void share();

        void review(String id,String handle);

        void praise(String id);

        void collect(String id);

        void info(PostQueryProto.PostResponse data);

    }
}
