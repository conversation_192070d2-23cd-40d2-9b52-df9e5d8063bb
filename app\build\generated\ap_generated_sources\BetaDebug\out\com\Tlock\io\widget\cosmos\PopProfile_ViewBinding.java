// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopProfile_ViewBinding implements Unbinder {
  private PopProfile target;

  private View view7f09013c;

  private View view7f09030e;

  @UiThread
  public PopProfile_ViewBinding(PopProfile target) {
    this(target, target);
  }

  @UiThread
  public PopProfile_ViewBinding(final PopProfile target, View source) {
    this.target = target;

    View view;
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_close, "field 'mIvClose' and method 'onViewClicked'");
    target.mIvClose = Utils.castView(view, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    view7f09013c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mEdOldPwd = Utils.findRequiredViewAsType(source, R.id.ed_old_pwd, "field 'mEdOldPwd'", EditText.class);
    target.mEdNewPwd = Utils.findRequiredViewAsType(source, R.id.ed_new_pwd, "field 'mEdNewPwd'", EditText.class);
    target.mIvNewSet = Utils.findRequiredViewAsType(source, R.id.iv_new_set, "field 'mIvNewSet'", ImageView.class);
    target.mRlNew = Utils.findRequiredViewAsType(source, R.id.rl_new, "field 'mRlNew'", RelativeLayout.class);
    target.mEdCheckPwd = Utils.findRequiredViewAsType(source, R.id.ed_check_pwd, "field 'mEdCheckPwd'", EditText.class);
    target.mIvCheckSet = Utils.findRequiredViewAsType(source, R.id.iv_check_set, "field 'mIvCheckSet'", ImageView.class);
    target.mRlCheck = Utils.findRequiredViewAsType(source, R.id.rl_check, "field 'mRlCheck'", RelativeLayout.class);
    target.mEdLocal = Utils.findRequiredViewAsType(source, R.id.ed_local, "field 'mEdLocal'", EditText.class);
    target.mRlLocal = Utils.findRequiredViewAsType(source, R.id.rl_local, "field 'mRlLocal'", RelativeLayout.class);
    target.mEdWebSite = Utils.findRequiredViewAsType(source, R.id.ed_web_site, "field 'mEdWebSite'", EditText.class);
    target.mRlWebSite = Utils.findRequiredViewAsType(source, R.id.rl_web_site, "field 'mRlWebSite'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onViewClicked'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f09030e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mEdBio = Utils.findRequiredViewAsType(source, R.id.ed_bio, "field 'mEdBio'", EditText.class);
    target.mRlBio = Utils.findRequiredViewAsType(source, R.id.rl_Bio, "field 'mRlBio'", RelativeLayout.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv1, "field 'mTv1'", TextView.class);
    target.mRlOld = Utils.findRequiredViewAsType(source, R.id.rl_old, "field 'mRlOld'", RelativeLayout.class);
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv2, "field 'mTv2'", TextView.class);
    target.mTv3 = Utils.findRequiredViewAsType(source, R.id.tv3, "field 'mTv3'", TextView.class);
    target.mTv4 = Utils.findRequiredViewAsType(source, R.id.tv4, "field 'mTv4'", TextView.class);
    target.mTv5 = Utils.findRequiredViewAsType(source, R.id.tv5, "field 'mTv5'", TextView.class);
    target.mTv6 = Utils.findRequiredViewAsType(source, R.id.tv6, "field 'mTv6'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopProfile target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mIvClose = null;
    target.mEdOldPwd = null;
    target.mEdNewPwd = null;
    target.mIvNewSet = null;
    target.mRlNew = null;
    target.mEdCheckPwd = null;
    target.mIvCheckSet = null;
    target.mRlCheck = null;
    target.mEdLocal = null;
    target.mRlLocal = null;
    target.mEdWebSite = null;
    target.mRlWebSite = null;
    target.mTvConfirm = null;
    target.mEdBio = null;
    target.mRlBio = null;
    target.mTv1 = null;
    target.mRlOld = null;
    target.mTv2 = null;
    target.mTv3 = null;
    target.mTv4 = null;
    target.mTv5 = null;
    target.mTv6 = null;

    view7f09013c.setOnClickListener(null);
    view7f09013c = null;
    view7f09030e.setOnClickListener(null);
    view7f09030e = null;
  }
}
