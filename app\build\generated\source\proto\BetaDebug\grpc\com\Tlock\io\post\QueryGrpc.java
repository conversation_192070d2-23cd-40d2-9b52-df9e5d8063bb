package com.Tlock.io.post;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.57.0)",
    comments = "Source: post/post_query.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class QueryGrpc {

  private QueryGrpc() {}

  public static final java.lang.String SERVICE_NAME = "post.v1.Query";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest,
      com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse> getResolveNameMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ResolveName",
      requestType = com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest,
      com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse> getResolveNameMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest, com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse> getResolveNameMethod;
    if ((getResolveNameMethod = QueryGrpc.getResolveNameMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getResolveNameMethod = QueryGrpc.getResolveNameMethod) == null) {
          QueryGrpc.getResolveNameMethod = getResolveNameMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest, com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ResolveName"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("ResolveName"))
              .build();
        }
      }
    }
    return getResolveNameMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse> getQueryHomePostsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryHomePosts",
      requestType = com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse> getQueryHomePostsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest, com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse> getQueryHomePostsMethod;
    if ((getQueryHomePostsMethod = QueryGrpc.getQueryHomePostsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryHomePostsMethod = QueryGrpc.getQueryHomePostsMethod) == null) {
          QueryGrpc.getQueryHomePostsMethod = getQueryHomePostsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest, com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryHomePosts"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryHomePosts"))
              .build();
        }
      }
    }
    return getQueryHomePostsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse> getQueryFirstPageHomePostsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryFirstPageHomePosts",
      requestType = com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse> getQueryFirstPageHomePostsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest, com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse> getQueryFirstPageHomePostsMethod;
    if ((getQueryFirstPageHomePostsMethod = QueryGrpc.getQueryFirstPageHomePostsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryFirstPageHomePostsMethod = QueryGrpc.getQueryFirstPageHomePostsMethod) == null) {
          QueryGrpc.getQueryFirstPageHomePostsMethod = getQueryFirstPageHomePostsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest, com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryFirstPageHomePosts"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryFirstPageHomePosts"))
              .build();
        }
      }
    }
    return getQueryFirstPageHomePostsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.SearchTopicsRequest,
      com.Tlock.io.post.PostQueryProto.SearchTopicsResponse> getSearchTopicsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SearchTopics",
      requestType = com.Tlock.io.post.PostQueryProto.SearchTopicsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.SearchTopicsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.SearchTopicsRequest,
      com.Tlock.io.post.PostQueryProto.SearchTopicsResponse> getSearchTopicsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.SearchTopicsRequest, com.Tlock.io.post.PostQueryProto.SearchTopicsResponse> getSearchTopicsMethod;
    if ((getSearchTopicsMethod = QueryGrpc.getSearchTopicsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getSearchTopicsMethod = QueryGrpc.getSearchTopicsMethod) == null) {
          QueryGrpc.getSearchTopicsMethod = getSearchTopicsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.SearchTopicsRequest, com.Tlock.io.post.PostQueryProto.SearchTopicsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SearchTopics"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.SearchTopicsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.SearchTopicsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("SearchTopics"))
              .build();
        }
      }
    }
    return getSearchTopicsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse> getQueryTopicPostsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryTopicPosts",
      requestType = com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse> getQueryTopicPostsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest, com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse> getQueryTopicPostsMethod;
    if ((getQueryTopicPostsMethod = QueryGrpc.getQueryTopicPostsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryTopicPostsMethod = QueryGrpc.getQueryTopicPostsMethod) == null) {
          QueryGrpc.getQueryTopicPostsMethod = getQueryTopicPostsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest, com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryTopicPosts"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryTopicPosts"))
              .build();
        }
      }
    }
    return getQueryTopicPostsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryPostRequest,
      com.Tlock.io.post.PostQueryProto.QueryPostResponse> getQueryPostMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryPost",
      requestType = com.Tlock.io.post.PostQueryProto.QueryPostRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryPostResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryPostRequest,
      com.Tlock.io.post.PostQueryProto.QueryPostResponse> getQueryPostMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryPostRequest, com.Tlock.io.post.PostQueryProto.QueryPostResponse> getQueryPostMethod;
    if ((getQueryPostMethod = QueryGrpc.getQueryPostMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryPostMethod = QueryGrpc.getQueryPostMethod) == null) {
          QueryGrpc.getQueryPostMethod = getQueryPostMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryPostRequest, com.Tlock.io.post.PostQueryProto.QueryPostResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryPost"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryPostRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryPostResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryPost"))
              .build();
        }
      }
    }
    return getQueryPostMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse> getQueryUserCreatedPostsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryUserCreatedPosts",
      requestType = com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse> getQueryUserCreatedPostsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest, com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse> getQueryUserCreatedPostsMethod;
    if ((getQueryUserCreatedPostsMethod = QueryGrpc.getQueryUserCreatedPostsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryUserCreatedPostsMethod = QueryGrpc.getQueryUserCreatedPostsMethod) == null) {
          QueryGrpc.getQueryUserCreatedPostsMethod = getQueryUserCreatedPostsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest, com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryUserCreatedPosts"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryUserCreatedPosts"))
              .build();
        }
      }
    }
    return getQueryUserCreatedPostsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.LikesIMadeRequest,
      com.Tlock.io.post.PostQueryProto.LikesIMadeResponse> getLikesIMadeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "LikesIMade",
      requestType = com.Tlock.io.post.PostQueryProto.LikesIMadeRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.LikesIMadeResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.LikesIMadeRequest,
      com.Tlock.io.post.PostQueryProto.LikesIMadeResponse> getLikesIMadeMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.LikesIMadeRequest, com.Tlock.io.post.PostQueryProto.LikesIMadeResponse> getLikesIMadeMethod;
    if ((getLikesIMadeMethod = QueryGrpc.getLikesIMadeMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getLikesIMadeMethod = QueryGrpc.getLikesIMadeMethod) == null) {
          QueryGrpc.getLikesIMadeMethod = getLikesIMadeMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.LikesIMadeRequest, com.Tlock.io.post.PostQueryProto.LikesIMadeResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "LikesIMade"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.LikesIMadeRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.LikesIMadeResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("LikesIMade"))
              .build();
        }
      }
    }
    return getLikesIMadeMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.SavesIMadeRequest,
      com.Tlock.io.post.PostQueryProto.SavesIMadeResponse> getSavesIMadeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SavesIMade",
      requestType = com.Tlock.io.post.PostQueryProto.SavesIMadeRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.SavesIMadeResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.SavesIMadeRequest,
      com.Tlock.io.post.PostQueryProto.SavesIMadeResponse> getSavesIMadeMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.SavesIMadeRequest, com.Tlock.io.post.PostQueryProto.SavesIMadeResponse> getSavesIMadeMethod;
    if ((getSavesIMadeMethod = QueryGrpc.getSavesIMadeMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getSavesIMadeMethod = QueryGrpc.getSavesIMadeMethod) == null) {
          QueryGrpc.getSavesIMadeMethod = getSavesIMadeMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.SavesIMadeRequest, com.Tlock.io.post.PostQueryProto.SavesIMadeResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SavesIMade"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.SavesIMadeRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.SavesIMadeResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("SavesIMade"))
              .build();
        }
      }
    }
    return getSavesIMadeMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.LikesReceivedRequest,
      com.Tlock.io.post.PostQueryProto.LikesReceivedResponse> getLikesReceivedMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "LikesReceived",
      requestType = com.Tlock.io.post.PostQueryProto.LikesReceivedRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.LikesReceivedResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.LikesReceivedRequest,
      com.Tlock.io.post.PostQueryProto.LikesReceivedResponse> getLikesReceivedMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.LikesReceivedRequest, com.Tlock.io.post.PostQueryProto.LikesReceivedResponse> getLikesReceivedMethod;
    if ((getLikesReceivedMethod = QueryGrpc.getLikesReceivedMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getLikesReceivedMethod = QueryGrpc.getLikesReceivedMethod) == null) {
          QueryGrpc.getLikesReceivedMethod = getLikesReceivedMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.LikesReceivedRequest, com.Tlock.io.post.PostQueryProto.LikesReceivedResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "LikesReceived"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.LikesReceivedRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.LikesReceivedResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("LikesReceived"))
              .build();
        }
      }
    }
    return getLikesReceivedMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCommentsRequest,
      com.Tlock.io.post.PostQueryProto.QueryCommentsResponse> getQueryCommentsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryComments",
      requestType = com.Tlock.io.post.PostQueryProto.QueryCommentsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryCommentsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCommentsRequest,
      com.Tlock.io.post.PostQueryProto.QueryCommentsResponse> getQueryCommentsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCommentsRequest, com.Tlock.io.post.PostQueryProto.QueryCommentsResponse> getQueryCommentsMethod;
    if ((getQueryCommentsMethod = QueryGrpc.getQueryCommentsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryCommentsMethod = QueryGrpc.getQueryCommentsMethod) == null) {
          QueryGrpc.getQueryCommentsMethod = getQueryCommentsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryCommentsRequest, com.Tlock.io.post.PostQueryProto.QueryCommentsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryComments"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCommentsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCommentsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryComments"))
              .build();
        }
      }
    }
    return getQueryCommentsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest,
      com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse> getQueryCommentsReceivedMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryCommentsReceived",
      requestType = com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest,
      com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse> getQueryCommentsReceivedMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest, com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse> getQueryCommentsReceivedMethod;
    if ((getQueryCommentsReceivedMethod = QueryGrpc.getQueryCommentsReceivedMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryCommentsReceivedMethod = QueryGrpc.getQueryCommentsReceivedMethod) == null) {
          QueryGrpc.getQueryCommentsReceivedMethod = getQueryCommentsReceivedMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest, com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryCommentsReceived"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryCommentsReceived"))
              .build();
        }
      }
    }
    return getQueryCommentsReceivedMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest,
      com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse> getQueryActivitiesReceivedMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryActivitiesReceived",
      requestType = com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest,
      com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse> getQueryActivitiesReceivedMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest, com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse> getQueryActivitiesReceivedMethod;
    if ((getQueryActivitiesReceivedMethod = QueryGrpc.getQueryActivitiesReceivedMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryActivitiesReceivedMethod = QueryGrpc.getQueryActivitiesReceivedMethod) == null) {
          QueryGrpc.getQueryActivitiesReceivedMethod = getQueryActivitiesReceivedMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest, com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryActivitiesReceived"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryActivitiesReceived"))
              .build();
        }
      }
    }
    return getQueryActivitiesReceivedMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest,
      com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse> getQueryCategoriesMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryCategories",
      requestType = com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest,
      com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse> getQueryCategoriesMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest, com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse> getQueryCategoriesMethod;
    if ((getQueryCategoriesMethod = QueryGrpc.getQueryCategoriesMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryCategoriesMethod = QueryGrpc.getQueryCategoriesMethod) == null) {
          QueryGrpc.getQueryCategoriesMethod = getQueryCategoriesMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest, com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryCategories"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryCategories"))
              .build();
        }
      }
    }
    return getQueryCategoriesMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest,
      com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse> getQueryTopicsByCategoryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryTopicsByCategory",
      requestType = com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest,
      com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse> getQueryTopicsByCategoryMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest, com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse> getQueryTopicsByCategoryMethod;
    if ((getQueryTopicsByCategoryMethod = QueryGrpc.getQueryTopicsByCategoryMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryTopicsByCategoryMethod = QueryGrpc.getQueryTopicsByCategoryMethod) == null) {
          QueryGrpc.getQueryTopicsByCategoryMethod = getQueryTopicsByCategoryMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest, com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryTopicsByCategory"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryTopicsByCategory"))
              .build();
        }
      }
    }
    return getQueryTopicsByCategoryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest,
      com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse> getQueryCategoryByTopicMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryCategoryByTopic",
      requestType = com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest,
      com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse> getQueryCategoryByTopicMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest, com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse> getQueryCategoryByTopicMethod;
    if ((getQueryCategoryByTopicMethod = QueryGrpc.getQueryCategoryByTopicMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryCategoryByTopicMethod = QueryGrpc.getQueryCategoryByTopicMethod) == null) {
          QueryGrpc.getQueryCategoryByTopicMethod = getQueryCategoryByTopicMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest, com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryCategoryByTopic"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryCategoryByTopic"))
              .build();
        }
      }
    }
    return getQueryCategoryByTopicMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse> getQueryCategoryPostsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryCategoryPosts",
      requestType = com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse> getQueryCategoryPostsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest, com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse> getQueryCategoryPostsMethod;
    if ((getQueryCategoryPostsMethod = QueryGrpc.getQueryCategoryPostsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryCategoryPostsMethod = QueryGrpc.getQueryCategoryPostsMethod) == null) {
          QueryGrpc.getQueryCategoryPostsMethod = getQueryCategoryPostsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest, com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryCategoryPosts"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryCategoryPosts"))
              .build();
        }
      }
    }
    return getQueryCategoryPostsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest,
      com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse> getQueryTrendingKeywordsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryTrendingKeywords",
      requestType = com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest,
      com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse> getQueryTrendingKeywordsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest, com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse> getQueryTrendingKeywordsMethod;
    if ((getQueryTrendingKeywordsMethod = QueryGrpc.getQueryTrendingKeywordsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryTrendingKeywordsMethod = QueryGrpc.getQueryTrendingKeywordsMethod) == null) {
          QueryGrpc.getQueryTrendingKeywordsMethod = getQueryTrendingKeywordsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest, com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryTrendingKeywords"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryTrendingKeywords"))
              .build();
        }
      }
    }
    return getQueryTrendingKeywordsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest,
      com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse> getQueryTrendingTopicsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryTrendingTopics",
      requestType = com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest,
      com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse> getQueryTrendingTopicsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest, com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse> getQueryTrendingTopicsMethod;
    if ((getQueryTrendingTopicsMethod = QueryGrpc.getQueryTrendingTopicsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryTrendingTopicsMethod = QueryGrpc.getQueryTrendingTopicsMethod) == null) {
          QueryGrpc.getQueryTrendingTopicsMethod = getQueryTrendingTopicsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest, com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryTrendingTopics"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryTrendingTopics"))
              .build();
        }
      }
    }
    return getQueryTrendingTopicsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse> getQueryFollowingPostsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryFollowingPosts",
      requestType = com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest,
      com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse> getQueryFollowingPostsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest, com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse> getQueryFollowingPostsMethod;
    if ((getQueryFollowingPostsMethod = QueryGrpc.getQueryFollowingPostsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryFollowingPostsMethod = QueryGrpc.getQueryFollowingPostsMethod) == null) {
          QueryGrpc.getQueryFollowingPostsMethod = getQueryFollowingPostsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest, com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryFollowingPosts"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryFollowingPosts"))
              .build();
        }
      }
    }
    return getQueryFollowingPostsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest,
      com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse> getQueryFollowingTopicsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryFollowingTopics",
      requestType = com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest,
      com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse> getQueryFollowingTopicsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest, com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse> getQueryFollowingTopicsMethod;
    if ((getQueryFollowingTopicsMethod = QueryGrpc.getQueryFollowingTopicsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryFollowingTopicsMethod = QueryGrpc.getQueryFollowingTopicsMethod) == null) {
          QueryGrpc.getQueryFollowingTopicsMethod = getQueryFollowingTopicsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest, com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryFollowingTopics"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryFollowingTopics"))
              .build();
        }
      }
    }
    return getQueryFollowingTopicsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest,
      com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse> getQueryIsFollowingTopicMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryIsFollowingTopic",
      requestType = com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest,
      com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse> getQueryIsFollowingTopicMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest, com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse> getQueryIsFollowingTopicMethod;
    if ((getQueryIsFollowingTopicMethod = QueryGrpc.getQueryIsFollowingTopicMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryIsFollowingTopicMethod = QueryGrpc.getQueryIsFollowingTopicMethod) == null) {
          QueryGrpc.getQueryIsFollowingTopicMethod = getQueryIsFollowingTopicMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest, com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryIsFollowingTopic"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryIsFollowingTopic"))
              .build();
        }
      }
    }
    return getQueryIsFollowingTopicMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest,
      com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse> getQueryUncategorizedTopicsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryUncategorizedTopics",
      requestType = com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest,
      com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse> getQueryUncategorizedTopicsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest, com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse> getQueryUncategorizedTopicsMethod;
    if ((getQueryUncategorizedTopicsMethod = QueryGrpc.getQueryUncategorizedTopicsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryUncategorizedTopicsMethod = QueryGrpc.getQueryUncategorizedTopicsMethod) == null) {
          QueryGrpc.getQueryUncategorizedTopicsMethod = getQueryUncategorizedTopicsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest, com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryUncategorizedTopics"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryUncategorizedTopics"))
              .build();
        }
      }
    }
    return getQueryUncategorizedTopicsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest,
      com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse> getQueryVoteOptionMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryVoteOption",
      requestType = com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest,
      com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse> getQueryVoteOptionMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest, com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse> getQueryVoteOptionMethod;
    if ((getQueryVoteOptionMethod = QueryGrpc.getQueryVoteOptionMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryVoteOptionMethod = QueryGrpc.getQueryVoteOptionMethod) == null) {
          QueryGrpc.getQueryVoteOptionMethod = getQueryVoteOptionMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest, com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryVoteOption"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryVoteOption"))
              .build();
        }
      }
    }
    return getQueryVoteOptionMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest,
      com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse> getQueryTopicImageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryTopicImage",
      requestType = com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest,
      com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse> getQueryTopicImageMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest, com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse> getQueryTopicImageMethod;
    if ((getQueryTopicImageMethod = QueryGrpc.getQueryTopicImageMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryTopicImageMethod = QueryGrpc.getQueryTopicImageMethod) == null) {
          QueryGrpc.getQueryTopicImageMethod = getQueryTopicImageMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest, com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryTopicImage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryTopicImage"))
              .build();
        }
      }
    }
    return getQueryTopicImageMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest,
      com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse> getQueryPaidPostImageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryPaidPostImage",
      requestType = com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest.class,
      responseType = com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest,
      com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse> getQueryPaidPostImageMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest, com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse> getQueryPaidPostImageMethod;
    if ((getQueryPaidPostImageMethod = QueryGrpc.getQueryPaidPostImageMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryPaidPostImageMethod = QueryGrpc.getQueryPaidPostImageMethod) == null) {
          QueryGrpc.getQueryPaidPostImageMethod = getQueryPaidPostImageMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest, com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryPaidPostImage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryPaidPostImage"))
              .build();
        }
      }
    }
    return getQueryPaidPostImageMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static QueryStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<QueryStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<QueryStub>() {
        @java.lang.Override
        public QueryStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new QueryStub(channel, callOptions);
        }
      };
    return QueryStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static QueryBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<QueryBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<QueryBlockingStub>() {
        @java.lang.Override
        public QueryBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new QueryBlockingStub(channel, callOptions);
        }
      };
    return QueryBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static QueryFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<QueryFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<QueryFutureStub>() {
        @java.lang.Override
        public QueryFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new QueryFutureStub(channel, callOptions);
        }
      };
    return QueryFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * <pre>
     * ResolveName allows a user to resolve the name of an account.
     * </pre>
     */
    default void resolveName(com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getResolveNameMethod(), responseObserver);
    }

    /**
     */
    default void queryHomePosts(com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryHomePostsMethod(), responseObserver);
    }

    /**
     */
    default void queryFirstPageHomePosts(com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryFirstPageHomePostsMethod(), responseObserver);
    }

    /**
     */
    default void searchTopics(com.Tlock.io.post.PostQueryProto.SearchTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.SearchTopicsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSearchTopicsMethod(), responseObserver);
    }

    /**
     */
    default void queryTopicPosts(com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryTopicPostsMethod(), responseObserver);
    }

    /**
     * <pre>
     * QueryPost allows querying a specific post by its ID
     * </pre>
     */
    default void queryPost(com.Tlock.io.post.PostQueryProto.QueryPostRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryPostResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryPostMethod(), responseObserver);
    }

    /**
     */
    default void queryUserCreatedPosts(com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryUserCreatedPostsMethod(), responseObserver);
    }

    /**
     */
    default void likesIMade(com.Tlock.io.post.PostQueryProto.LikesIMadeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.LikesIMadeResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLikesIMadeMethod(), responseObserver);
    }

    /**
     */
    default void savesIMade(com.Tlock.io.post.PostQueryProto.SavesIMadeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.SavesIMadeResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSavesIMadeMethod(), responseObserver);
    }

    /**
     */
    default void likesReceived(com.Tlock.io.post.PostQueryProto.LikesReceivedRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.LikesReceivedResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getLikesReceivedMethod(), responseObserver);
    }

    /**
     */
    default void queryComments(com.Tlock.io.post.PostQueryProto.QueryCommentsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCommentsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCommentsMethod(), responseObserver);
    }

    /**
     */
    default void queryCommentsReceived(com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCommentsReceivedMethod(), responseObserver);
    }

    /**
     */
    default void queryActivitiesReceived(com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryActivitiesReceivedMethod(), responseObserver);
    }

    /**
     */
    default void queryCategories(com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCategoriesMethod(), responseObserver);
    }

    /**
     */
    default void queryTopicsByCategory(com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryTopicsByCategoryMethod(), responseObserver);
    }

    /**
     */
    default void queryCategoryByTopic(com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCategoryByTopicMethod(), responseObserver);
    }

    /**
     */
    default void queryCategoryPosts(com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCategoryPostsMethod(), responseObserver);
    }

    /**
     */
    default void queryTrendingKeywords(com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryTrendingKeywordsMethod(), responseObserver);
    }

    /**
     */
    default void queryTrendingTopics(com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryTrendingTopicsMethod(), responseObserver);
    }

    /**
     */
    default void queryFollowingPosts(com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryFollowingPostsMethod(), responseObserver);
    }

    /**
     */
    default void queryFollowingTopics(com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryFollowingTopicsMethod(), responseObserver);
    }

    /**
     */
    default void queryIsFollowingTopic(com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryIsFollowingTopicMethod(), responseObserver);
    }

    /**
     */
    default void queryUncategorizedTopics(com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryUncategorizedTopicsMethod(), responseObserver);
    }

    /**
     */
    default void queryVoteOption(com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryVoteOptionMethod(), responseObserver);
    }

    /**
     */
    default void queryTopicImage(com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryTopicImageMethod(), responseObserver);
    }

    /**
     */
    default void queryPaidPostImage(com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryPaidPostImageMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service Query.
   */
  public static abstract class QueryImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return QueryGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service Query.
   */
  public static final class QueryStub
      extends io.grpc.stub.AbstractAsyncStub<QueryStub> {
    private QueryStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected QueryStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new QueryStub(channel, callOptions);
    }

    /**
     * <pre>
     * ResolveName allows a user to resolve the name of an account.
     * </pre>
     */
    public void resolveName(com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getResolveNameMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryHomePosts(com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryHomePostsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryFirstPageHomePosts(com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryFirstPageHomePostsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void searchTopics(com.Tlock.io.post.PostQueryProto.SearchTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.SearchTopicsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSearchTopicsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryTopicPosts(com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryTopicPostsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * QueryPost allows querying a specific post by its ID
     * </pre>
     */
    public void queryPost(com.Tlock.io.post.PostQueryProto.QueryPostRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryPostResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryPostMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryUserCreatedPosts(com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryUserCreatedPostsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void likesIMade(com.Tlock.io.post.PostQueryProto.LikesIMadeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.LikesIMadeResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLikesIMadeMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void savesIMade(com.Tlock.io.post.PostQueryProto.SavesIMadeRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.SavesIMadeResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSavesIMadeMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void likesReceived(com.Tlock.io.post.PostQueryProto.LikesReceivedRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.LikesReceivedResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getLikesReceivedMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryComments(com.Tlock.io.post.PostQueryProto.QueryCommentsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCommentsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCommentsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryCommentsReceived(com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCommentsReceivedMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryActivitiesReceived(com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryActivitiesReceivedMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryCategories(com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCategoriesMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryTopicsByCategory(com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryTopicsByCategoryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryCategoryByTopic(com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCategoryByTopicMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryCategoryPosts(com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCategoryPostsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryTrendingKeywords(com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryTrendingKeywordsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryTrendingTopics(com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryTrendingTopicsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryFollowingPosts(com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryFollowingPostsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryFollowingTopics(com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryFollowingTopicsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryIsFollowingTopic(com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryIsFollowingTopicMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryUncategorizedTopics(com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryUncategorizedTopicsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryVoteOption(com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryVoteOptionMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryTopicImage(com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryTopicImageMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryPaidPostImage(com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryPaidPostImageMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service Query.
   */
  public static final class QueryBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<QueryBlockingStub> {
    private QueryBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected QueryBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new QueryBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * ResolveName allows a user to resolve the name of an account.
     * </pre>
     */
    public com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse resolveName(com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getResolveNameMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse queryHomePosts(com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryHomePostsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse queryFirstPageHomePosts(com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryFirstPageHomePostsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.SearchTopicsResponse searchTopics(com.Tlock.io.post.PostQueryProto.SearchTopicsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSearchTopicsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse queryTopicPosts(com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryTopicPostsMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * QueryPost allows querying a specific post by its ID
     * </pre>
     */
    public com.Tlock.io.post.PostQueryProto.QueryPostResponse queryPost(com.Tlock.io.post.PostQueryProto.QueryPostRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryPostMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse queryUserCreatedPosts(com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryUserCreatedPostsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.LikesIMadeResponse likesIMade(com.Tlock.io.post.PostQueryProto.LikesIMadeRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLikesIMadeMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.SavesIMadeResponse savesIMade(com.Tlock.io.post.PostQueryProto.SavesIMadeRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSavesIMadeMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.LikesReceivedResponse likesReceived(com.Tlock.io.post.PostQueryProto.LikesReceivedRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getLikesReceivedMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryCommentsResponse queryComments(com.Tlock.io.post.PostQueryProto.QueryCommentsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCommentsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse queryCommentsReceived(com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCommentsReceivedMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse queryActivitiesReceived(com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryActivitiesReceivedMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse queryCategories(com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCategoriesMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse queryTopicsByCategory(com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryTopicsByCategoryMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse queryCategoryByTopic(com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCategoryByTopicMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse queryCategoryPosts(com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCategoryPostsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse queryTrendingKeywords(com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryTrendingKeywordsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse queryTrendingTopics(com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryTrendingTopicsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse queryFollowingPosts(com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryFollowingPostsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse queryFollowingTopics(com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryFollowingTopicsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse queryIsFollowingTopic(com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryIsFollowingTopicMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse queryUncategorizedTopics(com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryUncategorizedTopicsMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse queryVoteOption(com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryVoteOptionMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse queryTopicImage(com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryTopicImageMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse queryPaidPostImage(com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryPaidPostImageMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service Query.
   */
  public static final class QueryFutureStub
      extends io.grpc.stub.AbstractFutureStub<QueryFutureStub> {
    private QueryFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected QueryFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new QueryFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * ResolveName allows a user to resolve the name of an account.
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse> resolveName(
        com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getResolveNameMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse> queryHomePosts(
        com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryHomePostsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse> queryFirstPageHomePosts(
        com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryFirstPageHomePostsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.SearchTopicsResponse> searchTopics(
        com.Tlock.io.post.PostQueryProto.SearchTopicsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSearchTopicsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse> queryTopicPosts(
        com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryTopicPostsMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * QueryPost allows querying a specific post by its ID
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryPostResponse> queryPost(
        com.Tlock.io.post.PostQueryProto.QueryPostRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryPostMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse> queryUserCreatedPosts(
        com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryUserCreatedPostsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.LikesIMadeResponse> likesIMade(
        com.Tlock.io.post.PostQueryProto.LikesIMadeRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLikesIMadeMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.SavesIMadeResponse> savesIMade(
        com.Tlock.io.post.PostQueryProto.SavesIMadeRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSavesIMadeMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.LikesReceivedResponse> likesReceived(
        com.Tlock.io.post.PostQueryProto.LikesReceivedRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getLikesReceivedMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryCommentsResponse> queryComments(
        com.Tlock.io.post.PostQueryProto.QueryCommentsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCommentsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse> queryCommentsReceived(
        com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCommentsReceivedMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse> queryActivitiesReceived(
        com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryActivitiesReceivedMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse> queryCategories(
        com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCategoriesMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse> queryTopicsByCategory(
        com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryTopicsByCategoryMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse> queryCategoryByTopic(
        com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCategoryByTopicMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse> queryCategoryPosts(
        com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCategoryPostsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse> queryTrendingKeywords(
        com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryTrendingKeywordsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse> queryTrendingTopics(
        com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryTrendingTopicsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse> queryFollowingPosts(
        com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryFollowingPostsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse> queryFollowingTopics(
        com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryFollowingTopicsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse> queryIsFollowingTopic(
        com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryIsFollowingTopicMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse> queryUncategorizedTopics(
        com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryUncategorizedTopicsMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse> queryVoteOption(
        com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryVoteOptionMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse> queryTopicImage(
        com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryTopicImageMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse> queryPaidPostImage(
        com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryPaidPostImageMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_RESOLVE_NAME = 0;
  private static final int METHODID_QUERY_HOME_POSTS = 1;
  private static final int METHODID_QUERY_FIRST_PAGE_HOME_POSTS = 2;
  private static final int METHODID_SEARCH_TOPICS = 3;
  private static final int METHODID_QUERY_TOPIC_POSTS = 4;
  private static final int METHODID_QUERY_POST = 5;
  private static final int METHODID_QUERY_USER_CREATED_POSTS = 6;
  private static final int METHODID_LIKES_IMADE = 7;
  private static final int METHODID_SAVES_IMADE = 8;
  private static final int METHODID_LIKES_RECEIVED = 9;
  private static final int METHODID_QUERY_COMMENTS = 10;
  private static final int METHODID_QUERY_COMMENTS_RECEIVED = 11;
  private static final int METHODID_QUERY_ACTIVITIES_RECEIVED = 12;
  private static final int METHODID_QUERY_CATEGORIES = 13;
  private static final int METHODID_QUERY_TOPICS_BY_CATEGORY = 14;
  private static final int METHODID_QUERY_CATEGORY_BY_TOPIC = 15;
  private static final int METHODID_QUERY_CATEGORY_POSTS = 16;
  private static final int METHODID_QUERY_TRENDING_KEYWORDS = 17;
  private static final int METHODID_QUERY_TRENDING_TOPICS = 18;
  private static final int METHODID_QUERY_FOLLOWING_POSTS = 19;
  private static final int METHODID_QUERY_FOLLOWING_TOPICS = 20;
  private static final int METHODID_QUERY_IS_FOLLOWING_TOPIC = 21;
  private static final int METHODID_QUERY_UNCATEGORIZED_TOPICS = 22;
  private static final int METHODID_QUERY_VOTE_OPTION = 23;
  private static final int METHODID_QUERY_TOPIC_IMAGE = 24;
  private static final int METHODID_QUERY_PAID_POST_IMAGE = 25;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_RESOLVE_NAME:
          serviceImpl.resolveName((com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse>) responseObserver);
          break;
        case METHODID_QUERY_HOME_POSTS:
          serviceImpl.queryHomePosts((com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse>) responseObserver);
          break;
        case METHODID_QUERY_FIRST_PAGE_HOME_POSTS:
          serviceImpl.queryFirstPageHomePosts((com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse>) responseObserver);
          break;
        case METHODID_SEARCH_TOPICS:
          serviceImpl.searchTopics((com.Tlock.io.post.PostQueryProto.SearchTopicsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.SearchTopicsResponse>) responseObserver);
          break;
        case METHODID_QUERY_TOPIC_POSTS:
          serviceImpl.queryTopicPosts((com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse>) responseObserver);
          break;
        case METHODID_QUERY_POST:
          serviceImpl.queryPost((com.Tlock.io.post.PostQueryProto.QueryPostRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryPostResponse>) responseObserver);
          break;
        case METHODID_QUERY_USER_CREATED_POSTS:
          serviceImpl.queryUserCreatedPosts((com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse>) responseObserver);
          break;
        case METHODID_LIKES_IMADE:
          serviceImpl.likesIMade((com.Tlock.io.post.PostQueryProto.LikesIMadeRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.LikesIMadeResponse>) responseObserver);
          break;
        case METHODID_SAVES_IMADE:
          serviceImpl.savesIMade((com.Tlock.io.post.PostQueryProto.SavesIMadeRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.SavesIMadeResponse>) responseObserver);
          break;
        case METHODID_LIKES_RECEIVED:
          serviceImpl.likesReceived((com.Tlock.io.post.PostQueryProto.LikesReceivedRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.LikesReceivedResponse>) responseObserver);
          break;
        case METHODID_QUERY_COMMENTS:
          serviceImpl.queryComments((com.Tlock.io.post.PostQueryProto.QueryCommentsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCommentsResponse>) responseObserver);
          break;
        case METHODID_QUERY_COMMENTS_RECEIVED:
          serviceImpl.queryCommentsReceived((com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse>) responseObserver);
          break;
        case METHODID_QUERY_ACTIVITIES_RECEIVED:
          serviceImpl.queryActivitiesReceived((com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse>) responseObserver);
          break;
        case METHODID_QUERY_CATEGORIES:
          serviceImpl.queryCategories((com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse>) responseObserver);
          break;
        case METHODID_QUERY_TOPICS_BY_CATEGORY:
          serviceImpl.queryTopicsByCategory((com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse>) responseObserver);
          break;
        case METHODID_QUERY_CATEGORY_BY_TOPIC:
          serviceImpl.queryCategoryByTopic((com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse>) responseObserver);
          break;
        case METHODID_QUERY_CATEGORY_POSTS:
          serviceImpl.queryCategoryPosts((com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse>) responseObserver);
          break;
        case METHODID_QUERY_TRENDING_KEYWORDS:
          serviceImpl.queryTrendingKeywords((com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse>) responseObserver);
          break;
        case METHODID_QUERY_TRENDING_TOPICS:
          serviceImpl.queryTrendingTopics((com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse>) responseObserver);
          break;
        case METHODID_QUERY_FOLLOWING_POSTS:
          serviceImpl.queryFollowingPosts((com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse>) responseObserver);
          break;
        case METHODID_QUERY_FOLLOWING_TOPICS:
          serviceImpl.queryFollowingTopics((com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse>) responseObserver);
          break;
        case METHODID_QUERY_IS_FOLLOWING_TOPIC:
          serviceImpl.queryIsFollowingTopic((com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse>) responseObserver);
          break;
        case METHODID_QUERY_UNCATEGORIZED_TOPICS:
          serviceImpl.queryUncategorizedTopics((com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse>) responseObserver);
          break;
        case METHODID_QUERY_VOTE_OPTION:
          serviceImpl.queryVoteOption((com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse>) responseObserver);
          break;
        case METHODID_QUERY_TOPIC_IMAGE:
          serviceImpl.queryTopicImage((com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse>) responseObserver);
          break;
        case METHODID_QUERY_PAID_POST_IMAGE:
          serviceImpl.queryPaidPostImage((com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getResolveNameMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryResolveNameRequest,
              com.Tlock.io.post.PostQueryProto.QueryResolveNameResponse>(
                service, METHODID_RESOLVE_NAME)))
        .addMethod(
          getQueryHomePostsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryHomePostsRequest,
              com.Tlock.io.post.PostQueryProto.QueryHomePostsResponse>(
                service, METHODID_QUERY_HOME_POSTS)))
        .addMethod(
          getQueryFirstPageHomePostsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsRequest,
              com.Tlock.io.post.PostQueryProto.QueryFirstPageHomePostsResponse>(
                service, METHODID_QUERY_FIRST_PAGE_HOME_POSTS)))
        .addMethod(
          getSearchTopicsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.SearchTopicsRequest,
              com.Tlock.io.post.PostQueryProto.SearchTopicsResponse>(
                service, METHODID_SEARCH_TOPICS)))
        .addMethod(
          getQueryTopicPostsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryTopicPostsRequest,
              com.Tlock.io.post.PostQueryProto.QueryTopicPostsResponse>(
                service, METHODID_QUERY_TOPIC_POSTS)))
        .addMethod(
          getQueryPostMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryPostRequest,
              com.Tlock.io.post.PostQueryProto.QueryPostResponse>(
                service, METHODID_QUERY_POST)))
        .addMethod(
          getQueryUserCreatedPostsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsRequest,
              com.Tlock.io.post.PostQueryProto.QueryUserCreatedPostsResponse>(
                service, METHODID_QUERY_USER_CREATED_POSTS)))
        .addMethod(
          getLikesIMadeMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.LikesIMadeRequest,
              com.Tlock.io.post.PostQueryProto.LikesIMadeResponse>(
                service, METHODID_LIKES_IMADE)))
        .addMethod(
          getSavesIMadeMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.SavesIMadeRequest,
              com.Tlock.io.post.PostQueryProto.SavesIMadeResponse>(
                service, METHODID_SAVES_IMADE)))
        .addMethod(
          getLikesReceivedMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.LikesReceivedRequest,
              com.Tlock.io.post.PostQueryProto.LikesReceivedResponse>(
                service, METHODID_LIKES_RECEIVED)))
        .addMethod(
          getQueryCommentsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryCommentsRequest,
              com.Tlock.io.post.PostQueryProto.QueryCommentsResponse>(
                service, METHODID_QUERY_COMMENTS)))
        .addMethod(
          getQueryCommentsReceivedMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedRequest,
              com.Tlock.io.post.PostQueryProto.QueryCommentsReceivedResponse>(
                service, METHODID_QUERY_COMMENTS_RECEIVED)))
        .addMethod(
          getQueryActivitiesReceivedMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedRequest,
              com.Tlock.io.post.PostQueryProto.QueryActivitiesReceivedResponse>(
                service, METHODID_QUERY_ACTIVITIES_RECEIVED)))
        .addMethod(
          getQueryCategoriesMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryCategoriesRequest,
              com.Tlock.io.post.PostQueryProto.QueryCategoriesResponse>(
                service, METHODID_QUERY_CATEGORIES)))
        .addMethod(
          getQueryTopicsByCategoryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryRequest,
              com.Tlock.io.post.PostQueryProto.QueryTopicsByCategoryResponse>(
                service, METHODID_QUERY_TOPICS_BY_CATEGORY)))
        .addMethod(
          getQueryCategoryByTopicMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicRequest,
              com.Tlock.io.post.PostQueryProto.QueryCategoryByTopicResponse>(
                service, METHODID_QUERY_CATEGORY_BY_TOPIC)))
        .addMethod(
          getQueryCategoryPostsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryCategoryPostsRequest,
              com.Tlock.io.post.PostQueryProto.QueryCategoryPostsResponse>(
                service, METHODID_QUERY_CATEGORY_POSTS)))
        .addMethod(
          getQueryTrendingKeywordsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsRequest,
              com.Tlock.io.post.PostQueryProto.QueryTrendingKeywordsResponse>(
                service, METHODID_QUERY_TRENDING_KEYWORDS)))
        .addMethod(
          getQueryTrendingTopicsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsRequest,
              com.Tlock.io.post.PostQueryProto.QueryTrendingTopicsResponse>(
                service, METHODID_QUERY_TRENDING_TOPICS)))
        .addMethod(
          getQueryFollowingPostsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryFollowingPostsRequest,
              com.Tlock.io.post.PostQueryProto.QueryFollowingPostsResponse>(
                service, METHODID_QUERY_FOLLOWING_POSTS)))
        .addMethod(
          getQueryFollowingTopicsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsRequest,
              com.Tlock.io.post.PostQueryProto.QueryFollowingTopicsResponse>(
                service, METHODID_QUERY_FOLLOWING_TOPICS)))
        .addMethod(
          getQueryIsFollowingTopicMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicRequest,
              com.Tlock.io.post.PostQueryProto.QueryIsFollowingTopicResponse>(
                service, METHODID_QUERY_IS_FOLLOWING_TOPIC)))
        .addMethod(
          getQueryUncategorizedTopicsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsRequest,
              com.Tlock.io.post.PostQueryProto.QueryUncategorizedTopicsResponse>(
                service, METHODID_QUERY_UNCATEGORIZED_TOPICS)))
        .addMethod(
          getQueryVoteOptionMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryVoteOptionRequest,
              com.Tlock.io.post.PostQueryProto.QueryVoteOptionResponse>(
                service, METHODID_QUERY_VOTE_OPTION)))
        .addMethod(
          getQueryTopicImageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryTopicImageRequest,
              com.Tlock.io.post.PostQueryProto.QueryTopicImageResponse>(
                service, METHODID_QUERY_TOPIC_IMAGE)))
        .addMethod(
          getQueryPaidPostImageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.post.PostQueryProto.QueryPaidPostImageRequest,
              com.Tlock.io.post.PostQueryProto.QueryPaidPostImageResponse>(
                service, METHODID_QUERY_PAID_POST_IMAGE)))
        .build();
  }

  private static abstract class QueryBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    QueryBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.Tlock.io.post.PostQueryProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Query");
    }
  }

  private static final class QueryFileDescriptorSupplier
      extends QueryBaseDescriptorSupplier {
    QueryFileDescriptorSupplier() {}
  }

  private static final class QueryMethodDescriptorSupplier
      extends QueryBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    QueryMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (QueryGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new QueryFileDescriptorSupplier())
              .addMethod(getResolveNameMethod())
              .addMethod(getQueryHomePostsMethod())
              .addMethod(getQueryFirstPageHomePostsMethod())
              .addMethod(getSearchTopicsMethod())
              .addMethod(getQueryTopicPostsMethod())
              .addMethod(getQueryPostMethod())
              .addMethod(getQueryUserCreatedPostsMethod())
              .addMethod(getLikesIMadeMethod())
              .addMethod(getSavesIMadeMethod())
              .addMethod(getLikesReceivedMethod())
              .addMethod(getQueryCommentsMethod())
              .addMethod(getQueryCommentsReceivedMethod())
              .addMethod(getQueryActivitiesReceivedMethod())
              .addMethod(getQueryCategoriesMethod())
              .addMethod(getQueryTopicsByCategoryMethod())
              .addMethod(getQueryCategoryByTopicMethod())
              .addMethod(getQueryCategoryPostsMethod())
              .addMethod(getQueryTrendingKeywordsMethod())
              .addMethod(getQueryTrendingTopicsMethod())
              .addMethod(getQueryFollowingPostsMethod())
              .addMethod(getQueryFollowingTopicsMethod())
              .addMethod(getQueryIsFollowingTopicMethod())
              .addMethod(getQueryUncategorizedTopicsMethod())
              .addMethod(getQueryVoteOptionMethod())
              .addMethod(getQueryTopicImageMethod())
              .addMethod(getQueryPaidPostImageMethod())
              .build();
        }
      }
    }
    return result;
  }
}
