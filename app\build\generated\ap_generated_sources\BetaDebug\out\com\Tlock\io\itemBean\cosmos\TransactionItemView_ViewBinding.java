// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TransactionItemView_ViewBinding implements Unbinder {
  private TransactionItemView target;

  @UiThread
  public TransactionItemView_ViewBinding(TransactionItemView target) {
    this(target, target);
  }

  @UiThread
  public TransactionItemView_ViewBinding(TransactionItemView target, View source) {
    this.target = target;

    target.mTvMethod = Utils.findRequiredViewAsType(source, R.id.tv_method, "field 'mTvMethod'", TextView.class);
    target.mTvHash = Utils.findRequiredViewAsType(source, R.id.tv_hash, "field 'mTvHash'", TextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    target.mLlRoot = Utils.findRequiredViewAsType(source, R.id.ll_root, "field 'mLlRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TransactionItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvMethod = null;
    target.mTvHash = null;
    target.mTvTime = null;
    target.mLlRoot = null;
  }
}
