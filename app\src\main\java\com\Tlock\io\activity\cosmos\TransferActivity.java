package com.Tlock.io.activity.cosmos;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.core.os.CancellationSignal;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.Transfer;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.request.RequestOptions;
import com.yzq.zxinglibrary.android.CaptureActivity;
import com.yzq.zxinglibrary.common.Constant;

import butterknife.BindView;
import butterknife.OnClick;

public class TransferActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv1)
    ImageView mIv1;
    @BindView(R.id.tv1)
    TextView mTv1;
    @BindView(R.id.iv_scan)
    ImageView mIvScan;
    @BindView(R.id.ed_address)
    EditText mEdAddress;
    @BindView(R.id.tv2)
    TextView mTv2;
    @BindView(R.id.tv_balance)
    TextView mTvBalance;
    @BindView(R.id.ed_count)
    EditText mEdCount;
    @BindView(R.id.tv_all)
    TextView mTvAll;
    @BindView(R.id.btn_confirm)
    TextView mBtnConfirm;
    private ETHWallet current;
    private CancellationSignal cancellationSignal;
    public int REQUEST_CODE_SCAN = 1;
    private String baseBalance;
    private Transfer transfer = new Transfer();
    private int type = 0;

    /**
     * @param context
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, TransferActivity.class);
        context.startActivity(intent);
    }

    public static void start(Context context, int type) {
        Intent intent = new Intent(context, TransferActivity.class);
        intent.putExtra("type", type);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_transfer;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        Glide.with(getActivity()).load(R.mipmap.app_icon).apply(new RequestOptions()
                        .centerCrop()
                        .format(DecodeFormat.PREFER_RGB_565)
                        .dontTransform())
                .apply(RequestOptions.circleCropTransform().circleCrop())
                .into(mIv1);
        current = WalletDaoUtils.getCurrent();
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        type = getIntent().getIntExtra("type", 0);
    }


    @Override
    protected void loadData() {
        if (type == 2) {
            scanQRCode();
        }
        if (WalletDaoUtils.getCurrent() != null) {
            current = WalletDaoUtils.getCurrent();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    baseBalance = CosmosUtils.getBaseBalance(current.getAddress());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            String division = BigDecimalUtils.division(baseBalance, "1000000");
                            mTvBalance.setText("Available: " + BigDecimalUtils.saveDecimals(division, 2));
                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.iv_scan, R.id.tv_all, R.id.btn_confirm})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_scan:
                scanQRCode();
                break;
            case R.id.tv_all:
                mEdCount.setText(BigDecimalUtils.subtract(baseBalance, "100000"));
                break;
            case R.id.btn_confirm:
                // 指纹验证
                if (transfer()) {
                    startFingerprintAuth();
                }
                break;
        }
    }

    public void scanQRCode() {
        Intent intent = new Intent(this, CaptureActivity.class);
        startActivityForResult(intent, REQUEST_CODE_SCAN);
    }

    @SuppressLint("RestrictedApi")
    public void startFingerprintAuth() {
        FingerprintManagerCompat fingerprintManager = FingerprintManagerCompat.from(this);
        cancellationSignal = new CancellationSignal();

        fingerprintManager.authenticate(
                null, // 可选的CryptoObject（用于加密验证结果）
                0,
                cancellationSignal,
                new FingerprintManagerCompat.AuthenticationCallback() {
                    @Override
                    public void onAuthenticationError(int errMsgId, CharSequence errString) {
                        // 验证错误（如多次失败）
                        showToast("Validation error：" + errString);
                    }

                    @Override
                    public void onAuthenticationSucceeded(FingerprintManagerCompat.AuthenticationResult result) {
                        // 验证成功
                        TransferInfoActivity.start(getActivity(), 1, JsonUtils.objectToJson(transfer));
                        finish();
                    }

                    @Override
                    public void onAuthenticationFailed() {
                        // 单次验证失败
                        showToast("Fingerprint mismatch");
                    }
                },
                null
        );
    }

    private boolean transfer() {
        ETHWallet current = WalletDaoUtils.getCurrent();
        if (current == null) {
            ToastUtil.toastView("Please import wallet");
            return false;
        } else {
            transfer.setPayAddress(current.getAddress());
        }
        if (mEdCount.getText().toString().isEmpty()) {
            ToastUtil.toastView("The quantity cannot be empty");
            return false;
        } else {
            transfer.setCount(BigDecimalUtils.multiplication(mEdCount.getText().toString(), "1000000"));
        }
        if (mEdAddress.getText().toString().isEmpty()) {
            ToastUtil.toastView("The receiving address cannot be empty");
            return false;
        } else {
            transfer.setGetAddress(mEdAddress.getText().toString());
            return true;
        }

    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 101) {

        } else {
            //扫描返回
            if (requestCode == REQUEST_CODE_SCAN && resultCode == RESULT_OK) {
                if (data != null) {
                    String content = data.getStringExtra(Constant.CODED_CONTENT);
                    mEdAddress.setText(content);

                }
            }
        }
    }
}