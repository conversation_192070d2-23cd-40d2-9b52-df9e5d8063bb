// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.FontTextView;
import com.cy.tablayoutniubility.TabLayoutScroll;
import java.lang.IllegalStateException;
import java.lang.Override;

public class UserInfoActivity_ViewBinding implements Unbinder {
  private UserInfoActivity target;

  private View view7f090074;

  private View view7f090175;

  private View view7f090386;

  @UiThread
  public UserInfoActivity_ViewBinding(UserInfoActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public UserInfoActivity_ViewBinding(final UserInfoActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    view = Utils.findRequiredView(source, R.id.btn_updata, "field 'mBtnUpdata' and method 'onBindClick'");
    target.mBtnUpdata = Utils.castView(view, R.id.btn_updata, "field 'mBtnUpdata'", TextView.class);
    view7f090074 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mIvManger = Utils.findRequiredViewAsType(source, R.id.iv_manger, "field 'mIvManger'", ImageView.class);
    target.mTvName = Utils.findRequiredViewAsType(source, R.id.tv_name, "field 'mTvName'", TextView.class);
    target.mTvHandle = Utils.findRequiredViewAsType(source, R.id.tv_handle, "field 'mTvHandle'", TextView.class);
    target.mReviewCount = Utils.findRequiredViewAsType(source, R.id.review_count, "field 'mReviewCount'", TextView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    target.mFansCount = Utils.findRequiredViewAsType(source, R.id.fans_count, "field 'mFansCount'", TextView.class);
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv_2, "field 'mTv2'", TextView.class);
    target.mRlInfo = Utils.findRequiredViewAsType(source, R.id.rl_info, "field 'mRlInfo'", RelativeLayout.class);
    target.mTablayout = Utils.findRequiredViewAsType(source, R.id.tablayout, "field 'mTablayout'", TabLayoutScroll.class);
    target.mViewPager = Utils.findRequiredViewAsType(source, R.id.view_pager, "field 'mViewPager'", ViewPager2.class);
    target.mTvBio = Utils.findRequiredViewAsType(source, R.id.tv_Bio, "field 'mTvBio'", TextView.class);
    target.mLine2 = Utils.findRequiredView(source, R.id.line2, "field 'mLine2'");
    view = Utils.findRequiredView(source, R.id.iv_show_more, "field 'mIvShowMore' and method 'onBindClick'");
    target.mIvShowMore = Utils.castView(view, R.id.iv_show_more, "field 'mIvShowMore'", ImageView.class);
    view7f090175 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvLevel = Utils.findRequiredViewAsType(source, R.id.tv_level, "field 'mTvLevel'", FontTextView.class);
    target.mIvVip = Utils.findRequiredViewAsType(source, R.id.iv_vip, "field 'mIvVip'", ImageView.class);
    target.mIconCheck = Utils.findRequiredViewAsType(source, R.id.icon_check, "field 'mIconCheck'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.tv_wallet_address, "field 'mTvWalletAddress' and method 'onBindClick'");
    target.mTvWalletAddress = Utils.castView(view, R.id.tv_wallet_address, "field 'mTvWalletAddress'", TextView.class);
    view7f090386 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    UserInfoActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mBtnUpdata = null;
    target.mIvHeard = null;
    target.mIvManger = null;
    target.mTvName = null;
    target.mTvHandle = null;
    target.mReviewCount = null;
    target.mTv1 = null;
    target.mFansCount = null;
    target.mTv2 = null;
    target.mRlInfo = null;
    target.mTablayout = null;
    target.mViewPager = null;
    target.mTvBio = null;
    target.mLine2 = null;
    target.mIvShowMore = null;
    target.mTvLevel = null;
    target.mIvVip = null;
    target.mIconCheck = null;
    target.mTvWalletAddress = null;

    view7f090074.setOnClickListener(null);
    view7f090074 = null;
    view7f090175.setOnClickListener(null);
    view7f090175 = null;
    view7f090386.setOnClickListener(null);
    view7f090386 = null;
  }
}
