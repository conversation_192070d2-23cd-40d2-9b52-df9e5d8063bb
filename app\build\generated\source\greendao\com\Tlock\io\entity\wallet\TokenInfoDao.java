package com.Tlock.io.entity.wallet;

import java.util.List;
import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;
import org.greenrobot.greendao.query.Query;
import org.greenrobot.greendao.query.QueryBuilder;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TOKEN_INFO".
*/
public class TokenInfoDao extends AbstractDao<TokenInfo, Long> {

    public static final String TABLENAME = "TOKEN_INFO";

    /**
     * Properties of entity TokenInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property WalletId = new Property(1, Long.class, "walletId", false, "WALLET_ID");
        public final static Property Name = new Property(2, String.class, "name", false, "NAME");
        public final static Property Symbol = new Property(3, String.class, "symbol", false, "SYMBOL");
        public final static Property Decimals = new Property(4, String.class, "decimals", false, "DECIMALS");
        public final static Property Count = new Property(5, String.class, "count", false, "COUNT");
        public final static Property BalanceOf = new Property(6, String.class, "balanceOf", false, "BALANCE_OF");
        public final static Property Price = new Property(7, String.class, "price", false, "PRICE");
        public final static Property ImgUrl = new Property(8, String.class, "imgUrl", false, "IMG_URL");
        public final static Property Address = new Property(9, String.class, "address", false, "ADDRESS");
        public final static Property WalletAddress = new Property(10, String.class, "walletAddress", false, "WALLET_ADDRESS");
        public final static Property ChainID = new Property(11, int.class, "chainID", false, "CHAIN_ID");
    }

    private Query<TokenInfo> eTHWallet_TokenInfoListQuery;

    public TokenInfoDao(DaoConfig config) {
        super(config);
    }
    
    public TokenInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TOKEN_INFO\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"WALLET_ID\" INTEGER," + // 1: walletId
                "\"NAME\" TEXT," + // 2: name
                "\"SYMBOL\" TEXT," + // 3: symbol
                "\"DECIMALS\" TEXT," + // 4: decimals
                "\"COUNT\" TEXT," + // 5: count
                "\"BALANCE_OF\" TEXT," + // 6: balanceOf
                "\"PRICE\" TEXT," + // 7: price
                "\"IMG_URL\" TEXT," + // 8: imgUrl
                "\"ADDRESS\" TEXT," + // 9: address
                "\"WALLET_ADDRESS\" TEXT," + // 10: walletAddress
                "\"CHAIN_ID\" INTEGER NOT NULL );"); // 11: chainID
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TOKEN_INFO\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, TokenInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        Long walletId = entity.getWalletId();
        if (walletId != null) {
            stmt.bindLong(2, walletId);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(3, name);
        }
 
        String symbol = entity.getSymbol();
        if (symbol != null) {
            stmt.bindString(4, symbol);
        }
 
        String decimals = entity.getDecimals();
        if (decimals != null) {
            stmt.bindString(5, decimals);
        }
 
        String count = entity.getCount();
        if (count != null) {
            stmt.bindString(6, count);
        }
 
        String balanceOf = entity.getBalanceOf();
        if (balanceOf != null) {
            stmt.bindString(7, balanceOf);
        }
 
        String price = entity.getPrice();
        if (price != null) {
            stmt.bindString(8, price);
        }
 
        String imgUrl = entity.getImgUrl();
        if (imgUrl != null) {
            stmt.bindString(9, imgUrl);
        }
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(10, address);
        }
 
        String walletAddress = entity.getWalletAddress();
        if (walletAddress != null) {
            stmt.bindString(11, walletAddress);
        }
        stmt.bindLong(12, entity.getChainID());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, TokenInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        Long walletId = entity.getWalletId();
        if (walletId != null) {
            stmt.bindLong(2, walletId);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(3, name);
        }
 
        String symbol = entity.getSymbol();
        if (symbol != null) {
            stmt.bindString(4, symbol);
        }
 
        String decimals = entity.getDecimals();
        if (decimals != null) {
            stmt.bindString(5, decimals);
        }
 
        String count = entity.getCount();
        if (count != null) {
            stmt.bindString(6, count);
        }
 
        String balanceOf = entity.getBalanceOf();
        if (balanceOf != null) {
            stmt.bindString(7, balanceOf);
        }
 
        String price = entity.getPrice();
        if (price != null) {
            stmt.bindString(8, price);
        }
 
        String imgUrl = entity.getImgUrl();
        if (imgUrl != null) {
            stmt.bindString(9, imgUrl);
        }
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(10, address);
        }
 
        String walletAddress = entity.getWalletAddress();
        if (walletAddress != null) {
            stmt.bindString(11, walletAddress);
        }
        stmt.bindLong(12, entity.getChainID());
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public TokenInfo readEntity(Cursor cursor, int offset) {
        TokenInfo entity = new TokenInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getLong(offset + 1), // walletId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // name
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // symbol
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // decimals
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // count
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // balanceOf
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // price
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // imgUrl
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // address
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // walletAddress
            cursor.getInt(offset + 11) // chainID
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, TokenInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setWalletId(cursor.isNull(offset + 1) ? null : cursor.getLong(offset + 1));
        entity.setName(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSymbol(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDecimals(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setCount(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setBalanceOf(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setPrice(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setImgUrl(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setAddress(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setWalletAddress(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setChainID(cursor.getInt(offset + 11));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(TokenInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(TokenInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(TokenInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "tokenInfoList" to-many relationship of ETHWallet. */
    public List<TokenInfo> _queryETHWallet_TokenInfoList(Long walletId) {
        synchronized (this) {
            if (eTHWallet_TokenInfoListQuery == null) {
                QueryBuilder<TokenInfo> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.WalletId.eq(null));
                eTHWallet_TokenInfoListQuery = queryBuilder.build();
            }
        }
        Query<TokenInfo> query = eTHWallet_TokenInfoListQuery.forCurrentThread();
        query.setParameter(0, walletId);
        return query.list();
    }

}
