package com.Tlock.io.activity.wallet;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.widget.CustomNavBar;

import butterknife.BindView;
import butterknife.OnClick;

public class NewWalletActivity extends BaseActivity {

    @BindView(R.id.tv_cancel)
    TextView mTvCancel;
    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_logo)
    ImageView mIvLogo;
    @BindView(R.id.tv1)
    TextView mTv1;
    @BindView(R.id.tv_create)
    TextView mTvCreate;
    @BindView(R.id.tv_input)
    TextView mTvInput;


    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, NewWalletActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected int getContentViewId() {
        return R.layout.activity_new_wallet;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {

    }

    @OnClick({R.id.tv_create, R.id.tv_input, R.id.tv_cancel})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_create:
                //创建钱包
                CreateWalletActivity.start(getActivity());
                finish();
                break;
            case R.id.tv_input:
                //导入钱包
                InputWalletActivity.start(getActivity());
                finish();
                break;
            case R.id.tv_cancel:
                finish();
                break;
        }
    }
}