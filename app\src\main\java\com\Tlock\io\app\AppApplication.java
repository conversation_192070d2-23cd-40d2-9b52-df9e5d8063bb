package com.Tlock.io.app;

import static com.Tlock.io.entity.LanguageType.LANGUAGE_EN;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.database.CursorWindow;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.Tlock.io.callback.AdapterFullScreenCallback;

import com.Tlock.io.entity.wallet.DaoMaster;
import com.Tlock.io.entity.wallet.DaoSession;
import com.Tlock.io.netchange.NetWorkMonitorManager;
import com.Tlock.io.receiver.NetworkChangeReceiver;
import com.Tlock.io.utils.CrashHandler;
import com.Tlock.io.utils.LanguageUtils;
import com.Tlock.io.utils.NetworkUtils;
import com.Tlock.io.utils.db.MyOpenHelper;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.connection.FileDownloadUrlConnection;
import com.sl.utakephoto.manager.UTakePhoto;
import com.zy.devicelibrary.UtilsApp;

import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.security.Security;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


import org.bouncycastle.jce.provider.BouncyCastleProvider;


/**
 * 本类的主要功能是 :  application
 */
public class AppApplication extends Application {
    private static AppApplication instance;
    private CrashHandler mCrashHandler;
    public static String currentUserNick = "";
    private static final String TAG = "AppApplication";
    private boolean isFirst = true;
    public String dappURL = "";
    public String UM_DEVICE = "Unknown";
    public String UM_UUID = "Unknown";
    private DaoSession daoSession;
    private static final int count = Runtime.getRuntime().availableProcessors() * 3 + 2;
    public static ThreadPoolExecutor threadPoolExecutor;
    public int foregroundCount = 0;
    public int basePosition = 0;
    public int freePosition = 0;
    public String foregroundName = "";
//    public ArrayList<Activity> activitys = new ArrayList<>();
private static WeakReference<Activity> currentActivityRef;

    {
        System.loadLibrary("TrustWalletCore");
        Security.addProvider(new BouncyCastleProvider());
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void onCreate() {
        LanguageUtils.getInstance().updateLanguage(this,LANGUAGE_EN);
        registerActivityLifecycleCallbacks(new AdapterFullScreenCallback().addAdapterActivity("com.kwad.sdk.api.proxy.app.KsRewardVideoActivity"));
        UtilsApp.init(this);
//        initX5();
        //扫描
//        ZXingLibrary.initDisplayOpinion(this);
        // 下载
        initDownloader();
        super.onCreate();
        initDB();
        initThread();
//        initUM();

        //初始化 LitePal数据库
        instance = this;

        closeAndroidPDialog();

        //初始化网络变化工具
        NetWorkMonitorManager.getInstance().init(this);

        //注册监听网络变化
        NetworkChangeReceiver.register(this);
        //改变数据库CursorWindow大小,默认是2M
        try {
            Field field = CursorWindow.class.getDeclaredField("sCursorWindowSize");
            field.setAccessible(true);
            field.set(null, 20 * 1024 * 1024); //the 102400 is the new size added
        } catch (Exception e) {
//            e.printStackTrace();
        }
        //初始化网络状态
        NetworkUtils.getNetworkState(this);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                SubmitDailyUtil.init(this);
            }
        } catch (Exception e) {
        }
//        UTakePhoto.init(this,this);


        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {

            }

            @Override
            public void onActivityStarted(Activity activity) {
                currentActivityRef = new WeakReference<>(activity); // 弱引用避免内存泄漏
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {

            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                if (currentActivityRef != null && currentActivityRef.get() == activity) {
                    currentActivityRef.clear();
                }
            }
        });
    }

    public static Activity getCurrentActivity() {
        return currentActivityRef != null ? currentActivityRef.get() : null;
    }



    /**
     * 线程池  cup最大+2
     */
    private void initThread() {
        threadPoolExecutor = new ThreadPoolExecutor(count, count + 2, 1, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(100));
    }

    public ThreadPoolExecutor getThreadPoolExecutor() {
        boolean shutdown = threadPoolExecutor.isShutdown();
        if (shutdown) {
            initThread();
        }
        return threadPoolExecutor;
    }

    /**
     * 创建数据库
     */
    private void initDB() {
        //创建数据库表
        MyOpenHelper wallet = new MyOpenHelper(this, "wallet.db", null);
        wallet.getWritableDatabase().disableWriteAheadLogging();
        DaoMaster daoMaster = new DaoMaster(wallet.getWritableDatabase());
        daoSession = daoMaster.newSession();
    }

    public DaoSession getDaoSession() {
        return daoSession;
    }


//    private void initUM() {
//        PushConstants.CHANNEL = getChannel();
//        Log.e(TAG, "initUM: " + PushConstants.CHANNEL);
//        UMConfigure.init(this, UM_APPKEY, PushConstants.CHANNEL, UMConfigure.DEVICE_TYPE_PHONE, "");
//        UMConfigure.setLogEnabled(true);
//        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.AUTO);
//
//    }


//    /**
//     * 初始化X5浏览器内核
//     */
//    private void initX5() {
//
//        QbSdk.setDownloadWithoutWifi(true);
//        QbSdk.PreInitCallback cb = new QbSdk.PreInitCallback() {
//
//            @Override
//            public void onViewInitFinished(boolean arg0) {
//                //x5內核初始化完成的回调，为true表示x5内核加载成功，否则表示x5内核加载失败，会自动切换到系统内核。
//                Log.d("app", " onViewInitFinished is " + arg0);
//            }
//
//            @Override
//            public void onCoreInitFinished() {
//            }
//        };
//        //x5内核初始化接口
//        QbSdk.initX5Environment(getApplicationContext(), cb);
//        HashMap map = new HashMap();
//        map.put(TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER, true);
//        map.put(TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE, true);
//        QbSdk.initTbsSettings(map);
//    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
    }

    public static AppApplication getInstance() {
        return instance;
    }

    /**
     * 禁止androidP上一直弹detected problems with api
     */
    private void closeAndroidPDialog() {
        try {
            Class aClass = Class.forName("android.content.pm.PackageParser$Package");
            Constructor declaredConstructor = aClass.getDeclaredConstructor(String.class);
            declaredConstructor.setAccessible(true);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        try {
            Class cls = Class.forName("android.app.ActivityThread");
            Method declaredMethod = cls.getDeclaredMethod("currentActivityThread");
            declaredMethod.setAccessible(true);
            Object activityThread = declaredMethod.invoke(null);
            Field mHiddenApiWarningShown = cls.getDeclaredField("mHiddenApiWarningShown");
            mHiddenApiWarningShown.setAccessible(true);
            mHiddenApiWarningShown.setBoolean(activityThread, true);
        } catch (Exception e) {
            // e.printStackTrace();
        }
    }

    private void initDownloader() {
        FileDownloader.setupOnApplicationOnCreate(this);
        FileDownloader.init(getApplicationContext());
        FileDownloader.setupOnApplicationOnCreate(this)
                .connectionCreator(new FileDownloadUrlConnection
                        .Creator(new FileDownloadUrlConnection.Configuration()
                        .connectTimeout(15_000) // set connection timeout.
                        .readTimeout(15_000) // set read timeout.
                ))
                .commit();
    }

//    public String getChannel() {
//        ApplicationInfo applicationInfo = null;
//        String umeng_channel = "";
//        try {
//            applicationInfo = getPackageManager().getApplicationInfo(this.getPackageName(), PackageManager.GET_META_DATA);
//            umeng_channel = applicationInfo.metaData.getString("UMENG_CHANNEL");
//        } catch (PackageManager.NameNotFoundException e) {
////            e.printStackTrace();
//        }
//        return umeng_channel;
//    }

//    public void addActivity(Activity activity) {
//        if (activitys != null && activitys.size() > 0) {
//            if (!activitys.contains(activity)) {
//                activitys.add(activity);
//            }
//        } else {
//            activitys.add(activity);
//        }
//    }

    public void getMainActivity(Activity activity) {


    }

    public void exit() {
//        if (BuildConfig.DEBUG) {
//            return;
//        }
//        //防止导入 关闭app
//        if (activitys != null && activitys.size() > 0) {
//            for (Activity activity : activitys) {
//               if ( activity.getClass().getSimpleName().equalsIgnoreCase("InputWalletActivity"))
//                   return;
//            }
//        }
//        if (activitys != null && activitys.size() > 0) {
//            for (Activity activity : activitys) {
//                activity.finish();
//            }
//        }
//        System.exit(0);
    }
}
