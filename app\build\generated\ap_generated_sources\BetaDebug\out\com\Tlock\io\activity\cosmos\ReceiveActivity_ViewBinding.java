// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ReceiveActivity_ViewBinding implements Unbinder {
  private ReceiveActivity target;

  @UiThread
  public ReceiveActivity_ViewBinding(ReceiveActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public ReceiveActivity_ViewBinding(ReceiveActivity target, View source) {
    this.target = target;

    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvName = Utils.findRequiredViewAsType(source, R.id.tv_name, "field 'mTvName'", FontTextView.class);
    target.mTvAddress = Utils.findRequiredViewAsType(source, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mTvTip = Utils.findRequiredViewAsType(source, R.id.tv_tip, "field 'mTvTip'", TextView.class);
    target.mIvQrCode = Utils.findRequiredViewAsType(source, R.id.iv_qr_code, "field 'mIvQrCode'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    ReceiveActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvHeard = null;
    target.mTvName = null;
    target.mTvAddress = null;
    target.mLine1 = null;
    target.mTvTip = null;
    target.mIvQrCode = null;
  }
}
