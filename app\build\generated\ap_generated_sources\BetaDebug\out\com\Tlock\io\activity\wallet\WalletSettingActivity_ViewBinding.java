// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WalletSettingActivity_ViewBinding implements Unbinder {
  private WalletSettingActivity target;

  private View view7f090243;

  private View view7f09024c;

  private View view7f09024d;

  private View view7f09031c;

  private View view7f090237;

  @UiThread
  public WalletSettingActivity_ViewBinding(WalletSettingActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public WalletSettingActivity_ViewBinding(final WalletSettingActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mTvName = Utils.findRequiredViewAsType(source, R.id.tv_name, "field 'mTvName'", TextView.class);
    view = Utils.findRequiredView(source, R.id.rl_info, "field 'mRlInfo' and method 'onBindClick'");
    target.mRlInfo = Utils.castView(view, R.id.rl_info, "field 'mRlInfo'", RelativeLayout.class);
    view7f090243 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_out_private, "field 'mRlOutPrivate' and method 'onBindClick'");
    target.mRlOutPrivate = Utils.castView(view, R.id.rl_out_private, "field 'mRlOutPrivate'", RelativeLayout.class);
    view7f09024c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_out_world, "field 'mRlOutWorld' and method 'onBindClick'");
    target.mRlOutWorld = Utils.castView(view, R.id.rl_out_world, "field 'mRlOutWorld'", RelativeLayout.class);
    view7f09024d = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_delete, "field 'mTvDelete' and method 'onBindClick'");
    target.mTvDelete = Utils.castView(view, R.id.tv_delete, "field 'mTvDelete'", TextView.class);
    view7f09031c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvNotice2 = Utils.findRequiredViewAsType(source, R.id.iv_notice2, "field 'mIvNotice2'", ImageView.class);
    target.mIv2 = Utils.findRequiredViewAsType(source, R.id.iv2, "field 'mIv2'", ImageView.class);
    target.mIvNotice1 = Utils.findRequiredViewAsType(source, R.id.iv_notice1, "field 'mIvNotice1'", ImageView.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv1, "field 'mIv1'", ImageView.class);
    target.mTvChangePwd = Utils.findRequiredViewAsType(source, R.id.tv_change_pwd, "field 'mTvChangePwd'", TextView.class);
    target.mIvDot = Utils.findRequiredViewAsType(source, R.id.iv_dot, "field 'mIvDot'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_change_pwd, "field 'mRlChangePwd' and method 'onBindClick'");
    target.mRlChangePwd = Utils.castView(view, R.id.rl_change_pwd, "field 'mRlChangePwd'", RelativeLayout.class);
    view7f090237 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvPwdTip = Utils.findRequiredViewAsType(source, R.id.tv_pwd_tip, "field 'mTvPwdTip'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    WalletSettingActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mTvName = null;
    target.mRlInfo = null;
    target.mRlOutPrivate = null;
    target.mRlOutWorld = null;
    target.mTvDelete = null;
    target.mIvNotice2 = null;
    target.mIv2 = null;
    target.mIvNotice1 = null;
    target.mIv1 = null;
    target.mTvChangePwd = null;
    target.mIvDot = null;
    target.mRlChangePwd = null;
    target.mTvPwdTip = null;

    view7f090243.setOnClickListener(null);
    view7f090243 = null;
    view7f09024c.setOnClickListener(null);
    view7f09024c = null;
    view7f09024d.setOnClickListener(null);
    view7f09024d = null;
    view7f09031c.setOnClickListener(null);
    view7f09031c = null;
    view7f090237.setOnClickListener(null);
    view7f090237 = null;
  }
}
