package com.Tlock.io.utils.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.Tlock.io.entity.wallet.DaoMaster;
import com.Tlock.io.entity.wallet.ETHWalletDao;
import com.Tlock.io.entity.wallet.OperationBeanDao;
import com.Tlock.io.entity.wallet.TokenInfoDao;
import com.Tlock.io.entity.wallet.TransactionDao;
import com.Tlock.io.entity.wallet.TransferDao;

import org.greenrobot.greendao.database.Database;


public class MyOpenHelper extends DaoMaster.OpenHelper {

    public MyOpenHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }

//    @Override
//    public void onUpgrade(Database db, int oldVersion, int newVersion) {
//        MigrationHelper.migrate(db, new MigrationHelper.ReCreateAllTableListener() {
//            @Override
//            public void onCreateAllTables(Database db, boolean ifNotExists) {
//                DaoMaster.createAllTables(db, ifNotExists);
//            }
//
//            @Override
//            public void onDropAllTables(Database db, boolean ifExists) {
//                DaoMaster.dropAllTables(db, ifExists);
//            }
//
//        });
//
//}
//
//    @Override
//    @SuppressWarnings("all")
//    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
//        super.onUpgrade(db, oldVersion, newVersion);
//        Log.e("MyGreenDaoDbHelper", "----"+oldVersion + "---先前和更新之后的版本---" + newVersion+"----");
//        if (oldVersion < newVersion) {
//            Log.e("MyGreenDaoDbHelper","进行数据库升级");
//            new GreenDaoCompatibleUpdateHelper()
//                    .setCallBack(
//                            new GreenDaoCompatibleUpdateHelper.GreenDaoCompatibleUpdateCallBack() {
//                                @Override
//                                public void onFinalSuccess() {
//                                    Log.e("MyGreenDaoDbHelper","进行数据库升级 ===> 成功");
//                                }
//
//                                @Override
//                                public void onFailedLog(String errorMsg) {
//                                    Log.e("MyGreenDaoDbHelper","升级失败日志 ===> "+errorMsg);
//                                }
//                            }
//                    )
//                    .compatibleUpdate(
//                            db,
//                            TransferHistoryDao.class,
//                            ETHWalletDao.class,
//                            TokenInfoDao.class,
//                            TransferDao.class,
//                            TextTestDao.class
//                    );
//
//        }
//    }

    @Override
    @SuppressWarnings("all")
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        super.onUpgrade(db, oldVersion, newVersion);
        Log.e("MyGreenDaoDbHelper", "----"+oldVersion + "---先前和更新之后的版本---" + newVersion+"----");
        if (oldVersion < newVersion) {
            Log.e("MyGreenDaoDbHelper","进行数据库升级");
            new GreenDaoCompatibleUpdateHelper()
                    .setCallBack(
                            new GreenDaoCompatibleUpdateHelper.GreenDaoCompatibleUpdateCallBack() {
                                @Override
                                public void onFinalSuccess() {
                                    Log.e("MyGreenDaoDbHelper","进行数据库升级 ===> 成功");
                                }

                                @Override
                                public void onFailedLog(String errorMsg) {
                                    Log.e("MyGreenDaoDbHelper","升级失败日志 ===> "+errorMsg);
                                }
                            }
                    )
                    .compatibleUpdate(
                            db,
                            ETHWalletDao.class,
                            TokenInfoDao.class,
                            TransferDao.class,
                            TransactionDao.class,
                            OperationBeanDao.class
                    );
            Log.e("MyGreenDaoDbHelper","进行数据库升级--完成");
        }
    }

    @Override
    public void onUpgrade(Database db, int oldVersion, int newVersion) {
        // 不要调用父类的，它默认是先删除全部表再创建
        // super.onUpgrade(db, oldVersion, newVersion);

    }
}