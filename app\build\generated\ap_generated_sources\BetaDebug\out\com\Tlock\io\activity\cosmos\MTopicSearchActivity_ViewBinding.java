// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.SearchView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MTopicSearchActivity_ViewBinding implements Unbinder {
  private MTopicSearchActivity target;

  @UiThread
  public MTopicSearchActivity_ViewBinding(MTopicSearchActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MTopicSearchActivity_ViewBinding(MTopicSearchActivity target, View source) {
    this.target = target;

    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mSearchView = Utils.findRequiredViewAsType(source, R.id.search_view, "field 'mSearchView'", SearchView.class);
    target.mRecyclerView = Utils.findRequiredViewAsType(source, R.id.recyclerView, "field 'mRecyclerView'", RecyclerView.class);
    target.mLoadError = Utils.findRequiredViewAsType(source, R.id.load_error, "field 'mLoadError'", LoadErrorView.class);
    target.mRefreshLayout = Utils.findRequiredViewAsType(source, R.id.refresh_layout, "field 'mRefreshLayout'", SmartRefreshLayout.class);
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    MTopicSearchActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mSearchView = null;
    target.mRecyclerView = null;
    target.mLoadError = null;
    target.mRefreshLayout = null;
    target.mMain = null;
  }
}
