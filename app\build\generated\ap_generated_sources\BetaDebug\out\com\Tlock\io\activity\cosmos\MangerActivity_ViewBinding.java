// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MangerActivity_ViewBinding implements Unbinder {
  private MangerActivity target;

  private View view7f090330;

  private View view7f090256;

  private View view7f090259;

  private View view7f09024a;

  private View view7f090385;

  @UiThread
  public MangerActivity_ViewBinding(MangerActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MangerActivity_ViewBinding(final MangerActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvName = Utils.findRequiredViewAsType(source, R.id.tv_name, "field 'mTvName'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_handle, "field 'mTvHandle' and method 'onBindClick'");
    target.mTvHandle = Utils.castView(view, R.id.tv_handle, "field 'mTvHandle'", TextView.class);
    view7f090330 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvDate = Utils.findRequiredViewAsType(source, R.id.tv_date, "field 'mTvDate'", TextView.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv1, "field 'mIv1'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_task, "field 'mRlTask' and method 'onBindClick'");
    target.mRlTask = Utils.castView(view, R.id.rl_task, "field 'mRlTask'", RelativeLayout.class);
    view7f090256 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIv2 = Utils.findRequiredViewAsType(source, R.id.iv2, "field 'mIv2'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_topic, "field 'mRlTopic' and method 'onBindClick'");
    target.mRlTopic = Utils.castView(view, R.id.rl_topic, "field 'mRlTopic'", RelativeLayout.class);
    view7f090259 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIv3 = Utils.findRequiredViewAsType(source, R.id.iv3, "field 'mIv3'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_member_list, "field 'mRlMemberList' and method 'onBindClick'");
    target.mRlMemberList = Utils.castView(view, R.id.rl_member_list, "field 'mRlMemberList'", RelativeLayout.class);
    view7f09024a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_wallet_address, "field 'mTvWalletAddress' and method 'onBindClick'");
    target.mTvWalletAddress = Utils.castView(view, R.id.tv_wallet_address, "field 'mTvWalletAddress'", TextView.class);
    view7f090385 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    MangerActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvHeard = null;
    target.mTvName = null;
    target.mTvHandle = null;
    target.mTvDate = null;
    target.mIv1 = null;
    target.mRlTask = null;
    target.mIv2 = null;
    target.mRlTopic = null;
    target.mIv3 = null;
    target.mRlMemberList = null;
    target.mMain = null;
    target.mTvWalletAddress = null;

    view7f090330.setOnClickListener(null);
    view7f090330 = null;
    view7f090256.setOnClickListener(null);
    view7f090256 = null;
    view7f090259.setOnClickListener(null);
    view7f090259 = null;
    view7f09024a.setOnClickListener(null);
    view7f09024a = null;
    view7f090385.setOnClickListener(null);
    view7f090385 = null;
  }
}
