package com.Tlock.io.utils.cosmos;

import static com.Tlock.io.utils.cosmos.CosmosUtils.lastHeight;
import static com.Tlock.io.utils.cosmos.CosmosUtils.queryHash;
import static com.cosmos.tx.v1beta1.ServiceGrpc.newBlockingStub;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.Tlock.io.app.AppApplication;
import com.Tlock.io.entity.wallet.Transaction;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.post.QueryGrpc;
import com.Tlock.io.profile.ProfileTXProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.pop.PopTip;
import com.cosmos.auth.v1beta1.AuthProto;
import com.cosmos.auth.v1beta1.QueryProto;
import com.cosmos.base.abci.v1beta1.AbciProto;
import com.cosmos.base.v1beta1.CoinProto;
import com.cosmos.crypto.secp256k1.KeysProto;
import com.cosmos.feegrant.v1beta1.FeegrantProto;
import com.cosmos.gov.v1beta1.GovProto;
import com.cosmos.params.v1beta1.ParamsProto;
import com.cosmos.tx.signing.v1beta1.SigningProto;
import com.cosmos.tx.v1beta1.ServiceGrpc;
import com.cosmos.tx.v1beta1.ServiceProto;
import com.cosmos.tx.v1beta1.TxProto;
import com.cosmos.vesting.v1beta1.VestingProto;
import com.ethermint.types.v1.AccountProto;
import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.lxj.xpopup.XPopup;

import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.Sha256Hash;

import java.math.BigInteger;
import java.security.PrivateKey;
import java.security.Signature;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import kotlin.Triple;

public class NewCosmosUtils {

    public static String BASE_DENOM = "uTOK";

    public static ManagedChannel channel = ManagedChannelBuilder.forAddress("*************", 9090)
            .usePlaintext()
            .build();
    private static String CHAIN_ID = "10889";
    private static String PAYER = "tlock1wg33qsaurqr7da6qkg67kag3ajeny4dxsf7df8";
    private static AbciProto.TxResponse txResponse;
    private static final LinkedBlockingQueue<TransactionTask> transactionQueue = new LinkedBlockingQueue<>();
    private static final ExecutorService queueExecutor = Executors.newSingleThreadExecutor();
    private static volatile boolean isQueueRunning = true;

    // 交易任务类
    private static class TransactionTask {
        List<Any> body;
        boolean isBase;
        String address;
        String privateKey;
        CompletableFuture<String> resultFuture;

        TransactionTask(List<Any> body, boolean isBase, String address, String privateKey) {
            this.body = body;
            this.isBase = isBase;
            this.address = address;
            this.privateKey = privateKey;
            this.resultFuture = new CompletableFuture<>();
        }
    }

    // 初始化异步队列处理
    static {
        queueExecutor.submit(() -> {
            while (isQueueRunning) {
                try {
                    TransactionTask task = transactionQueue.take(); // 阻塞等待任务
                    String txHash = processTransaction(task);
                    task.resultFuture.complete(txHash);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    Log.e("Cosmos", "Queue processing interrupted: " + e.getMessage());
                } catch (Exception e) {
                    Log.e("Cosmos", "Queue processing error: " + e.getMessage());
                }
            }
        });
    }

    public static long getGranteeBalance() throws InvalidProtocolBufferException {
        try {
            com.cosmos.feegrant.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.feegrant.v1beta1.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceRequest build = com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceRequest.newBuilder()
                    .setGrantee(WalletDaoUtils.getCurrent().getAddress())
                    .setGranter(PAYER)
                    .build();

            com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceResponse allowance = queryBlockingStub.allowance(build);

            ByteString value = allowance.getAllowance().getAllowance().getValue();
            FeegrantProto.PeriodicAllowance basicAllowance = FeegrantProto.PeriodicAllowance.parseFrom(value);
            Log.e("TAG", "getGranteeBalance: " + JsonUtils.objectToJson(basicAllowance));
            String amount = basicAllowance.getPeriodCanSpend(0).getAmount();
            return Long.parseLong(amount);
        } catch (Exception e) {
            return 0L;
        }
    }

    // 处理单个交易任务，包含确认逻辑
    private static String processTransaction(TransactionTask task) {
        try {
            long granteeBalance = getGranteeBalance();
            TxProto.Fee initSimuFee;
            TxProto.Fee initFee;
            if (granteeBalance == 0) {
                initSimuFee = getInitFee(signSimuTx(task.body, getInitSimuFee(), "", WalletDaoUtils.getCurrent().getPrivateKey(), ""));
                initFee = getInitFee(initSimuFee.getGasLimit());
                txResponse = broadcastTx(task.body, initFee, "", WalletDaoUtils.getCurrent().getPrivateKey(), "");
                return txResponse.getTxhash();
            }else{
                initSimuFee = getInitFee(signSimuTx(task.body, getInitSimuFee(), "", WalletDaoUtils.getCurrent().getPrivateKey(), PAYER));
                initFee = getInitFee(initSimuFee.getGasLimit());
            }
            if (granteeBalance < initSimuFee.getGasLimit()) {
                //授权不够
                txResponse = broadcastTx(task.body, initFee, "", WalletDaoUtils.getCurrent().getPrivateKey(), "");
            } else {
                //授权足够
                txResponse = broadcastTx(task.body, initFee, "", WalletDaoUtils.getCurrent().getPrivateKey(), PAYER);
                return txResponse.getTxhash();
            }
            if (txResponse == null || txResponse.getTxhash().isEmpty()) {
              //  showNote("Transaction broadcast failed");
                return "";
            }
            // 轮询确认交易上链
            String txHash = txResponse.getTxhash();
            boolean confirmed = confirmTransaction(txHash);
            if (!confirmed) {
//                showNote("Transaction confirmation failed or timed out: " + txHash);
                return "";
            }

            return txHash;
        } catch (Exception e) {
//            showNote(e.getLocalizedMessage());
            return "";
        }
    }

    // 确认交易是否上链
    private static boolean confirmTransaction(String txHash) {
        final long maxWaitTimeSeconds = 60; // 最大等待时间
        final long pollIntervalSeconds = 2; // 轮询间隔
        long startTime = System.currentTimeMillis();

        while (System.currentTimeMillis() - startTime < maxWaitTimeSeconds * 1000) {
            try {
                ServiceProto.GetTxResponse txResponse = queryHash(txHash);
                if (txResponse != null && txResponse.getTxResponse().getCode() == 0) {
                    Log.e("Cosmos", "Transaction confirmed: " + txHash);
                    return true; // 交易成功上链
                } else if (txResponse != null && txResponse.getTxResponse().getCode() != 0) {
                    Log.e("Cosmos", "Transaction failed with code: " + txResponse.getTxResponse().getCode());
                    return false; // 交易失败
                }
                // 交易尚未确认，继续轮询
                Thread.sleep(pollIntervalSeconds * 1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                Log.e("Cosmos", "Transaction confirmation interrupted: " + e.getMessage());
                return false;
            } catch (Exception e) {
                Log.e("Cosmos", "Error querying transaction: " + e.getMessage());
                // 继续轮询，可能是暂时的网络问题
                try {
                    Thread.sleep(pollIntervalSeconds * 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        Log.e("Cosmos", "Transaction confirmation timed out: " + txHash);
        return false; // 超时未确认
    }

    // 关闭队列和执行器，防止内存泄漏
    public static void shutdownQueue() {
        isQueueRunning = false;
        transactionQueue.clear(); // 清空队列
        queueExecutor.shutdown();
        try {
            if (!queueExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                queueExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            queueExecutor.shutdownNow();
            Thread.currentThread().interrupt();
            Log.e("Cosmos", "Queue shutdown interrupted: " + e.getMessage());
        }
    }

    /**
     * 设置税
     */
    public static TxProto.Fee getInitFee(long gasLimit) {
        String multipliy = BigDecimalUtils.multipliy(gasLimit + "", "1.2");
        CoinProto.Coin feeCoin = CoinProto.Coin.newBuilder()
                .setDenom(BASE_DENOM)
                .setAmount(BigDecimalUtils.saveDecimals(multipliy, -1))
                .build();
        TxProto.Fee build = TxProto.Fee.newBuilder()
                .setGasLimit(Long.parseLong(BigDecimalUtils.saveDecimals(multipliy, -1)))
                .addAmount(feeCoin)
                .build();
        return build;
    }

    @SuppressLint("CheckResult")
    public static PostQueryProto.PostResponse queryPost(String s) {
        PostQueryProto.QueryPostRequest builder = PostQueryProto.QueryPostRequest.newBuilder().setPostId(s).build();
        QueryGrpc.QueryBlockingStub queryBlockingStub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
        return queryBlockingStub.queryPost(builder).getPost();
    }

    /**
     * 创建转账用户信息
     */
    private static TxProto.AuthInfo grpcAuthInfo(TxProto.SignerInfo signerInfo, TxProto.Fee fee, String granter) {
        if (fee != null && fee.getAmountCount() > 0) {
            CoinProto.Coin feeCoin = CoinProto.Coin.newBuilder()
                    .setAmount(fee.getAmount(0).getAmount())
                    .setDenom(fee.getAmount(0).getDenom())
                    .build();

            TxProto.Fee txFee = TxProto.Fee.newBuilder()
                    .addAmount(feeCoin)
                    .setGranter(granter)
                    .setGasLimit(fee.getGasLimit())
                    .build();

            return TxProto.AuthInfo.newBuilder()
                    .setFee(txFee)
                    .addSignerInfos(signerInfo)
                    .build();
        }
        return null;
    }

    /**
     * 使用私钥对数据进行签名
     */
    public static byte[] signData(PrivateKey privateKey, byte[] data) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA", "BC");
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }

    /**
     * 修改后的 sendTX 方法，放入异步队列
     */
    @SuppressLint("CheckResult")
    public static String sendTX(List<Any> body, boolean isBase) {
        TransactionTask task = new TransactionTask(body, isBase, "", "");
        try {
            transactionQueue.put(task);
            return task.resultFuture.get(360, TimeUnit.SECONDS); // 增加超时时间以适应确认延迟
        } catch (Exception e) {
//            showNote(e.getLocalizedMessage());
            return "";
        }
    }

    /**
     * 修改后的 sendTX 方法，放入异步队列
     */
    @SuppressLint("CheckResult")
    public static String sendTX(List<Any> body, String address, String privateKey) {
        TransactionTask task = new TransactionTask(body, false, address, privateKey);
        try {
            transactionQueue.put(task);
            return task.resultFuture.get(360, TimeUnit.SECONDS); // 增加超时时间以适应确认延迟
        } catch (Exception e) {
//            showNote(e.getLocalizedMessage());
            return "";
        }
    }

    /**
     * 显示错误提示
     */
    private static void showNote(String error) {
        Activity currentActivity = AppApplication.getInstance().getCurrentActivity();
        if (currentActivity != null) {
            currentActivity.runOnUiThread(() -> {
                PopTip popTip = new PopTip(currentActivity,
                        error,
                        "Something went wrong",
                        "Cancel",
                        "Ok",
                        true, false);
                popTip.setCallback(new PopTip.Callback() {
                    @Override
                    public void notShow() {
                    }

                    @Override
                    public void confirm() {
                    }
                });
                new XPopup.Builder(currentActivity)
                        .hasShadowBg(true)
                        .isViewMode(true)
                        .isDestroyOnDismiss(true)
                        .asCustom(popTip).show();
            });
        }
    }

//    public static TxProto.Fee getFee(List<Any> body) {
//        TxProto.Fee initSimuFee = getInitSimuFee();
//        long gasLimit = signSimuTx(body, initSimuFee, "", WalletDaoUtils.getCurrent().getPrivateKey());
//        return getInitFee(gasLimit);
//    }

    /********************************************** 交易TX start **********************************************************/

    public static void sendPostBody(PostTXProto.MsgCreatePost mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgCreateFreePost")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void sendPostListBody(List<PostTXProto.MsgCreatePost> mycontent) {
        List<Any> anyList = new ArrayList<>();
        for (PostTXProto.MsgCreatePost msgCreateFreePost : mycontent) {
            Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgCreateFreePost")
                    .setValue(msgCreateFreePost.toByteString()).build();
            anyList.add(build);
        }
        Log.e("TAG", "发送爬虫数据量=======================: " + anyList.size());
        sendTX(anyList, false);
    }

    public static void sendGov(PostTXProto.MsgCreatePost mycontent) {
        List<Any> anyList = new ArrayList<>();
        GovProto.TextProposal build1 = GovProto.TextProposal.newBuilder().setTitle("这是标题1").setDescription("这是描述1").build();
        Any proposalContent = Any.newBuilder()
                .setTypeUrl("/cosmos.gov.v1beta1.TextProposal")
                .setValue(build1.toByteString())
                .build();
        com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal proposal = com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal.newBuilder()
                .setContent(proposalContent)
                .setProposer(WalletDaoUtils.getCurrent().getAddress())
                .addInitialDeposit(CoinProto.Coin.newBuilder()
                        .setAmount("10000")
                        .setDenom(BASE_DENOM)
                        .build())
                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1beta1.MsgSubmitProposal")
                        .setValue(proposal.toByteString()).build()
        );
        sendTX(anyList, false);
    }

    public static void sendProposal(String mycontent) {
        ParamsProto.ParamChange paramChange = ParamsProto.ParamChange.newBuilder()
                .setValue("true")
                .setKey("someValue")
                .setSubspace("test")
                .build();

        ParamsProto.ParameterChangeProposal parameterChangeProposal = ParamsProto.ParameterChangeProposal.newBuilder()
                .setTitle("这是修改标题")
                .addChanges(paramChange)
                .setDescription("我想修改params 为false")
                .build();
        List<Any> anyList = new ArrayList<>();
        Any proposalContent = Any.newBuilder()
                .setTypeUrl("/cosmos.params.v1beta1.ParameterChangeProposal")
                .setValue(parameterChangeProposal.toByteString())
                .build();
        com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal proposal = com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal.newBuilder()
                .setContent(proposalContent)
                .setProposer(WalletDaoUtils.getCurrent().getAddress())
                .addInitialDeposit(CoinProto.Coin.newBuilder()
                        .setAmount("10000")
                        .setDenom(BASE_DENOM)
                        .build())
                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1beta1.MsgSubmitProposal")
                        .setValue(proposal.toByteString()).build()
        );
        sendTX(anyList, false);
    }

    public static void submitDeposit(String id, String count) {
        List<Any> anyList = new ArrayList<>();
        com.cosmos.gov.v1beta1.TxProto.MsgDeposit msgDeposit = com.cosmos.gov.v1beta1.TxProto.MsgDeposit.newBuilder()
                .setProposalId(Long.parseLong(id))
                .setDepositor(WalletDaoUtils.getCurrent().getAddress())
                .addAmount(CoinProto.Coin.newBuilder()
                        .setAmount(count)
                        .setDenom(BASE_DENOM)
                        .build())
                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1beta1.MsgDeposit")
                        .setValue(msgDeposit.toByteString()).build()
        );
        sendTX(anyList, false);
    }

    public static void sendGovVote(String id) {
        List<Any> anyList = new ArrayList<>();
        com.cosmos.gov.v1.TxProto.MsgVote msgVote = com.cosmos.gov.v1.TxProto.MsgVote.newBuilder()
                .setProposalId(Long.parseLong(id))
                .setVoter(WalletDaoUtils.getCurrent().getAddress())
                .setOption(com.cosmos.gov.v1.GovProto.VoteOption.VOTE_OPTION_YES)
                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1.MsgVote")
                        .setValue(msgVote.toByteString()).build()
        );
        sendTX(anyList, false);
    }

    public static void sendPostWithTitleBody(PostTXProto.MsgCreatePost mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgCreateFreePostWithTitle")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static String sendBase(com.cosmos.bank.v1beta1.TxProto.MsgSend build) {
        List<Any> anyList = new ArrayList<>();
        anyList.add(
                Any.newBuilder().setTypeUrl("/cosmos.bank.v1beta1.MsgSend")
                        .setValue(build.toByteString()).build()
        );
        return sendTX(anyList, true);
    }

    public static void sendMangerBody(ProfileTXProto.MsgManageAdminRequest mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgManageAdminRequest")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public static AbciProto.TxResponse broadcastTx(List<Any> msgs, TxProto.Fee fee, String memo, String privateKey, String granter) {
        try {
            ServiceProto.BroadcastTxRequest broadcastTx = signBroadcastTx(msgs, fee, memo, privateKey, granter);
            Log.e("Cosmos", " signBroadcastTx 信息: " + JsonUtils.objectToJson(broadcastTx));

            ServiceGrpc.ServiceBlockingStub txStub = newBlockingStub(channel)
                    .withDeadlineAfter(300L, TimeUnit.SECONDS);
            AbciProto.TxResponse txResponse1 = CompletableFuture.completedFuture(txStub.broadcastTx(broadcastTx).getTxResponse()).get();
            Log.e("Cosmos", " ModeInfo 信息: " + JsonUtils.objectToJson(txResponse1));
            Transaction transaction = new Transaction();
            transaction.setHash(txResponse1.getTxhash());
            transaction.setTime(System.currentTimeMillis() + "");
            transaction.setFunction(msgs.get(0).getTypeUrl());
            if (memo.isEmpty()) {
                transaction.setWalletAddress(WalletDaoUtils.getCurrent().getAddress());
            } else {
                transaction.setWalletAddress(memo);
            }
            WalletDaoUtils.insertNewTransaction(transaction);

            return txResponse1;
        } catch (Exception e) {
            Log.e("Cosmos", " broadcastTx 错误: " + e.getMessage());
            return null;
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private static ServiceProto.BroadcastTxRequest signBroadcastTx(List<Any> msgAnys, TxProto.Fee fee, String memo, String privateKey, String granter) {
        Long height = lastHeight();
        Triple<String, Long, Long> auth;
        if (memo.isEmpty()) {
            auth = getAuth();
        } else {
            auth = getAuth(memo);
        }

        if (height != null) {
            TxProto.TxBody txBody = grpcTxBody(msgAnys, memo, height);
            TxProto.SignerInfo signerInfo = grpcSignerInfo(auth.getThird(), privateKey);
            TxProto.AuthInfo authInfo = grpcAuthInfo(signerInfo, fee, granter);
            TxProto.TxRaw broadcastTx = grpcBroadcastTx(txBody, authInfo, auth.getSecond(), privateKey);
            TxProto.TxRaw simulateTx = grpcSimulTx(txBody, authInfo);
            ServiceProto.SimulateRequest build = ServiceProto.SimulateRequest.newBuilder().setTxBytes(simulateTx.toByteString()).build();
            ServiceGrpc.ServiceBlockingStub serviceBlockingStub = newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            ServiceProto.SimulateResponse simulate = serviceBlockingStub.simulate(build);
            Log.e("Cosmos", "gasInfo      信息 : " + JsonUtils.objectToJson(simulate.getResult()));

            return ServiceProto.BroadcastTxRequest.newBuilder()
                    .setModeValue(ServiceProto.BroadcastMode.BROADCAST_MODE_SYNC.getNumber())
                    .setTxBytes(broadcastTx.toByteString())
                    .build();
        }
        Log.e("Cosmos", "  grpcBroadcastTx 错误: height为空");
        return null;
    }

    private static TxProto.TxBody grpcTxBody(List<Any> msgsAny, String memo, long height) {
        TxProto.TxBody.Builder builder = TxProto.TxBody.newBuilder();
        if (msgsAny != null) {
            for (Any msg : msgsAny) {
                builder.addMessages(msg);
            }
        }
        return builder.setTimeoutHeight(height + 500L).build();
    }

    private static TxProto.SignerInfo grpcSignerInfo(long Sequence, String privateKey) {
        if (privateKey.toLowerCase().startsWith("0x")) {
            privateKey = privateKey.substring(2);
        }
        Any pubKey = generateGrpcPubKeyFromPriv(privateKey);
        Log.e("Cosmos", "  generateGrpcPubKeyFromPriv 信息: " + privateKey);

        TxProto.ModeInfo.Single singleMode = TxProto.ModeInfo.Single.newBuilder()
                .setMode(SigningProto.SignMode.SIGN_MODE_DIRECT)
                .build();
        Log.e("Cosmos", "  singleMode 信息: " + JsonUtils.objectToJson(singleMode));

        TxProto.ModeInfo modeInfo = TxProto.ModeInfo.newBuilder()
                .setSingle(singleMode)
                .build();
        Log.e("Cosmos", "  ModeInfo 信息: " + JsonUtils.objectToJson(modeInfo));

        return TxProto.SignerInfo.newBuilder()
                .setPublicKey(pubKey)
                .setModeInfo(modeInfo)
                .setSequence(Sequence)
                .build();
    }

    private static Any generateGrpcPubKeyFromPriv(String privateKey) {
        ECKey ecKey = ECKey.fromPrivate(new BigInteger(privateKey, 16));
        KeysProto.PubKey pubKey = KeysProto.PubKey.newBuilder()
                .setKey(ByteString.copyFrom(ecKey.getPubKey()))
                .build();
        return Any.newBuilder()
                .setTypeUrl("/cosmos.crypto.secp256k1.PubKey")
                .setValue(pubKey.toByteString())
                .build();
    }

    private static TxProto.TxRaw grpcBroadcastTx(TxProto.TxBody txBody, TxProto.AuthInfo authInfo, long accountNumber, String privateKey) {
        TxProto.SignDoc.Builder signDocBuilder = TxProto.SignDoc.newBuilder()
                .setBodyBytes(txBody.toByteString())
                .setAuthInfoBytes(authInfo.toByteString())
                .setChainId(CHAIN_ID)
                .setAccountNumber(accountNumber);

        TxProto.SignDoc signDoc = signDocBuilder.build();
        byte[] sigByte = grpcByteSignature(signDoc.toByteArray(), privateKey);

        return TxProto.TxRaw.newBuilder()
                .setBodyBytes(txBody.toByteString())
                .setAuthInfoBytes(authInfo.toByteString())
                .addSignatures(ByteString.copyFrom(sigByte))
                .build();
    }

    private static byte[] grpcByteSignature(byte[] toSignByte, String privateKey) {
        byte[] sigData = new byte[64];
        byte[] sha256Hash = Sha256Hash.hash(toSignByte);

        if (privateKey.toLowerCase().startsWith("0x")) {
            privateKey = privateKey.substring(2);
        }
        ECKey ecKey = ECKey.fromPrivate(new BigInteger(privateKey, 16));

        if (ecKey != null) {
            ECKey.ECDSASignature signature = ecKey.sign(Sha256Hash.wrap(sha256Hash));
            System.arraycopy(integerToBytes(signature.r, 32), 0, sigData, 0, 32);
            System.arraycopy(integerToBytes(signature.s, 32), 0, sigData, 32, 32);
        }
        return sigData;
    }

    private static byte[] integerToBytes(BigInteger s, int length) {
        byte[] bytes = s.toByteArray();
        if (length < bytes.length) {
            byte[] tmp = new byte[length];
            System.arraycopy(bytes, bytes.length - tmp.length, tmp, 0, tmp.length);
            return tmp;
        } else if (length > bytes.length) {
            byte[] tmp = new byte[length];
            System.arraycopy(bytes, 0, tmp, tmp.length - bytes.length, bytes.length);
            return tmp;
        }
        return bytes;
    }

    public static Triple<String, Long, Long> accountInfos(Any rawAccount) {
        try {
            String typeUrl = rawAccount.getTypeUrl();
            if (typeUrl.contains(AuthProto.BaseAccount.getDescriptor().getFullName())) {
                AuthProto.BaseAccount account = AuthProto.BaseAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(account.getAddress(), account.getAccountNumber(), account.getSequence());
            } else if (typeUrl.contains(VestingProto.PeriodicVestingAccount.getDescriptor().getFullName())) {
                VestingProto.PeriodicVestingAccount vestingAccount = VestingProto.PeriodicVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(VestingProto.ContinuousVestingAccount.getDescriptor().getFullName())) {
                VestingProto.ContinuousVestingAccount vestingAccount = VestingProto.ContinuousVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(VestingProto.DelayedVestingAccount.getDescriptor().getFullName())) {
                VestingProto.DelayedVestingAccount vestingAccount = VestingProto.DelayedVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(com.stride.vesting.VestingProto.StridePeriodicVestingAccount.getDescriptor().getFullName())) {
                com.stride.vesting.VestingProto.StridePeriodicVestingAccount vestingAccount = com.stride.vesting.VestingProto.StridePeriodicVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(com.injective.types.v1beta1.AccountProto.EthAccount.getDescriptor().getFullName())) {
                com.injective.types.v1beta1.AccountProto.EthAccount ethAccount = com.injective.types.v1beta1.AccountProto.EthAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(ethAccount.getBaseAccount().getAddress(),
                        ethAccount.getBaseAccount().getAccountNumber(),
                        ethAccount.getBaseAccount().getSequence());
            } else if (typeUrl.contains(com.artela.types.v1.AccountProto.EthAccount.getDescriptor().getFullName())) {
                com.artela.types.v1.AccountProto.EthAccount ethAccount = com.artela.types.v1.AccountProto.EthAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(ethAccount.getBaseAccount().getAddress(),
                        ethAccount.getBaseAccount().getAccountNumber(),
                        ethAccount.getBaseAccount().getSequence());
            } else if (typeUrl.contains(AccountProto.EthAccount.getDescriptor().getFullName())) {
                AccountProto.EthAccount ethAccount = AccountProto.EthAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(ethAccount.getBaseAccount().getAddress(),
                        ethAccount.getBaseAccount().getAccountNumber(),
                        ethAccount.getBaseAccount().getSequence());
            } else {
                return new Triple<>("", -1L, -1L);
            }
        } catch (Exception e) {
            Log.e("Cosmos", " accountInfos 错误: " + e.getMessage());
            return null;
        }
    }

    public static void postLike(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgLikeRequest build1 = PostTXProto.MsgLikeRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgLikeRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void postUnLike(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgUnlikeRequest build1 = PostTXProto.MsgUnlikeRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgUnlikeRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void savePost(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgSaveRequest build1 = PostTXProto.MsgSaveRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgSaveRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void sendPoll(PostTXProto.CastVoteOnPollRequest mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.CastVoteOnPollRequest")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void unSavePost(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgUnsaveRequest build1 = PostTXProto.MsgUnsaveRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgUnsaveRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void postComment(String comment, String address, String id, ArrayList<String> tags) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgCommentRequest build1 = PostTXProto.MsgCommentRequest.newBuilder()
                .setCreator(address)
                .setParentId(id)
                .addAllMention(tags)
                .setComment(comment)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgCommentRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void postQuote(String comment, String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgQuotePostRequest build1 = PostTXProto.MsgQuotePostRequest.newBuilder()
                .setCreator(address)
                .setQuote(id)
                .setComment(comment)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgQuotePostRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void postRepost(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgRepostRequest build1 = PostTXProto.MsgRepostRequest.newBuilder()
                .setCreator(address)
                .setQuote(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgRepostRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void classifyUncategorizedTopic(String topicId, String category) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.ClassifyUncategorizedTopicRequest build1 = PostTXProto.ClassifyUncategorizedTopicRequest.newBuilder()
                .setCreator(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(topicId)
                .setCategoryId(category)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.ClassifyUncategorizedTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void profileUpdata(ProfileTXProto.ProfileOptions data, String address) {
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.MsgAddProfileRequest build1 = ProfileTXProto.MsgAddProfileRequest.newBuilder()
                .setCreator(address)
                .setProfileJson(data)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgAddProfileRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void topicUpdata(String address, PostTXProto.UpdateTopicJson data) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.UpdateTopicRequest build1 = PostTXProto.UpdateTopicRequest.newBuilder()
                .setCreator(address)
                .setTopicJson(data)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.UpdateTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void follow(String address, String targetAddr) {
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.MsgFollowRequest build1 = ProfileTXProto.MsgFollowRequest.newBuilder()
                .setCreator(address)
                .setTargetAddr(targetAddr)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgFollowRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void unfollow(String address, String targetAddr) {
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.MsgUnfollowRequest build1 = ProfileTXProto.MsgUnfollowRequest.newBuilder()
                .setCreator(address)
                .setTargetAddr(targetAddr)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgUnfollowRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void followTopic(String topicId) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgFollowTopicRequest build1 = PostTXProto.MsgFollowTopicRequest.newBuilder()
                .setCreator(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(topicId)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgFollowTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    public static void unFollowTopic(String topicId) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgUnfollowTopicRequest build1 = PostTXProto.MsgUnfollowTopicRequest.newBuilder()
                .setCreator(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(topicId)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgUnfollowTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /********************************************** SimulTx start **********************************************************/

    private static TxProto.TxRaw grpcSimulTx(TxProto.TxBody txBody, TxProto.AuthInfo authInfo) {
        TxProto.TxRaw.Builder builder = TxProto.TxRaw.newBuilder().setAuthInfoBytes(authInfo.toByteString())
                .setBodyBytes(txBody.toByteString());
        for (TxProto.SignerInfo signerInfo : authInfo.getSignerInfosList()) {
            builder.addSignatures(ByteString.copyFrom(signerInfo.toByteArray()));
        }
        return builder.build();
    }

    private static long signSimuTx(List<Any> msgAnys, TxProto.Fee fee, String memo, String privateKey, String granter) {
        Long height = lastHeight();
        Triple<String, Long, Long> auth;
        if (memo.isEmpty()) {
            auth = getAuth();
        } else {
            auth = getAuth(memo);
        }
        if (height != null) {
            TxProto.TxBody txBody = grpcTxBody(msgAnys, memo, height);
            Log.e("TAG", "getNameHeard: noce-----------------------" + auth.getThird());
            TxProto.SignerInfo signerInfo = grpcSignerInfo(auth.getThird(), privateKey);
            TxProto.AuthInfo authInfo = grpcAuthInfo(signerInfo, fee, granter);
            TxProto.TxRaw simulateTx = grpcSimulTx(txBody, authInfo);
            ServiceProto.SimulateRequest build = ServiceProto.SimulateRequest.newBuilder().setTxBytes(simulateTx.toByteString()).build();
            ServiceGrpc.ServiceBlockingStub serviceBlockingStub = newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            return serviceBlockingStub.simulate(build).getGasInfo().getGasUsed();
        }
        Log.e("Cosmos", "  grpcBroadcastTx 错误: height为空");
        return 1000000L;
    }

    public static TxProto.Fee getInitSimuFee() {
        CoinProto.Coin feeCoin = CoinProto.Coin.newBuilder()
                .setDenom(BASE_DENOM)
                .setAmount("10")
                .build();
        return TxProto.Fee.newBuilder()
                .setGasLimit(500000L)
                .addAmount(feeCoin)
                .build();
    }

    /********************************************** SimulTx end **********************************************************/

    /********************************************** 查询 start **********************************************************/

    public static Triple<String, Long, Long> getAuth() {
        com.cosmos.auth.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.auth.v1beta1.QueryGrpc.newBlockingStub(channel)
                .withDeadlineAfter(8L, TimeUnit.SECONDS);
        Log.e("TAG", "getAuth: 执行");
        QueryProto.QueryAccountRequest req = QueryProto.QueryAccountRequest.newBuilder().setAddress(WalletDaoUtils.getCurrent().getAddress()).build();
        Any account = queryBlockingStub.account(req).getAccount();
        Long second = accountInfos(account).getSecond();
        Long third = accountInfos(account).getThird();
        Log.e("Cosmos", "getAuth   account second信息: " + second);
        Log.e("Cosmos", "getAuth   account  third信息 : " + third);
        return accountInfos(account);
    }

    public static Triple<String, Long, Long> getAuth(String address) {
        com.cosmos.auth.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.auth.v1beta1.QueryGrpc.newBlockingStub(channel)
                .withDeadlineAfter(8L, TimeUnit.SECONDS);
        QueryProto.QueryAccountRequest req = QueryProto.QueryAccountRequest.newBuilder().setAddress(address).build();
        Any account = queryBlockingStub.account(req).getAccount();
        Long second = accountInfos(account).getSecond();
        Long third = accountInfos(account).getThird();
        Log.e("Cosmos", "getAuth   account second信息: " + second);
        Log.e("Cosmos", "getAuth   account  third信息 : " + third);
        return accountInfos(account);
    }


    /********************************************** 查询 end **********************************************************/
}
