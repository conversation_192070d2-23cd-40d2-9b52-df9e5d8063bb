package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.wallet.Transfer;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.DateUtil;
import com.Tlock.io.utils.ToastUtil;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TransferItemView extends BaseView {


    @BindView(R.id.iv_status)
    ImageView mIvStatus;
    @BindView(R.id.tv_1)
    TextView mTv1;
    @BindView(R.id.tv_address)
    TextView mTvAddress;
    @BindView(R.id.tv_amount)
    TextView mTvAmount;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.ll_root)
    RelativeLayout mLlRoot;

    public TransferItemView(Context context) {
        super(context);
    }

    public TransferItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TransferItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.item_transfer;
    }

    public void setData(Transfer data) {

        mTvAddress.setText(data.getGetAddress());
        mTvAddress.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                CopyUtils.copyToClipboard(data.getGetAddress());
                ToastUtil.toastView("Copy success");
            }
        });
        mTvAmount.setText("-" + BigDecimalUtils.saveDecimals(BigDecimalUtils.uTok2Tok(data.getCount()), 0) );
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(DateUtil.formatDate(Long.parseLong(data.getTime()), "yyyy/MM/dd"));
        }

    }

}
