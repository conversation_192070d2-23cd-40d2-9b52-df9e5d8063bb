package com.Tlock.io.base;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;


import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import androidx.annotation.DrawableRes;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.config.AppConstants;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.request.CondBean;
import com.Tlock.io.entity.request.PageableBean;
import com.Tlock.io.entity.request.RequestBean;
import com.Tlock.io.manger.ActivitiesManager;
import com.Tlock.io.network.OKHttpManager;
import com.Tlock.io.utils.LanguageUtils;
import com.Tlock.io.utils.LoadingDialogUtils;
import com.Tlock.io.utils.NetworkUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.widget.CustomHeader;
import com.Tlock.io.widget.CustomNavBar;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import butterknife.ButterKnife;

/**
 * 本类的主要功能是 :  基类Activity
 *
 * <AUTHOR>  2020-04-04 21:00
 */
public abstract class BaseActivity extends FragmentActivity implements OnLoadMoreListener, OnRefreshListener {
    protected HashMap<String, Object> params = new HashMap<>();//装载网络请求参数
    protected CustomNavBar mCustomNavBar;
    protected LoadErrorView mErrorView; //加载错误页面
    protected SmartRefreshLayout mRefreshLayout; //下拉刷新上拉加载
    protected InputMethodManager inputMethodManager;
    protected RecyclerView mRecylerView;
    protected int page = 1;
    protected String TAG = getClass().getSimpleName();

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        beforeSetContentView();
        setContentView(getContentViewId());
        ButterKnife.bind(this);
        NetworkUtils.getNetworkState(AppApplication.getInstance());
        ActivitiesManager.getInstance().pushActivity(this);
        initBase();
        initView(savedInstanceState);
        loadData();

        inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
    }


    // 初始化base
    private void initBase() {
        //标题栏
        mCustomNavBar = findViewById(R.id.custom_nav_bar);
        if (mCustomNavBar != null) {
            mCustomNavBar.setOnLelftClickListener(this::onBackClick);
            mCustomNavBar.setOnRightClickListener(this::onRightClick);
        }
        //加载错误页面
        mErrorView = findViewById(R.id.load_error);
        // RefreshLayout
        mRefreshLayout = findViewById(R.id.refresh_layout);
        if (mRefreshLayout != null) {
            mRefreshLayout.setRefreshHeader(new CustomHeader(getActivity()));
            mRefreshLayout.setOnLoadMoreListener(this);
            mRefreshLayout.setOnRefreshListener(this);
        }
        //加载失败的点击
        if (mErrorView != null) {
            mErrorView.setOnClickListener(this::onErrorViewClick);
        }
        //设置 RecylerView的布局管理器
        mRecylerView = findViewById(R.id.recyler_view);
        if (mRecylerView != null) {
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
            mRecylerView.setLayoutManager(linearLayoutManager);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(Color.TRANSPARENT);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            //适配刘海屏
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            getActivity().getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            getWindow().setAttributes(layoutParams);
        }
        setStatusColor(getResources().getColor(R.color.white));
    }

    /**************************************************************************************/
    /**
     * 显示加载错误页面
     *
     * @param message {@link #}
     */
    protected void showError(String message) {
        if (mErrorView != null) {
            mErrorView.setErrorMessage(message);
            mErrorView.setVisibility(View.VISIBLE);
        }
        finishRefresh();
    }

    /**
     * 显示加载错误页面
     *
     * @param message
     * @param page
     */
    protected void showError(String message, int page) {
        if (page == 1) {
            showError(message);
        } else {
            showToast(message);
            finishRefresh();
        }
    }

    //隐藏加载错误页面
    protected void hideError() {
        if (mErrorView != null) {
            mErrorView.setVisibility(View.GONE);
        }
        finishRefresh();
    }

    /**
     * 改变加载更多或者刷新状态
     */
    protected void finishRefresh() {
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    /**
     * 取消加載
     */
    public void cancleLoad(int size) {
        if (mRefreshLayout != null) {
            //如果正在下拉刷新,停止刷新
            if (mRefreshLayout.getState() == RefreshState.Refreshing) {
                mRefreshLayout.finishRefresh();
            }
            //如果正在上拉加载,停止加载
            if (mRefreshLayout.getState() == RefreshState.Loading) {
                mRefreshLayout.finishLoadMore();
            }
            if (size == 0) {
                mRefreshLayout.finishLoadMore();
                mRefreshLayout.finishRefresh();
            }
        }
    }

    /**************************************************************************************/
    // 左侧标题点击
    protected void onBackClick(View view) {
        finish();
    }

    // 右侧标题点击
    protected void onRightClick(View view) {
    }

    // 设置中间标题
    protected void setTitle(String title) {
        if (mCustomNavBar != null) {
            mCustomNavBar.setMidTitle(title);
        }
    }

    // 设置左标题
    protected void setLeftTitle(String leftTitle) {
        if (mCustomNavBar != null) {
            mCustomNavBar.setLeftTitle(leftTitle);
        }
    }

    // 设置左标题
    protected void setRightTitle(String rightTitle) {
        if (mCustomNavBar != null) {
            mCustomNavBar.setRightTitle(rightTitle);
        }
    }

    // 设置左图标
    protected void setLeftIcon(@DrawableRes int leftIcon) {
        if (mCustomNavBar != null) {
            mCustomNavBar.setLeftIcon(leftIcon);
        }
    }

    // 设置右图标
    protected void setRightIcon(@DrawableRes int rightIcon) {
        if (mCustomNavBar != null) {
            mCustomNavBar.setRightIcon(rightIcon);
        }
    }

    /**************************************************************************************/

    protected void beforeSetContentView() {

    }

    // 吐司
    protected void showToast(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        ToastUtil.toastShortCenter(AppApplication.getInstance(), msg);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            onBackClick(null);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    //加载布局文件
    protected abstract int getContentViewId();

    //初始化Views
    protected abstract void initView(Bundle savedInstanceState);

    //加载数据
    protected abstract void loadData();

    //设置状态栏颜色
    protected void setStatusColor(int color) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(color);
        }
    }

    /**
     * 初始化请求参数
     */
    protected RequestBean initRequestParams() {
        RequestBean mRequestBean = new RequestBean();
        CondBean condBean = new CondBean();
        condBean.setGroups(new ArrayList());
        condBean.setRules(new ArrayList());
        mRequestBean.setCond(condBean);
        PageableBean pageableBean = new PageableBean();
        mRequestBean.setPageable(pageableBean);
        return mRequestBean;
    }

    /**************************************************************************************/

    public void showLoading() {
        LoadingDialogUtils.getUtils().showProgressDialog(getActivity());
    }

    public void hideLoading() {
        LoadingDialogUtils.getUtils().dismissDialog();
    }

    /**************************************************************************************/
    /**
     * 失败点击重新弄加载
     */

    public void onErrorViewClick(View view) {
        refresh();
    }

    public void refresh() {
        if (mRefreshLayout != null) {
            mRefreshLayout.autoRefresh();
        }
    }

    /**
     * 上拉加载更多回调
     *
     * @param refreshLayout
     */
    @Override
    public void onLoadMore(RefreshLayout refreshLayout) {
        page++;
        getData(page);
    }

    /**
     * 下拉刷新回调
     *
     * @param refreshLayout
     */
    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        page = 1;
        getData(page);
    }
    /**************************************************************************************/
    /**
     * 重写此方法即可实现带页码刷新
     *
     * @param page 页码
     */
    protected void getData(int page) {
        getData();
    }

    /**
     * 刷新数据执行
     */
    protected void getData() {

    }

    /**************************************************************************************/

    protected Activity getActivity() {
        return this;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        LoadingDialogUtils.getUtils().dismissDialog();

        ActivitiesManager.getInstance().popCurrentActivity(this);

        OKHttpManager.cancelTag(this);

    }


    /**
     * 清理临时压缩的图片和语音
     */
    public void deleteCacheDir() {
        try {
            File file = new File(Environment.getExternalStorageDirectory(), AppConstants.SD_PATH);
            deleteDirWihtFile(file);
        } catch (Exception e) {
//            e.printStackTrace();
        }

    }

    private void deleteDirWihtFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
            else if (file.isDirectory())
                deleteDirWihtFile(file); // 递规的方式删除文件夹
        }
        dir.delete();// 删除目录本身
    }

    protected void hideSoftKeyboard() {
        if (getWindow().getAttributes().softInputMode != WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN) {
            if (getCurrentFocus() != null)
                inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(),
                        InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    /**
     * 下拉刷新 上拉加载完成后的动作
     *
     * @param respSize  服务器返回的List长度
     * @param limitSize 设置的每次请求条数
     */
    public void loadOrRefreshComplete(int respSize, int limitSize) {
        if (mRefreshLayout != null) {
            //如果正在下拉刷新,停止刷新
            if (mRefreshLayout.getState() == RefreshState.Refreshing) {
                mRefreshLayout.finishRefresh();
            }
            //如果正在上拉加载,停止加载
            if (mRefreshLayout.getState() == RefreshState.Loading) {
                mRefreshLayout.finishLoadMore();
            }
            //如果服务器返回数据小于设置的请求数,说明此次请求已取出最后所有数据,不必再上拉加载
            if (respSize < limitSize) {
                mRefreshLayout.setEnableLoadMore(false);
            } else if (!mRefreshLayout.isEnableLoadMore()) {
                mRefreshLayout.setEnableLoadMore(true);
            }
        }
        if (respSize == 0 && page == 1 && mErrorView != null) {
            showError(getResources().getString(R.string.no_data));
        } else if (mErrorView != null && mErrorView.getVisibility() == View.VISIBLE) {
            mErrorView.setVisibility(View.GONE);
        }
    }

    protected GridLayoutManager getGridlayoutManger(int clumns) {
        return new GridLayoutManager(getActivity(), clumns);
    }


    public void setAndroidNativeLightStatusBar(Activity activity, boolean dark) {
        View decor = activity.getWindow().getDecorView();
        if (dark) {
            decor.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        } else {
            decor.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
//        MobclickAgent.onResume(this);
//        Log.e("这是广告", "判断 :"+foregroundTime+"  "+TAG);
////        if (foregroundTime!=0&&System.currentTimeMillis()-foregroundTime>(5*60*1000)){
//        if (foregroundTime!=0&&(System.currentTimeMillis()-foregroundTime)>0){
//            Log.e("这是广告", "执行广告"+foregroundTime);
//            foregroundTime=0;
//            showToast("判断 "+foregroundTime+" "+TAG);
//            System.exit(0);
////            showAD();
//        }
//        Log.e("这是广告", "onResume: " + AppApplication.getInstance().foregroundCount + "   页面 :" + TAG);
        if (!AppApplication.getInstance().foregroundName.equals(TAG)) {
            AppApplication.getInstance().foregroundName = TAG;

        }
    }

    @Override
    protected void onPause() {
        super.onPause();
//        MobclickAgent.onPause(this);
    }
    //    /**
//     * 获取统一GridLayoutManger
//     *
//     * @return
//     */
//    protected GridLayoutManager getGridlayoutManger() {
//        boolean phone = LayoutUtil.isPhone();
//        boolean screenPortrait = LayoutUtil.isScreenPortrait();
//
//        if (phone) {
//            return new GridLayoutManager(getActivity(), 1);
//        }
//        return new GridLayoutManager(getActivity(), screenPortrait ? 2 : 3);
//
//    }


    @Override
    protected void onStop() {
        super.onStop();
//        Log.e("这是广告", "onStop: "+AppApplication.getInstance().foregroundCount +"   页面 :" +TAG );

//        Log.e("这是广告", "onStop: " + AppApplication.getInstance().foregroundCount + "   页面 :" + TAG);
        if (AppApplication.getInstance().foregroundCount <= 0) {
        AppApplication.getInstance().exit();
        }
    }

    /**
     * 判断是否是前台应用
     *
     * @return
     */
    private boolean appOnForeground() {
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = this.getPackageName();
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();

        if (appProcesses == null)
            return false;

        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(packageName) && appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                return true;
            }
        }

        return false;
    }
    @Override
    public void applyOverrideConfiguration(Configuration overrideConfiguration) {
        // 兼容androidX在部分手机切换语言失败问题
        if (overrideConfiguration != null) {
            int uiMode = overrideConfiguration.uiMode;
            overrideConfiguration.setTo(getBaseContext().getResources().getConfiguration());
            overrideConfiguration.uiMode = uiMode;
        }
        super.applyOverrideConfiguration(overrideConfiguration);
    }

    @Override
    protected void attachBaseContext(Context newBase) {
//        Log.e(TAG, "attachBaseContext");
        super.attachBaseContext(LanguageUtils.attachBaseContext(newBase));
        //app杀进程启动后会调用Activity attachBaseContext
        LanguageUtils.getInstance().setConfiguration(newBase);
    }
}
