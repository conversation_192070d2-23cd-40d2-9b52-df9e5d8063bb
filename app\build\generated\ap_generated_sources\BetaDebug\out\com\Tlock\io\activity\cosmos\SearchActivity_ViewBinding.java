// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class SearchActivity_ViewBinding implements Unbinder {
  private SearchActivity target;

  private View view7f090133;

  private View view7f090137;

  private View view7f090138;

  @UiThread
  public SearchActivity_ViewBinding(SearchActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public SearchActivity_ViewBinding(final SearchActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_back, "field 'mIvBack' and method 'onBindClick'");
    target.mIvBack = Utils.castView(view, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    view7f090133 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mEdSearch = Utils.findRequiredViewAsType(source, R.id.ed_search, "field 'mEdSearch'", EditText.class);
    view = Utils.findRequiredView(source, R.id.iv_clear, "field 'mIvClear' and method 'onBindClick'");
    target.mIvClear = Utils.castView(view, R.id.iv_clear, "field 'mIvClear'", ImageView.class);
    view7f090137 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_clear_history, "field 'mIvClearHistory' and method 'onBindClick'");
    target.mIvClearHistory = Utils.castView(view, R.id.iv_clear_history, "field 'mIvClearHistory'", ImageView.class);
    view7f090138 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLlHistory = Utils.findRequiredViewAsType(source, R.id.ll_history, "field 'mLlHistory'", RelativeLayout.class);
    target.mRvHistory = Utils.findRequiredViewAsType(source, R.id.rv_history, "field 'mRvHistory'", RecyclerView.class);
    target.mRvUser = Utils.findRequiredViewAsType(source, R.id.rv_user, "field 'mRvUser'", RecyclerView.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mRvTopic = Utils.findRequiredViewAsType(source, R.id.rv_topic, "field 'mRvTopic'", RecyclerView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    SearchActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mEdSearch = null;
    target.mIvClear = null;
    target.mIvClearHistory = null;
    target.mLlHistory = null;
    target.mRvHistory = null;
    target.mRvUser = null;
    target.mLine1 = null;
    target.mRvTopic = null;

    view7f090133.setOnClickListener(null);
    view7f090133 = null;
    view7f090137.setOnClickListener(null);
    view7f090137 = null;
    view7f090138.setOnClickListener(null);
    view7f090138 = null;
  }
}
