package com.Tlock.io.callback;

import android.app.Activity;
import android.app.Application;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

public class AdapterFullScreenCallback implements Application.ActivityLifecycleCallbacks {
    private List<Class<? extends Activity>> adapterActivites = new ArrayList<>();

    public AdapterFullScreenCallback addAdapterActivity(Activity activity) {
        adapterActivites.add(activity.getClass());
        return this;
    }

    public AdapterFullScreenCallback addAdapterActivity(String className) {
        try {
            adapterActivites.add((Class<? extends Activity>) Class.forName(className));
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return this;
    }
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        for(final Class<? extends Activity> activityClass : adapterActivites) {
            if(activityClass.isAssignableFrom(activity.getClass())) {
                hideSystemUI(activity);
                return;
            }
        }
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
    }
    @Override
    public void onActivityPaused(@NonNull Activity activity) {
    }
    @Override
    public void onActivityStopped(@NonNull Activity activity) {
    }
    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
    }
    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
    }

    private void hideSystemUI(Activity activity) {
        int systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                View.SYSTEM_UI_FLAG_FULLSCREEN |
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            systemUiVisibility |= View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
        } else {
            systemUiVisibility |= View.SYSTEM_UI_FLAG_LOW_PROFILE;
        }
        Window window = activity.getWindow();
        window.getDecorView().setSystemUiVisibility(systemUiVisibility);
        // 五要素隐私详情页或五要素弹窗关闭回到开屏广告时，再次设置SystemUi
//        window.getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> setSystemUi());

        // Android P 官方方法
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            window.setAttributes(params);
        }
    }

}
