package com.Tlock.io.profile;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * Query provides defines the gRPC querier service.
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.57.0)",
    comments = "Source: profile/profile_query.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class QueryGrpc {

  private QueryGrpc() {}

  public static final java.lang.String SERVICE_NAME = "profile.v1.Query";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse> getQueryProfileMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryProfile",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse> getQueryProfileMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest, com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse> getQueryProfileMethod;
    if ((getQueryProfileMethod = QueryGrpc.getQueryProfileMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryProfileMethod = QueryGrpc.getQueryProfileMethod) == null) {
          QueryGrpc.getQueryProfileMethod = getQueryProfileMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest, com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryProfile"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryProfile"))
              .build();
        }
      }
    }
    return getQueryProfileMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse> getQueryFollowingMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryFollowing",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse> getQueryFollowingMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest, com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse> getQueryFollowingMethod;
    if ((getQueryFollowingMethod = QueryGrpc.getQueryFollowingMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryFollowingMethod = QueryGrpc.getQueryFollowingMethod) == null) {
          QueryGrpc.getQueryFollowingMethod = getQueryFollowingMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest, com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryFollowing"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryFollowing"))
              .build();
        }
      }
    }
    return getQueryFollowingMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse> getQueryFollowersMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryFollowers",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse> getQueryFollowersMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest, com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse> getQueryFollowersMethod;
    if ((getQueryFollowersMethod = QueryGrpc.getQueryFollowersMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryFollowersMethod = QueryGrpc.getQueryFollowersMethod) == null) {
          QueryGrpc.getQueryFollowersMethod = getQueryFollowersMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest, com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryFollowers"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryFollowers"))
              .build();
        }
      }
    }
    return getQueryFollowersMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse> getQueryActivitiesReceivedCountMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryActivitiesReceivedCount",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse> getQueryActivitiesReceivedCountMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest, com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse> getQueryActivitiesReceivedCountMethod;
    if ((getQueryActivitiesReceivedCountMethod = QueryGrpc.getQueryActivitiesReceivedCountMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryActivitiesReceivedCountMethod = QueryGrpc.getQueryActivitiesReceivedCountMethod) == null) {
          QueryGrpc.getQueryActivitiesReceivedCountMethod = getQueryActivitiesReceivedCountMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest, com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryActivitiesReceivedCount"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryActivitiesReceivedCount"))
              .build();
        }
      }
    }
    return getQueryActivitiesReceivedCountMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest,
      com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse> getSearchUsersMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SearchUsers",
      requestType = com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest,
      com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse> getSearchUsersMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest, com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse> getSearchUsersMethod;
    if ((getSearchUsersMethod = QueryGrpc.getSearchUsersMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getSearchUsersMethod = QueryGrpc.getSearchUsersMethod) == null) {
          QueryGrpc.getSearchUsersMethod = getSearchUsersMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest, com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SearchUsers"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("SearchUsers"))
              .build();
        }
      }
    }
    return getSearchUsersMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse> getQueryFollowRelationshipMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryFollowRelationship",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse> getQueryFollowRelationshipMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest, com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse> getQueryFollowRelationshipMethod;
    if ((getQueryFollowRelationshipMethod = QueryGrpc.getQueryFollowRelationshipMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryFollowRelationshipMethod = QueryGrpc.getQueryFollowRelationshipMethod) == null) {
          QueryGrpc.getQueryFollowRelationshipMethod = getQueryFollowRelationshipMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest, com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryFollowRelationship"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryFollowRelationship"))
              .build();
        }
      }
    }
    return getQueryFollowRelationshipMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse> getQueryProfileAvatarMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "QueryProfileAvatar",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse> getQueryProfileAvatarMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest, com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse> getQueryProfileAvatarMethod;
    if ((getQueryProfileAvatarMethod = QueryGrpc.getQueryProfileAvatarMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getQueryProfileAvatarMethod = QueryGrpc.getQueryProfileAvatarMethod) == null) {
          QueryGrpc.getQueryProfileAvatarMethod = getQueryProfileAvatarMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest, com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "QueryProfileAvatar"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("QueryProfileAvatar"))
              .build();
        }
      }
    }
    return getQueryProfileAvatarMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse> getGetMentionSuggestionsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetMentionSuggestions",
      requestType = com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.class,
      responseType = com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest,
      com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse> getGetMentionSuggestionsMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest, com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse> getGetMentionSuggestionsMethod;
    if ((getGetMentionSuggestionsMethod = QueryGrpc.getGetMentionSuggestionsMethod) == null) {
      synchronized (QueryGrpc.class) {
        if ((getGetMentionSuggestionsMethod = QueryGrpc.getGetMentionSuggestionsMethod) == null) {
          QueryGrpc.getGetMentionSuggestionsMethod = getGetMentionSuggestionsMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest, com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetMentionSuggestions"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new QueryMethodDescriptorSupplier("GetMentionSuggestions"))
              .build();
        }
      }
    }
    return getGetMentionSuggestionsMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static QueryStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<QueryStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<QueryStub>() {
        @java.lang.Override
        public QueryStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new QueryStub(channel, callOptions);
        }
      };
    return QueryStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static QueryBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<QueryBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<QueryBlockingStub>() {
        @java.lang.Override
        public QueryBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new QueryBlockingStub(channel, callOptions);
        }
      };
    return QueryBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static QueryFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<QueryFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<QueryFutureStub>() {
        @java.lang.Override
        public QueryFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new QueryFutureStub(channel, callOptions);
        }
      };
    return QueryFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * Query provides defines the gRPC querier service.
   * </pre>
   */
  public interface AsyncService {

    /**
     * <pre>
     * Params queries all parameters of the module.
     * </pre>
     */
    default void queryProfile(com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryProfileMethod(), responseObserver);
    }

    /**
     */
    default void queryFollowing(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryFollowingMethod(), responseObserver);
    }

    /**
     */
    default void queryFollowers(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryFollowersMethod(), responseObserver);
    }

    /**
     */
    default void queryActivitiesReceivedCount(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryActivitiesReceivedCountMethod(), responseObserver);
    }

    /**
     */
    default void searchUsers(com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSearchUsersMethod(), responseObserver);
    }

    /**
     */
    default void queryFollowRelationship(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryFollowRelationshipMethod(), responseObserver);
    }

    /**
     */
    default void queryProfileAvatar(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryProfileAvatarMethod(), responseObserver);
    }

    /**
     */
    default void getMentionSuggestions(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetMentionSuggestionsMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service Query.
   * <pre>
   * Query provides defines the gRPC querier service.
   * </pre>
   */
  public static abstract class QueryImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return QueryGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service Query.
   * <pre>
   * Query provides defines the gRPC querier service.
   * </pre>
   */
  public static final class QueryStub
      extends io.grpc.stub.AbstractAsyncStub<QueryStub> {
    private QueryStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected QueryStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new QueryStub(channel, callOptions);
    }

    /**
     * <pre>
     * Params queries all parameters of the module.
     * </pre>
     */
    public void queryProfile(com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryProfileMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryFollowing(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryFollowingMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryFollowers(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryFollowersMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryActivitiesReceivedCount(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryActivitiesReceivedCountMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void searchUsers(com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSearchUsersMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryFollowRelationship(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryFollowRelationshipMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryProfileAvatar(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryProfileAvatarMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void getMentionSuggestions(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetMentionSuggestionsMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service Query.
   * <pre>
   * Query provides defines the gRPC querier service.
   * </pre>
   */
  public static final class QueryBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<QueryBlockingStub> {
    private QueryBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected QueryBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new QueryBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * Params queries all parameters of the module.
     * </pre>
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse queryProfile(com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryProfileMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse queryFollowing(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryFollowingMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse queryFollowers(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryFollowersMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse queryActivitiesReceivedCount(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryActivitiesReceivedCountMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse searchUsers(com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSearchUsersMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse queryFollowRelationship(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryFollowRelationshipMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse queryProfileAvatar(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryProfileAvatarMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse getMentionSuggestions(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetMentionSuggestionsMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service Query.
   * <pre>
   * Query provides defines the gRPC querier service.
   * </pre>
   */
  public static final class QueryFutureStub
      extends io.grpc.stub.AbstractFutureStub<QueryFutureStub> {
    private QueryFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected QueryFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new QueryFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * Params queries all parameters of the module.
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse> queryProfile(
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryProfileMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse> queryFollowing(
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryFollowingMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse> queryFollowers(
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryFollowersMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse> queryActivitiesReceivedCount(
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryActivitiesReceivedCountMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse> searchUsers(
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSearchUsersMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse> queryFollowRelationship(
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryFollowRelationshipMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse> queryProfileAvatar(
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryProfileAvatarMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse> getMentionSuggestions(
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetMentionSuggestionsMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_QUERY_PROFILE = 0;
  private static final int METHODID_QUERY_FOLLOWING = 1;
  private static final int METHODID_QUERY_FOLLOWERS = 2;
  private static final int METHODID_QUERY_ACTIVITIES_RECEIVED_COUNT = 3;
  private static final int METHODID_SEARCH_USERS = 4;
  private static final int METHODID_QUERY_FOLLOW_RELATIONSHIP = 5;
  private static final int METHODID_QUERY_PROFILE_AVATAR = 6;
  private static final int METHODID_GET_MENTION_SUGGESTIONS = 7;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_QUERY_PROFILE:
          serviceImpl.queryProfile((com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse>) responseObserver);
          break;
        case METHODID_QUERY_FOLLOWING:
          serviceImpl.queryFollowing((com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse>) responseObserver);
          break;
        case METHODID_QUERY_FOLLOWERS:
          serviceImpl.queryFollowers((com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse>) responseObserver);
          break;
        case METHODID_QUERY_ACTIVITIES_RECEIVED_COUNT:
          serviceImpl.queryActivitiesReceivedCount((com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse>) responseObserver);
          break;
        case METHODID_SEARCH_USERS:
          serviceImpl.searchUsers((com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse>) responseObserver);
          break;
        case METHODID_QUERY_FOLLOW_RELATIONSHIP:
          serviceImpl.queryFollowRelationship((com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse>) responseObserver);
          break;
        case METHODID_QUERY_PROFILE_AVATAR:
          serviceImpl.queryProfileAvatar((com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse>) responseObserver);
          break;
        case METHODID_GET_MENTION_SUGGESTIONS:
          serviceImpl.getMentionSuggestions((com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getQueryProfileMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse>(
                service, METHODID_QUERY_PROFILE)))
        .addMethod(
          getQueryFollowingMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse>(
                service, METHODID_QUERY_FOLLOWING)))
        .addMethod(
          getQueryFollowersMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse>(
                service, METHODID_QUERY_FOLLOWERS)))
        .addMethod(
          getQueryActivitiesReceivedCountMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse>(
                service, METHODID_QUERY_ACTIVITIES_RECEIVED_COUNT)))
        .addMethod(
          getSearchUsersMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest,
              com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse>(
                service, METHODID_SEARCH_USERS)))
        .addMethod(
          getQueryFollowRelationshipMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse>(
                service, METHODID_QUERY_FOLLOW_RELATIONSHIP)))
        .addMethod(
          getQueryProfileAvatarMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse>(
                service, METHODID_QUERY_PROFILE_AVATAR)))
        .addMethod(
          getGetMentionSuggestionsMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest,
              com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse>(
                service, METHODID_GET_MENTION_SUGGESTIONS)))
        .build();
  }

  private static abstract class QueryBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    QueryBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Query");
    }
  }

  private static final class QueryFileDescriptorSupplier
      extends QueryBaseDescriptorSupplier {
    QueryFileDescriptorSupplier() {}
  }

  private static final class QueryMethodDescriptorSupplier
      extends QueryBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    QueryMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (QueryGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new QueryFileDescriptorSupplier())
              .addMethod(getQueryProfileMethod())
              .addMethod(getQueryFollowingMethod())
              .addMethod(getQueryFollowersMethod())
              .addMethod(getQueryActivitiesReceivedCountMethod())
              .addMethod(getSearchUsersMethod())
              .addMethod(getQueryFollowRelationshipMethod())
              .addMethod(getQueryProfileAvatarMethod())
              .addMethod(getGetMentionSuggestionsMethod())
              .build();
        }
      }
    }
    return result;
  }
}
