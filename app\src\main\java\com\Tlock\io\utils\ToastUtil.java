package com.Tlock.io.utils;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;


/**
 * 本类的主要功能是 :  吐司
 */
public class ToastUtil {
    private static Toast toast;
    private static View toastView;
    private static long lastShowTime = 0;
    private static final int MIN_INTERVAL = 2000; // 2秒间隔

    public static void toastShortCenter(Context context, String msg) {
        if (TextUtils.isEmpty(msg)) {
            msg = "";
        }
        if (toast == null) {
            toast = Toast.makeText(context, msg, Toast.LENGTH_SHORT);
        } else {
            toast.cancel();
            toast = Toast.makeText(context, msg, Toast.LENGTH_SHORT);
        }
//
//        toast.setGravity(Gravity.CENTER, 0, 0);
//        toast.show();
        toastView(msg);
    }

    public static void toastLongCenter(Context context, String msg) {
        if (TextUtils.isEmpty(msg)) {
            msg = "";
        }
        if (toast == null) {
            toast = Toast.makeText(context, msg, Toast.LENGTH_LONG);
        } else {
            toast.cancel();
            toast = Toast.makeText(context, msg, Toast.LENGTH_LONG);
        }
        toast.setGravity(Gravity.CENTER, 0, 0);
        toast.show();
    }

    /**
     * 自定义吐司布局
     *
     * @param message
     */
    public static void toastView(String message) {
        if (TextUtils.isEmpty(message)) return;
        if (System.currentTimeMillis() - lastShowTime > MIN_INTERVAL) {
            if (toastView == null) {
                toastView = LayoutInflater.from(AppApplication.getInstance()).inflate(R.layout.layout_toast, null);
            }
            TextView textView = toastView.findViewById(R.id.toast_msg);
            textView.setText(message);
            Toast toast = new Toast(AppApplication.getInstance().getApplicationContext());
            toast.setView(toastView);
            toast.setGravity(Gravity.TOP, 0, 250);
            toast.show();
            lastShowTime = System.currentTimeMillis();

        }

    }


}
