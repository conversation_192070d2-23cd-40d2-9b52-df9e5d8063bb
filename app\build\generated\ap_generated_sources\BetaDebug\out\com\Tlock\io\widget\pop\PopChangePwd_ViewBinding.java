// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopChangePwd_ViewBinding implements Unbinder {
  private PopChangePwd target;

  private View view7f090311;

  private View view7f09013f;

  @UiThread
  public PopChangePwd_ViewBinding(PopChangePwd target) {
    this(target, target);
  }

  @UiThread
  public PopChangePwd_ViewBinding(final PopChangePwd target, View source) {
    this.target = target;

    View view;
    target.mEdOldPwd = Utils.findRequiredViewAsType(source, R.id.ed_old_pwd, "field 'mEdOldPwd'", EditText.class);
    target.mEdNewPwd = Utils.findRequiredViewAsType(source, R.id.ed_new_pwd, "field 'mEdNewPwd'", EditText.class);
    target.mIvNewSet = Utils.findRequiredViewAsType(source, R.id.iv_new_set, "field 'mIvNewSet'", ImageView.class);
    target.mRlNew = Utils.findRequiredViewAsType(source, R.id.rl_new, "field 'mRlNew'", RelativeLayout.class);
    target.mEdCheckPwd = Utils.findRequiredViewAsType(source, R.id.ed_check_pwd, "field 'mEdCheckPwd'", EditText.class);
    target.mIvCheckSet = Utils.findRequiredViewAsType(source, R.id.iv_check_set, "field 'mIvCheckSet'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.tv_confirm, "method 'onViewClicked'");
    view7f090311 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_close, "method 'onViewClicked'");
    view7f09013f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopChangePwd target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mEdOldPwd = null;
    target.mEdNewPwd = null;
    target.mIvNewSet = null;
    target.mRlNew = null;
    target.mEdCheckPwd = null;
    target.mIvCheckSet = null;

    view7f090311.setOnClickListener(null);
    view7f090311 = null;
    view7f09013f.setOnClickListener(null);
    view7f09013f = null;
  }
}
