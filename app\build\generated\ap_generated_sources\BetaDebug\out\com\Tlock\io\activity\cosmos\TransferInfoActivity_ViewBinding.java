// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TransferInfoActivity_ViewBinding implements Unbinder {
  private TransferInfoActivity target;

  private View view7f09014e;

  private View view7f090178;

  private View view7f09030c;

  private View view7f090312;

  private View view7f090150;

  @UiThread
  public TransferInfoActivity_ViewBinding(TransferInfoActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public TransferInfoActivity_ViewBinding(final TransferInfoActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvSend = Utils.findRequiredViewAsType(source, R.id.iv_send, "field 'mIvSend'", ImageView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv1, "field 'mTv1'", TextView.class);
    target.mTvCount = Utils.findRequiredViewAsType(source, R.id.tv_count, "field 'mTvCount'", TextView.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mTvGasTitle = Utils.findRequiredViewAsType(source, R.id.tv_gas_title, "field 'mTvGasTitle'", TextView.class);
    target.mTvGas = Utils.findRequiredViewAsType(source, R.id.tv_gas, "field 'mTvGas'", TextView.class);
    target.mTvFromTitle = Utils.findRequiredViewAsType(source, R.id.tv_From_title, "field 'mTvFromTitle'", TextView.class);
    target.mTvFrom = Utils.findRequiredViewAsType(source, R.id.tv_from, "field 'mTvFrom'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_from, "field 'mIvFrom' and method 'onBindClick'");
    target.mIvFrom = Utils.castView(view, R.id.iv_from, "field 'mIvFrom'", ImageView.class);
    view7f09014e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvToTitle = Utils.findRequiredViewAsType(source, R.id.tv_to_title, "field 'mTvToTitle'", TextView.class);
    target.mTvTo = Utils.findRequiredViewAsType(source, R.id.tv_to, "field 'mTvTo'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_to, "field 'mIvTo' and method 'onBindClick'");
    target.mIvTo = Utils.castView(view, R.id.iv_to, "field 'mIvTo'", ImageView.class);
    view7f090178 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_cancel, "field 'mTvCancel' and method 'onBindClick'");
    target.mTvCancel = Utils.castView(view, R.id.tv_cancel, "field 'mTvCancel'", TextView.class);
    view7f09030c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onBindClick'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f090312 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLlBtn = Utils.findRequiredViewAsType(source, R.id.ll_btn, "field 'mLlBtn'", LinearLayout.class);
    target.mTvHashTitle = Utils.findRequiredViewAsType(source, R.id.tv_hash_title, "field 'mTvHashTitle'", TextView.class);
    target.mTvHash = Utils.findRequiredViewAsType(source, R.id.tv_hash, "field 'mTvHash'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_hash, "field 'mIvHash' and method 'onBindClick'");
    target.mIvHash = Utils.castView(view, R.id.iv_hash, "field 'mIvHash'", ImageView.class);
    view7f090150 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TransferInfoActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvSend = null;
    target.mTv1 = null;
    target.mTvCount = null;
    target.mLine1 = null;
    target.mTvGasTitle = null;
    target.mTvGas = null;
    target.mTvFromTitle = null;
    target.mTvFrom = null;
    target.mIvFrom = null;
    target.mTvToTitle = null;
    target.mTvTo = null;
    target.mIvTo = null;
    target.mTvCancel = null;
    target.mTvConfirm = null;
    target.mLlBtn = null;
    target.mTvHashTitle = null;
    target.mTvHash = null;
    target.mIvHash = null;
    target.mTvTime = null;

    view7f09014e.setOnClickListener(null);
    view7f09014e = null;
    view7f090178.setOnClickListener(null);
    view7f090178 = null;
    view7f09030c.setOnClickListener(null);
    view7f09030c = null;
    view7f090312.setOnClickListener(null);
    view7f090312 = null;
    view7f090150.setOnClickListener(null);
    view7f090150 = null;
  }
}
