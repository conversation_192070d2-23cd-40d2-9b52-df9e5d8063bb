<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_16"
    android:paddingTop="@dimen/dp_10"
    android:paddingBottom="@dimen/dp_20">

    <View
        android:id="@+id/line1"
        android:layout_width="46dp"
        android:layout_height="5dp"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/rounded_corner_60" />

<!--    <TextView-->
<!--        android:id="@+id/tv_list_title"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_below="@id/line1"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_centerInParent="true"-->
<!--        android:text="Share post"-->
<!--        android:layout_marginTop="@dimen/dp_15"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="@dimen/sp_16" />-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/line1"
        android:layout_marginTop="28dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_copy_link"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableTop="@mipmap/icon_copy_url0"
            android:drawablePadding="@dimen/dp_8"
            android:gravity="center"
            android:text="Copy link"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tv_share_tg"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableTop="@mipmap/icon_tg"
            android:drawablePadding="@dimen/dp_8"
            android:gravity="center"
            android:text="Telegram"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tv_share_x"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableTop="@mipmap/icon_x"
            android:drawablePadding="@dimen/dp_8"
            android:gravity="center"
            android:text="X"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

</RelativeLayout>
