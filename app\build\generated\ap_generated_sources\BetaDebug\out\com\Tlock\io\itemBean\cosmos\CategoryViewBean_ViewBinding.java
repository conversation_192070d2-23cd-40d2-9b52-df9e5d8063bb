// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CategoryViewBean_ViewBinding implements Unbinder {
  private CategoryViewBean target;

  @UiThread
  public CategoryViewBean_ViewBinding(CategoryViewBean target) {
    this(target, target);
  }

  @UiThread
  public CategoryViewBean_ViewBinding(CategoryViewBean target, View source) {
    this.target = target;

    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mRlRoot = Utils.findRequiredViewAsType(source, R.id.rl_root, "field 'mRlRoot'", RelativeLayout.class);
    target.mTvIndex = Utils.findRequiredViewAsType(source, R.id.tv_index, "field 'mTvIndex'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    CategoryViewBean target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mRlRoot = null;
    target.mTvIndex = null;
  }
}
