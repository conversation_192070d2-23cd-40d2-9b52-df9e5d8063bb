package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.itemBean.cosmos.PostTopicItemView;
import com.Tlock.io.widget.CustomNavBar;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;


public class MMemberListActivity extends BaseActivity {
    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.rv_account)
    RecyclerView mRvAccount;
    @BindView(R.id.tv_add_account)
    TextView mTvAddAccount;

    private BaseRecyclerViewAdapter<ProfileProto.Profile> adapter;
    private ArrayList<ProfileProto.Profile> pairList = new ArrayList<>();
    private String address;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String address
    ) {
        Intent intent = new Intent(context, MMemberListActivity.class);
        intent.putExtra("address", address);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_member_list;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        address = getIntent().getStringExtra("address");
        initRecyclerView();

    }

    private void initRecyclerView() {
        mRvAccount.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), pairList, new BaseRecyclerViewAdapter.Delegate<ProfileProto.Profile>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new PostTopicItemView(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, ProfileProto.Profile data, View view) {
                PostTopicItemView viewBean = (PostTopicItemView) view;
                viewBean.setData(data);
            }
        });
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<ProfileProto.Profile>() {
            @Override
            public void onItemClick(int position, ProfileProto.Profile data, View view) {
                MMemberListActivity.start(getActivity(), address);
            }
        });
        mRvAccount.setAdapter(adapter);
    }

    @Override
    protected void loadData() {

    }


    @OnClick({R.id.tv_add_account})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_add_account:


                break;
        }
    }
}