package com.Tlock.io.entity.wallet;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TRANSFER".
*/
public class TransferDao extends AbstractDao<Transfer, Long> {

    public static final String TABLENAME = "TRANSFER";

    /**
     * Properties of entity Transfer.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property TokenId = new Property(1, Long.class, "tokenId", false, "tokenId");
        public final static Property GetAddress = new Property(2, String.class, "getAddress", false, "GET_ADDRESS");
        public final static Property TokenAddress = new Property(3, String.class, "tokenAddress", false, "TOKEN_ADDRESS");
        public final static Property Name = new Property(4, String.class, "name", false, "NAME");
        public final static Property Symbol = new Property(5, String.class, "symbol", false, "SYMBOL");
        public final static Property ImgUrl = new Property(6, String.class, "imgUrl", false, "IMG_URL");
        public final static Property Decimals = new Property(7, int.class, "decimals", false, "DECIMALS");
        public final static Property LogIndex = new Property(8, int.class, "logIndex", false, "LOG_INDEX");
        public final static Property Count = new Property(9, String.class, "count", false, "COUNT");
        public final static Property PayAddress = new Property(10, String.class, "payAddress", false, "PAY_ADDRESS");
        public final static Property Time = new Property(11, String.class, "time", false, "TIME");
        public final static Property GasPrice = new Property(12, String.class, "gasPrice", false, "GAS_PRICE");
        public final static Property GasLimit = new Property(13, String.class, "gasLimit", false, "GAS_LIMIT");
        public final static Property Miners = new Property(14, String.class, "miners", false, "MINERS");
        public final static Property TokenBalance = new Property(15, String.class, "tokenBalance", false, "TOKEN_BALANCE");
        public final static Property TransferHash = new Property(16, String.class, "transferHash", false, "TRANSFER_HASH");
        public final static Property PayStatus = new Property(17, int.class, "payStatus", false, "PAY_STATUS");
        public final static Property Status = new Property(18, boolean.class, "status", false, "STATUS");
    }


    public TransferDao(DaoConfig config) {
        super(config);
    }
    
    public TransferDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TRANSFER\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"tokenId\" INTEGER," + // 1: tokenId
                "\"GET_ADDRESS\" TEXT," + // 2: getAddress
                "\"TOKEN_ADDRESS\" TEXT," + // 3: tokenAddress
                "\"NAME\" TEXT," + // 4: name
                "\"SYMBOL\" TEXT," + // 5: symbol
                "\"IMG_URL\" TEXT," + // 6: imgUrl
                "\"DECIMALS\" INTEGER NOT NULL ," + // 7: decimals
                "\"LOG_INDEX\" INTEGER NOT NULL ," + // 8: logIndex
                "\"COUNT\" TEXT," + // 9: count
                "\"PAY_ADDRESS\" TEXT," + // 10: payAddress
                "\"TIME\" TEXT," + // 11: time
                "\"GAS_PRICE\" TEXT," + // 12: gasPrice
                "\"GAS_LIMIT\" TEXT," + // 13: gasLimit
                "\"MINERS\" TEXT," + // 14: miners
                "\"TOKEN_BALANCE\" TEXT," + // 15: tokenBalance
                "\"TRANSFER_HASH\" TEXT," + // 16: transferHash
                "\"PAY_STATUS\" INTEGER NOT NULL ," + // 17: payStatus
                "\"STATUS\" INTEGER NOT NULL );"); // 18: status
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TRANSFER\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, Transfer entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        Long tokenId = entity.getTokenId();
        if (tokenId != null) {
            stmt.bindLong(2, tokenId);
        }
 
        String getAddress = entity.getGetAddress();
        if (getAddress != null) {
            stmt.bindString(3, getAddress);
        }
 
        String tokenAddress = entity.getTokenAddress();
        if (tokenAddress != null) {
            stmt.bindString(4, tokenAddress);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(5, name);
        }
 
        String symbol = entity.getSymbol();
        if (symbol != null) {
            stmt.bindString(6, symbol);
        }
 
        String imgUrl = entity.getImgUrl();
        if (imgUrl != null) {
            stmt.bindString(7, imgUrl);
        }
        stmt.bindLong(8, entity.getDecimals());
        stmt.bindLong(9, entity.getLogIndex());
 
        String count = entity.getCount();
        if (count != null) {
            stmt.bindString(10, count);
        }
 
        String payAddress = entity.getPayAddress();
        if (payAddress != null) {
            stmt.bindString(11, payAddress);
        }
 
        String time = entity.getTime();
        if (time != null) {
            stmt.bindString(12, time);
        }
 
        String gasPrice = entity.getGasPrice();
        if (gasPrice != null) {
            stmt.bindString(13, gasPrice);
        }
 
        String gasLimit = entity.getGasLimit();
        if (gasLimit != null) {
            stmt.bindString(14, gasLimit);
        }
 
        String miners = entity.getMiners();
        if (miners != null) {
            stmt.bindString(15, miners);
        }
 
        String tokenBalance = entity.getTokenBalance();
        if (tokenBalance != null) {
            stmt.bindString(16, tokenBalance);
        }
 
        String transferHash = entity.getTransferHash();
        if (transferHash != null) {
            stmt.bindString(17, transferHash);
        }
        stmt.bindLong(18, entity.getPayStatus());
        stmt.bindLong(19, entity.getStatus() ? 1L: 0L);
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, Transfer entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        Long tokenId = entity.getTokenId();
        if (tokenId != null) {
            stmt.bindLong(2, tokenId);
        }
 
        String getAddress = entity.getGetAddress();
        if (getAddress != null) {
            stmt.bindString(3, getAddress);
        }
 
        String tokenAddress = entity.getTokenAddress();
        if (tokenAddress != null) {
            stmt.bindString(4, tokenAddress);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(5, name);
        }
 
        String symbol = entity.getSymbol();
        if (symbol != null) {
            stmt.bindString(6, symbol);
        }
 
        String imgUrl = entity.getImgUrl();
        if (imgUrl != null) {
            stmt.bindString(7, imgUrl);
        }
        stmt.bindLong(8, entity.getDecimals());
        stmt.bindLong(9, entity.getLogIndex());
 
        String count = entity.getCount();
        if (count != null) {
            stmt.bindString(10, count);
        }
 
        String payAddress = entity.getPayAddress();
        if (payAddress != null) {
            stmt.bindString(11, payAddress);
        }
 
        String time = entity.getTime();
        if (time != null) {
            stmt.bindString(12, time);
        }
 
        String gasPrice = entity.getGasPrice();
        if (gasPrice != null) {
            stmt.bindString(13, gasPrice);
        }
 
        String gasLimit = entity.getGasLimit();
        if (gasLimit != null) {
            stmt.bindString(14, gasLimit);
        }
 
        String miners = entity.getMiners();
        if (miners != null) {
            stmt.bindString(15, miners);
        }
 
        String tokenBalance = entity.getTokenBalance();
        if (tokenBalance != null) {
            stmt.bindString(16, tokenBalance);
        }
 
        String transferHash = entity.getTransferHash();
        if (transferHash != null) {
            stmt.bindString(17, transferHash);
        }
        stmt.bindLong(18, entity.getPayStatus());
        stmt.bindLong(19, entity.getStatus() ? 1L: 0L);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public Transfer readEntity(Cursor cursor, int offset) {
        Transfer entity = new Transfer( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getLong(offset + 1), // tokenId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // getAddress
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // tokenAddress
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // name
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // symbol
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // imgUrl
            cursor.getInt(offset + 7), // decimals
            cursor.getInt(offset + 8), // logIndex
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // count
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // payAddress
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // time
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // gasPrice
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // gasLimit
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // miners
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // tokenBalance
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // transferHash
            cursor.getInt(offset + 17), // payStatus
            cursor.getShort(offset + 18) != 0 // status
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, Transfer entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setTokenId(cursor.isNull(offset + 1) ? null : cursor.getLong(offset + 1));
        entity.setGetAddress(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setTokenAddress(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setName(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setSymbol(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setImgUrl(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setDecimals(cursor.getInt(offset + 7));
        entity.setLogIndex(cursor.getInt(offset + 8));
        entity.setCount(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setPayAddress(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setTime(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setGasPrice(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setGasLimit(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setMiners(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setTokenBalance(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setTransferHash(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setPayStatus(cursor.getInt(offset + 17));
        entity.setStatus(cursor.getShort(offset + 18) != 0);
     }
    
    @Override
    protected final Long updateKeyAfterInsert(Transfer entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(Transfer entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(Transfer entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
