package com.Tlock.io.utils;


import android.app.Dialog;
import android.content.Context;

import com.Tlock.io.widget.pop.PopLoading;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BasePopupView;


/**
 * @Describe loading工具
 */

public class LoadingDialogUtils {
    private Dialog dialog;
    private static LoadingDialogUtils utils = null;
    private BasePopupView loadingPopup;

    public LoadingDialogUtils() {
    }

    public static LoadingDialogUtils getUtils() {
        if (utils == null) {
            synchronized (LoadingDialogUtils.class) {
                utils = new LoadingDialogUtils();
            }
        }

        return utils;
    }


    public void showProgressDialog(Context context) {
        if (context != null) {
            if (this.loadingPopup == null) {
                loadingPopup = new XPopup.Builder(context)
                        .dismissOnBackPressed(false)
                        .hasShadowBg(false)
                        .isLightNavigationBar(false)
                        .dismissOnTouchOutside(false)
                        .asCustom(new PopLoading(context));
            }
            if (!this.loadingPopup.isShow()) {
                this.loadingPopup.show();
            }
        }

    }


    public void dismissDialog() {
        if (this.loadingPopup != null && this.loadingPopup.isShow()) {
            this.loadingPopup.dismiss();
        }
        this.loadingPopup = null;

    }


}
