package com.Tlock.io.fragment.cosmos;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.MTopicDetailActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.TaskTopicItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import java.util.ArrayList;

import butterknife.BindView;

public class MTopicListFragment extends LazyLoadBaseFragment {

    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    @BindView(R.id.root)
    RelativeLayout mRoot;

    int position = 0;

    private BaseRecyclerViewAdapter<PostQueryProto.TopicResponse> adapter;
    private ETHWallet current;

    public MTopicListFragment(int position) {
        this.position = position;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_hot_topic;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mRefreshLayout.setEnableAutoLoadMore(false);
        mRefreshLayout.setEnableLoadMore(false);
        current = WalletDaoUtils.getCurrent();
        initRecycleView();
    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        adapter = new BaseRecyclerViewAdapter<>(getContext(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.TopicResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                TaskTopicItemView itemView = new TaskTopicItemView(getContext());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.TopicResponse data, View view) {
                ((TaskTopicItemView) view).setData(data);

            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.TopicResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.TopicResponse data, View view) {
                MTopicDetailActivity.start(getActivity(), data.getId(),"" , data.getName());
            }
        });
    }

    @Override
    protected void loadData() {
        getPostList(page, 20);
    }

    @Override
    protected void getData() {
        super.getData();
        getPostList(page, 20);
    }

    public void getPostList(int page, int size) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.TopicResponse> hotTopics72 = CosmosUtils.queryUncategorizedTopics(page, size);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
//                        Log.e(TAG, "getPostList: " + JsonUtils.listToJson(hotTopics72));
                        if (adapter != null) {
                            adapter.setList(hotTopics72);
                        }
                        finishRefresh();
                    }
                });
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        current = WalletDaoUtils.getCurrent();
    }

}