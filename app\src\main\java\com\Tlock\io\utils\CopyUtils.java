package com.Tlock.io.utils;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;

/**
 * @ClassName CopyUtils
 * <AUTHOR>
 * @Data 2021/11/15 13:05
 * @Desc
 */

public class CopyUtils {
    // CopyUtils实例
    private static CopyUtils instance = new CopyUtils();

    /**
     * 获取CopyUtils实例 ,单例模式
     */
    public static CopyUtils getInstance() {
        return instance;
    }

    /**
     * 复制到剪切板
     * @param content
     */
    public static void copyToClipboard( String content) {
        ClipboardManager cm = (ClipboardManager) AppApplication.getInstance().getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData mClipData = ClipData.newPlainText("Label", content);
        cm.setPrimaryClip(mClipData);
        ToastUtil.toastShortCenter(AppApplication.getInstance(), AppApplication.getInstance().getResources().getString(R.string.copy_to));
    }

    /**
     * 系统剪贴板-获取:
     */
    public static String getClipboardContent () {
        // 获取系统剪贴板
        ClipboardManager clipboard = (ClipboardManager) AppApplication.getInstance().getSystemService(Context.CLIPBOARD_SERVICE);
        // 返回数据
        ClipData clipData = clipboard.getPrimaryClip();
        if(clipData == null || clipData.getItemCount() <= 0){
            return "";
        }
        ClipData.Item item = clipData.getItemAt(0);
        if(item == null || item.getText() == null ){
            return "";
        }
        return item.getText().toString();
    }
}
