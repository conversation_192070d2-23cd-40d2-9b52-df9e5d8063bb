{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3099,3202,3304,3407,3512,3613,3715,8056", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3197,3299,3402,3507,3608,3710,3829,8152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,398,503,582,647,736,801,860,946,1010,1073,1146,1210,1264,1376,1434,1496,1550,1622,1744,1831,1917,2027,2104,2185,2276,2343,2409,2479,2556,2643,2714,2791,2860,2929,3020,3092,3181,3270,3344,3416,3502,3552,3618,3698,3782,3844,3908,3971,4071,4168,4260,4359", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "222,299,393,498,577,642,731,796,855,941,1005,1068,1141,1205,1259,1371,1429,1491,1545,1617,1739,1826,1912,2022,2099,2180,2271,2338,2404,2474,2551,2638,2709,2786,2855,2924,3015,3087,3176,3265,3339,3411,3497,3547,3613,3693,3777,3839,3903,3966,4066,4163,4255,4354,4438"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3022,3834,3928,4033,4112,4177,4266,4331,4390,4476,4540,4603,4676,4740,4794,4906,4964,5026,5080,5152,5274,5361,5447,5557,5634,5715,5806,5873,5939,6009,6086,6173,6244,6321,6390,6459,6550,6622,6711,6800,6874,6946,7032,7082,7148,7228,7312,7374,7438,7501,7601,7698,7790,7889", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "272,3094,3923,4028,4107,4172,4261,4326,4385,4471,4535,4598,4671,4735,4789,4901,4959,5021,5075,5147,5269,5356,5442,5552,5629,5710,5801,5868,5934,6004,6081,6168,6239,6316,6385,6454,6545,6617,6706,6795,6869,6941,7027,7077,7143,7223,7307,7369,7433,7496,7596,7693,7785,7884,7968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,906,997,1090,1186,1281,1381,1474,1569,1665,1756,1846,1935,2045,2149,2248,2359,2463,2581,2744,2850", "endColumns": "118,109,106,85,103,119,78,75,90,92,95,94,99,92,94,95,90,89,88,109,103,98,110,103,117,162,105,82", "endOffsets": "219,329,436,522,626,746,825,901,992,1085,1181,1276,1376,1469,1564,1660,1751,1841,1930,2040,2144,2243,2354,2458,2576,2739,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,396,506,613,699,803,923,1002,1078,1169,1262,1358,1453,1553,1646,1741,1837,1928,2018,2107,2217,2321,2420,2531,2635,2753,2916,7973", "endColumns": "118,109,106,85,103,119,78,75,90,92,95,94,99,92,94,95,90,89,88,109,103,98,110,103,117,162,105,82", "endOffsets": "391,501,608,694,798,918,997,1073,1164,1257,1353,1448,1548,1641,1736,1832,1923,2013,2102,2212,2316,2415,2526,2630,2748,2911,3017,8051"}}]}]}