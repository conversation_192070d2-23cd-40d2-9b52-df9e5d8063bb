// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopPostImageSelect_ViewBinding implements Unbinder {
  private PopPostImageSelect target;

  private View view7f090367;

  private View view7f09030f;

  @UiThread
  public PopPostImageSelect_ViewBinding(PopPostImageSelect target) {
    this(target, target);
  }

  @UiThread
  public PopPostImageSelect_ViewBinding(final PopPostImageSelect target, View source) {
    this.target = target;

    View view;
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv_2, "field 'mTv2'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_service, "field 'mTvService' and method 'onBindClick'");
    target.mTvService = Utils.castView(view, R.id.tv_service, "field 'mTvService'", TextView.class);
    view7f090367 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_chain, "field 'mTvChain' and method 'onBindClick'");
    target.mTvChain = Utils.castView(view, R.id.tv_chain, "field 'mTvChain'", TextView.class);
    view7f09030f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopPostImageSelect target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mLine1 = null;
    target.mTvTitle = null;
    target.mTv1 = null;
    target.mTv2 = null;
    target.mTvService = null;
    target.mTvChain = null;

    view7f090367.setOnClickListener(null);
    view7f090367 = null;
    view7f09030f.setOnClickListener(null);
    view7f09030f = null;
  }
}
