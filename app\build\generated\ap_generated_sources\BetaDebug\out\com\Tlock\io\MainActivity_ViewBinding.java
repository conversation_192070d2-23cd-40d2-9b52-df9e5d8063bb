// Generated code from Butter Knife. Do not modify!
package com.Tlock.io;

import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MainActivity_ViewBinding implements Unbinder {
  private MainActivity target;

  private View view7f090159;

  private View view7f090313;

  @UiThread
  public MainActivity_ViewBinding(MainActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MainActivity_ViewBinding(final MainActivity target, View source) {
    this.target = target;

    View view;
    target.mVpMain = Utils.findRequiredViewAsType(source, R.id.vp_main, "field 'mVpMain'", ViewPager2.class);
    target.mRlIndex = Utils.findRequiredViewAsType(source, R.id.rl_index, "field 'mRlIndex'", RelativeLayout.class);
    target.mBottomNavigationView = Utils.findRequiredViewAsType(source, R.id.bottomNavigationView, "field 'mBottomNavigationView'", BottomNavigationView.class);
    view = Utils.findRequiredView(source, R.id.iv_more, "field 'mIvMore' and method 'onBindClick'");
    target.mIvMore = Utils.castView(view, R.id.iv_more, "field 'mIvMore'", ImageView.class);
    view7f090159 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mMenuFrame = Utils.findRequiredViewAsType(source, R.id.menu_frame, "field 'mMenuFrame'", FrameLayout.class);
    target.mDrawerLayout = Utils.findRequiredViewAsType(source, R.id.drawer_layout, "field 'mDrawerLayout'", DrawerLayout.class);
    target.mContentFrame = Utils.findRequiredViewAsType(source, R.id.content_frame, "field 'mContentFrame'", FrameLayout.class);
    target.mTvListTitle = Utils.findRequiredViewAsType(source, R.id.tv_list_title, "field 'mTvListTitle'", TextView.class);
    target.mRvWallet = Utils.findRequiredViewAsType(source, R.id.rv_wallet, "field 'mRvWallet'", RecyclerView.class);
    view = Utils.findRequiredView(source, R.id.tv_create, "field 'mTvCreate' and method 'onBindClick'");
    target.mTvCreate = Utils.castView(view, R.id.tv_create, "field 'mTvCreate'", TextView.class);
    view7f090313 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    MainActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mVpMain = null;
    target.mRlIndex = null;
    target.mBottomNavigationView = null;
    target.mIvMore = null;
    target.mMenuFrame = null;
    target.mDrawerLayout = null;
    target.mContentFrame = null;
    target.mTvListTitle = null;
    target.mRvWallet = null;
    target.mTvCreate = null;

    view7f090159.setOnClickListener(null);
    view7f090159 = null;
    view7f090313.setOnClickListener(null);
    view7f090313 = null;
  }
}
