package com.Tlock.io.activity.cosmos;

import static android.Manifest.permission.READ_MEDIA_IMAGES;
import static android.Manifest.permission.READ_MEDIA_VIDEO;

import android.Manifest;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomEditBox;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.pop.PopSelectList;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;
import com.sl.utakephoto.compress.CompressConfig;
import com.sl.utakephoto.compress.CompressImage;
import com.sl.utakephoto.compress.CompressImageImpl;
import com.sl.utakephoto.crop.CropOptions;
import com.sl.utakephoto.utils.IntentUtils;
import com.sl.utakephoto.utils.TUriUtils;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;

import butterknife.BindView;
import butterknife.OnClick;

public class EditTopicActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_avatar)
    ImageView mIvAvatar;
    @BindView(R.id.iv_avatar1)
    ImageView mIvAvatar1;
    @BindView(R.id.ed_nike_name)
    CustomEditBox mEdNikeName;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.editText)
    EditText mEditText;
    @BindView(R.id.rl_category)
    RelativeLayout mRlCategory;
    @BindView(R.id.ed_bio)
    CustomEditBox mEdBio;
    @BindView(R.id.tv_save)
    TextView mTvSave;
    @BindView(R.id.tv_2)
    TextView mTv2;
    @BindView(R.id.tv_3)
    TextView mTv3;
    private String base64String = "";
    private PostQueryProto.TopicResponse data;
    private String groupId = "";

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context, String data) {
        Intent intent = new Intent(context, EditTopicActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_edit_topic;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        String dataStr = getIntent().getStringExtra("data");
        ArrayList<PostQueryProto.CategoryResponse> beans = SpUtil.getTopicClassify();
        PostQueryProto.CategoryResponse categoryResponse = beans.get(beans.size() - 1);
        mTv3.setText(categoryResponse.getName());
        groupId = categoryResponse.getId();
        data = JsonUtils.jsonToObject(dataStr, PostQueryProto.TopicResponse.class);
        mEdNikeName.setEditText(data.getTitle().isEmpty()?data.getName():data.getTitle());
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {
        getAuthInfo();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String authStr = CosmosUtils.getTopicHeard(data.getId());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (TextUtils.isEmpty(authStr)) {
                                TextAvatarDrawable a = new TextAvatarDrawable(data.getName().substring(0, 1));
                                mIvAvatar.setImageDrawable(a);
                            } else {
                                if (authStr.startsWith("http")) {
                                    Glide.with(getActivity()).load(authStr).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(data.getId()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvAvatar);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authStr);
                                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvAvatar);
                                }

                            }


                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.tv_save, R.id.iv_avatar, R.id.rl_category})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_save:

                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        PostTXProto.UpdateTopicJson.Builder builder = PostTXProto.UpdateTopicJson.newBuilder();
                        builder.setId(data.getId());
                        if (!base64String.isEmpty())
                            builder.setImage(base64String);
                        builder.setTitle(mEdNikeName.getEditText().toString().trim());
                        builder.setCategoryId(groupId);
                        builder.setSummary(mEdBio.getEditText().toString().trim());
                        CosmosUtils.topicUpdata(WalletDaoUtils.getCurrent().getAddress(), builder.build());
                    }
                });
                mTvSave.post(new Runnable() {
                    @Override
                    public void run() {
                        showToast("Save success");
                        finish();
                    }
                });
                break;
            case R.id.iv_avatar:
                checkPermissions();
                break;
            case R.id.rl_category:
                PopSelectList popSelectList = new PopSelectList(getActivity());
                popSelectList.setCallBack(new PopSelectList.CallBack() {
                    @Override
                    public void Done(PostQueryProto.CategoryResponse data, int position) {
                        mTv3.setText(data.getName());
                        groupId = data.getId();
                    }
                });
                new XPopup.Builder(getActivity())
                        .hasShadowBg(true)
                        .isDestroyOnDismiss(true)
                        .isClickThrough(true)
                        .isRequestFocus(true)
                        .asCustom(popSelectList).show();
                break;
        }
    }

    private Bitmap getBitmapFromUri(Uri uri) {
        try {
            ContentResolver cr = getActivity().getContentResolver();
            InputStream is = cr.openInputStream(uri);
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inSampleSize = 2; // 压缩图片，避免内存溢出
            return BitmapFactory.decodeStream(is, null, options);
        } catch (FileNotFoundException e) {
        }
        return null;
    }


    private static final int REQUEST_CODE_PERMISSIONS = 100;
    private final String[] REQUIRED_PERMISSIONS = {
            READ_MEDIA_IMAGES,
            READ_MEDIA_VIDEO,
            Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
    };

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, REQUIRED_PERMISSIONS[0]) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS);
        } else {
            openGallery(); // 已有权限，直接打开相册
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openGallery();
                }
            } else {
                openGallery();
            }

        }
    }

    private ActivityResultLauncher<String> galleryLauncher = registerForActivityResult(
            new ActivityResultContracts.GetContent(),
            uri -> handleImageResult(uri)
    );

    private void openGallery() {
        galleryLauncher.launch("image/*");
    }

    private void handleImageResult(Uri uri) {
        if (uri != null) {
            crop(uri);
        }
    }

    private void setCompose(Uri uri) {
        CompressImageImpl.of(getActivity(), new CompressConfig.Builder()
                .setFocusAlpha(false)//是否支持透明度
                .setLeastCompressSize(200)//最小压缩尺寸
                .create(), Collections.singletonList(uri), new CompressImage.CompressListener() {
            @Override
            public void onStart() {

            }

            @Override
            public void onSuccess(Uri uri) {
                Bitmap bitmap = getBitmapFromUri(uri);
                base64String = BitmapUtils.bitmapToBase64(bitmap);
                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(base64String);
                Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                .centerCrop()
                                .dontTransform())
                        .apply(RequestOptions.circleCropTransform().circleCrop())
                        .into(mIvAvatar);
            }

            @Override
            public void onError(Throwable obj) {

            }
        }).compress();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 101 && resultCode == Activity.RESULT_OK) {
            setCompose(tempUri);
        }
    }

    private Uri tempUri;

    private void crop(Uri takePhotoUri) {
        CropOptions cropOptions = new CropOptions.Builder()
                .setAspectX(1)
                .setAspectY(1)
                .setOutputX(300)
                .setOutputY(300)
                .setWithOwnCrop(true)//使用系统裁剪还是自带裁剪
                .create();
        tempUri = TUriUtils.getTempSchemeUri(getActivity());
        Intent cropIntentWithOtherApp = IntentUtils.getCropIntent(takePhotoUri, tempUri, cropOptions);
        startActivityForResult(cropIntentWithOtherApp, 101);
    }
}