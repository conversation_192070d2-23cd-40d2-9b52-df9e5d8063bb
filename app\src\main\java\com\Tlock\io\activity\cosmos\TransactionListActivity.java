package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.wallet.Transaction;
import com.Tlock.io.itemBean.cosmos.PostTopicItemView;
import com.Tlock.io.itemBean.cosmos.TransactionItemView;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.MeasureRecyclerView;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class TransactionListActivity extends BaseActivity {

    @BindView(R.id.recyclerView_community)
    MeasureRecyclerView mRecyclerView;
    private ArrayList<Transaction> userList = new ArrayList<>();
    private BaseRecyclerViewAdapter<Transaction> adapter;

    /**
     * @param context
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, TransactionListActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_community;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRecyclerView();
        mCustomNavBar.setMidTitle("Transactions");
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initRecyclerView() {
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), userList, new BaseRecyclerViewAdapter.Delegate<Transaction>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new TransactionItemView(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, Transaction data, View view) {
                TransactionItemView viewBean = (TransactionItemView) view;
                viewBean.setData(data);
            }
        });
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<Transaction>() {
            @Override
            public void onItemClick(int position, Transaction data, View view) {
                TransactionInfoActivity.start(getActivity(),data.getHash());
            }
        });
        mRecyclerView.setAdapter(adapter);
    }

    @Override
    protected void loadData() {
        params.clear();
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                List<Transaction> currentTransactionList = WalletDaoUtils.getCurrentTransactionList();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.setList((ArrayList<Transaction>) currentTransactionList);
                    }
                });
            }
        });


    }
}