package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.itemBean.cosmos.CategoryViewBean;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;


public class MTopicDetailActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.tv_topic_name)
    TextView mTvTopicName;
    @BindView(R.id.flowlayout_mnemonic)
    TagFlowLayout mFlowlayoutMnemonic;
    @BindView(R.id.confirm_button)
    TextView mConfirmButton;
    private TagAdapter<String> tagAdapter;
    ArrayList<String> valueBeans = new ArrayList<>();
    private String category;
    private String topic;
    private ArrayList<PostQueryProto.CategoryResponse> categories;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String topic, String category, String title
    ) {
        Intent intent = new Intent(context, MTopicDetailActivity.class);
        intent.putExtra("topic", topic);
        intent.putExtra("category", category);
        intent.putExtra("title", title);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_m_topic;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        category = getIntent().getStringExtra("category");
        topic = getIntent().getStringExtra("topic");
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
        mTvTopicName.setText("# " + getIntent().getStringExtra("title"));
    }

    private void initTag() {
        tagAdapter = new TagAdapter<String>((List<String>) valueBeans) {
            @Override
            public View getView(FlowLayout parent, int position, String s) {
                CategoryViewBean mnemonicViewBean = new CategoryViewBean(getActivity());
                ViewGroup.LayoutParams layoutParams = mnemonicViewBean.getLayoutParams();
                layoutParams.width = LinearLayout.LayoutParams.WRAP_CONTENT;
                mnemonicViewBean.setLayoutParams(layoutParams);
                mnemonicViewBean.setData(valueBeans.get(position), categories.get(position).getId().equalsIgnoreCase(category));
                return mnemonicViewBean;
            }

            @Override
            public void onSelected(int position, View view) {
                super.onSelected(position, view);
                ((CategoryViewBean) view).setData(valueBeans.get(position), true);
                category = categories.get(position).getId();
            }

            @Override
            public void unSelected(int position, View view) {
                super.unSelected(position, view);
                ((CategoryViewBean) view).setData(valueBeans.get(position), false);
            }
        };
        mFlowlayoutMnemonic.setAdapter(tagAdapter);

        mFlowlayoutMnemonic.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                return true;
            }
        });
    }


    @Override
    protected void loadData() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                categories = CosmosUtils.getCategories();
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 0; i < categories.size(); i++) {
                            PostQueryProto.CategoryResponse category = categories.get(i);
                            valueBeans.add(category.getName());
                        }
                        initTag();

                    }
                });
            }
        });
    }

    @OnClick({R.id.confirm_button})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.confirm_button:
                // 提交
                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        CosmosUtils.classifyUncategorizedTopic(topic, category);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showToast("分类成功");
                                finish();
                            }
                        });
                    }
                });
                break;
        }
    }
}