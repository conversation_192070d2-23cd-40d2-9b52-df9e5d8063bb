<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".activity.cosmos.PostQuoteActivity">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_15"
        android:padding="@dimen/dp_10"
        android:src="@mipmap/icon_close_post" />

    <TextView
        android:id="@+id/send_post"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_15"
        android:layout_marginRight="@dimen/dp_16"
        android:background="@drawable/shape_deep_gray_60"
        android:paddingLeft="@dimen/dp_18"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_18"
        android:paddingBottom="@dimen/dp_7"
        android:text="Post"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_14" />


    <EditText
        android:id="@+id/ed_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_close"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginRight="@dimen/dp_16"
        android:background="@null"
        android:hint="Input title"
        android:textColor="@color/cosmos_black"
        android:textColorHint="@color/hint_color"
        android:textSize="@dimen/sp_16"
        android:theme="@style/MyEditText" />

    <View
        android:id="@+id/line2"
        style="@style/deep_gray_horizontal_line_view"
        android:layout_below="@id/ed_title"
        android:layout_marginTop="@dimen/dp_14" />

    <EditText
        android:id="@+id/ed_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_200"
        android:layout_below="@id/line2"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_14"
        android:layout_marginRight="@dimen/dp_16"
        android:background="@null"
        android:gravity="top"
        android:hint="Input content"
        android:maxHeight="@dimen/dp_350"
        android:textColor="@color/cosmos_black"
        android:textColorHint="@color/hint_color"
        android:textSize="@dimen/sp_16"
        android:theme="@style/MyEditText" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_images"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/rl_item"
        android:layout_below="@id/ed_content"
        android:layout_marginLeft="@dimen/dp_4"
        android:layout_marginRight="@dimen/dp_4" />

    <RelativeLayout
        android:id="@+id/rl_proposal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ed_content"
        android:layout_margin="@dimen/dp_16"
        android:background="@drawable/shape_gray_line_13"
        android:orientation="vertical"
        android:padding="@dimen/dp_14"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_1"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:gravity="center_vertical"
            android:text="Viewpoint"
            android:textColor="@color/cosmos_black" />

        <ImageView
            android:id="@+id/iv_close_Proposal"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignTop="@id/tv_1"
            android:layout_alignBottom="@id/tv_1"
            android:layout_alignParentRight="true"
            android:padding="@dimen/dp_7"
            android:src="@mipmap/icon_close_post" />

        <EditText
            android:id="@+id/ed_proposal1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_close_Proposal"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/shape_gray_line_13"
            android:hint="Choice"
            android:minHeight="@dimen/dp_50"
            android:padding="@dimen/dp_17"
            android:textColor="@color/cosmos_default"
            android:textColorHint="@color/hint_color"
            android:textSize="@dimen/sp_14"
            android:theme="@style/edit_text_proposal" />

        <ScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/ed_proposal1">

            <LinearLayout
                android:id="@+id/ll_vote"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_18"

                android:orientation="vertical" />
        </ScrollView>

        <TextView
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/scrollView"
            android:text="Poll length: 1 Day" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignWithParentIfMissing="true"
        android:layout_above="@id/ll_menu"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/shape_deep_gray_60"
        android:paddingLeft="@dimen/dp_8"
        android:paddingTop="@dimen/dp_6"
        android:paddingRight="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_6"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_1"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_8" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_1"
            android:text="123123123"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_select"
            android:layout_width="@dimen/dp_14"
            android:layout_height="@dimen/dp_14"
            android:layout_centerInParent="true"
            android:layout_marginLeft="@dimen/dp_11"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_toRightOf="@id/tv_title"
            android:src="@mipmap/icon_close_post" />
    </RelativeLayout>

    <View
        android:id="@+id/line3"
        style="@style/gray_horizontal_line_view"
        android:layout_above="@id/ll_menu" />

    <LinearLayout
        android:id="@+id/ll_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:background="#FCFEFD"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_menu_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_16"
            android:background="@null"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/icon_topic_select" />

        <ImageView
            android:id="@+id/iv_menu_at"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_16"
            android:background="@null"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/icon_at" />

        <ImageView
            android:id="@+id/iv_menu_topic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_16"
            android:background="@null"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/icon_topic" />


        <ImageView
            android:id="@+id/iv_proposal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_16"
            android:background="@null"
            android:padding="@dimen/dp_10"
            android:visibility="gone"
            android:src="@mipmap/icon_proposal" />

        <ImageView
            android:id="@+id/iv_classify"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:background="@null"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/icon_classify" />
    </LinearLayout>


</RelativeLayout>




