package com.Tlock.io.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.Tlock.io.R;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 本类的主要功能是 :  日期工具类
 */
@RequiresApi(api = Build.VERSION_CODES.O)
public class DateUtil {
    private static final String TAG = "DateUtil";

    public DateUtil() {

    }

    public static final DateTimeFormatter dtf4 = DateTimeFormatter.ofPattern("dd");
    public static final DateTimeFormatter dtf3 = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
    public static final DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("HH:mm:ss");
    public static final DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern("YYYY/MM/dd");
    public static final DateTimeFormatter dtf5 = DateTimeFormatter.ofPattern("YYYY-MM-dd HH:mm:ss");
    public static final DateTimeFormatter dtf6 = DateTimeFormatter.ofPattern("YYYY/MM/dd HH:mm");
    public static final DateTimeFormatter dtf7 = DateTimeFormatter.ofPattern("YYYY-MM-dd");

    /**
     * 以友好的方式显示时间 ,几秒前/几分钟前/几小时前/昨天/前天/天前/日期......
     *
     * @param strDate 1423224342字符串毫秒值
     * @return
     */
    public static String friendly_time(String strDate) {
        long oldTime = new Date(Long.parseLong(strDate)).getTime();
        String ftime = "";
        int minite = 0;
        Calendar cal = Calendar.getInstance();
        // 判断是否是同一天
        String curDate = formatDate(cal.getTime().getTime(), "yyyy-MM-dd");
        String paramDate = formatDate(oldTime, "yyyy-MM-dd");
        ;
        if (curDate.equals(paramDate)) {
            int hour = (int) ((cal.getTimeInMillis() - oldTime) / 3600000);
            if (hour == 0) {
                // 判断是否为同一分钟内
                minite = (int) ((cal.getTimeInMillis() - oldTime) / 60000);
                if (minite == 0) {
                    ftime = Math.max(
                            (cal.getTimeInMillis() - oldTime) / 1000,
                            1)
                            + "秒前";
                } else {
                    ftime = Math
                            .max((cal.getTimeInMillis() - oldTime) / 60000,
                                    1)
                            + "分钟前";
                }

            } else {
                ftime = hour + "小时前";
            }
            return ftime;
        }

        long lt = oldTime / 86400000;
        long ct = cal.getTimeInMillis() / 86400000;
        int days = (int) (ct - lt);
        if (days == 0) {
            int hour = (int) ((cal.getTimeInMillis() - oldTime) / 3600000);
            if (hour == 0) {
                // 判断是否为同一分钟内
                minite = (int) ((cal.getTimeInMillis() - oldTime) / 60000);
                if (minite == 0) {
                    ftime = Math.max(
                            (cal.getTimeInMillis() - oldTime) / 1000,
                            1)
                            + "秒前";
                } else {
                    ftime = Math
                            .max((cal.getTimeInMillis() - oldTime) / 60000,
                                    1)
                            + "分钟前";
                }
            } else {
                ftime = hour + "小时前";
            }
        } else if (days == 1) {
            ftime = "昨天";
        } else if (days == 2) {
            ftime = "前天 ";
        } else if (days > 2 && days < 31) {
            ftime = days + "天前";
        } else if (days >= 31 && days <= 2 * 31) {
            ftime = "一个月前";
        } else if (days > 2 * 31 && days <= 3 * 31) {
            ftime = "2个月前";
        } else if (days > 3 * 31 && days <= 4 * 31) {
            ftime = "3个月前";
        } else {
            ftime = formatDate(oldTime, "yyyy-MM-dd");
        }
        return ftime;
    }

    /**
     * 1413131232 -->一分钟之内,几分钟前,几小时前,日期2016-12-12 13:10
     * 用到的地方 : 1,投诉建议主页面,详情页.
     * 2, 有求必应主页面
     * 3, 随叫随到主页面
     * 4,园圈主页面,园圈详情页右上角
     *
     * @param strDate
     * @return
     */
    public static String friendly_time_2(String strDate) {
        long oldTime = new Date(Long.parseLong(strDate)).getTime();
        String ftime = "";
        int minite;
        Calendar cal = Calendar.getInstance();
        // 判断是否是同一天
        String curDate = formatDate(cal.getTime().getTime(), "yyyy-MM-dd");
        String paramDate = formatDate(oldTime, "yyyy-MM-dd");

        if (curDate.equals(paramDate)) {
            int hour = (int) ((cal.getTimeInMillis() - oldTime) / 3600000);
            if (hour == 0) {
                // 判断是否为同一分钟内
                minite = (int) ((cal.getTimeInMillis() - oldTime) / 60000);
                if (minite == 0) {
//					ftime = Math.max(
//							(cal.getTimeInMillis() - oldTime) / 1000,
//							1)
//							+ "秒前";
                    ftime = "in one minute";
                } else {
                    ftime = Math
                            .max((cal.getTimeInMillis() - oldTime) / 60000,
                                    1)
                            + " minutes ago";
                }

            } else {
                ftime = hour + " hours ago";
            }
            return ftime;
        }

        long lt = oldTime / 86400000;
        long ct = cal.getTimeInMillis() / 86400000;
        int days = (int) (ct - lt);
        if (days == 0) {
            int hour = (int) ((cal.getTimeInMillis() - oldTime) / 3600000);
            if (hour == 0) {
                // 判断是否为同一分钟内
                minite = (int) ((cal.getTimeInMillis() - oldTime) / 60000);
                if (minite == 0) {
//					ftime = Math.max(
//							(cal.getTimeInMillis() - oldTime) / 1000,
//							1)
//							+ "秒前";
                    ftime = "In one minute";
                } else {
                    ftime = Math
                            .max((cal.getTimeInMillis() - oldTime) / 60000,
                                    1)
                            + " Minute ago";
                }
            } else {
                ftime = hour + "Hours ago";
            }
        } else {
            ftime = formatDate(oldTime, "yyyy-MM-dd HH:mm");
        }
        return ftime;
    }


    public static String friendly_time_3(String strDate) {
        long oldTime = new Date(Long.parseLong(strDate)).getTime();
        String ftime = "";
        int minite;
        Calendar cal = Calendar.getInstance();
        // 判断是否是同一天
        String curDate = formatDate(cal.getTime().getTime(), "yyyy-MM-dd");
        String paramDate = formatDate(oldTime, "yyyy-MM-dd");

        if (curDate.equals(paramDate)) {
            int hour = (int) ((cal.getTimeInMillis() - oldTime) / 3600000);
            if (hour == 0) {
                // 判断是否为同一分钟内
                minite = (int) ((cal.getTimeInMillis() - oldTime) / 60000);
                if (minite == 0) {
                    ftime = Math.max(
                            (cal.getTimeInMillis() - oldTime) / 1000,
                            1)
                            + "s";
                } else {
                    ftime = Math
                            .max((cal.getTimeInMillis() - oldTime) / 60000,
                                    1)
                            + "m";
                }

            } else {
                ftime = hour + "h";
            }
            return ftime;
        }

        long lt = oldTime / 86400000;
        long ct = cal.getTimeInMillis() / 86400000;
        int days = (int) (ct - lt);
        if (days == 0) {
            int hour = (int) ((cal.getTimeInMillis() - oldTime) / 3600000);
            if (hour == 0) {
                // 判断是否为同一分钟内
                minite = (int) ((cal.getTimeInMillis() - oldTime) / 60000);
                if (minite == 0) {
                    ftime = Math.max(
                            (cal.getTimeInMillis() - oldTime) / 1000,
                            1)
                            + "s";
                } else {
                    ftime = Math
                            .max((cal.getTimeInMillis() - oldTime) / 60000,
                                    1)
                            + "m";
                }
            } else {
                ftime = hour + "h";
            }
        } else {
            if (days <= 99) {
                ftime = days + "d";

            } else {
                ftime = "99d";

            }
        }
        return ftime;
    }

    /**
     * 毫秒值转换成  分  ,1时23分,3日21时34分
     *
     * @param timeMillis
     * @return
     */
    public static String friendly_time_3(long timeMillis) {
        if (timeMillis <= 0) {
            return 0 + "min";
        }
        //秒
        int second = (int) (timeMillis / 1000);
        if (second <= 60) {
            return 0 + "min";
        }
        //min
        int mint = second / 60;
        if (mint <= 60) {
            return mint + "min";
        }
        int hour = second / 3600;
        if (hour <= 24) {
            return hour + "h" + (second % 3600) / 60 + "min";
        }
        int days = second / (3600 * 24);
        // if (days<30){
        return days + "d" + second % (3600 * 24) / 3600 + "h" + second % (3600 * 24) % 3600 / 60 + "min";
        //}


    }

    /**
     * 毫秒时间转换成一定格式的日期,14002020304--->2016-12-12 14:12:43
     *
     * @param timeMillis  时间戳毫秒值
     * @param datePattern yyyy-MM-dd yyyy-MM-dd HH:mm:ss
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    public static String formatDate(long timeMillis, String datePattern) {
        if (timeMillis == 0) {
            return "";
        }
        SimpleDateFormat simpleDateFormat = getSimpleDateFormat(datePattern);
        String dateString = simpleDateFormat.format(new Date(timeMillis));
        return dateString;
    }

    /**
     * 获取一个日期的毫秒值
     *
     * @param dateTime    日期时间  2018-09-11 12:12:34
     * @param datePattern yyyy-MM-dd HH:mm:ss
     * @return 日期的毫秒值
     */
    public static long getDateTimeMillis(String dateTime, String datePattern) {
        if (TextUtils.isEmpty(dateTime) || TextUtils.isEmpty(datePattern))
            return 0;
        SimpleDateFormat simpleDateFormat = getSimpleDateFormat(datePattern);
        try {
            return simpleDateFormat.parse(dateTime).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * @param dateStr        日期时间  2018-09-11 12:12:34
     * @param datePattern    对应的yyyy-MM-dd HH:mm:ss
     * @param newDatePattern 需要转换成的  yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String translateDateStr(String dateStr, String datePattern, String newDatePattern) {

        if (TextUtils.isEmpty(dateStr)) {
            return "";
        }
        if (TextUtils.isEmpty(datePattern)) {
            return "";
        }
        if (TextUtils.isEmpty(newDatePattern)) {
            return "";
        }
        long dateTimeMillis = getDateTimeMillis(dateStr, datePattern);

        String s = formatDate(dateTimeMillis, newDatePattern);
        return s;
    }

    /**
     * 判断两个日期是否是同一天
     *
     * @param firstTimeMillis 第一个时间的毫秒值
     * @param lastTimeMillis  第二个时间的毫秒值
     * @return 是否是同一天.
     */
    public static boolean isSameDay(long firstTimeMillis, long lastTimeMillis) {
        if (firstTimeMillis <= 0 || lastTimeMillis <= 0)
            return false;
        String first = formatDate(firstTimeMillis, "yyyy-MM-dd");
        String second = formatDate(lastTimeMillis, "yyyy-MM-dd");
        return first.equalsIgnoreCase(second);
    }

    /**
     * 获取 SimpleDateFormat
     *
     * @param datePattern
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    private static SimpleDateFormat getSimpleDateFormat(String datePattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePattern);
        return simpleDateFormat;
    }

    /**
     * 获取当前时间的毫秒值
     *
     * @return long毫秒时间
     */
    public static long getCurrentTimeMilliseconds() {

        return new Date().getTime();
    }

    /**
     * 获取当前时间  如2017-12-02
     *
     * @param datePattern yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getCurrentDateStr(String datePattern) {

        return formatDate(getCurrentTimeMilliseconds(), datePattern);
    }


    /**
     * 对比时间是否超过某个时间长度 timelong
     *
     * @param historyTimeMilli 历史某个时间    .单位毫秒值
     * @param timeLongMilli    现在时间-历史时间是否超过这个时间.  单位毫秒值
     * @return true 超过规定时间,  false 未超过规定时间
     */
    public static boolean isTimeOverOneTime(String historyTimeMilli, String timeLongMilli) {
        long timeLong_ = Long.parseLong(timeLongMilli);//时长
        long historyTime_ = Long.parseLong(historyTimeMilli);//历史时间
        long currentTimeMilliseconds_ = getCurrentTimeMilliseconds();//现在时间
        long result_ = currentTimeMilliseconds_ - historyTime_;
        return result_ > timeLong_;
    }

    /**
     * 获取2月的天数
     *
     * @param year
     * @return
     */
    public static int getFebruaryDay(int year) {
        Calendar c = Calendar.getInstance();
        c.set(year, 2, 1);// year年的3月1日
        c.add(Calendar.DAY_OF_MONTH, -1);//将3月1日往左偏移一天结果是2月的天数
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取月的天数集合
     *
     * @param year
     * @param month
     * @return
     */
    public static ArrayList<String> getDaysList(int year, int month) {
        ArrayList<String> days = new ArrayList<>();

        int daysLength = 0;
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                daysLength = 31;
                break;
            case 2:
                daysLength = getFebruaryDay(year);
                break;
            case 4:
            case 6:
            case 9:
            case 11:
                daysLength = 30;
                break;
        }

        for (int i = 1; i <= daysLength; i++) {
            days.add((i + "").length() == 1 ? "0" + i : i + "");
        }
        return days;
    }

    /**
     * 时间转换
     *
     * @param timestamp 秒
     * @return
     */
    public static LocalDateTime getDateTimeOfTimestamp(long timestamp, String s) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.of(s));
    }

    /**
     * 友好显示时间
     *
     * @param strDate
     * @return
     */
    public static String friendly_pool_time(@NonNull Context context, String strDate) {
        if (TextUtils.isEmpty(strDate)) return "";
        try {
            Long getTime = Long.parseLong(strDate);
            String id = TimeZone.getDefault().getID();
            long minNow = System.currentTimeMillis() / 1000;
            String format = DateUtil.dtf1.format(DateUtil.getDateTimeOfTimestamp(getTime, id));
            int mm = (int) ((minNow - getTime) / 60);
            if (mm == 0) {
                //刚刚
                return context.getResources().getString(R.string.In_minute);
            } else if (mm < 60 && mm > 0) {
                //几分前
                return mm + " " + context.getResources().getString(R.string.min_ago);
            } else if (mm >= 60 && mm < 1440) {
                //几小时前
                return mm / 60 + " " + context.getResources().getString(R.string.hour_ago);
            } else if (mm >= 1440 && mm < 1440 * 7) {
                //几天前
                return mm / 1440 + " " + context.getResources().getString(R.string.day_ago);
            } else {
                //显示日期
                return format;
            }
        } catch (Exception e) {

        }
        return "";
    }
}
