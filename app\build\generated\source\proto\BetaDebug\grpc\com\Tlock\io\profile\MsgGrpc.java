package com.Tlock.io.profile;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * Msg defines the Msg service.
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.57.0)",
    comments = "Source: profile/profile_tx.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class MsgGrpc {

  private MsgGrpc() {}

  public static final java.lang.String SERVICE_NAME = "profile.v1.Msg";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse> getAddProfileMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "AddProfile",
      requestType = com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.class,
      responseType = com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse> getAddProfileMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest, com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse> getAddProfileMethod;
    if ((getAddProfileMethod = MsgGrpc.getAddProfileMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getAddProfileMethod = MsgGrpc.getAddProfileMethod) == null) {
          MsgGrpc.getAddProfileMethod = getAddProfileMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest, com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "AddProfile"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("AddProfile"))
              .build();
        }
      }
    }
    return getAddProfileMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse> getFollowMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Follow",
      requestType = com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.class,
      responseType = com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse> getFollowMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest, com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse> getFollowMethod;
    if ((getFollowMethod = MsgGrpc.getFollowMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getFollowMethod = MsgGrpc.getFollowMethod) == null) {
          MsgGrpc.getFollowMethod = getFollowMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest, com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Follow"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("Follow"))
              .build();
        }
      }
    }
    return getFollowMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse> getUnfollowMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Unfollow",
      requestType = com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.class,
      responseType = com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse> getUnfollowMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest, com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse> getUnfollowMethod;
    if ((getUnfollowMethod = MsgGrpc.getUnfollowMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getUnfollowMethod = MsgGrpc.getUnfollowMethod) == null) {
          MsgGrpc.getUnfollowMethod = getUnfollowMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest, com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Unfollow"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("Unfollow"))
              .build();
        }
      }
    }
    return getUnfollowMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse> getManageAdminMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ManageAdmin",
      requestType = com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.class,
      responseType = com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest,
      com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse> getManageAdminMethod() {
    io.grpc.MethodDescriptor<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest, com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse> getManageAdminMethod;
    if ((getManageAdminMethod = MsgGrpc.getManageAdminMethod) == null) {
      synchronized (MsgGrpc.class) {
        if ((getManageAdminMethod = MsgGrpc.getManageAdminMethod) == null) {
          MsgGrpc.getManageAdminMethod = getManageAdminMethod =
              io.grpc.MethodDescriptor.<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest, com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ManageAdmin"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse.getDefaultInstance()))
              .setSchemaDescriptor(new MsgMethodDescriptorSupplier("ManageAdmin"))
              .build();
        }
      }
    }
    return getManageAdminMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static MsgStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MsgStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MsgStub>() {
        @java.lang.Override
        public MsgStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MsgStub(channel, callOptions);
        }
      };
    return MsgStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static MsgBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MsgBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MsgBlockingStub>() {
        @java.lang.Override
        public MsgBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MsgBlockingStub(channel, callOptions);
        }
      };
    return MsgBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static MsgFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<MsgFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<MsgFutureStub>() {
        @java.lang.Override
        public MsgFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new MsgFutureStub(channel, callOptions);
        }
      };
    return MsgFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * Msg defines the Msg service.
   * </pre>
   */
  public interface AsyncService {

    /**
     */
    default void addProfile(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAddProfileMethod(), responseObserver);
    }

    /**
     */
    default void follow(com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getFollowMethod(), responseObserver);
    }

    /**
     */
    default void unfollow(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUnfollowMethod(), responseObserver);
    }

    /**
     */
    default void manageAdmin(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getManageAdminMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service Msg.
   * <pre>
   * Msg defines the Msg service.
   * </pre>
   */
  public static abstract class MsgImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return MsgGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service Msg.
   * <pre>
   * Msg defines the Msg service.
   * </pre>
   */
  public static final class MsgStub
      extends io.grpc.stub.AbstractAsyncStub<MsgStub> {
    private MsgStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MsgStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MsgStub(channel, callOptions);
    }

    /**
     */
    public void addProfile(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAddProfileMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void follow(com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getFollowMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void unfollow(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUnfollowMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void manageAdmin(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest request,
        io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getManageAdminMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service Msg.
   * <pre>
   * Msg defines the Msg service.
   * </pre>
   */
  public static final class MsgBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<MsgBlockingStub> {
    private MsgBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MsgBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MsgBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse addProfile(com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAddProfileMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse follow(com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getFollowMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse unfollow(com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUnfollowMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse manageAdmin(com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getManageAdminMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service Msg.
   * <pre>
   * Msg defines the Msg service.
   * </pre>
   */
  public static final class MsgFutureStub
      extends io.grpc.stub.AbstractFutureStub<MsgFutureStub> {
    private MsgFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected MsgFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new MsgFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse> addProfile(
        com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAddProfileMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse> follow(
        com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getFollowMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse> unfollow(
        com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUnfollowMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse> manageAdmin(
        com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getManageAdminMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_ADD_PROFILE = 0;
  private static final int METHODID_FOLLOW = 1;
  private static final int METHODID_UNFOLLOW = 2;
  private static final int METHODID_MANAGE_ADMIN = 3;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_ADD_PROFILE:
          serviceImpl.addProfile((com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse>) responseObserver);
          break;
        case METHODID_FOLLOW:
          serviceImpl.follow((com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse>) responseObserver);
          break;
        case METHODID_UNFOLLOW:
          serviceImpl.unfollow((com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse>) responseObserver);
          break;
        case METHODID_MANAGE_ADMIN:
          serviceImpl.manageAdmin((com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest) request,
              (io.grpc.stub.StreamObserver<com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getAddProfileMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileTXProto.MsgAddProfileRequest,
              com.Tlock.io.profile.ProfileTXProto.MsgAddProfileResponse>(
                service, METHODID_ADD_PROFILE)))
        .addMethod(
          getFollowMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileTXProto.MsgFollowRequest,
              com.Tlock.io.profile.ProfileTXProto.MsgFollowResponse>(
                service, METHODID_FOLLOW)))
        .addMethod(
          getUnfollowMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileTXProto.MsgUnfollowRequest,
              com.Tlock.io.profile.ProfileTXProto.MsgUnfollowResponse>(
                service, METHODID_UNFOLLOW)))
        .addMethod(
          getManageAdminMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.Tlock.io.profile.ProfileTXProto.MsgManageAdminRequest,
              com.Tlock.io.profile.ProfileTXProto.MsgManageAdminResponse>(
                service, METHODID_MANAGE_ADMIN)))
        .build();
  }

  private static abstract class MsgBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    MsgBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.Tlock.io.profile.ProfileTXProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Msg");
    }
  }

  private static final class MsgFileDescriptorSupplier
      extends MsgBaseDescriptorSupplier {
    MsgFileDescriptorSupplier() {}
  }

  private static final class MsgMethodDescriptorSupplier
      extends MsgBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    MsgMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (MsgGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new MsgFileDescriptorSupplier())
              .addMethod(getAddProfileMethod())
              .addMethod(getFollowMethod())
              .addMethod(getUnfollowMethod())
              .addMethod(getManageAdminMethod())
              .build();
        }
      }
    }
    return result;
  }
}
