// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TopicTabView_ViewBinding implements Unbinder {
  private TopicTabView target;

  @UiThread
  public TopicTabView_ViewBinding(TopicTabView target) {
    this(target, target);
  }

  @UiThread
  public TopicTabView_ViewBinding(TopicTabView target, View source) {
    this.target = target;

    target.mIvLine = Utils.findRequiredViewAsType(source, R.id.iv_line, "field 'mIvLine'", ImageView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mRlItem = Utils.findRequiredViewAsType(source, R.id.rl_item, "field 'mRlItem'", RelativeLayout.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv_1, "field 'mIv1'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TopicTabView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvLine = null;
    target.mTvTitle = null;
    target.mRlItem = null;
    target.mIv1 = null;
  }
}
