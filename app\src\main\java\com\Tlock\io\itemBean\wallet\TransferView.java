package com.Tlock.io.itemBean.wallet;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.wallet.Transfer;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.DateUtil;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.wallet.WalletDaoUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.TimeZone;

import butterknife.BindView;

import static com.Tlock.io.config.AppConstants.ETHW_CHAIN_ID;


/**
 * @ClassName TransactionView
 * <AUTHOR>
 * @Data 2021/11/18 12:07
 * @Desc
 */

public class TransferView extends BaseView {
    @BindView(R.id.iv_status)
    ImageView mIvStatus;
    @BindView(R.id.tv_address)
    TextView mTvAddress;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.tv_count)
    TextView mTvCount;

    @BindView(R.id.tv_cancel)
    TextView mTvCancel;
    private Context context;

    public TransferView(Context context) {
        super(context);
        this.context = context;
    }

    public TransferView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
    }

    public TransferView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_transfer;
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public void setData(Transfer data, String decimal,int from) {
        try {
            mTvCancel.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    EventBus.getDefault().post(new Event(EventConstant.CANCEL_TRANSFER));
                }
            });
            String address = WalletDaoUtils.getCurrent().getAddress();
            String format="";
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.O){
                 format = DateUtil.dtf5.format(DateUtil.getDateTimeOfTimestamp(Long.parseLong(data.getTime()), TimeZone.getDefault().getID()));
            }else{
                format ="未知";
            }
            mTvTime.setText(format);
            if (data.getPayStatus() == 1) {
                mTvAddress.setText(data.getPayAddress());
            }
            if (data.getPayStatus() == 2 || data.getPayStatus() == 0) {
                mTvAddress.setText(data.getGetAddress());
            }
            mTvAddress.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    CopyUtils.getInstance().copyToClipboard( mTvAddress.getText().toString());
                }
            });
            if (TextUtils.isEmpty(data.getTransferHash())) {
                mTvCount.setText("失败");
                return;
            }
            mTvCancel.setVisibility(GONE);
            mTvCount.setText(data.getStatus() ? context.getString(R.string.success) : context.getString(R.string.fail));

            if (data.getPayAddress().equals(address)) {
                //卖出
                setOut(data, decimal);
            }
            if (data.getGetAddress().equals(address)) {
                //买入
                setIn(data, decimal);
            }
            if (data.getGetAddress().equals(address) && data.getPayAddress().equals(address)) {
                if (data.getPayStatus() == 1) {
                    //买入
                    setIn(data, decimal);
                } else if (data.getPayStatus() == 2) {
                    //卖出
                    setOut(data, decimal);
                }
            }

        } catch (Exception e) {
        }
    }

    private void setIn(Transfer data, String decimal) {
        String s = BigDecimalUtils.weiToEthWithDecimal(data.getCount(), Integer.parseInt(decimal));
        mTvCount.setText("+" + s);
        mTvCount.setTextColor(getResources().getColor(R.color.green_text));
    }

    private void setOut(Transfer data, String decimal) {
        mTvCount.setText("-" + BigDecimalUtils.weiToEthWithDecimal(data.getCount(), Integer.parseInt(decimal)));
        mTvCount.setTextColor(getResources().getColor(R.color.red_text));
    }
}
