// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ProposalOptionItemView_ViewBinding implements Unbinder {
  private ProposalOptionItemView target;

  @UiThread
  public ProposalOptionItemView_ViewBinding(ProposalOptionItemView target) {
    this(target, target);
  }

  @UiThread
  public ProposalOptionItemView_ViewBinding(ProposalOptionItemView target, View source) {
    this.target = target;

    target.mTvProposalOption = Utils.findRequiredViewAsType(source, R.id.tv_proposal_option, "field 'mTvProposalOption'", TextView.class);
    target.mTvPercent = Utils.findRequiredViewAsType(source, R.id.tv_percent, "field 'mTvPercent'", TextView.class);
    target.mRlRoot = Utils.findRequiredViewAsType(source, R.id.rl_root, "field 'mRlRoot'", RelativeLayout.class);
    target.mBgProgress = Utils.findRequiredViewAsType(source, R.id.bg_progress, "field 'mBgProgress'", ProgressBar.class);
    target.mIvSelect = Utils.findRequiredViewAsType(source, R.id.iv_select, "field 'mIvSelect'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    ProposalOptionItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvProposalOption = null;
    target.mTvPercent = null;
    target.mRlRoot = null;
    target.mBgProgress = null;
    target.mIvSelect = null;
  }
}
