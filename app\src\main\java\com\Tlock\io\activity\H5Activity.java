//package com.Tlock.io.activity;
//
//import android.content.ClipData;
//import android.content.ClipboardManager;
//import android.content.Context;
//import android.content.Intent;
//import android.graphics.PixelFormat;
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.view.KeyEvent;
//import android.view.View;
//import android.widget.LinearLayout;
//import android.widget.ProgressBar;
//
//import com.Tlock.io.R;
//import com.Tlock.io.base.BaseActivity;
//import com.Tlock.io.widget.CustomNavBar;
//import com.Tlock.io.widget.X5WebView;
//import com.Tlock.io.widget.pop.PopH5More;
//import com.lxj.xpopup.XPopup;
//import com.lxj.xpopup.core.BasePopupView;
//import com.tencent.smtt.sdk.URLUtil;
//import com.tencent.smtt.sdk.WebBackForwardList;
//import com.tencent.smtt.sdk.WebHistoryItem;
//import com.tencent.smtt.sdk.WebView;
//import com.tencent.smtt.sdk.WebViewClient;
//
//import butterknife.BindView;
//
//public class H5Activity extends BaseActivity {
//
//    @BindView(R.id.custom_nav_bar)
//    CustomNavBar mCustomNavBar;
//
//    @BindView(R.id.full_web_webview)
//    X5WebView webView;
//    @BindView(R.id.pb_load_progress)
//    ProgressBar mPbLoadProgress;
//    @BindView(R.id.ll_root)
//    LinearLayout mLlRoot;
//
//    /**
//     * 跳转
//     *
//     * @param context 上下文
//     */
//    public static void start(Context context, String url, String title) {
//        Intent intent = new Intent(context, H5Activity.class);
//        intent.putExtra("url", url);
//        intent.putExtra("title", title);
//
//        context.startActivity(intent);
//    }
//
//    @Override
//    protected int getContentViewId() {
//        return R.layout.activity_h5;
//    }
//
//    @Override
//    protected void initView(Bundle savedInstanceState) {
//        mPbLoadProgress.setMax(100);
//    }
//
//    @Override
//    protected void loadData() {
//        mPbLoadProgress.setVisibility(View.VISIBLE);
//        mPbLoadProgress.setProgress(0);
//        String url = getIntent().getStringExtra("url");
//        String title = getIntent().getStringExtra("url");
//        mCustomNavBar.setMidTitle(title);
//        mCustomNavBar.setRightIcon(R.mipmap.icon_more);
//        mCustomNavBar.setOnRightClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                PopH5More popH5More = new PopH5More(getActivity());
//                popH5More.setClickCallback(new PopH5More.ClickCallback() {
//                    @Override
//                    public void onRefresh(View v) {
//                        webView.reload();
//                        popH5More.dismiss();
//                    }
//
//                    @Override
//                    public void onCopyUrl(View v) {
//                        ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
//                        ClipData mClipData = ClipData.newPlainText("Label", url);
//                        cm.setPrimaryClip(mClipData);
//                        showToast(getString(R.string.copy));
//                        popH5More.dismiss();
//                    }
//                });
//
//                BasePopupView basePopupView = new XPopup.Builder(getActivity())
//                        .asCustom(popH5More);
//
//                basePopupView.show();
//            }
//        });
//        if (TextUtils.isEmpty(url)) {
//            return;
//        }
//        webView.loadUrl(url);
//        webView.getSettings().setJavaScriptEnabled(true);
//        webView.setWebViewClient(new WebViewClient(){
//            @Override
//            public boolean shouldOverrideUrlLoading(WebView view, String url) {
//                if (URLUtil.isNetworkUrl(url)){
//                    view.loadUrl(url);
//                    return super.shouldOverrideUrlLoading(view, url);
//                } else {
//                    try {
//                        startActivity(Intent.parseUri(url, Intent.URI_INTENT_SCHEME));
//                    } catch (Exception ex) {
//                        ex.printStackTrace();
//                    }
//                    return true;
//                }
//            }
//
//        });
//        getWindow().setFormat(PixelFormat.TRANSLUCENT);
//    }
//
//    private void getWebTitle() {
//        WebBackForwardList forwardList = webView.copyBackForwardList();
//        WebHistoryItem item = forwardList.getCurrentItem();
//        if (item != null) {
//            mCustomNavBar.setMidTitle(item.getTitle());
//        }
//    }
//
//    private void onWebViewGoBack() {
//        webView.goBack();
//        getWebTitle();
//    }
//
//    @Override
//    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        if (webView.canGoBack()) {
//            onWebViewGoBack();
//            return false;
//        }
//        return super.onKeyDown(keyCode, event);
//    }
//
//
//}