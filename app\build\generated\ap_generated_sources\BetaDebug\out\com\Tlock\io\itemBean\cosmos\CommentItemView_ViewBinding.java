// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CommentItemView_ViewBinding implements Unbinder {
  private CommentItemView target;

  private View view7f090151;

  private View view7f090314;

  private View view7f090339;

  private View view7f0901a3;

  private View view7f0901a0;

  private View view7f09023e;

  @UiThread
  public CommentItemView_ViewBinding(CommentItemView target) {
    this(target, target);
  }

  @UiThread
  public CommentItemView_ViewBinding(final CommentItemView target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_heard, "field 'mIvHeard' and method 'onBindClick'");
    target.mIvHeard = Utils.castView(view, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    view7f090151 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvAccountName = Utils.findRequiredViewAsType(source, R.id.tv_account_name, "field 'mTvAccountName'", FontTextView.class);
    target.mTvHandle = Utils.findRequiredViewAsType(source, R.id.tv_handle, "field 'mTvHandle'", FontTextView.class);
    view = Utils.findRequiredView(source, R.id.tv_content, "field 'mTvContent' and method 'onBindClick'");
    target.mTvContent = Utils.castView(view, R.id.tv_content, "field 'mTvContent'", FontTextView.class);
    view7f090314 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvPost = Utils.findRequiredViewAsType(source, R.id.iv_post, "field 'mIvPost'", ImageView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_isShow, "field 'mTvIsShow' and method 'onBindClick'");
    target.mTvIsShow = Utils.castView(view, R.id.tv_isShow, "field 'mTvIsShow'", TextView.class);
    view7f090339 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvReview = Utils.findRequiredViewAsType(source, R.id.iv_review, "field 'mIvReview'", ImageView.class);
    target.mTvReview = Utils.findRequiredViewAsType(source, R.id.tv_review, "field 'mTvReview'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_review, "field 'mLlReview' and method 'onBindClick'");
    target.mLlReview = Utils.castView(view, R.id.ll_review, "field 'mLlReview'", RelativeLayout.class);
    view7f0901a3 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvPraise = Utils.findRequiredViewAsType(source, R.id.iv_praise, "field 'mIvPraise'", ImageView.class);
    target.mTvPraise = Utils.findRequiredViewAsType(source, R.id.tv_praise, "field 'mTvPraise'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_praise, "field 'mLlPraise' and method 'onBindClick'");
    target.mLlPraise = Utils.castView(view, R.id.ll_praise, "field 'mLlPraise'", RelativeLayout.class);
    view7f0901a0 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mConstraint = Utils.findRequiredViewAsType(source, R.id.constraint, "field 'mConstraint'", LinearLayout.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mLlCommentChild = Utils.findRequiredViewAsType(source, R.id.ll_comment_child, "field 'mLlCommentChild'", LinearLayout.class);
    view = Utils.findRequiredView(source, R.id.rl_comment, "method 'onBindClick'");
    view7f09023e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    CommentItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvHeard = null;
    target.mTvAccountName = null;
    target.mTvHandle = null;
    target.mTvContent = null;
    target.mIvPost = null;
    target.mTvTime = null;
    target.mTvIsShow = null;
    target.mIvReview = null;
    target.mTvReview = null;
    target.mLlReview = null;
    target.mIvPraise = null;
    target.mTvPraise = null;
    target.mLlPraise = null;
    target.mConstraint = null;
    target.mLine1 = null;
    target.mLlCommentChild = null;

    view7f090151.setOnClickListener(null);
    view7f090151 = null;
    view7f090314.setOnClickListener(null);
    view7f090314 = null;
    view7f090339.setOnClickListener(null);
    view7f090339 = null;
    view7f0901a3.setOnClickListener(null);
    view7f0901a3 = null;
    view7f0901a0.setOnClickListener(null);
    view7f0901a0 = null;
    view7f09023e.setOnClickListener(null);
    view7f09023e = null;
  }
}
