// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopWalletEditName_ViewBinding implements Unbinder {
  private PopWalletEditName target;

  private View view7f090311;

  private View view7f09013f;

  @UiThread
  public PopWalletEditName_ViewBinding(PopWalletEditName target) {
    this(target, target);
  }

  @UiThread
  public PopWalletEditName_ViewBinding(final PopWalletEditName target, View source) {
    this.target = target;

    View view;
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mEdName = Utils.findRequiredViewAsType(source, R.id.ed_name, "field 'mEdName'", EditText.class);
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onViewClicked'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f090311 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_close, "method 'onViewClicked'");
    view7f09013f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopWalletEditName target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mEdName = null;
    target.mTvConfirm = null;

    view7f090311.setOnClickListener(null);
    view7f090311 = null;
    view7f09013f.setOnClickListener(null);
    view7f09013f = null;
  }
}
