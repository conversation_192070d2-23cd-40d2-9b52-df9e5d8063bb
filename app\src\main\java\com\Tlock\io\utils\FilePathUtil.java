package com.Tlock.io.utils;


import static android.content.Context.MODE_PRIVATE;

import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.OpenableColumns;
import android.text.TextUtils;

import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 本类的主要功能是 :  文件夹工具类
 * <p>
 * 存储分为:
 * 1,手机内部存储(系统级,不建议存储)-->
 * {@link #getAppIntenalPublicDir(Context, String)} e.g. /data/user/0/{包名}/app_/{subPath}
 * {@link #getAppIntenalFilesDir(Context, String)}  e.g. /data/user/0/{包名}/files/{subPath}
 * {@link #getAppIntenalCacheDir(Context)}          e.g. /data/user/0/{包名}/cache
 * <p>
 * 2,手机外部存储(自带存储或SD)-->
 * (1) app外,公共存储,可以被外部识别,卸载App不会被清除
 * {@link #getAppExternalPublicDir(String)}         e.g. /storage/emulated/0/{@link #EXTERNAL_PUBLIC_ROOT_DIR}/{subPath}
 * <p>
 * (2) app内,本app专属目录,不可以被外部识别,卸载app会被清除
 * {@link #getAppExternalFilesDir(Context, String)} e.g. /storage/emulated/0/Android/data/{包名}/files/{subPath}
 * {@link #getAppExternalCacheDir(Context)}         e.g. /storage/emulated/0/Android/data/{包名}/cache
 */
public class FilePathUtil {

    // json 文件
    public static final String JSON_FILE_DIR = "json-file";
    //上传图片文件夹名
    public static final String UPLOAD_IMAGE_DIR = "image-upload";
    //图片文件夹名
    public static final String IMAGE_DIR = "image";
    //保存图书文件夹名
    public static final String BOOK_DIR = "book";
    //保存Audio文件夹名
    public static final String AUDIO_DIR = "audio";
    // apk保存目录
    public static final String APK_DIR = "apk";
    // SD卡文件根目录
    private static final String EXTERNAL_PUBLIC_ROOT_DIR = "com.tlock.io";


    private static final FilePathUtil ourInstance = new FilePathUtil();

    private FilePathUtil() {
    }

    public static FilePathUtil getInstance() {
        return ourInstance;
    }


    /*******内部隐私存储(不建议存储)**********************************************************************************/

    /**
     * 内部隐私存储缓存 /data/user/0/{包名}/cache
     * 内部隐私存储(不建议存储)
     *
     * @param context
     * @return
     */
    public static String getAppIntenalCacheDir(Context context) {
        if (context == null) {
            return null;
        }
        //内部隐私存储缓存 /data/user/0/com.jingkai.lawapp/cache
        File dir = context.getCacheDir();
        if (dir == null) {
            return null;
        }
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir.getAbsolutePath();
    }

    /**
     * //内部隐私存储缓存 /data/user/0/{包名}/files/{subPath}
     * 内部隐私存储(不建议存储)
     *
     * @param context
     * @param subPath
     * @return
     */

    public static String getAppIntenalFilesDir(Context context, String subPath) {
        if (TextUtils.isEmpty(subPath)) {
            subPath = "";
        }
        if (context == null) {
            return null;
        }
        //内部隐私存储缓存 /data/user/0/com.jingkai.lawapp/files
        File dir = context.getFilesDir();
        if (dir == null) {
            return null;
        }
        File file = new File(dir, subPath);
        if (!file.exists()) {
            file.mkdirs();
        }
        return file.getAbsolutePath();
    }

    /**
     * //内部隐私存储缓存 /data/user/0/{包名}/app_/{subPath}
     * 内部隐私存储(不建议存储)
     *
     * @param context
     * @param subPath
     * @return
     */
    public static String getAppIntenalPublicDir(Context context, String subPath) {
        if (TextUtils.isEmpty(subPath)) {
            subPath = "";
        }
        if (context == null) {
            return null;
        }
        //内部隐私存储缓存 /data/user/0/com.jingkai.lawapp/app_
        File dir = context.getDir("", MODE_PRIVATE);
        if (dir == null) {
            return null;
        }
        File file = new File(dir, subPath);
        if (!file.exists()) {
            file.mkdirs();
        }
        return file.getAbsolutePath();
    }


    /******获取外部存储公共(根目录)***********************************************************************************/

    /**
     * 获取外部存储根目录或根目录自定义文件夹 /storage/emulated/0/{@link #EXTERNAL_PUBLIC_ROOT_DIR}/{subPath}
     *
     * @param subPath 自定义文件夹 可以为空 ,   abc/cde
     * @return
     */
    public static String getAppExternalPublicDir(String subPath) {
        if (TextUtils.isEmpty(subPath)) {
            subPath = "";
        }
        File dir = Environment.getExternalStoragePublicDirectory(EXTERNAL_PUBLIC_ROOT_DIR + "/" + subPath);
        if (dir == null) {
            return null;
        }
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir.getAbsolutePath();
    }


    /******获取app外部存储(卸载app可以被清空掉)***********************************************************************************/
    /**
     * //卸载app可以被清空掉
     * //获取app外部存储专属路径 /storage/emulated/0/Android/data/{包名}/cache
     *
     * @param context
     * @return
     */
    public static String getAppExternalCacheDir(Context context) {
        if (context == null) {
            return null;
        }
        //获取app外部存储专属路径 /storage/emulated/0/Android/data/{包名}/cache
        File dir = context.getExternalCacheDir();
        if (dir == null) {
            return null;
        }
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir.getAbsolutePath();
    }

    /**
     * //卸载app可以被清空掉
     * //获取app外部存储专属路径 /storage/emulated/0/Android/data/{包名}/files/{subPath}
     *
     * @param context
     * @param subPath 扩展路径 可以为 ""   abc/cde
     * @return
     */
    public static String getAppExternalFilesDir(Context context, String subPath) {
        if (TextUtils.isEmpty(subPath)) {
            subPath = "";
        }
        if (context == null) {
            return null;
        }
        //获取app外部存储专属路径 /storage/emulated/0/Android/data/{包名}/files/abcd
        File dir = context.getExternalFilesDir(subPath);
        if (dir == null) {
            return null;
        }
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir.getAbsolutePath();
    }

    //写文件
    public void writeSDFile(String fileName, String write_str) throws IOException {
        try {
            File file = new File(fileName);

            FileOutputStream fos = new FileOutputStream(file);

            byte[] bytes = write_str.getBytes();

            fos.write(bytes);

            fos.close();
        } catch (Exception e) {
        }

    }

    /**
     * 将单个 Uri 转换为 File
     *
     * @param context 上下文
     * @param uri     图片的 Uri
     * @return File 文件
     */
    public static File convertUriToFile(Context context, Uri uri) {
        ContentResolver contentResolver = context.getContentResolver();
        String fileName = getFileName(context, uri);

        try {
            // 创建临时文件
            File file = new File(context.getCacheDir(), fileName);
            InputStream inputStream = contentResolver.openInputStream(uri);

            if (inputStream != null) {
                FileOutputStream outputStream = new FileOutputStream(file);
                byte[] buffer = new byte[1024];
                int bytesRead;

                // 将输入流写入文件
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                outputStream.close();
                inputStream.close();
                return file;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 从 Uri 获取文件名
     *
     * @param context 上下文
     * @param uri     图片的 Uri
     * @return 文件名
     */
    private static String getFileName(Context context, Uri uri) {
        String fileName = null;
        ContentResolver contentResolver = context.getContentResolver();

        Cursor cursor = contentResolver.query(uri, null, null, null, null);
        if (cursor != null && cursor.moveToFirst()) {
            int nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
            if (nameIndex != -1) {
                fileName = cursor.getString(nameIndex);
            }
            cursor.close();
        }

        // 如果无法获取文件名，使用默认值
        if (fileName == null) {
            fileName = "image_" + System.currentTimeMillis() + ".jpg";
        }

        return fileName;
    }

}
