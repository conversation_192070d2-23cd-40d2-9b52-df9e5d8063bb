<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_5"
    android:id="@+id/root"
    android:paddingTop="@dimen/dp_12">

    <ImageView
        android:id="@+id/iv_heard"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_8"
        android:src="@drawable/shape_light_gray_60"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_account_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text="name"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_14"
        app:fontType="name"
        app:layout_constraintEnd_toStartOf="@id/tv_time"
        app:layout_constraintStart_toEndOf="@id/iv_heard"
        app:layout_constraintTop_toTopOf="@id/iv_heard" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_6"
        android:text="time"
        android:textColor="@color/cosmos_default"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_account_name"
        app:layout_constraintEnd_toStartOf="@id/iv_more" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="@dimen/dp_20"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_gray_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_account_name" />

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/tv_account_name">

        <com.Tlock.io.widget.FontTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_6"
            android:lineHeight="@dimen/dp_19"
            android:maxLines="2"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_15"
            app:fontFamily="@font/font_family_bold"
            app:fontType="name" />

        <com.Tlock.io.widget.FontTextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:lineHeight="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_6"
            android:ellipsize="end"
            android:lineHeight="@dimen/dp_19"
            android:maxLines="4"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_15"
            app:fontFamily="@font/font_family"
            app:fontType="content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_image"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp_6"
            android:nestedScrollingEnabled="false" />

        <LinearLayout
            android:id="@+id/ll_content_quote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:visibility="gone"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@drawable/bg_gray_13"
            android:orientation="vertical"
            android:padding="@dimen/dp_10">

            <TextView
                android:id="@+id/tv_quote_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_quote_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/sp_13" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_vote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_voteTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:text="1 votes · 2025/2/11 12:00"
            android:visibility="gone" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_time1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/cosmos_default"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ll_content"
        app:layout_constraintTop_toBottomOf="@id/ll_content" />

    <!-- 优化后的操作栏 - 使用LinearLayout替代多个RelativeLayout -->
    <LinearLayout
        android:id="@+id/constraint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="horizontal"
        android:weightSum="5"
        app:layout_constraintEnd_toEndOf="@id/ll_content"
        app:layout_constraintStart_toStartOf="@id/ll_content"
        app:layout_constraintTop_toBottomOf="@id/tv_time1">

        <LinearLayout
            android:id="@+id/ll_praise"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="start|center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp_10"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:id="@+id/iv_praise"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_7"
                android:adjustViewBounds="true"
                android:src="@mipmap/icon_post_like" />

            <TextView
                android:id="@+id/tv_praise"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/dp_12" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_quote"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="start|center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp_10"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:id="@+id/iv_quote"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_7"
                android:adjustViewBounds="true"
                android:src="@mipmap/icon_post_forward" />

            <TextView
                android:id="@+id/tv_quote"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/dp_12" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_review"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="start|center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp_10"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:id="@+id/iv_review"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_7"
                android:adjustViewBounds="true"
                android:src="@mipmap/icon_post_comment" />

            <TextView
                android:id="@+id/tv_review"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/dp_12" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_Collect"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="start|center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp_10"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:id="@+id/iv_Collect"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_7"
                android:adjustViewBounds="true"
                android:src="@mipmap/icon_post_save" />

            <TextView
                android:id="@+id/tv_Collect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/dp_12" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_share"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="start|center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp_10"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:id="@+id/iv_share"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_7"
                android:adjustViewBounds="true"
                android:src="@mipmap/icon_post_share" />

            <TextView
                android:id="@+id/tv_share"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/dp_12" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/line1"
        style="@style/deep_gray_horizontal_line_view"
        android:layout_marginTop="@dimen/dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/constraint" />

</androidx.constraintlayout.widget.ConstraintLayout>

