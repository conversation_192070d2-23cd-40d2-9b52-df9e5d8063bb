<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_5"
    android:id="@+id/root"
    android:paddingTop="@dimen/dp_12">

    <ImageView
        android:id="@+id/iv_heard"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_8"
        android:src="@drawable/shape_light_gray_60"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_account_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text="name"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_14"
        app:fontType="name"
        app:layout_constraintEnd_toStartOf="@id/tv_time"
        app:layout_constraintStart_toEndOf="@id/iv_heard"
        app:layout_constraintTop_toTopOf="@id/iv_heard" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_6"
        android:text="time"
        android:textColor="@color/cosmos_default"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_account_name"
        app:layout_constraintEnd_toStartOf="@id/iv_more" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="@dimen/dp_20"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_gray_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_account_name" />

    <!-- 直接在ConstraintLayout中放置内容视图，移除ll_content嵌套 -->
    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_16"
        android:lineHeight="@dimen/dp_19"
        android:maxLines="2"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_15"
        app:fontFamily="@font/font_family_bold"
        app:fontType="name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/tv_account_name" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_16"
        app:lineHeight="@dimen/dp_20"
        android:ellipsize="end"
        android:lineHeight="@dimen/dp_19"
        android:maxLines="4"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_15"
        app:fontFamily="@font/font_family"
        app:fontType="content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_image"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_16"
        android:visibility="gone"
        android:nestedScrollingEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <LinearLayout
        android:id="@+id/ll_content_quote"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_10"
        android:visibility="gone"
        android:background="@drawable/bg_gray_13"
        android:orientation="vertical"
        android:padding="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/rv_image">

        <TextView
            android:id="@+id/tv_quote_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_quote_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_13" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_vote"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/ll_content_quote" />

    <TextView
        android:id="@+id/tv_voteTime"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="1 votes · 2025/2/11 12:00"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/ll_vote" />

    <!-- 使用Barrier来处理内容区域的底部边界 -->
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_content_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_content,rv_image,ll_content_quote,ll_vote,tv_voteTime" />

    <TextView
        android:id="@+id/tv_time1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/cosmos_default"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintTop_toBottomOf="@id/barrier_content_bottom" />

    <!-- 创建5个等宽区域的引导线 -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_20"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.2" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_40"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_60"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.6" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_80"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.8" />

    <!-- 点赞按钮 -->
    <ImageView
        android:id="@+id/iv_praise"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_15"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_post_like"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintEnd_toStartOf="@id/guideline_20"
        app:layout_constraintTop_toBottomOf="@id/tv_time1" />

    <TextView
        android:id="@+id/tv_praise"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_7"
        android:includeFontPadding="false"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_praise"
        app:layout_constraintStart_toEndOf="@id/iv_praise" />

    <!-- 转发按钮 -->
    <ImageView
        android:id="@+id/iv_quote"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_post_forward"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_praise"
        app:layout_constraintStart_toStartOf="@id/guideline_20"
        app:layout_constraintEnd_toStartOf="@id/guideline_40" />

    <TextView
        android:id="@+id/tv_quote"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_7"
        android:includeFontPadding="false"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_quote"
        app:layout_constraintStart_toEndOf="@id/iv_quote" />

    <!-- 评论按钮 -->
    <ImageView
        android:id="@+id/iv_review"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_post_comment"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_praise"
        app:layout_constraintStart_toStartOf="@id/guideline_40"
        app:layout_constraintEnd_toStartOf="@id/guideline_60" />

    <TextView
        android:id="@+id/tv_review"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_7"
        android:includeFontPadding="false"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_review"
        app:layout_constraintStart_toEndOf="@id/iv_review" />

    <!-- 收藏按钮 -->
    <ImageView
        android:id="@+id/iv_Collect"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_post_save"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_praise"
        app:layout_constraintStart_toStartOf="@id/guideline_60"
        app:layout_constraintEnd_toStartOf="@id/guideline_80" />

    <TextView
        android:id="@+id/tv_Collect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_7"
        android:includeFontPadding="false"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_Collect"
        app:layout_constraintStart_toEndOf="@id/iv_Collect" />

    <!-- 分享按钮 -->
    <ImageView
        android:id="@+id/iv_share"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:adjustViewBounds="true"
        android:src="@mipmap/icon_post_share"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_praise"
        app:layout_constraintStart_toStartOf="@id/guideline_80"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_7"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBaseline_toBaselineOf="@id/iv_share"
        app:layout_constraintStart_toEndOf="@id/iv_share" />

    <!-- 为了保持代码兼容性，添加透明的点击区域 -->
    <View
        android:id="@+id/ll_praise"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        app:layout_constraintStart_toStartOf="@id/tv_account_name"
        app:layout_constraintEnd_toEndOf="@id/guideline_20"
        app:layout_constraintTop_toTopOf="@id/iv_praise"
        app:layout_constraintBottom_toBottomOf="@id/iv_praise" />

    <View
        android:id="@+id/ll_quote"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        app:layout_constraintStart_toStartOf="@id/guideline_20"
        app:layout_constraintEnd_toEndOf="@id/guideline_40"
        app:layout_constraintTop_toTopOf="@id/iv_quote"
        app:layout_constraintBottom_toBottomOf="@id/iv_quote" />

    <View
        android:id="@+id/ll_review"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        app:layout_constraintStart_toStartOf="@id/guideline_40"
        app:layout_constraintEnd_toEndOf="@id/guideline_60"
        app:layout_constraintTop_toTopOf="@id/iv_review"
        app:layout_constraintBottom_toBottomOf="@id/iv_review" />

    <View
        android:id="@+id/ll_Collect"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        app:layout_constraintStart_toStartOf="@id/guideline_60"
        app:layout_constraintEnd_toEndOf="@id/guideline_80"
        app:layout_constraintTop_toTopOf="@id/iv_Collect"
        app:layout_constraintBottom_toBottomOf="@id/iv_Collect" />

    <View
        android:id="@+id/ll_share"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackground"
        app:layout_constraintStart_toStartOf="@id/guideline_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_share"
        app:layout_constraintBottom_toBottomOf="@id/iv_share" />

    <!-- 使用Barrier来处理操作按钮的底部边界 -->
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_actions_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="ll_praise,ll_quote,ll_review,ll_Collect,ll_share" />

    <View
        android:id="@+id/line1"
        style="@style/deep_gray_horizontal_line_view"
        android:layout_marginTop="@dimen/dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier_actions_bottom" />

</androidx.constraintlayout.widget.ConstraintLayout>

