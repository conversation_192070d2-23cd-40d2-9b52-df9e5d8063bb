package com.Tlock.io.entity.wallet;

/**
 * @ClassName AccountTransaction
 * <AUTHOR>
 * @Data 2021/11/25 14:02
 * @Desc
 */

public class AccountTransaction {

    /**
     * accountAddress :
     * amount :
     * gasPrice :
     * payStatus : 0
     * status : true
     * time : 0
     * transactionHash :
     */

    private String accountAddress;
    private String amount;
    private String gasPrice;
    private String gasLimit;
    private String gasUse;
    private String symbol;
    private int payStatus;
    private int logIndex;
    private boolean status;
    private int time;
    private String transactionHash;

    public int getLogIndex() {
        return logIndex;
    }

    public void setLogIndex(int logIndex) {
        this.logIndex = logIndex;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getGasUse() {
        return gasUse;
    }

    public void setGasUse(String gasUse) {
        this.gasUse = gasUse;
    }

    public String getGasLimit() {
        return gasLimit;
    }

    public void setGasLimit(String gasLimit) {
        this.gasLimit = gasLimit;
    }

    public String getAccountAddress() {
        return accountAddress;
    }

    public void setAccountAddress(String accountAddress) {
        this.accountAddress = accountAddress;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getGasPrice() {
        return gasPrice;
    }

    public void setGasPrice(String gasPrice) {
        this.gasPrice = gasPrice;
    }

    public int getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(int payStatus) {
        this.payStatus = payStatus;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public String getTransactionHash() {
        return transactionHash;
    }

    public void setTransactionHash(String transactionHash) {
        this.transactionHash = transactionHash;
    }
}
