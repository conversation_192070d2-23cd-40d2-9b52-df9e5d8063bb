// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: profile/profile_query.proto
// Protobuf Java Version: 4.28.3

package com.Tlock.io.profile;

public final class ProfileQueryProto {
  private ProfileQueryProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      ProfileQueryProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryGetMentionSuggestionsRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryGetMentionSuggestionsRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    java.lang.String getAddress();
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    com.google.protobuf.ByteString
        getAddressBytes();

    /**
     * <code>string matching = 2;</code>
     * @return The matching.
     */
    java.lang.String getMatching();
    /**
     * <code>string matching = 2;</code>
     * @return The bytes for matching.
     */
    com.google.protobuf.ByteString
        getMatchingBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryGetMentionSuggestionsRequest}
   */
  public static final class QueryGetMentionSuggestionsRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryGetMentionSuggestionsRequest)
      QueryGetMentionSuggestionsRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryGetMentionSuggestionsRequest.class.getName());
    }
    // Use QueryGetMentionSuggestionsRequest.newBuilder() to construct.
    private QueryGetMentionSuggestionsRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryGetMentionSuggestionsRequest() {
      address_ = "";
      matching_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.Builder.class);
    }

    public static final int ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object address_ = "";
    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    @java.lang.Override
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      }
    }
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MATCHING_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object matching_ = "";
    /**
     * <code>string matching = 2;</code>
     * @return The matching.
     */
    @java.lang.Override
    public java.lang.String getMatching() {
      java.lang.Object ref = matching_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        matching_ = s;
        return s;
      }
    }
    /**
     * <code>string matching = 2;</code>
     * @return The bytes for matching.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMatchingBytes() {
      java.lang.Object ref = matching_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        matching_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, address_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(matching_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, matching_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, address_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(matching_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, matching_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest) obj;

      if (!getAddress()
          .equals(other.getAddress())) return false;
      if (!getMatching()
          .equals(other.getMatching())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAddress().hashCode();
      hash = (37 * hash) + MATCHING_FIELD_NUMBER;
      hash = (53 * hash) + getMatching().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryGetMentionSuggestionsRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryGetMentionSuggestionsRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        address_ = "";
        matching_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.address_ = address_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.matching_ = matching_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest.getDefaultInstance()) return this;
        if (!other.getAddress().isEmpty()) {
          address_ = other.address_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getMatching().isEmpty()) {
          matching_ = other.matching_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                address_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                matching_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object address_ = "";
      /**
       * <code>string address = 1;</code>
       * @return The address.
       */
      public java.lang.String getAddress() {
        java.lang.Object ref = address_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          address_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @return The bytes for address.
       */
      public com.google.protobuf.ByteString
          getAddressBytes() {
        java.lang.Object ref = address_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          address_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @param value The address to set.
       * @return This builder for chaining.
       */
      public Builder setAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddress() {
        address_ = getDefaultInstance().getAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @param value The bytes for address to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object matching_ = "";
      /**
       * <code>string matching = 2;</code>
       * @return The matching.
       */
      public java.lang.String getMatching() {
        java.lang.Object ref = matching_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          matching_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string matching = 2;</code>
       * @return The bytes for matching.
       */
      public com.google.protobuf.ByteString
          getMatchingBytes() {
        java.lang.Object ref = matching_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          matching_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string matching = 2;</code>
       * @param value The matching to set.
       * @return This builder for chaining.
       */
      public Builder setMatching(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        matching_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string matching = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMatching() {
        matching_ = getDefaultInstance().getMatching();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string matching = 2;</code>
       * @param value The bytes for matching to set.
       * @return This builder for chaining.
       */
      public Builder setMatchingBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        matching_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryGetMentionSuggestionsRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryGetMentionSuggestionsRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryGetMentionSuggestionsRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryGetMentionSuggestionsRequest>() {
      @java.lang.Override
      public QueryGetMentionSuggestionsRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryGetMentionSuggestionsRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryGetMentionSuggestionsRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryGetMentionSuggestionsResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryGetMentionSuggestionsResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> 
        getProfilesList();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index);
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    int getProfilesCount();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getProfilesOrBuilderList();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code profile.v1.QueryGetMentionSuggestionsResponse}
   */
  public static final class QueryGetMentionSuggestionsResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryGetMentionSuggestionsResponse)
      QueryGetMentionSuggestionsResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryGetMentionSuggestionsResponse.class.getName());
    }
    // Use QueryGetMentionSuggestionsResponse.newBuilder() to construct.
    private QueryGetMentionSuggestionsResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryGetMentionSuggestionsResponse() {
      profiles_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.Builder.class);
    }

    public static final int PROFILES_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> profiles_;
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getProfilesList() {
      return profiles_;
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getProfilesOrBuilderList() {
      return profiles_;
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public int getProfilesCount() {
      return profiles_.size();
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index) {
      return profiles_.get(index);
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
        int index) {
      return profiles_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < profiles_.size(); i++) {
        output.writeMessage(1, profiles_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < profiles_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, profiles_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse) obj;

      if (!getProfilesList()
          .equals(other.getProfilesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getProfilesCount() > 0) {
        hash = (37 * hash) + PROFILES_FIELD_NUMBER;
        hash = (53 * hash) + getProfilesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryGetMentionSuggestionsResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryGetMentionSuggestionsResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (profilesBuilder_ == null) {
          profiles_ = java.util.Collections.emptyList();
        } else {
          profiles_ = null;
          profilesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryGetMentionSuggestionsResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse result) {
        if (profilesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            profiles_ = java.util.Collections.unmodifiableList(profiles_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.profiles_ = profiles_;
        } else {
          result.profiles_ = profilesBuilder_.build();
        }
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse.getDefaultInstance()) return this;
        if (profilesBuilder_ == null) {
          if (!other.profiles_.isEmpty()) {
            if (profiles_.isEmpty()) {
              profiles_ = other.profiles_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureProfilesIsMutable();
              profiles_.addAll(other.profiles_);
            }
            onChanged();
          }
        } else {
          if (!other.profiles_.isEmpty()) {
            if (profilesBuilder_.isEmpty()) {
              profilesBuilder_.dispose();
              profilesBuilder_ = null;
              profiles_ = other.profiles_;
              bitField0_ = (bitField0_ & ~0x00000001);
              profilesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getProfilesFieldBuilder() : null;
            } else {
              profilesBuilder_.addAllMessages(other.profiles_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.Tlock.io.entity.profile.ProfileProto.Profile m =
                    input.readMessage(
                        com.Tlock.io.entity.profile.ProfileProto.Profile.parser(),
                        extensionRegistry);
                if (profilesBuilder_ == null) {
                  ensureProfilesIsMutable();
                  profiles_.add(m);
                } else {
                  profilesBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> profiles_ =
        java.util.Collections.emptyList();
      private void ensureProfilesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          profiles_ = new java.util.ArrayList<com.Tlock.io.entity.profile.ProfileProto.Profile>(profiles_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> profilesBuilder_;

      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getProfilesList() {
        if (profilesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(profiles_);
        } else {
          return profilesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public int getProfilesCount() {
        if (profilesBuilder_ == null) {
          return profiles_.size();
        } else {
          return profilesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index) {
        if (profilesBuilder_ == null) {
          return profiles_.get(index);
        } else {
          return profilesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder setProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.set(index, value);
          onChanged();
        } else {
          profilesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder setProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.set(index, builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.add(value);
          onChanged();
        } else {
          profilesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.add(index, value);
          onChanged();
        } else {
          profilesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.add(builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.add(index, builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addAllProfiles(
          java.lang.Iterable<? extends com.Tlock.io.entity.profile.ProfileProto.Profile> values) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, profiles_);
          onChanged();
        } else {
          profilesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder clearProfiles() {
        if (profilesBuilder_ == null) {
          profiles_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          profilesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder removeProfiles(int index) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.remove(index);
          onChanged();
        } else {
          profilesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder getProfilesBuilder(
          int index) {
        return getProfilesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
          int index) {
        if (profilesBuilder_ == null) {
          return profiles_.get(index);  } else {
          return profilesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
           getProfilesOrBuilderList() {
        if (profilesBuilder_ != null) {
          return profilesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(profiles_);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addProfilesBuilder() {
        return getProfilesFieldBuilder().addBuilder(
            com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addProfilesBuilder(
          int index) {
        return getProfilesFieldBuilder().addBuilder(
            index, com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile.Builder> 
           getProfilesBuilderList() {
        return getProfilesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
          getProfilesFieldBuilder() {
        if (profilesBuilder_ == null) {
          profilesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder>(
                  profiles_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          profiles_ = null;
        }
        return profilesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryGetMentionSuggestionsResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryGetMentionSuggestionsResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryGetMentionSuggestionsResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryGetMentionSuggestionsResponse>() {
      @java.lang.Override
      public QueryGetMentionSuggestionsResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryGetMentionSuggestionsResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryGetMentionSuggestionsResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryGetMentionSuggestionsResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryProfileAvatarRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryProfileAvatarRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    java.lang.String getAddress();
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    com.google.protobuf.ByteString
        getAddressBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryProfileAvatarRequest}
   */
  public static final class QueryProfileAvatarRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryProfileAvatarRequest)
      QueryProfileAvatarRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryProfileAvatarRequest.class.getName());
    }
    // Use QueryProfileAvatarRequest.newBuilder() to construct.
    private QueryProfileAvatarRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryProfileAvatarRequest() {
      address_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.Builder.class);
    }

    public static final int ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object address_ = "";
    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    @java.lang.Override
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      }
    }
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, address_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, address_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest) obj;

      if (!getAddress()
          .equals(other.getAddress())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAddress().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryProfileAvatarRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryProfileAvatarRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        address_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.address_ = address_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest.getDefaultInstance()) return this;
        if (!other.getAddress().isEmpty()) {
          address_ = other.address_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                address_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object address_ = "";
      /**
       * <code>string address = 1;</code>
       * @return The address.
       */
      public java.lang.String getAddress() {
        java.lang.Object ref = address_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          address_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @return The bytes for address.
       */
      public com.google.protobuf.ByteString
          getAddressBytes() {
        java.lang.Object ref = address_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          address_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @param value The address to set.
       * @return This builder for chaining.
       */
      public Builder setAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddress() {
        address_ = getDefaultInstance().getAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @param value The bytes for address to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryProfileAvatarRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryProfileAvatarRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryProfileAvatarRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryProfileAvatarRequest>() {
      @java.lang.Override
      public QueryProfileAvatarRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryProfileAvatarRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryProfileAvatarRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryProfileAvatarResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryProfileAvatarResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string avatar = 1;</code>
     * @return The avatar.
     */
    java.lang.String getAvatar();
    /**
     * <code>string avatar = 1;</code>
     * @return The bytes for avatar.
     */
    com.google.protobuf.ByteString
        getAvatarBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryProfileAvatarResponse}
   */
  public static final class QueryProfileAvatarResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryProfileAvatarResponse)
      QueryProfileAvatarResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryProfileAvatarResponse.class.getName());
    }
    // Use QueryProfileAvatarResponse.newBuilder() to construct.
    private QueryProfileAvatarResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryProfileAvatarResponse() {
      avatar_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.Builder.class);
    }

    public static final int AVATAR_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object avatar_ = "";
    /**
     * <code>string avatar = 1;</code>
     * @return The avatar.
     */
    @java.lang.Override
    public java.lang.String getAvatar() {
      java.lang.Object ref = avatar_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        avatar_ = s;
        return s;
      }
    }
    /**
     * <code>string avatar = 1;</code>
     * @return The bytes for avatar.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAvatarBytes() {
      java.lang.Object ref = avatar_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        avatar_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(avatar_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, avatar_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(avatar_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, avatar_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse) obj;

      if (!getAvatar()
          .equals(other.getAvatar())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + AVATAR_FIELD_NUMBER;
      hash = (53 * hash) + getAvatar().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryProfileAvatarResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryProfileAvatarResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        avatar_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileAvatarResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.avatar_ = avatar_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse.getDefaultInstance()) return this;
        if (!other.getAvatar().isEmpty()) {
          avatar_ = other.avatar_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                avatar_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object avatar_ = "";
      /**
       * <code>string avatar = 1;</code>
       * @return The avatar.
       */
      public java.lang.String getAvatar() {
        java.lang.Object ref = avatar_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          avatar_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string avatar = 1;</code>
       * @return The bytes for avatar.
       */
      public com.google.protobuf.ByteString
          getAvatarBytes() {
        java.lang.Object ref = avatar_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          avatar_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string avatar = 1;</code>
       * @param value The avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatar(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        avatar_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string avatar = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatar() {
        avatar_ = getDefaultInstance().getAvatar();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string avatar = 1;</code>
       * @param value The bytes for avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatarBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        avatar_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryProfileAvatarResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryProfileAvatarResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryProfileAvatarResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryProfileAvatarResponse>() {
      @java.lang.Override
      public QueryProfileAvatarResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryProfileAvatarResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryProfileAvatarResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryProfileAvatarResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryFollowRelationshipRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryFollowRelationshipRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string addressA = 1;</code>
     * @return The addressA.
     */
    java.lang.String getAddressA();
    /**
     * <code>string addressA = 1;</code>
     * @return The bytes for addressA.
     */
    com.google.protobuf.ByteString
        getAddressABytes();

    /**
     * <code>string addressB = 2;</code>
     * @return The addressB.
     */
    java.lang.String getAddressB();
    /**
     * <code>string addressB = 2;</code>
     * @return The bytes for addressB.
     */
    com.google.protobuf.ByteString
        getAddressBBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryFollowRelationshipRequest}
   */
  public static final class QueryFollowRelationshipRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryFollowRelationshipRequest)
      QueryFollowRelationshipRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryFollowRelationshipRequest.class.getName());
    }
    // Use QueryFollowRelationshipRequest.newBuilder() to construct.
    private QueryFollowRelationshipRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryFollowRelationshipRequest() {
      addressA_ = "";
      addressB_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.Builder.class);
    }

    public static final int ADDRESSA_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object addressA_ = "";
    /**
     * <code>string addressA = 1;</code>
     * @return The addressA.
     */
    @java.lang.Override
    public java.lang.String getAddressA() {
      java.lang.Object ref = addressA_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        addressA_ = s;
        return s;
      }
    }
    /**
     * <code>string addressA = 1;</code>
     * @return The bytes for addressA.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressABytes() {
      java.lang.Object ref = addressA_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        addressA_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ADDRESSB_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object addressB_ = "";
    /**
     * <code>string addressB = 2;</code>
     * @return The addressB.
     */
    @java.lang.Override
    public java.lang.String getAddressB() {
      java.lang.Object ref = addressB_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        addressB_ = s;
        return s;
      }
    }
    /**
     * <code>string addressB = 2;</code>
     * @return The bytes for addressB.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBBytes() {
      java.lang.Object ref = addressB_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        addressB_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(addressA_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, addressA_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(addressB_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, addressB_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(addressA_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, addressA_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(addressB_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, addressB_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest) obj;

      if (!getAddressA()
          .equals(other.getAddressA())) return false;
      if (!getAddressB()
          .equals(other.getAddressB())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESSA_FIELD_NUMBER;
      hash = (53 * hash) + getAddressA().hashCode();
      hash = (37 * hash) + ADDRESSB_FIELD_NUMBER;
      hash = (53 * hash) + getAddressB().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryFollowRelationshipRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryFollowRelationshipRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        addressA_ = "";
        addressB_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.addressA_ = addressA_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.addressB_ = addressB_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest.getDefaultInstance()) return this;
        if (!other.getAddressA().isEmpty()) {
          addressA_ = other.addressA_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getAddressB().isEmpty()) {
          addressB_ = other.addressB_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                addressA_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                addressB_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object addressA_ = "";
      /**
       * <code>string addressA = 1;</code>
       * @return The addressA.
       */
      public java.lang.String getAddressA() {
        java.lang.Object ref = addressA_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          addressA_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string addressA = 1;</code>
       * @return The bytes for addressA.
       */
      public com.google.protobuf.ByteString
          getAddressABytes() {
        java.lang.Object ref = addressA_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          addressA_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string addressA = 1;</code>
       * @param value The addressA to set.
       * @return This builder for chaining.
       */
      public Builder setAddressA(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        addressA_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string addressA = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddressA() {
        addressA_ = getDefaultInstance().getAddressA();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string addressA = 1;</code>
       * @param value The bytes for addressA to set.
       * @return This builder for chaining.
       */
      public Builder setAddressABytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        addressA_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object addressB_ = "";
      /**
       * <code>string addressB = 2;</code>
       * @return The addressB.
       */
      public java.lang.String getAddressB() {
        java.lang.Object ref = addressB_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          addressB_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string addressB = 2;</code>
       * @return The bytes for addressB.
       */
      public com.google.protobuf.ByteString
          getAddressBBytes() {
        java.lang.Object ref = addressB_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          addressB_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string addressB = 2;</code>
       * @param value The addressB to set.
       * @return This builder for chaining.
       */
      public Builder setAddressB(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        addressB_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string addressB = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddressB() {
        addressB_ = getDefaultInstance().getAddressB();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string addressB = 2;</code>
       * @param value The bytes for addressB to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        addressB_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryFollowRelationshipRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryFollowRelationshipRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryFollowRelationshipRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryFollowRelationshipRequest>() {
      @java.lang.Override
      public QueryFollowRelationshipRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryFollowRelationshipRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryFollowRelationshipRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryFollowRelationshipResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryFollowRelationshipResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 relationship = 1;</code>
     * @return The relationship.
     */
    long getRelationship();
  }
  /**
   * <pre>
   * relationship: 0-No relationship; 1-A follows B; 2-B follows A; 3-Mutual follows
   * </pre>
   *
   * Protobuf type {@code profile.v1.QueryFollowRelationshipResponse}
   */
  public static final class QueryFollowRelationshipResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryFollowRelationshipResponse)
      QueryFollowRelationshipResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryFollowRelationshipResponse.class.getName());
    }
    // Use QueryFollowRelationshipResponse.newBuilder() to construct.
    private QueryFollowRelationshipResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryFollowRelationshipResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.Builder.class);
    }

    public static final int RELATIONSHIP_FIELD_NUMBER = 1;
    private long relationship_ = 0L;
    /**
     * <code>uint64 relationship = 1;</code>
     * @return The relationship.
     */
    @java.lang.Override
    public long getRelationship() {
      return relationship_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (relationship_ != 0L) {
        output.writeUInt64(1, relationship_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (relationship_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, relationship_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse) obj;

      if (getRelationship()
          != other.getRelationship()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RELATIONSHIP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRelationship());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * relationship: 0-No relationship; 1-A follows B; 2-B follows A; 3-Mutual follows
     * </pre>
     *
     * Protobuf type {@code profile.v1.QueryFollowRelationshipResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryFollowRelationshipResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        relationship_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowRelationshipResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.relationship_ = relationship_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse.getDefaultInstance()) return this;
        if (other.getRelationship() != 0L) {
          setRelationship(other.getRelationship());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                relationship_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long relationship_ ;
      /**
       * <code>uint64 relationship = 1;</code>
       * @return The relationship.
       */
      @java.lang.Override
      public long getRelationship() {
        return relationship_;
      }
      /**
       * <code>uint64 relationship = 1;</code>
       * @param value The relationship to set.
       * @return This builder for chaining.
       */
      public Builder setRelationship(long value) {

        relationship_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 relationship = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRelationship() {
        bitField0_ = (bitField0_ & ~0x00000001);
        relationship_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryFollowRelationshipResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryFollowRelationshipResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryFollowRelationshipResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryFollowRelationshipResponse>() {
      @java.lang.Override
      public QueryFollowRelationshipResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryFollowRelationshipResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryFollowRelationshipResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowRelationshipResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchUsersRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.SearchUsersRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string matching = 1;</code>
     * @return The matching.
     */
    java.lang.String getMatching();
    /**
     * <code>string matching = 1;</code>
     * @return The bytes for matching.
     */
    com.google.protobuf.ByteString
        getMatchingBytes();
  }
  /**
   * <pre>
   * QueryParamsRequest is the request type for the Query/Params RPC method.
   * </pre>
   *
   * Protobuf type {@code profile.v1.SearchUsersRequest}
   */
  public static final class SearchUsersRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.SearchUsersRequest)
      SearchUsersRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        SearchUsersRequest.class.getName());
    }
    // Use SearchUsersRequest.newBuilder() to construct.
    private SearchUsersRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SearchUsersRequest() {
      matching_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.class, com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.Builder.class);
    }

    public static final int MATCHING_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object matching_ = "";
    /**
     * <code>string matching = 1;</code>
     * @return The matching.
     */
    @java.lang.Override
    public java.lang.String getMatching() {
      java.lang.Object ref = matching_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        matching_ = s;
        return s;
      }
    }
    /**
     * <code>string matching = 1;</code>
     * @return The bytes for matching.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMatchingBytes() {
      java.lang.Object ref = matching_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        matching_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(matching_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, matching_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(matching_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, matching_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest other = (com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest) obj;

      if (!getMatching()
          .equals(other.getMatching())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MATCHING_FIELD_NUMBER;
      hash = (53 * hash) + getMatching().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * QueryParamsRequest is the request type for the Query/Params RPC method.
     * </pre>
     *
     * Protobuf type {@code profile.v1.SearchUsersRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.SearchUsersRequest)
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.class, com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        matching_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest result = new com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.matching_ = matching_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest.getDefaultInstance()) return this;
        if (!other.getMatching().isEmpty()) {
          matching_ = other.matching_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                matching_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object matching_ = "";
      /**
       * <code>string matching = 1;</code>
       * @return The matching.
       */
      public java.lang.String getMatching() {
        java.lang.Object ref = matching_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          matching_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string matching = 1;</code>
       * @return The bytes for matching.
       */
      public com.google.protobuf.ByteString
          getMatchingBytes() {
        java.lang.Object ref = matching_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          matching_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string matching = 1;</code>
       * @param value The matching to set.
       * @return This builder for chaining.
       */
      public Builder setMatching(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        matching_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string matching = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMatching() {
        matching_ = getDefaultInstance().getMatching();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string matching = 1;</code>
       * @param value The bytes for matching to set.
       * @return This builder for chaining.
       */
      public Builder setMatchingBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        matching_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.SearchUsersRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.SearchUsersRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SearchUsersRequest>
        PARSER = new com.google.protobuf.AbstractParser<SearchUsersRequest>() {
      @java.lang.Override
      public SearchUsersRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SearchUsersRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchUsersRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.SearchUsersRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchUsersResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.SearchUsersResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Profile users = 1;</code>
     */
    java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> 
        getUsersList();
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.Profile getUsers(int index);
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    int getUsersCount();
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getUsersOrBuilderList();
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getUsersOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code profile.v1.SearchUsersResponse}
   */
  public static final class SearchUsersResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.SearchUsersResponse)
      SearchUsersResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        SearchUsersResponse.class.getName());
    }
    // Use SearchUsersResponse.newBuilder() to construct.
    private SearchUsersResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SearchUsersResponse() {
      users_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.class, com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.Builder.class);
    }

    public static final int USERS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> users_;
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getUsersList() {
      return users_;
    }
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getUsersOrBuilderList() {
      return users_;
    }
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    @java.lang.Override
    public int getUsersCount() {
      return users_.size();
    }
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getUsers(int index) {
      return users_.get(index);
    }
    /**
     * <code>repeated .Profile users = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getUsersOrBuilder(
        int index) {
      return users_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < users_.size(); i++) {
        output.writeMessage(1, users_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < users_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, users_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse other = (com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse) obj;

      if (!getUsersList()
          .equals(other.getUsersList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUsersCount() > 0) {
        hash = (37 * hash) + USERS_FIELD_NUMBER;
        hash = (53 * hash) + getUsersList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.SearchUsersResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.SearchUsersResponse)
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.class, com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (usersBuilder_ == null) {
          users_ = java.util.Collections.emptyList();
        } else {
          users_ = null;
          usersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_SearchUsersResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse result = new com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse result) {
        if (usersBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            users_ = java.util.Collections.unmodifiableList(users_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.users_ = users_;
        } else {
          result.users_ = usersBuilder_.build();
        }
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse.getDefaultInstance()) return this;
        if (usersBuilder_ == null) {
          if (!other.users_.isEmpty()) {
            if (users_.isEmpty()) {
              users_ = other.users_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureUsersIsMutable();
              users_.addAll(other.users_);
            }
            onChanged();
          }
        } else {
          if (!other.users_.isEmpty()) {
            if (usersBuilder_.isEmpty()) {
              usersBuilder_.dispose();
              usersBuilder_ = null;
              users_ = other.users_;
              bitField0_ = (bitField0_ & ~0x00000001);
              usersBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getUsersFieldBuilder() : null;
            } else {
              usersBuilder_.addAllMessages(other.users_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.Tlock.io.entity.profile.ProfileProto.Profile m =
                    input.readMessage(
                        com.Tlock.io.entity.profile.ProfileProto.Profile.parser(),
                        extensionRegistry);
                if (usersBuilder_ == null) {
                  ensureUsersIsMutable();
                  users_.add(m);
                } else {
                  usersBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> users_ =
        java.util.Collections.emptyList();
      private void ensureUsersIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          users_ = new java.util.ArrayList<com.Tlock.io.entity.profile.ProfileProto.Profile>(users_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> usersBuilder_;

      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getUsersList() {
        if (usersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(users_);
        } else {
          return usersBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public int getUsersCount() {
        if (usersBuilder_ == null) {
          return users_.size();
        } else {
          return usersBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile getUsers(int index) {
        if (usersBuilder_ == null) {
          return users_.get(index);
        } else {
          return usersBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder setUsers(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (usersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUsersIsMutable();
          users_.set(index, value);
          onChanged();
        } else {
          usersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder setUsers(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (usersBuilder_ == null) {
          ensureUsersIsMutable();
          users_.set(index, builderForValue.build());
          onChanged();
        } else {
          usersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder addUsers(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (usersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUsersIsMutable();
          users_.add(value);
          onChanged();
        } else {
          usersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder addUsers(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (usersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUsersIsMutable();
          users_.add(index, value);
          onChanged();
        } else {
          usersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder addUsers(
          com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (usersBuilder_ == null) {
          ensureUsersIsMutable();
          users_.add(builderForValue.build());
          onChanged();
        } else {
          usersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder addUsers(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (usersBuilder_ == null) {
          ensureUsersIsMutable();
          users_.add(index, builderForValue.build());
          onChanged();
        } else {
          usersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder addAllUsers(
          java.lang.Iterable<? extends com.Tlock.io.entity.profile.ProfileProto.Profile> values) {
        if (usersBuilder_ == null) {
          ensureUsersIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, users_);
          onChanged();
        } else {
          usersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder clearUsers() {
        if (usersBuilder_ == null) {
          users_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          usersBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public Builder removeUsers(int index) {
        if (usersBuilder_ == null) {
          ensureUsersIsMutable();
          users_.remove(index);
          onChanged();
        } else {
          usersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder getUsersBuilder(
          int index) {
        return getUsersFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getUsersOrBuilder(
          int index) {
        if (usersBuilder_ == null) {
          return users_.get(index);  } else {
          return usersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
           getUsersOrBuilderList() {
        if (usersBuilder_ != null) {
          return usersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(users_);
        }
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addUsersBuilder() {
        return getUsersFieldBuilder().addBuilder(
            com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addUsersBuilder(
          int index) {
        return getUsersFieldBuilder().addBuilder(
            index, com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile users = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile.Builder> 
           getUsersBuilderList() {
        return getUsersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
          getUsersFieldBuilder() {
        if (usersBuilder_ == null) {
          usersBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder>(
                  users_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          users_ = null;
        }
        return usersBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.SearchUsersResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.SearchUsersResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SearchUsersResponse>
        PARSER = new com.google.protobuf.AbstractParser<SearchUsersResponse>() {
      @java.lang.Override
      public SearchUsersResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SearchUsersResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchUsersResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.SearchUsersResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryProfileRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryProfileRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    java.lang.String getAddress();
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    com.google.protobuf.ByteString
        getAddressBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryProfileRequest}
   */
  public static final class QueryProfileRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryProfileRequest)
      QueryProfileRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryProfileRequest.class.getName());
    }
    // Use QueryProfileRequest.newBuilder() to construct.
    private QueryProfileRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryProfileRequest() {
      address_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.Builder.class);
    }

    public static final int ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object address_ = "";
    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    @java.lang.Override
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      }
    }
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, address_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, address_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest) obj;

      if (!getAddress()
          .equals(other.getAddress())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAddress().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryProfileRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryProfileRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        address_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.address_ = address_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest.getDefaultInstance()) return this;
        if (!other.getAddress().isEmpty()) {
          address_ = other.address_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                address_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object address_ = "";
      /**
       * <code>string address = 1;</code>
       * @return The address.
       */
      public java.lang.String getAddress() {
        java.lang.Object ref = address_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          address_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @return The bytes for address.
       */
      public com.google.protobuf.ByteString
          getAddressBytes() {
        java.lang.Object ref = address_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          address_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @param value The address to set.
       * @return This builder for chaining.
       */
      public Builder setAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddress() {
        address_ = getDefaultInstance().getAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @param value The bytes for address to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryProfileRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryProfileRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryProfileRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryProfileRequest>() {
      @java.lang.Override
      public QueryProfileRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryProfileRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryProfileRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryProfileRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryProfileResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryProfileResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Profile profile = 1;</code>
     * @return Whether the profile field is set.
     */
    boolean hasProfile();
    /**
     * <code>.Profile profile = 1;</code>
     * @return The profile.
     */
    com.Tlock.io.entity.profile.ProfileProto.Profile getProfile();
    /**
     * <code>.Profile profile = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfileOrBuilder();
  }
  /**
   * Protobuf type {@code profile.v1.QueryProfileResponse}
   */
  public static final class QueryProfileResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryProfileResponse)
      QueryProfileResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryProfileResponse.class.getName());
    }
    // Use QueryProfileResponse.newBuilder() to construct.
    private QueryProfileResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryProfileResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.Builder.class);
    }

    private int bitField0_;
    public static final int PROFILE_FIELD_NUMBER = 1;
    private com.Tlock.io.entity.profile.ProfileProto.Profile profile_;
    /**
     * <code>.Profile profile = 1;</code>
     * @return Whether the profile field is set.
     */
    @java.lang.Override
    public boolean hasProfile() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Profile profile = 1;</code>
     * @return The profile.
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getProfile() {
      return profile_ == null ? com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
    }
    /**
     * <code>.Profile profile = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfileOrBuilder() {
      return profile_ == null ? com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getProfile());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getProfile());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse) obj;

      if (hasProfile() != other.hasProfile()) return false;
      if (hasProfile()) {
        if (!getProfile()
            .equals(other.getProfile())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasProfile()) {
        hash = (37 * hash) + PROFILE_FIELD_NUMBER;
        hash = (53 * hash) + getProfile().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryProfileResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryProfileResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getProfileFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        profile_ = null;
        if (profileBuilder_ != null) {
          profileBuilder_.dispose();
          profileBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryProfileResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.profile_ = profileBuilder_ == null
              ? profile_
              : profileBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse.getDefaultInstance()) return this;
        if (other.hasProfile()) {
          mergeProfile(other.getProfile());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getProfileFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.Tlock.io.entity.profile.ProfileProto.Profile profile_;
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> profileBuilder_;
      /**
       * <code>.Profile profile = 1;</code>
       * @return Whether the profile field is set.
       */
      public boolean hasProfile() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Profile profile = 1;</code>
       * @return The profile.
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile getProfile() {
        if (profileBuilder_ == null) {
          return profile_ == null ? com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
        } else {
          return profileBuilder_.getMessage();
        }
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder setProfile(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profileBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          profile_ = value;
        } else {
          profileBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder setProfile(
          com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profileBuilder_ == null) {
          profile_ = builderForValue.build();
        } else {
          profileBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder mergeProfile(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profileBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            profile_ != null &&
            profile_ != com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance()) {
            getProfileBuilder().mergeFrom(value);
          } else {
            profile_ = value;
          }
        } else {
          profileBuilder_.mergeFrom(value);
        }
        if (profile_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public Builder clearProfile() {
        bitField0_ = (bitField0_ & ~0x00000001);
        profile_ = null;
        if (profileBuilder_ != null) {
          profileBuilder_.dispose();
          profileBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder getProfileBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getProfileFieldBuilder().getBuilder();
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfileOrBuilder() {
        if (profileBuilder_ != null) {
          return profileBuilder_.getMessageOrBuilder();
        } else {
          return profile_ == null ?
              com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance() : profile_;
        }
      }
      /**
       * <code>.Profile profile = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
          getProfileFieldBuilder() {
        if (profileBuilder_ == null) {
          profileBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder>(
                  getProfile(),
                  getParentForChildren(),
                  isClean());
          profile_ = null;
        }
        return profileBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryProfileResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryProfileResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryProfileResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryProfileResponse>() {
      @java.lang.Override
      public QueryProfileResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryProfileResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryProfileResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryProfileResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryFollowingRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryFollowingRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    java.lang.String getAddress();
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    com.google.protobuf.ByteString
        getAddressBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryFollowingRequest}
   */
  public static final class QueryFollowingRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryFollowingRequest)
      QueryFollowingRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryFollowingRequest.class.getName());
    }
    // Use QueryFollowingRequest.newBuilder() to construct.
    private QueryFollowingRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryFollowingRequest() {
      address_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.Builder.class);
    }

    public static final int ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object address_ = "";
    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    @java.lang.Override
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      }
    }
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, address_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, address_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest) obj;

      if (!getAddress()
          .equals(other.getAddress())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAddress().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryFollowingRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryFollowingRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        address_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.address_ = address_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest.getDefaultInstance()) return this;
        if (!other.getAddress().isEmpty()) {
          address_ = other.address_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                address_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object address_ = "";
      /**
       * <code>string address = 1;</code>
       * @return The address.
       */
      public java.lang.String getAddress() {
        java.lang.Object ref = address_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          address_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @return The bytes for address.
       */
      public com.google.protobuf.ByteString
          getAddressBytes() {
        java.lang.Object ref = address_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          address_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @param value The address to set.
       * @return This builder for chaining.
       */
      public Builder setAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddress() {
        address_ = getDefaultInstance().getAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @param value The bytes for address to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryFollowingRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryFollowingRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryFollowingRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryFollowingRequest>() {
      @java.lang.Override
      public QueryFollowingRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryFollowingRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryFollowingRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryFollowingResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryFollowingResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> 
        getProfilesList();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index);
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    int getProfilesCount();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getProfilesOrBuilderList();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code profile.v1.QueryFollowingResponse}
   */
  public static final class QueryFollowingResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryFollowingResponse)
      QueryFollowingResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryFollowingResponse.class.getName());
    }
    // Use QueryFollowingResponse.newBuilder() to construct.
    private QueryFollowingResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryFollowingResponse() {
      profiles_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.Builder.class);
    }

    public static final int PROFILES_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> profiles_;
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getProfilesList() {
      return profiles_;
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getProfilesOrBuilderList() {
      return profiles_;
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public int getProfilesCount() {
      return profiles_.size();
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index) {
      return profiles_.get(index);
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
        int index) {
      return profiles_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < profiles_.size(); i++) {
        output.writeMessage(1, profiles_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < profiles_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, profiles_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse) obj;

      if (!getProfilesList()
          .equals(other.getProfilesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getProfilesCount() > 0) {
        hash = (37 * hash) + PROFILES_FIELD_NUMBER;
        hash = (53 * hash) + getProfilesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryFollowingResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryFollowingResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (profilesBuilder_ == null) {
          profiles_ = java.util.Collections.emptyList();
        } else {
          profiles_ = null;
          profilesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowingResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse result) {
        if (profilesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            profiles_ = java.util.Collections.unmodifiableList(profiles_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.profiles_ = profiles_;
        } else {
          result.profiles_ = profilesBuilder_.build();
        }
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse.getDefaultInstance()) return this;
        if (profilesBuilder_ == null) {
          if (!other.profiles_.isEmpty()) {
            if (profiles_.isEmpty()) {
              profiles_ = other.profiles_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureProfilesIsMutable();
              profiles_.addAll(other.profiles_);
            }
            onChanged();
          }
        } else {
          if (!other.profiles_.isEmpty()) {
            if (profilesBuilder_.isEmpty()) {
              profilesBuilder_.dispose();
              profilesBuilder_ = null;
              profiles_ = other.profiles_;
              bitField0_ = (bitField0_ & ~0x00000001);
              profilesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getProfilesFieldBuilder() : null;
            } else {
              profilesBuilder_.addAllMessages(other.profiles_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.Tlock.io.entity.profile.ProfileProto.Profile m =
                    input.readMessage(
                        com.Tlock.io.entity.profile.ProfileProto.Profile.parser(),
                        extensionRegistry);
                if (profilesBuilder_ == null) {
                  ensureProfilesIsMutable();
                  profiles_.add(m);
                } else {
                  profilesBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> profiles_ =
        java.util.Collections.emptyList();
      private void ensureProfilesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          profiles_ = new java.util.ArrayList<com.Tlock.io.entity.profile.ProfileProto.Profile>(profiles_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> profilesBuilder_;

      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getProfilesList() {
        if (profilesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(profiles_);
        } else {
          return profilesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public int getProfilesCount() {
        if (profilesBuilder_ == null) {
          return profiles_.size();
        } else {
          return profilesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index) {
        if (profilesBuilder_ == null) {
          return profiles_.get(index);
        } else {
          return profilesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder setProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.set(index, value);
          onChanged();
        } else {
          profilesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder setProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.set(index, builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.add(value);
          onChanged();
        } else {
          profilesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.add(index, value);
          onChanged();
        } else {
          profilesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.add(builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.add(index, builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addAllProfiles(
          java.lang.Iterable<? extends com.Tlock.io.entity.profile.ProfileProto.Profile> values) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, profiles_);
          onChanged();
        } else {
          profilesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder clearProfiles() {
        if (profilesBuilder_ == null) {
          profiles_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          profilesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder removeProfiles(int index) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.remove(index);
          onChanged();
        } else {
          profilesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder getProfilesBuilder(
          int index) {
        return getProfilesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
          int index) {
        if (profilesBuilder_ == null) {
          return profiles_.get(index);  } else {
          return profilesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
           getProfilesOrBuilderList() {
        if (profilesBuilder_ != null) {
          return profilesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(profiles_);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addProfilesBuilder() {
        return getProfilesFieldBuilder().addBuilder(
            com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addProfilesBuilder(
          int index) {
        return getProfilesFieldBuilder().addBuilder(
            index, com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile.Builder> 
           getProfilesBuilderList() {
        return getProfilesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
          getProfilesFieldBuilder() {
        if (profilesBuilder_ == null) {
          profilesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder>(
                  profiles_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          profiles_ = null;
        }
        return profilesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryFollowingResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryFollowingResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryFollowingResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryFollowingResponse>() {
      @java.lang.Override
      public QueryFollowingResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryFollowingResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryFollowingResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowingResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryFollowersRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryFollowersRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    java.lang.String getAddress();
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    com.google.protobuf.ByteString
        getAddressBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryFollowersRequest}
   */
  public static final class QueryFollowersRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryFollowersRequest)
      QueryFollowersRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryFollowersRequest.class.getName());
    }
    // Use QueryFollowersRequest.newBuilder() to construct.
    private QueryFollowersRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryFollowersRequest() {
      address_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.Builder.class);
    }

    public static final int ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object address_ = "";
    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    @java.lang.Override
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      }
    }
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, address_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, address_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest) obj;

      if (!getAddress()
          .equals(other.getAddress())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAddress().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryFollowersRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryFollowersRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        address_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.address_ = address_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest.getDefaultInstance()) return this;
        if (!other.getAddress().isEmpty()) {
          address_ = other.address_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                address_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object address_ = "";
      /**
       * <code>string address = 1;</code>
       * @return The address.
       */
      public java.lang.String getAddress() {
        java.lang.Object ref = address_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          address_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @return The bytes for address.
       */
      public com.google.protobuf.ByteString
          getAddressBytes() {
        java.lang.Object ref = address_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          address_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @param value The address to set.
       * @return This builder for chaining.
       */
      public Builder setAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddress() {
        address_ = getDefaultInstance().getAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @param value The bytes for address to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryFollowersRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryFollowersRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryFollowersRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryFollowersRequest>() {
      @java.lang.Override
      public QueryFollowersRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryFollowersRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryFollowersRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryFollowersResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryFollowersResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> 
        getProfilesList();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index);
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    int getProfilesCount();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getProfilesOrBuilderList();
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code profile.v1.QueryFollowersResponse}
   */
  public static final class QueryFollowersResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryFollowersResponse)
      QueryFollowersResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryFollowersResponse.class.getName());
    }
    // Use QueryFollowersResponse.newBuilder() to construct.
    private QueryFollowersResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryFollowersResponse() {
      profiles_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.Builder.class);
    }

    public static final int PROFILES_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> profiles_;
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getProfilesList() {
      return profiles_;
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
        getProfilesOrBuilderList() {
      return profiles_;
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public int getProfilesCount() {
      return profiles_.size();
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index) {
      return profiles_.get(index);
    }
    /**
     * <code>repeated .Profile Profiles = 1;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
        int index) {
      return profiles_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < profiles_.size(); i++) {
        output.writeMessage(1, profiles_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < profiles_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, profiles_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse) obj;

      if (!getProfilesList()
          .equals(other.getProfilesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getProfilesCount() > 0) {
        hash = (37 * hash) + PROFILES_FIELD_NUMBER;
        hash = (53 * hash) + getProfilesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryFollowersResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryFollowersResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (profilesBuilder_ == null) {
          profiles_ = java.util.Collections.emptyList();
        } else {
          profiles_ = null;
          profilesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryFollowersResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse result) {
        if (profilesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            profiles_ = java.util.Collections.unmodifiableList(profiles_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.profiles_ = profiles_;
        } else {
          result.profiles_ = profilesBuilder_.build();
        }
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse.getDefaultInstance()) return this;
        if (profilesBuilder_ == null) {
          if (!other.profiles_.isEmpty()) {
            if (profiles_.isEmpty()) {
              profiles_ = other.profiles_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureProfilesIsMutable();
              profiles_.addAll(other.profiles_);
            }
            onChanged();
          }
        } else {
          if (!other.profiles_.isEmpty()) {
            if (profilesBuilder_.isEmpty()) {
              profilesBuilder_.dispose();
              profilesBuilder_ = null;
              profiles_ = other.profiles_;
              bitField0_ = (bitField0_ & ~0x00000001);
              profilesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getProfilesFieldBuilder() : null;
            } else {
              profilesBuilder_.addAllMessages(other.profiles_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.Tlock.io.entity.profile.ProfileProto.Profile m =
                    input.readMessage(
                        com.Tlock.io.entity.profile.ProfileProto.Profile.parser(),
                        extensionRegistry);
                if (profilesBuilder_ == null) {
                  ensureProfilesIsMutable();
                  profiles_.add(m);
                } else {
                  profilesBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> profiles_ =
        java.util.Collections.emptyList();
      private void ensureProfilesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          profiles_ = new java.util.ArrayList<com.Tlock.io.entity.profile.ProfileProto.Profile>(profiles_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> profilesBuilder_;

      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile> getProfilesList() {
        if (profilesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(profiles_);
        } else {
          return profilesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public int getProfilesCount() {
        if (profilesBuilder_ == null) {
          return profiles_.size();
        } else {
          return profilesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile getProfiles(int index) {
        if (profilesBuilder_ == null) {
          return profiles_.get(index);
        } else {
          return profilesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder setProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.set(index, value);
          onChanged();
        } else {
          profilesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder setProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.set(index, builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.add(value);
          onChanged();
        } else {
          profilesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile value) {
        if (profilesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureProfilesIsMutable();
          profiles_.add(index, value);
          onChanged();
        } else {
          profilesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.add(builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addProfiles(
          int index, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder builderForValue) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.add(index, builderForValue.build());
          onChanged();
        } else {
          profilesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder addAllProfiles(
          java.lang.Iterable<? extends com.Tlock.io.entity.profile.ProfileProto.Profile> values) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, profiles_);
          onChanged();
        } else {
          profilesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder clearProfiles() {
        if (profilesBuilder_ == null) {
          profiles_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          profilesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public Builder removeProfiles(int index) {
        if (profilesBuilder_ == null) {
          ensureProfilesIsMutable();
          profiles_.remove(index);
          onChanged();
        } else {
          profilesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder getProfilesBuilder(
          int index) {
        return getProfilesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder getProfilesOrBuilder(
          int index) {
        if (profilesBuilder_ == null) {
          return profiles_.get(index);  } else {
          return profilesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<? extends com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
           getProfilesOrBuilderList() {
        if (profilesBuilder_ != null) {
          return profilesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(profiles_);
        }
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addProfilesBuilder() {
        return getProfilesFieldBuilder().addBuilder(
            com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public com.Tlock.io.entity.profile.ProfileProto.Profile.Builder addProfilesBuilder(
          int index) {
        return getProfilesFieldBuilder().addBuilder(
            index, com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance());
      }
      /**
       * <code>repeated .Profile Profiles = 1;</code>
       */
      public java.util.List<com.Tlock.io.entity.profile.ProfileProto.Profile.Builder> 
           getProfilesBuilderList() {
        return getProfilesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder> 
          getProfilesFieldBuilder() {
        if (profilesBuilder_ == null) {
          profilesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.Tlock.io.entity.profile.ProfileProto.Profile, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder, com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder>(
                  profiles_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          profiles_ = null;
        }
        return profilesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryFollowersResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryFollowersResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryFollowersResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryFollowersResponse>() {
      @java.lang.Override
      public QueryFollowersResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryFollowersResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryFollowersResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryFollowersResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryActivitiesReceivedCountRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryActivitiesReceivedCountRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    java.lang.String getAddress();
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    com.google.protobuf.ByteString
        getAddressBytes();
  }
  /**
   * Protobuf type {@code profile.v1.QueryActivitiesReceivedCountRequest}
   */
  public static final class QueryActivitiesReceivedCountRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryActivitiesReceivedCountRequest)
      QueryActivitiesReceivedCountRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryActivitiesReceivedCountRequest.class.getName());
    }
    // Use QueryActivitiesReceivedCountRequest.newBuilder() to construct.
    private QueryActivitiesReceivedCountRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryActivitiesReceivedCountRequest() {
      address_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.Builder.class);
    }

    public static final int ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object address_ = "";
    /**
     * <code>string address = 1;</code>
     * @return The address.
     */
    @java.lang.Override
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      }
    }
    /**
     * <code>string address = 1;</code>
     * @return The bytes for address.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, address_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(address_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, address_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest other = (com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest) obj;

      if (!getAddress()
          .equals(other.getAddress())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getAddress().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryActivitiesReceivedCountRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryActivitiesReceivedCountRequest)
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.class, com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        address_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountRequest_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest result = new com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.address_ = address_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest.getDefaultInstance()) return this;
        if (!other.getAddress().isEmpty()) {
          address_ = other.address_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                address_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object address_ = "";
      /**
       * <code>string address = 1;</code>
       * @return The address.
       */
      public java.lang.String getAddress() {
        java.lang.Object ref = address_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          address_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @return The bytes for address.
       */
      public com.google.protobuf.ByteString
          getAddressBytes() {
        java.lang.Object ref = address_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          address_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string address = 1;</code>
       * @param value The address to set.
       * @return This builder for chaining.
       */
      public Builder setAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddress() {
        address_ = getDefaultInstance().getAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string address = 1;</code>
       * @param value The bytes for address to set.
       * @return This builder for chaining.
       */
      public Builder setAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        address_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryActivitiesReceivedCountRequest)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryActivitiesReceivedCountRequest)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryActivitiesReceivedCountRequest>
        PARSER = new com.google.protobuf.AbstractParser<QueryActivitiesReceivedCountRequest>() {
      @java.lang.Override
      public QueryActivitiesReceivedCountRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryActivitiesReceivedCountRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryActivitiesReceivedCountRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryActivitiesReceivedCountResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:profile.v1.QueryActivitiesReceivedCountResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 count = 1;</code>
     * @return The count.
     */
    long getCount();
  }
  /**
   * Protobuf type {@code profile.v1.QueryActivitiesReceivedCountResponse}
   */
  public static final class QueryActivitiesReceivedCountResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:profile.v1.QueryActivitiesReceivedCountResponse)
      QueryActivitiesReceivedCountResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        QueryActivitiesReceivedCountResponse.class.getName());
    }
    // Use QueryActivitiesReceivedCountResponse.newBuilder() to construct.
    private QueryActivitiesReceivedCountResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private QueryActivitiesReceivedCountResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.Builder.class);
    }

    public static final int COUNT_FIELD_NUMBER = 1;
    private long count_ = 0L;
    /**
     * <code>uint64 count = 1;</code>
     * @return The count.
     */
    @java.lang.Override
    public long getCount() {
      return count_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (count_ != 0L) {
        output.writeUInt64(1, count_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (count_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, count_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse)) {
        return super.equals(obj);
      }
      com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse other = (com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse) obj;

      if (getCount()
          != other.getCount()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCount());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code profile.v1.QueryActivitiesReceivedCountResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:profile.v1.QueryActivitiesReceivedCountResponse)
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.class, com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.Builder.class);
      }

      // Construct using com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        count_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.profile.ProfileQueryProto.internal_static_profile_v1_QueryActivitiesReceivedCountResponse_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse getDefaultInstanceForType() {
        return com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse build() {
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse buildPartial() {
        com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse result = new com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.count_ = count_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse) {
          return mergeFrom((com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse other) {
        if (other == com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse.getDefaultInstance()) return this;
        if (other.getCount() != 0L) {
          setCount(other.getCount());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                count_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long count_ ;
      /**
       * <code>uint64 count = 1;</code>
       * @return The count.
       */
      @java.lang.Override
      public long getCount() {
        return count_;
      }
      /**
       * <code>uint64 count = 1;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(long value) {

        count_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 count = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        bitField0_ = (bitField0_ & ~0x00000001);
        count_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:profile.v1.QueryActivitiesReceivedCountResponse)
    }

    // @@protoc_insertion_point(class_scope:profile.v1.QueryActivitiesReceivedCountResponse)
    private static final com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse();
    }

    public static com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryActivitiesReceivedCountResponse>
        PARSER = new com.google.protobuf.AbstractParser<QueryActivitiesReceivedCountResponse>() {
      @java.lang.Override
      public QueryActivitiesReceivedCountResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<QueryActivitiesReceivedCountResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryActivitiesReceivedCountResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.profile.ProfileQueryProto.QueryActivitiesReceivedCountResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryGetMentionSuggestionsRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryGetMentionSuggestionsRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryGetMentionSuggestionsResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryGetMentionSuggestionsResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryProfileAvatarRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryProfileAvatarRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryProfileAvatarResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryProfileAvatarResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryFollowRelationshipRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryFollowRelationshipRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryFollowRelationshipResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryFollowRelationshipResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_SearchUsersRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_SearchUsersRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_SearchUsersResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_SearchUsersResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryProfileRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryProfileRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryProfileResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryProfileResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryFollowingRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryFollowingRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryFollowingResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryFollowingResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryFollowersRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryFollowersRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryFollowersResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryFollowersResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryActivitiesReceivedCountRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryActivitiesReceivedCountRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_profile_v1_QueryActivitiesReceivedCountResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_profile_v1_QueryActivitiesReceivedCountResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033profile/profile_query.proto\022\nprofile.v" +
      "1\032\034google/api/annotations.proto\032\021entity/" +
      "post.proto\032\024entity/profile.proto\"F\n!Quer" +
      "yGetMentionSuggestionsRequest\022\017\n\007address" +
      "\030\001 \001(\t\022\020\n\010matching\030\002 \001(\t\"@\n\"QueryGetMent" +
      "ionSuggestionsResponse\022\032\n\010Profiles\030\001 \003(\013" +
      "2\010.Profile\",\n\031QueryProfileAvatarRequest\022" +
      "\017\n\007address\030\001 \001(\t\",\n\032QueryProfileAvatarRe" +
      "sponse\022\016\n\006avatar\030\001 \001(\t\"D\n\036QueryFollowRel" +
      "ationshipRequest\022\020\n\010addressA\030\001 \001(\t\022\020\n\010ad" +
      "dressB\030\002 \001(\t\"7\n\037QueryFollowRelationshipR" +
      "esponse\022\024\n\014relationship\030\001 \001(\004\"&\n\022SearchU" +
      "sersRequest\022\020\n\010matching\030\001 \001(\t\".\n\023SearchU" +
      "sersResponse\022\027\n\005users\030\001 \003(\0132\010.Profile\"&\n" +
      "\023QueryProfileRequest\022\017\n\007address\030\001 \001(\t\"1\n" +
      "\024QueryProfileResponse\022\031\n\007profile\030\001 \001(\0132\010" +
      ".Profile\"(\n\025QueryFollowingRequest\022\017\n\007add" +
      "ress\030\001 \001(\t\"4\n\026QueryFollowingResponse\022\032\n\010" +
      "Profiles\030\001 \003(\0132\010.Profile\"(\n\025QueryFollowe" +
      "rsRequest\022\017\n\007address\030\001 \001(\t\"4\n\026QueryFollo" +
      "wersResponse\022\032\n\010Profiles\030\001 \003(\0132\010.Profile" +
      "\"6\n#QueryActivitiesReceivedCountRequest\022" +
      "\017\n\007address\030\001 \001(\t\"5\n$QueryActivitiesRecei" +
      "vedCountResponse\022\r\n\005count\030\001 \001(\0042\260\t\n\005Quer" +
      "y\022t\n\014QueryProfile\022\037.profile.v1.QueryProf" +
      "ileRequest\032 .profile.v1.QueryProfileResp" +
      "onse\"!\202\323\344\223\002\033\022\031/profile/v1/get/{address}\022" +
      "~\n\016QueryFollowing\022!.profile.v1.QueryFoll" +
      "owingRequest\032\".profile.v1.QueryFollowing" +
      "Response\"%\202\323\344\223\002\037\022\035/profile/v1/flowing/{a" +
      "ddress}\022~\n\016QueryFollowers\022!.profile.v1.Q" +
      "ueryFollowersRequest\032\".profile.v1.QueryF" +
      "ollowersResponse\"%\202\323\344\223\002\037\022\035/profile/v1/fl" +
      "owers/{address}\022\272\001\n\034QueryActivitiesRecei" +
      "vedCount\022/.profile.v1.QueryActivitiesRec" +
      "eivedCountRequest\0320.profile.v1.QueryActi" +
      "vitiesReceivedCountResponse\"7\202\323\344\223\0021\022//pr" +
      "ofile/v1/activities/received/count/{addr" +
      "ess}\022{\n\013SearchUsers\022\036.profile.v1.SearchU" +
      "sersRequest\032\037.profile.v1.SearchUsersResp" +
      "onse\"+\202\323\344\223\002%\022#/profile/v1/users/search/{" +
      "matching}\022\261\001\n\027QueryFollowRelationship\022*." +
      "profile.v1.QueryFollowRelationshipReques" +
      "t\032+.profile.v1.QueryFollowRelationshipRe" +
      "sponse\"=\202\323\344\223\0027\0225/profile/v1/follow/relat" +
      "ionship/{addressA}/{addressB}\022\211\001\n\022QueryP" +
      "rofileAvatar\022%.profile.v1.QueryProfileAv" +
      "atarRequest\032&.profile.v1.QueryProfileAva" +
      "tarResponse\"$\202\323\344\223\002\036\022\034/profile/v1/avatar/" +
      "{address}\022\266\001\n\025GetMentionSuggestions\022-.pr" +
      "ofile.v1.QueryGetMentionSuggestionsReque" +
      "st\032..profile.v1.QueryGetMentionSuggestio" +
      "nsResponse\">\202\323\344\223\0028\0226/profile/v1/getMenti" +
      "onSuggestions/{address}/{matching}BV\n\024co" +
      "m.Tlock.io.profileB\021ProfileQueryProtoZ+g" +
      "ithub.com/rollchains/tlock/x/profile/typ" +
      "esb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.api.AnnotationsProto.getDescriptor(),
          com.Tlock.io.entity.post.PostProto.getDescriptor(),
          com.Tlock.io.entity.profile.ProfileProto.getDescriptor(),
        });
    internal_static_profile_v1_QueryGetMentionSuggestionsRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_profile_v1_QueryGetMentionSuggestionsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryGetMentionSuggestionsRequest_descriptor,
        new java.lang.String[] { "Address", "Matching", });
    internal_static_profile_v1_QueryGetMentionSuggestionsResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_profile_v1_QueryGetMentionSuggestionsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryGetMentionSuggestionsResponse_descriptor,
        new java.lang.String[] { "Profiles", });
    internal_static_profile_v1_QueryProfileAvatarRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_profile_v1_QueryProfileAvatarRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryProfileAvatarRequest_descriptor,
        new java.lang.String[] { "Address", });
    internal_static_profile_v1_QueryProfileAvatarResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_profile_v1_QueryProfileAvatarResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryProfileAvatarResponse_descriptor,
        new java.lang.String[] { "Avatar", });
    internal_static_profile_v1_QueryFollowRelationshipRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_profile_v1_QueryFollowRelationshipRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryFollowRelationshipRequest_descriptor,
        new java.lang.String[] { "AddressA", "AddressB", });
    internal_static_profile_v1_QueryFollowRelationshipResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_profile_v1_QueryFollowRelationshipResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryFollowRelationshipResponse_descriptor,
        new java.lang.String[] { "Relationship", });
    internal_static_profile_v1_SearchUsersRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_profile_v1_SearchUsersRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_SearchUsersRequest_descriptor,
        new java.lang.String[] { "Matching", });
    internal_static_profile_v1_SearchUsersResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_profile_v1_SearchUsersResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_SearchUsersResponse_descriptor,
        new java.lang.String[] { "Users", });
    internal_static_profile_v1_QueryProfileRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_profile_v1_QueryProfileRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryProfileRequest_descriptor,
        new java.lang.String[] { "Address", });
    internal_static_profile_v1_QueryProfileResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_profile_v1_QueryProfileResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryProfileResponse_descriptor,
        new java.lang.String[] { "Profile", });
    internal_static_profile_v1_QueryFollowingRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_profile_v1_QueryFollowingRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryFollowingRequest_descriptor,
        new java.lang.String[] { "Address", });
    internal_static_profile_v1_QueryFollowingResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_profile_v1_QueryFollowingResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryFollowingResponse_descriptor,
        new java.lang.String[] { "Profiles", });
    internal_static_profile_v1_QueryFollowersRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_profile_v1_QueryFollowersRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryFollowersRequest_descriptor,
        new java.lang.String[] { "Address", });
    internal_static_profile_v1_QueryFollowersResponse_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_profile_v1_QueryFollowersResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryFollowersResponse_descriptor,
        new java.lang.String[] { "Profiles", });
    internal_static_profile_v1_QueryActivitiesReceivedCountRequest_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_profile_v1_QueryActivitiesReceivedCountRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryActivitiesReceivedCountRequest_descriptor,
        new java.lang.String[] { "Address", });
    internal_static_profile_v1_QueryActivitiesReceivedCountResponse_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_profile_v1_QueryActivitiesReceivedCountResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_profile_v1_QueryActivitiesReceivedCountResponse_descriptor,
        new java.lang.String[] { "Count", });
    descriptor.resolveAllFeaturesImmutable();
    com.google.api.AnnotationsProto.getDescriptor();
    com.Tlock.io.entity.post.PostProto.getDescriptor();
    com.Tlock.io.entity.profile.ProfileProto.getDescriptor();
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(com.google.api.AnnotationsProto.http);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
