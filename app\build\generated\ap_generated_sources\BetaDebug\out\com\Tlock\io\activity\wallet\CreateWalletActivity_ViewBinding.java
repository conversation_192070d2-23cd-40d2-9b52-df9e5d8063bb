// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomInputBox;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CreateWalletActivity_ViewBinding implements Unbinder {
  private CreateWalletActivity target;

  private View view7f090319;

  @UiThread
  public CreateWalletActivity_ViewBinding(CreateWalletActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public CreateWalletActivity_ViewBinding(final CreateWalletActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mEtWalletName = Utils.findRequiredViewAsType(source, R.id.et_wallet_name, "field 'mEtWalletName'", CustomInputBox.class);
    target.mEtPwd = Utils.findRequiredViewAsType(source, R.id.et_pwd, "field 'mEtPwd'", CustomInputBox.class);
    target.mEtPwdConfirm = Utils.findRequiredViewAsType(source, R.id.et_pwd_confirm, "field 'mEtPwdConfirm'", CustomInputBox.class);
    view = Utils.findRequiredView(source, R.id.tv_create, "field 'mTvCreate' and method 'onViewClicked'");
    target.mTvCreate = Utils.castView(view, R.id.tv_create, "field 'mTvCreate'", TextView.class);
    view7f090319 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked();
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    CreateWalletActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mEtWalletName = null;
    target.mEtPwd = null;
    target.mEtPwdConfirm = null;
    target.mTvCreate = null;

    view7f090319.setOnClickListener(null);
    view7f090319 = null;
  }
}
