package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.fragment.cosmos.LikeFragment;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.cy.tablayoutniubility.FragPageAdapterVp2;
import com.cy.tablayoutniubility.TabAdapter;
import com.cy.tablayoutniubility.TabLayoutScroll;
import com.cy.tablayoutniubility.TabMediatorVp2;
import com.cy.tablayoutniubility.TabViewHolder;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;


public class UserInfoActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.btn_updata)
    TextView mBtnUpdata;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.iv_manger)
    ImageView mIvManger;
    @BindView(R.id.tv_name)
    TextView mTvName;
    @BindView(R.id.tv_handle)
    TextView mTvHandle;
    @BindView(R.id.review_count)
    TextView mReviewCount;
    @BindView(R.id.tv_1)
    TextView mTv1;
    @BindView(R.id.fans_count)
    TextView mFansCount;
    @BindView(R.id.tv_2)
    TextView mTv2;
    @BindView(R.id.rl_info)
    RelativeLayout mRlInfo;
    @BindView(R.id.tablayout)
    TabLayoutScroll mTablayout;
    @BindView(R.id.view_pager)
    ViewPager2 mViewPager;
    @BindView(R.id.tv_Bio)
    TextView mTvBio;
    @BindView(R.id.line2)
    View mLine2;
    @BindView(R.id.iv_show_more)
    ImageView mIvShowMore;
    @BindView(R.id.tv_level)
    FontTextView mTvLevel;
    @BindView(R.id.iv_vip)
    ImageView mIvVip;
    @BindView(R.id.icon_check)
    ImageView mIconCheck;
    @BindView(R.id.tv_wallet_address)
    TextView mTvWalletAddress;
    private ProfileProto.Profile authInfo;
    private boolean isSelf;
    private String address;
    private boolean isFlow = false;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String address) {
        Intent intent = new Intent(context, UserInfoActivity.class);
        intent.putExtra("address", address);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_user_info;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initFragment();
    }


    @Override
    protected void loadData() {
        address = getIntent().getStringExtra("address");
        if (address.equalsIgnoreCase(WalletDaoUtils.getCurrent().getAddress())) {
            //自己
            mBtnUpdata.setText("Edit profile");
            mBtnUpdata.setTextColor(getResources().getColor(R.color.cosmos_black));
            mBtnUpdata.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
            isSelf = true;
        } else {
            //他人
            mBtnUpdata.setBackground(getResources().getDrawable(R.drawable.btn_black_60));
            mBtnUpdata.setTextColor(getResources().getColor(R.color.white));
            isSelf = false;
            getFlowing();
        }
        getAuthInfo(address);
    }

    private void getAuthInfo(String address) {
        if (WalletDaoUtils.getCurrent() != null) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    authInfo = CosmosUtils.getAuthInfo(address);
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mTvHandle.setText("@" + authInfo.getUserHandle());
                            mReviewCount.setText(authInfo.getFollowing() + "");
                            mFansCount.setText(authInfo.getFollowers() + "");
                            setText(authInfo.getBio(), authInfo.getLocation(), authInfo.getWebsite());
                            mTvLevel.setText("LV " + authInfo.getLevel());
                            mTvName.setText(authInfo.getNickname().isEmpty() ?authInfo.getUserHandle() : authInfo.getNickname());
                            mTvWalletAddress.setText(authInfo.getWalletAddress());

                            if (TextUtils.isEmpty(authInfo.getAvatar())) {
                                TextAvatarDrawable a = new TextAvatarDrawable( authInfo.getUserHandle().substring(0, 1));
                                // 应用到 ImageView
                                mIvHeard.setBackground(a);
                            } else {
                                if (authInfo.getAvatar().startsWith("http")) {
                                    Glide.with(getActivity()).load(authInfo.getAvatar()).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(authInfo.getAvatar()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authInfo.getAvatar());
                                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                }

                            }

                        }
                    });
                }
            });
        }
    }

    private void setText(String bio, String location, String website) {
        // 定义文本
        String[] paragraphs = {
                bio,
                location,
                website
        };

        // 拼接文本
        SpannableStringBuilder builder = new SpannableStringBuilder();
        for (int i = 0; i < paragraphs.length; i++) {
            if (paragraphs[i].isEmpty()) {
                continue;
            }
            builder.append(paragraphs[i]);
            if (i != paragraphs.length - 1) {
                builder.append("\n");
            }
        }

        // 设置最后一段颜色
        int lastStart = builder.length() - paragraphs[2].length();
        ForegroundColorSpan blueSpan = new ForegroundColorSpan(getResources().getColor(R.color.cosmos_topic));
        builder.setSpan(blueSpan, lastStart, builder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        mTvBio.setText(builder);
        mTvBio.post(new Runnable() {
            @Override
            public void run() {
                int lineCount = mTvBio.getLineCount();
                if (lineCount > 3) {
                    mIvShowMore.setVisibility(View.VISIBLE);
                    mTvBio.setMaxLines(3);
                } else {
                    mIvShowMore.setVisibility(View.GONE);
                }
            }
        });
    }

    private void initFragment() {
        FragPageAdapterVp2<String> fragmentPageAdapter = new FragPageAdapterVp2<String>(this) {

            @Override
            public Fragment createFragment(String bean, int position) {
                return new LikeFragment(position, address);
            }

            @Override
            public void bindDataToTab(TabViewHolder holder, int position, String bean, boolean isSelected) {
                TextView textView = holder.getView(R.id.tv);
                if (isSelected) {
                    textView.setTextColor(getResources().getColor(R.color.cosmos_black));
                } else {
                    textView.setTextColor(getResources().getColor(R.color.cosmos_default));
                }
                textView.setText(bean);
            }

            @Override
            public int getTabLayoutID(int position, String bean) {
                return R.layout.item_home_tab;
            }
        };
        TabAdapter<String> tabAdapter = new TabMediatorVp2<String>(mTablayout, mViewPager).setAdapter(fragmentPageAdapter);
        List<String> list = new ArrayList<>();
        list.add("Posts");
//        list.add("Replies");
        fragmentPageAdapter.add(list);
        tabAdapter.add(list);
    }

    @OnClick({R.id.btn_updata, R.id.iv_show_more, R.id.tv_wallet_address})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.btn_updata:
                if (isSelf) {
                    showEdit();
                } else {
                    if (isFlow) {
                        //取消关注
                        showToast("Unfollow success");
                        mBtnUpdata.setText("Follow");
                        mBtnUpdata.setBackground(getResources().getDrawable(R.drawable.btn_black_60));
                        mBtnUpdata.setTextColor(getColor(R.color.white));
                        isFlow = false;
                        unFlow();

                    } else {
                        //关注
                        showToast("Follow success");
                        mBtnUpdata.setText("Following");
                        mBtnUpdata.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
                        mBtnUpdata.setTextColor(getColor(R.color.cosmos_black));
                        isFlow = true;
                        flow();

                    }
                }
                break;

            case R.id.iv_show_more:
                mTvBio.setMaxLines(50);
                mIvShowMore.setVisibility(View.GONE);
                break;
            case R.id.tv_wallet_address:
                CopyUtils.copyToClipboard(authInfo.getWalletAddress());
                showToast("Copied");
                break;
        }
    }

    private void getFlowing() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                long l = CosmosUtils.queryFollowRelationship(address);
                mBtnUpdata.post(new Runnable() {
                    @Override
                    public void run() {
                        switch (Integer.parseInt(l + "")) {
                            case 0:
                            case 2:
                                //未关注
                                isFlow = false;
                                mBtnUpdata.setText("Follow");
                                break;
                            case 1:
                            case 3:
                                //已关注
                                mBtnUpdata.setText("Following");
                                mBtnUpdata.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
                                mBtnUpdata.setTextColor(getColor(R.color.cosmos_black));
                                isFlow = true;
                                break;
                        }
                    }
                });
            }
        });
    }

    private void unFlow() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.unfollow(WalletDaoUtils.getCurrent().getAddress(), address);
            }
        });
    }

    private void flow() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.follow(WalletDaoUtils.getCurrent().getAddress(), address);
            }
        });
    }

    private void showEdit() {
        EditProfileActivity.start(getActivity());

    }


}