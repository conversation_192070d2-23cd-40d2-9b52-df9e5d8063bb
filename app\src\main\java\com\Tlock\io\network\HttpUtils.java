package com.Tlock.io.network;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @ClassName HttpUtils
 * <AUTHOR>
 * @Data 2021/9/26 12:29
 * @Desc
 */

public class HttpUtils {
    public static InputStream getStreamFromURL(String imageURL) {
        InputStream in = null;
        try {
            URL url = new URL(imageURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            in = connection.getInputStream();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return in;

    }
}
