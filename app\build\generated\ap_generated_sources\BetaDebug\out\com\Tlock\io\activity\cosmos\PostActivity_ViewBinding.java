// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PostActivity_ViewBinding implements Unbinder {
  private PostActivity target;

  private View view7f09013f;

  private View view7f090287;

  private View view7f09015a;

  private View view7f09015c;

  private View view7f090140;

  private View view7f09015b;

  private View view7f090166;

  private View view7f0902d8;

  private View view7f09013b;

  private View view7f090170;

  @UiThread
  public PostActivity_ViewBinding(PostActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public PostActivity_ViewBinding(final PostActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_close, "field 'mIvClose' and method 'onBindClick'");
    target.mIvClose = Utils.castView(view, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    view7f09013f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.send_post, "field 'mSendPost' and method 'onBindClick'");
    target.mSendPost = Utils.castView(view, R.id.send_post, "field 'mSendPost'", TextView.class);
    view7f090287 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mEdTitle = Utils.findRequiredViewAsType(source, R.id.ed_title, "field 'mEdTitle'", EditText.class);
    target.mLine2 = Utils.findRequiredView(source, R.id.line2, "field 'mLine2'");
    target.mEdContent = Utils.findRequiredViewAsType(source, R.id.ed_content, "field 'mEdContent'", EditText.class);
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.iv_menu_at, "field 'mIvMenuAt' and method 'onBindClick'");
    target.mIvMenuAt = Utils.castView(view, R.id.iv_menu_at, "field 'mIvMenuAt'", ImageView.class);
    view7f09015a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_menu_topic, "field 'mIvMenuTopic' and method 'onBindClick'");
    target.mIvMenuTopic = Utils.castView(view, R.id.iv_menu_topic, "field 'mIvMenuTopic'", ImageView.class);
    view7f09015c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLlMenu = Utils.findRequiredViewAsType(source, R.id.ll_menu, "field 'mLlMenu'", LinearLayout.class);
    view = Utils.findRequiredView(source, R.id.iv_close_Proposal, "field 'mIvCloseProposal' and method 'onBindClick'");
    target.mIvCloseProposal = Utils.castView(view, R.id.iv_close_Proposal, "field 'mIvCloseProposal'", ImageView.class);
    view7f090140 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mEdProposal1 = Utils.findRequiredViewAsType(source, R.id.ed_proposal1, "field 'mEdProposal1'", EditText.class);
    target.mLlVote = Utils.findRequiredViewAsType(source, R.id.ll_vote, "field 'mLlVote'", LinearLayout.class);
    target.mRlProposal = Utils.findRequiredViewAsType(source, R.id.rl_proposal, "field 'mRlProposal'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.iv_menu_list, "field 'mIvMenuList' and method 'onBindClick'");
    target.mIvMenuList = Utils.castView(view, R.id.iv_menu_list, "field 'mIvMenuList'", ImageView.class);
    view7f09015b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_proposal, "field 'mIvProposal' and method 'onBindClick'");
    target.mIvProposal = Utils.castView(view, R.id.iv_proposal, "field 'mIvProposal'", ImageView.class);
    view7f090166 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.time, "field 'mTime' and method 'onBindClick'");
    target.mTime = Utils.castView(view, R.id.time, "field 'mTime'", TextView.class);
    view7f0902d8 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mScrollView = Utils.findRequiredViewAsType(source, R.id.scrollView, "field 'mScrollView'", ScrollView.class);
    view = Utils.findRequiredView(source, R.id.iv_classify, "field 'mIvClassify' and method 'onBindClick'");
    target.mIvClassify = Utils.castView(view, R.id.iv_classify, "field 'mIvClassify'", ImageView.class);
    view7f09013b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv_1, "field 'mIv1'", ImageView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_select, "field 'mIvSelect' and method 'onBindClick'");
    target.mIvSelect = Utils.castView(view, R.id.iv_select, "field 'mIvSelect'", ImageView.class);
    view7f090170 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mRlItem = Utils.findRequiredViewAsType(source, R.id.rl_item, "field 'mRlItem'", RelativeLayout.class);
    target.mRvImages = Utils.findRequiredViewAsType(source, R.id.rv_images, "field 'mRvImages'", RecyclerView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    target.mLine3 = Utils.findRequiredView(source, R.id.line3, "field 'mLine3'");
  }

  @Override
  @CallSuper
  public void unbind() {
    PostActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvClose = null;
    target.mSendPost = null;
    target.mEdTitle = null;
    target.mLine2 = null;
    target.mEdContent = null;
    target.mMain = null;
    target.mIvMenuAt = null;
    target.mIvMenuTopic = null;
    target.mLlMenu = null;
    target.mIvCloseProposal = null;
    target.mEdProposal1 = null;
    target.mLlVote = null;
    target.mRlProposal = null;
    target.mIvMenuList = null;
    target.mIvProposal = null;
    target.mTime = null;
    target.mScrollView = null;
    target.mIvClassify = null;
    target.mIv1 = null;
    target.mTvTitle = null;
    target.mIvSelect = null;
    target.mRlItem = null;
    target.mRvImages = null;
    target.mTv1 = null;
    target.mLine3 = null;

    view7f09013f.setOnClickListener(null);
    view7f09013f = null;
    view7f090287.setOnClickListener(null);
    view7f090287 = null;
    view7f09015a.setOnClickListener(null);
    view7f09015a = null;
    view7f09015c.setOnClickListener(null);
    view7f09015c = null;
    view7f090140.setOnClickListener(null);
    view7f090140 = null;
    view7f09015b.setOnClickListener(null);
    view7f09015b = null;
    view7f090166.setOnClickListener(null);
    view7f090166 = null;
    view7f0902d8.setOnClickListener(null);
    view7f0902d8 = null;
    view7f09013b.setOnClickListener(null);
    view7f09013b = null;
    view7f090170.setOnClickListener(null);
    view7f090170 = null;
  }
}
