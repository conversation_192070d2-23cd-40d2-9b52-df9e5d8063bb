package com.Tlock.io.utils.cosmos;

import static com.cosmos.tx.v1beta1.ServiceGrpc.newBlockingStub;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.Tlock.io.app.AppApplication;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.Transaction;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.post.QueryGrpc;
import com.Tlock.io.profile.ProfileQueryProto;
import com.Tlock.io.profile.ProfileTXProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.pop.PopPayer;
import com.Tlock.io.widget.pop.PopTip;
import com.cosmos.auth.v1beta1.AuthProto;
import com.cosmos.auth.v1beta1.QueryProto;
import com.cosmos.base.abci.v1beta1.AbciProto;
import com.cosmos.base.v1beta1.CoinProto;
import com.cosmos.crypto.secp256k1.KeysProto;
import com.cosmos.feegrant.v1beta1.FeegrantProto;
import com.cosmos.gov.v1beta1.GovProto;
import com.cosmos.params.v1beta1.ParamsProto;
import com.cosmos.tx.signing.v1beta1.SigningProto;
import com.cosmos.tx.v1beta1.ServiceGrpc;
import com.cosmos.tx.v1beta1.ServiceProto;
import com.cosmos.tx.v1beta1.TxProto;
import com.cosmos.vesting.v1beta1.VestingProto;
import com.ethermint.types.v1.AccountProto;
import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.google.protobuf.Timestamp;
import com.lxj.xpopup.XPopup;

import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.Sha256Hash;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.security.PrivateKey;
import java.security.Signature;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import kotlin.Triple;

public class CosmosUtils {

    public static String BASE_DENOM = "uTOK";

    public static ManagedChannel channel = ManagedChannelBuilder.forAddress("*************", 9090)
//    public static ManagedChannel channel = ManagedChannelBuilder.forAddress("127.0.0.1", 9090)
            .usePlaintext() // 如果使用非加密连接
            .build();
    private static String CHAIN_ID = "10889";
    private static AbciProto.TxResponse txResponse;
    private static String PAYER = "tlock1wg33qsaurqr7da6qkg67kag3ajeny4dxsf7df8";

    /**
     * 设置税
     *
     * @return
     */
    public static TxProto.Fee getInitFee(long gasLimit) {
        String multipliy = BigDecimalUtils.multipliy(gasLimit + "", "1.2");
        CoinProto.Coin feeCoin = CoinProto.Coin.newBuilder()
                .setDenom(BASE_DENOM)
                .setAmount(BigDecimalUtils.saveDecimals(multipliy, -1))
                .build();
        TxProto.Fee build = TxProto.Fee.newBuilder()
                .setGasLimit(Long.parseLong(BigDecimalUtils.saveDecimals(multipliy, -1)))
                .addAmount(feeCoin)
                .build();
        return build;
    }


    @SuppressLint("CheckResult")
    public static PostQueryProto.PostResponse queryPost(String s) {
        PostQueryProto.QueryPostRequest builder = PostQueryProto.QueryPostRequest.newBuilder().setPostId(s).build();
        QueryGrpc.QueryBlockingStub queryBlockingStub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
        PostQueryProto.PostResponse post = queryBlockingStub.queryPost(builder).getPost();
        return post;
    }


    /**
     * 创建转账用户信息
     *
     * @param signerInfo 签名信息
     * @param fee        gas部分费用信息
     * @return
     */
    private static TxProto.AuthInfo grpcAuthInfo(TxProto.SignerInfo signerInfo, TxProto.Fee fee, String granter) {
        if (fee != null && fee.getAmountCount() > 0) {
            CoinProto.Coin feeCoin = CoinProto.Coin.newBuilder()
                    .setAmount(fee.getAmount(0).getAmount())
                    .setDenom(fee.getAmount(0).getDenom())
                    .build();

            TxProto.Fee txFee = TxProto.Fee.newBuilder()
                    .addAmount(feeCoin)
                    .setGranter(granter)
                    .setGasLimit(fee.getGasLimit())
                    .build();

            return TxProto.AuthInfo.newBuilder()
                    .setFee(txFee)
                    .addSignerInfos(signerInfo)
                    .build();
        }
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public static long getGranteeBalance() {
        try {
            com.cosmos.feegrant.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.feegrant.v1beta1.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceRequest build = com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceRequest.newBuilder()
                    .setGrantee(WalletDaoUtils.getCurrent().getAddress())
                    .setGranter(PAYER)
                    .build();

            com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceResponse allowance = queryBlockingStub.allowance(build);

            ByteString value = allowance.getAllowance().getAllowance().getValue();
            FeegrantProto.PeriodicAllowance basicAllowance = FeegrantProto.PeriodicAllowance.parseFrom(value);
            Log.e("TAG", "getGranteeBalance: " + JsonUtils.objectToJson(basicAllowance));
            Timestamp periodReset = basicAllowance.getPeriodReset();

            Instant instant = Instant.ofEpochSecond(periodReset.getSeconds(), periodReset.getNanos());
            long epochSecond = instant.getEpochSecond();
            long l = System.currentTimeMillis();
            if (l / 1000 > epochSecond) {
                return 2000000L;

            } else {
                String amount = basicAllowance.getPeriodCanSpend(0).getAmount();
                return Long.parseLong(amount);
            }

        } catch (Exception e) {
            return 0L;
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    public static FeegrantProto.PeriodicAllowance getGrantee() {
        try {
            com.cosmos.feegrant.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.feegrant.v1beta1.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceRequest build = com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceRequest.newBuilder()
                    .setGrantee(WalletDaoUtils.getCurrent().getAddress())
                    .setGranter(PAYER)
                    .build();

            com.cosmos.feegrant.v1beta1.QueryProto.QueryAllowanceResponse allowance = queryBlockingStub.allowance(build);

            ByteString value = allowance.getAllowance().getAllowance().getValue();
            FeegrantProto.PeriodicAllowance basicAllowance = FeegrantProto.PeriodicAllowance.parseFrom(value);
            return basicAllowance;

        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 使用私钥对数据进行签名。
     *
     * @param privateKey 私钥
     * @param data       要签名的数据
     * @return 签名后的字节数组
     * @throws Exception 如果签名失败
     */
    public static byte[] signData(PrivateKey privateKey, byte[] data) throws Exception {
        // 使用SHA256withRSA签名算法，如果是其他算法请调整
        Signature signature = Signature.getInstance("SHA256withRSA", "BC");
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public static String sendTX(List<Any> body, boolean isBase) {
        try {
            long granteeBalance = getGranteeBalance();
            TxProto.Fee initSimuFee;
            TxProto.Fee initFee;
            if (granteeBalance == 0) {
                initSimuFee = getInitFee(signSimuTx(body, getInitSimuFee(), "", WalletDaoUtils.getCurrent().getPrivateKey(), ""));
                initFee = getInitFee(initSimuFee.getGasLimit());
                txResponse = broadcastTx(body, initFee, "", WalletDaoUtils.getCurrent().getPrivateKey(), "");
                return txResponse.getTxhash();
            }else{
                initSimuFee = getInitFee(signSimuTx(body, getInitSimuFee(), "", WalletDaoUtils.getCurrent().getPrivateKey(), PAYER));
                initFee = getInitFee(initSimuFee.getGasLimit());
            }

            if (granteeBalance < initSimuFee.getGasLimit()) {
                //授权不够
                PopPayer popPayer = new PopPayer(AppApplication.getInstance().getCurrentActivity());
                popPayer.setCallback(new PopPayer.Callback() {
                    @Override
                    public void cancel() {
                    }

                    @Override
                    public void wallet() {
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                txResponse = broadcastTx(body, initFee, "", WalletDaoUtils.getCurrent().getPrivateKey(), "");
                            }
                        });
                    }
                });
                new XPopup.Builder(AppApplication.getInstance().getCurrentActivity())
                        .asCustom(popPayer).show();
            } else {
                //授权足够
                txResponse = broadcastTx(body, initFee, "", WalletDaoUtils.getCurrent().getPrivateKey(), PAYER);
                return txResponse.getTxhash();
            }

            return txResponse.getTxhash();
        } catch (Exception e) {
            showNote(e.getLocalizedMessage());

        }
        return "";
    }

    public static String sendTX(List<Any> body, String address, String privateKey) {
        try {
            TxProto.Fee initSimuFee = getInitSimuFee();
            long l = signSimuTx(body, initSimuFee, address, privateKey, "");
            TxProto.Fee initFee = getInitFee(l);
            txResponse = broadcastTx(body, initFee, address, privateKey, PAYER);
            return txResponse.getTxhash();
        } catch (Exception e) {
            showNote(e.getLocalizedMessage());
        }
        return "";
    }

    /**
     * 显示内容
     */
    private static void showNote(String error) {
        Activity currentActivity = AppApplication.getInstance().getCurrentActivity();
        currentActivity.runOnUiThread(new Runnable() {

            @Override
            public void run() {
                PopTip popTip = new PopTip(currentActivity,
                        error,
                        "Something went wrong",
                        "Cancel",
                        "Ok",
                        true, false);
                popTip.setCallback(new PopTip.Callback() {
                    @Override
                    public void notShow() {
                    }

                    @Override
                    public void confirm() {
                    }
                });
                new XPopup.Builder(currentActivity)
                        .hasShadowBg(true)
                        .isViewMode(true)
                        .isDestroyOnDismiss(true)
                        .asCustom(popTip).show();
            }
        });


    }


    public static TxProto.Fee getFee(List<Any> body) {
        return getInitFee(signSimuTx(body, getInitSimuFee(), "", WalletDaoUtils.getCurrent().getPrivateKey(), ""));
    }


    /**********************************************交易TX   start**********************************************************/

    /**
     * 构建消息体
     *
     * @return
     */
    public static void sendPostBody(PostTXProto.MsgCreatePost mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgCreatePost")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        CosmosUtils.sendTX(anyList, false);
    }



    public static void sendGov(PostTXProto.MsgCreatePost mycontent) {
        List<Any> anyList = new ArrayList<>();
        //mycontent =1020条数据

        GovProto.TextProposal build1 = GovProto.TextProposal.newBuilder().setTitle("这是标题1").setDescription("这是描述1").build();
        Any proposalContent = Any.newBuilder()
                .setTypeUrl("/cosmos.gov.v1beta1.TextProposal")
                .setValue(build1.toByteString())
                .build();
        com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal proposal = com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal.newBuilder()
                .setContent(proposalContent)
                .setProposer(WalletDaoUtils.getCurrent().getAddress()) // 提案人的地址
                .addInitialDeposit(CoinProto.Coin.newBuilder()
                        .setAmount("10000")
                        .setDenom(BASE_DENOM)
                        .build())
                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1beta1.MsgSubmitProposal")
                        .setValue(proposal.toByteString()).build()
        );

        CosmosUtils.sendTX(anyList, false);
    }

    /**
     * 提交 提案
     *
     * @param mycontent
     */
    public static void sendProposal(String mycontent) {
        ParamsProto.ParamChange paramChange = ParamsProto.ParamChange.newBuilder()
                .setValue("true")//变量值
                .setKey("someValue")//变量名称
                .setSubspace("test")//模块位置
                .build();

        ParamsProto.ParameterChangeProposal parameterChangeProposal = ParamsProto.ParameterChangeProposal.newBuilder()
                .setTitle("这是修改标题")
                .addChanges(paramChange)
                .setDescription("我想修改params 为false")
                .build();
        List<Any> anyList = new ArrayList<>();
        Any proposalContent = Any.newBuilder()
                .setTypeUrl("/cosmos.params.v1beta1.ParameterChangeProposal")
                .setValue(parameterChangeProposal.toByteString())
                .build();
        com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal proposal = com.cosmos.gov.v1beta1.TxProto.MsgSubmitProposal.newBuilder()
                .setContent(proposalContent)
                .setProposer(WalletDaoUtils.getCurrent().getAddress()) // 提案人的地址
                .addInitialDeposit(CoinProto.Coin.newBuilder()
                        .setAmount("10000")
                        .setDenom(BASE_DENOM)
                        .build())
                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1beta1.MsgSubmitProposal")
                        .setValue(proposal.toByteString()).build()
        );

        CosmosUtils.sendTX(anyList, false);
    }

    /**
     * 存款
     *
     * @param id
     * @param count
     */
    public static void submitDeposit(String id, String count) {
        List<Any> anyList = new ArrayList<>();
        com.cosmos.gov.v1beta1.TxProto.MsgDeposit msgDeposit = com.cosmos.gov.v1beta1.TxProto.MsgDeposit.newBuilder()
                .setProposalId(Long.parseLong(id))
                .setDepositor(WalletDaoUtils.getCurrent().getAddress())
                .addAmount(CoinProto.Coin.newBuilder()
                        .setAmount(count)
                        .setDenom(BASE_DENOM)
                        .build())
                .build();


        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1beta1.MsgDeposit")
                        .setValue(msgDeposit.toByteString()).build()
        );

        CosmosUtils.sendTX(anyList, false);
    }

    /**
     * 投票
     *
     * @param id
     */
    public static void sendGovVote(String id) {

        List<Any> anyList = new ArrayList<>();

        com.cosmos.gov.v1.TxProto.MsgVote msgVote = com.cosmos.gov.v1.TxProto.MsgVote.newBuilder()
                .setProposalId(Long.parseLong(id))
                .setVoter(WalletDaoUtils.getCurrent().getAddress())
                .setOption(com.cosmos.gov.v1.GovProto.VoteOption.VOTE_OPTION_YES)

                .build();

        anyList.add(
                Any.newBuilder()
                        .setTypeUrl("/cosmos.gov.v1.MsgVote")
                        .setValue(msgVote.toByteString()).build()
        );

        CosmosUtils.sendTX(anyList, false);
    }


    public static String sendBase(com.cosmos.bank.v1beta1.TxProto.MsgSend build) {
        List<Any> anyList = new ArrayList<>();
        anyList.add(
                Any.newBuilder().setTypeUrl("/cosmos.bank.v1beta1.MsgSend")
                        .setValue(build.toByteString()).build()
        );
        return CosmosUtils.sendTX(anyList, true);
    }

    /**
     * 构建修改管理员消息
     *
     * @return
     */
    public static void sendMangerBody(ProfileTXProto.MsgManageAdminRequest mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgManageAdminRequest")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        CosmosUtils.sendTX(anyList, false);
    }

    /**
     * 发送广播
     *
     * @param msgs
     * @param fee
     * @param memo
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static AbciProto.TxResponse broadcastTx(List<Any> msgs, TxProto.Fee fee, String memo, String privateKey, String granter) {
        try {
            // 生成广播交易
            ServiceProto.BroadcastTxRequest broadcastTx = signBroadcastTx(msgs, fee, memo, privateKey, granter);
            Log.e("Cosmos", " signBroadcastTx 信息: " + JsonUtils.objectToJson(broadcastTx));

            ServiceGrpc.ServiceBlockingStub txStub = newBlockingStub(channel)
                    .withDeadlineAfter(300L, TimeUnit.SECONDS);
            AbciProto.TxResponse txResponse1 = CompletableFuture.completedFuture(txStub.broadcastTx(broadcastTx).getTxResponse()).get();
            Log.e("Cosmos", " ModeInfo 信息: " + JsonUtils.objectToJson(txResponse1));
            Transaction transaction = new Transaction();
            transaction.setHash(txResponse1.getTxhash());
            transaction.setTime(System.currentTimeMillis() + "");
            transaction.setFunction(msgs.get(0).getTypeUrl());
            if (memo.isEmpty()) {
                transaction.setWalletAddress(WalletDaoUtils.getCurrent().getAddress());
            } else {
                transaction.setWalletAddress(memo);
            }
            WalletDaoUtils.insertNewTransaction(transaction);

            return txResponse1;
        } catch (Exception e) {
            Log.e("Cosmos", " broadcastTx 错误: " + e.getMessage());
            return null;
        }
    }

    /**
     * 构建广播交易
     *
     * @param msgAnys
     * @param fee
     * @param memo
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    private static ServiceProto.BroadcastTxRequest signBroadcastTx(List<Any> msgAnys, TxProto.Fee fee, String memo, String privateKey, String granter) {
        Long height = lastHeight();
        Triple<String, Long, Long> auth;
        if (memo.isEmpty()) {
            auth = getAuth();
        } else {
            auth = getAuth(memo);
        }

        if (height != null) {
            //构建消息体
            TxProto.TxBody txBody = grpcTxBody(msgAnys, memo, height);
            //构建签名者信息
            TxProto.SignerInfo signerInfo = grpcSignerInfo(auth.getThird(), privateKey);
            //构建授权信息
            TxProto.AuthInfo authInfo = grpcAuthInfo(signerInfo, fee, granter);
            //构建签名
            TxProto.TxRaw broadcastTx = grpcBroadcastTx(txBody, authInfo, auth.getSecond(), privateKey);
            TxProto.TxRaw simulateTx = grpcSimulTx(txBody, authInfo);
            ServiceProto.SimulateRequest build = ServiceProto.SimulateRequest.newBuilder().setTxBytes(simulateTx.toByteString()).build();
            ServiceGrpc.ServiceBlockingStub serviceBlockingStub = newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            ServiceProto.SimulateResponse simulate = serviceBlockingStub.simulate(build);
            Log.e("Cosmos", "gasInfo      信息 : " + JsonUtils.objectToJson(simulate.getResult()));


            return ServiceProto.BroadcastTxRequest.newBuilder()
                    .setModeValue(ServiceProto.BroadcastMode.BROADCAST_MODE_SYNC.getNumber())
                    .setTxBytes(broadcastTx.toByteString())
                    .build();
        }
        Log.e("Cosmos", "  grpcBroadcastTx 错误: height为空");

        return null;
    }

    /**
     * 构建消息体信息
     *
     * @param msgsAny
     * @param memo
     * @param height
     * @return
     */
    private static TxProto.TxBody grpcTxBody(List<Any> msgsAny, String memo, long height) {
        TxProto.TxBody.Builder builder = TxProto.TxBody.newBuilder();
        if (msgsAny != null) {
            for (Any msg : msgsAny) {
                builder.addMessages(msg);
            }
        }

        return builder
                .setTimeoutHeight(height + 500L)
                .build();
    }

    /**
     * 构建签名者信息
     *
     * @param Sequence
     * @return
     */
    private static TxProto.SignerInfo grpcSignerInfo(long Sequence, String privateKey) {
        if (privateKey.toLowerCase().startsWith("0x")) {
            privateKey = privateKey.substring(2);
        }
        Any pubKey = generateGrpcPubKeyFromPriv(privateKey);
        Log.e("Cosmos", "  generateGrpcPubKeyFromPriv 信息: " + privateKey);

        TxProto.ModeInfo.Single singleMode = TxProto.ModeInfo.Single.newBuilder()
                .setMode(SigningProto.SignMode.SIGN_MODE_DIRECT)
                .build();
        Log.e("Cosmos", "  singleMode 信息: " + JsonUtils.objectToJson(singleMode));

        TxProto.ModeInfo modeInfo = TxProto.ModeInfo.newBuilder()
                .setSingle(singleMode)
                .build();
        Log.e("Cosmos", "  ModeInfo 信息: " + JsonUtils.objectToJson(modeInfo));

        return TxProto.SignerInfo.newBuilder()
                .setPublicKey(pubKey)
                .setModeInfo(modeInfo)
                .setSequence(Sequence)//这里生产环境要改成实际的
                .build();
    }

    /**
     * 通过私钥生成公钥
     *
     * @param privateKey
     * @return
     */
    private static Any generateGrpcPubKeyFromPriv(String privateKey) {
        ECKey ecKey = ECKey.fromPrivate(new BigInteger(privateKey, 16));

        KeysProto.PubKey pubKey =
                KeysProto.PubKey.newBuilder()
                        .setKey(ByteString.copyFrom(ecKey.getPubKey()))
                        .build();


        return Any.newBuilder()
                .setTypeUrl("/cosmos.crypto.secp256k1.PubKey")
                .setValue(pubKey.toByteString())
                .build();
    }

    /**
     * grpc 签名数据
     *
     * @param txBody
     * @param authInfo
     * @param accountNumber
     * @return
     */
    private static TxProto.TxRaw grpcBroadcastTx(TxProto.TxBody txBody, TxProto.AuthInfo authInfo, long accountNumber, String privateKey) {
        TxProto.SignDoc.Builder signDocBuilder = TxProto.SignDoc.newBuilder()
                .setBodyBytes(txBody.toByteString())
                .setAuthInfoBytes(authInfo.toByteString())
                .setChainId(CHAIN_ID)
                .setAccountNumber(accountNumber);

        TxProto.SignDoc signDoc = signDocBuilder.build();
        byte[] sigByte = grpcByteSignature(signDoc.toByteArray(), privateKey);

        return TxProto.TxRaw.newBuilder()
                .setBodyBytes(txBody.toByteString())
                .setAuthInfoBytes(authInfo.toByteString())
                .addSignatures(ByteString.copyFrom(sigByte))
                .build();
    }

    /**
     * grpc签名
     *
     * @param toSignByte
     * @return
     */
    private static byte[] grpcByteSignature(byte[] toSignByte, String privateKey) {
        byte[] sigData = new byte[64];
        byte[] sha256Hash = Sha256Hash.hash(toSignByte);

        if (privateKey.toLowerCase().startsWith("0x")) {
            privateKey = privateKey.substring(2);
        }
        ECKey ecKey = ECKey.fromPrivate(new BigInteger(privateKey, 16));

        if (ecKey != null) {
            ECKey.ECDSASignature signature = ecKey.sign(Sha256Hash.wrap(sha256Hash));
            System.arraycopy(integerToBytes(signature.r, 32), 0, sigData, 0, 32);
            System.arraycopy(integerToBytes(signature.s, 32), 0, sigData, 32, 32);
        }
        return sigData;
    }

    /**
     * 数字转byte
     *
     * @param s
     * @param length
     * @return
     */
    private static byte[] integerToBytes(BigInteger s, int length) {
        byte[] bytes = s.toByteArray();

        if (length < bytes.length) {
            byte[] tmp = new byte[length];
            System.arraycopy(bytes, bytes.length - tmp.length, tmp, 0, tmp.length);
            return tmp;
        } else if (length > bytes.length) {
            byte[] tmp = new byte[length];
            System.arraycopy(bytes, 0, tmp, tmp.length - bytes.length, bytes.length);
            return tmp;
        }
        return bytes;
    }


    /**
     * 获取用户信息
     *
     * @param rawAccount
     * @return first 地址  second 账号  third 序列
     */
    public static Triple<String, Long, Long> accountInfos(Any rawAccount) {
        try {
            String typeUrl = rawAccount.getTypeUrl();
            if (typeUrl.contains(AuthProto.BaseAccount.getDescriptor().getFullName())) {
                AuthProto.BaseAccount account = AuthProto.BaseAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(account.getAddress(), account.getAccountNumber(), account.getSequence());
            } else if (typeUrl.contains(VestingProto.PeriodicVestingAccount.getDescriptor().getFullName())) {
                VestingProto.PeriodicVestingAccount vestingAccount = VestingProto.PeriodicVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(VestingProto.ContinuousVestingAccount.getDescriptor().getFullName())) {
                VestingProto.ContinuousVestingAccount vestingAccount = VestingProto.ContinuousVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(VestingProto.DelayedVestingAccount.getDescriptor().getFullName())) {
                VestingProto.DelayedVestingAccount vestingAccount = VestingProto.DelayedVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(com.stride.vesting.VestingProto.StridePeriodicVestingAccount.getDescriptor().getFullName())) {
                com.stride.vesting.VestingProto.StridePeriodicVestingAccount vestingAccount = com.stride.vesting.VestingProto.StridePeriodicVestingAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(vestingAccount.getBaseVestingAccount().getBaseAccount().getAddress(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getAccountNumber(),
                        vestingAccount.getBaseVestingAccount().getBaseAccount().getSequence());
            } else if (typeUrl.contains(com.injective.types.v1beta1.AccountProto.EthAccount.getDescriptor().getFullName())) {
                com.injective.types.v1beta1.AccountProto.EthAccount ethAccount = com.injective.types.v1beta1.AccountProto.EthAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(ethAccount.getBaseAccount().getAddress(),
                        ethAccount.getBaseAccount().getAccountNumber(),
                        ethAccount.getBaseAccount().getSequence());
            } else if (typeUrl.contains(com.artela.types.v1.AccountProto.EthAccount.getDescriptor().getFullName())) {
                com.artela.types.v1.AccountProto.EthAccount ethAccount = com.artela.types.v1.AccountProto.EthAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(ethAccount.getBaseAccount().getAddress(),
                        ethAccount.getBaseAccount().getAccountNumber(),
                        ethAccount.getBaseAccount().getSequence());
            } else if (typeUrl.contains(AccountProto.EthAccount.getDescriptor().getFullName())) {
                AccountProto.EthAccount ethAccount = AccountProto.EthAccount.parseFrom(rawAccount.getValue());
                return new Triple<>(ethAccount.getBaseAccount().getAddress(),
                        ethAccount.getBaseAccount().getAccountNumber(),
                        ethAccount.getBaseAccount().getSequence());
            } else {
                return new Triple<>("", -1L, -1L);
            }
        } catch (Exception e) {
            Log.e("Cosmos", " accountInfos 错误: " + e.getMessage());
            return null;
        }

    }

    /**
     * 获取点赞
     *
     * @return
     */
    public static void postLike(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgLikeRequest build1 = PostTXProto.MsgLikeRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgLikeRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 获取取消点赞
     *
     * @return
     */
    public static void postUnLike(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgUnlikeRequest build1 = PostTXProto.MsgUnlikeRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgUnlikeRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 保存帖子
     *
     * @return
     */
    public static void savePost(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgSaveRequest build1 = PostTXProto.MsgSaveRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgSaveRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 构建消息体
     *
     * @return
     */
    public static void sendPoll(PostTXProto.CastVoteOnPollRequest mycontent) {
        List<Any> anyList = new ArrayList<>();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.CastVoteOnPollRequest")
                .setValue(mycontent.toByteString()).build();
        anyList.add(build);
        CosmosUtils.sendTX(anyList, false);
    }

    /**
     * 取消保存帖子
     *
     * @return
     */
    public static void unSavePost(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgUnsaveRequest build1 = PostTXProto.MsgUnsaveRequest.newBuilder()
                .setSender(address)
                .setId(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgUnsaveRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 帖子评论
     *
     * @return
     */
    public static void postComment(String comment, String address, String id, ArrayList<String> tags) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgCommentRequest build1 = PostTXProto.MsgCommentRequest.newBuilder()
                .setCreator(address)
                .setParentId(id)
                .addAllMention(tags)
                .setComment(comment)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgCommentRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }


    /**
     * 评论转发
     *
     * @return
     */
    public static void postQuote(String comment, String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgQuotePostRequest build1 = PostTXProto.MsgQuotePostRequest.newBuilder()
                .setCreator(address)
                .setQuote(id)
                .setComment(comment)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgQuotePostRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 转发
     *
     * @return
     */
    public static void postRepost(String address, String id) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgRepostRequest build1 = PostTXProto.MsgRepostRequest.newBuilder()
                .setCreator(address)
                .setQuote(id)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgRepostRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 对未分类话题分类
     *
     * @return
     */
    public static void classifyUncategorizedTopic(String topicId, String category) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.ClassifyUncategorizedTopicRequest build1 = PostTXProto.ClassifyUncategorizedTopicRequest.newBuilder()
                .setCreator(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(topicId)
                .setCategoryId(category)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.ClassifyUncategorizedTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 更新用户详情
     *
     * @return
     */
    public static void profileUpdata(ProfileTXProto.ProfileOptions data, String address) {
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.MsgAddProfileRequest build1 = ProfileTXProto.MsgAddProfileRequest.newBuilder()
                .setCreator(address)
                .setProfileJson(data)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgAddProfileRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 更新话题详情
     *
     * @return
     */
    public static void topicUpdata(String address, PostTXProto.UpdateTopicJson data) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.UpdateTopicRequest build1 = PostTXProto.UpdateTopicRequest.newBuilder()
                .setCreator(address)
                .setTopicJson(data)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.UpdateTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }


    /**
     * 关注
     *
     * @return
     */
    public static void follow(String address, String targetAddr) {
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.MsgFollowRequest build1 = ProfileTXProto.MsgFollowRequest.newBuilder()
                .setCreator(address)
                .setTargetAddr(targetAddr)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgFollowRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }


    /**
     * 取消关注
     *
     * @return
     */
    public static void unfollow(String address, String targetAddr) {
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.MsgUnfollowRequest build1 = ProfileTXProto.MsgUnfollowRequest.newBuilder()
                .setCreator(address)
                .setTargetAddr(targetAddr)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgUnfollowRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

    /**
     * 关注话题
     *
     * @return
     */
    public static void followTopic(String topicId) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgFollowTopicRequest build1 = PostTXProto.MsgFollowTopicRequest.newBuilder()
                .setCreator(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(topicId)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgFollowTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }


    /**
     * 取消关注话题
     *
     * @return
     */
    public static void unFollowTopic(String topicId) {
        List<Any> anyList = new ArrayList<>();
        PostTXProto.MsgUnfollowTopicRequest build1 = PostTXProto.MsgUnfollowTopicRequest.newBuilder()
                .setCreator(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(topicId)
                .build();
        Any build = Any.newBuilder().setTypeUrl("/post.v1.MsgUnfollowTopicRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        sendTX(anyList, false);
    }

/**********************************************交易TX   end**********************************************************/


    /********************************************** SimulTx start**********************************************************/


    /**
     * 构建测试gas消息体
     *
     * @param txBody
     * @param authInfo
     * @return
     */
    private static TxProto.TxRaw grpcSimulTx(TxProto.TxBody txBody, TxProto.AuthInfo authInfo) {
        TxProto.TxRaw.Builder builder = TxProto.TxRaw.newBuilder().setAuthInfoBytes(authInfo.toByteString())
                .setBodyBytes(txBody.toByteString());
        for (TxProto.SignerInfo signerInfo : authInfo.getSignerInfosList()) {
            builder.addSignatures(ByteString.copyFrom(signerInfo.toByteArray()));
        }
        return builder.build();
    }

    /**
     * 签名测试gas消息体
     *
     * @param msgAnys
     * @param fee
     * @param memo
     * @return
     */
    private static long signSimuTx(List<Any> msgAnys, TxProto.Fee fee, String memo, String privateKey, String granter) {

        Long height = lastHeight();
        Triple<String, Long, Long> auth;
        if (memo.isEmpty()) {
            auth = getAuth();
        } else {
            auth = getAuth(memo);
        }

        if (height != null) {
            //构建消息体
            TxProto.TxBody txBody = grpcTxBody(msgAnys, memo, height);
            //构建签名者信息
            Log.e("TAG", "getNameHeard: noce-----------------------" + auth.getThird());
            TxProto.SignerInfo signerInfo = grpcSignerInfo(auth.getThird(), privateKey);
            //构建授权信息
            TxProto.AuthInfo authInfo = grpcAuthInfo(signerInfo, fee, granter);
            //构建签名
            TxProto.TxRaw simulateTx = grpcSimulTx(txBody, authInfo);
            ServiceProto.SimulateRequest build = ServiceProto.SimulateRequest.newBuilder().setTxBytes(simulateTx.toByteString()).build();
            ServiceGrpc.ServiceBlockingStub serviceBlockingStub = newBlockingStub(channel).withDeadlineAfter(8L, TimeUnit.SECONDS);
            long gasUsed = serviceBlockingStub.simulate(build).getGasInfo().getGasUsed();
            return gasUsed;
        }
        Log.e("Cosmos", "  grpcBroadcastTx 错误: height为空");
        try {

        } catch (Exception e) {
//            PopPayer popPayer = new PopPayer(AppApplication.getInstance());
//            popPayer.setCallback(new PopPayer.Callback() {
//                @Override
//                public void cancel() {
//
//
//                }
//
//                @Override
//                public void wallet() {
//
//                }
//            });
//            new XPopup.Builder(AppApplication.getInstance())
//                    .dismissOnTouchOutside(false)
//                    .asCustom(popPayer)
//                    .show();
        }
        return 1000000L;
    }

    /**
     * 获取模拟交易费用
     *
     * @return
     */
    public static TxProto.Fee getInitSimuFee() {
        CoinProto.Coin feeCoin = CoinProto.Coin.newBuilder()
                .setDenom(BASE_DENOM)
                .setAmount("0")
                .build();
        TxProto.Fee build = TxProto.Fee.newBuilder()
                .setGasLimit(500000L)
                .addAmount(feeCoin)
                .build();
        return build;
    }

/********************************************** SimulTx end**********************************************************/

    /**********************************************查询   start**********************************************************/


    public static Triple<String, Long, Long> getAuth() {
        com.cosmos.auth.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.auth.v1beta1.QueryGrpc.newBlockingStub(channel)
                .withDeadlineAfter(8L, TimeUnit.SECONDS);
        QueryProto.QueryAccountRequest req = QueryProto.QueryAccountRequest.newBuilder().setAddress(WalletDaoUtils.getCurrent().getAddress()).build();

        Any account = queryBlockingStub.account(req).getAccount();
        Long second = accountInfos(account).getSecond();
        Long third = accountInfos(account).getThird();
        Log.e("Cosmos", "getAuth   account second信息: " + second);
        Log.e("Cosmos", "getAuth   account  third信息 : " + third);

        return accountInfos(account);
    }

    public static Triple<String, Long, Long> getAuth(String address) {
        com.cosmos.auth.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.auth.v1beta1.QueryGrpc.newBlockingStub(channel)
                .withDeadlineAfter(8L, TimeUnit.SECONDS);

        QueryProto.QueryAccountRequest req = QueryProto.QueryAccountRequest.newBuilder().setAddress(address).build();

        Any account = queryBlockingStub.account(req).getAccount();
        Long second = accountInfos(account).getSecond();
        Long third = accountInfos(account).getThird();
        Log.e("Cosmos", "getAuth   account second信息: " + second);
        Log.e("Cosmos", "getAuth   account  third信息 : " + third);

        return accountInfos(account);
    }

    /**
     * 获取最新区块高度
     *
     * @return
     */
    public static long lastHeight() {
        com.cosmos.base.tendermint.v1beta1.ServiceGrpc.ServiceBlockingStub blockStub = com.cosmos.base.tendermint.v1beta1.ServiceGrpc.newBlockingStub(channel)
                .withDeadlineAfter(30L, TimeUnit.SECONDS);
        com.cosmos.base.tendermint.v1beta1.QueryProto.GetLatestBlockRequest blockRequest = com.cosmos.base.tendermint.v1beta1.QueryProto.GetLatestBlockRequest.newBuilder().build();
        com.cosmos.base.tendermint.v1beta1.QueryProto.GetLatestBlockResponse lastBlock = blockStub.getLatestBlock(blockRequest);
        Log.e("Cosmos", "getAuth   lastBlock.getBlock().getHeader().getHeight() 信息 : " + lastBlock.getBlock().getHeader().getHeight());
        return lastBlock.getBlock().getHeader().getHeight();

    }


    /**
     * 获取当前提案存款阶段 的提案
     *
     * @return
     */
    public static com.cosmos.gov.v1.QueryProto.QueryProposalsResponse lastDepositProposal() {
        com.cosmos.gov.v1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.gov.v1.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        com.cosmos.gov.v1.QueryProto.QueryProposalsRequest request = com.cosmos.gov.v1.QueryProto.QueryProposalsRequest.newBuilder()
                .setProposalStatus(com.cosmos.gov.v1.GovProto.ProposalStatus.PROPOSAL_STATUS_DEPOSIT_PERIOD)
                .build();
        com.cosmos.gov.v1.QueryProto.QueryProposalsResponse proposals = queryBlockingStub.proposals(request);
        return proposals;

    }

    /**
     * 获取当前提案存款阶段 的提案
     *
     * @return
     */
    public static com.cosmos.gov.v1.QueryProto.QueryProposalsResponse lastVoteProposal() {
        com.cosmos.gov.v1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.gov.v1.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        com.cosmos.gov.v1.QueryProto.QueryProposalsRequest request = com.cosmos.gov.v1.QueryProto.QueryProposalsRequest.newBuilder()
                .setProposalStatus(com.cosmos.gov.v1.GovProto.ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD)
                .build();
        com.cosmos.gov.v1.QueryProto.QueryProposalsResponse proposals = queryBlockingStub.proposals(request);
        return proposals;

    }


    /**
     * 获取母币余额
     *
     * @return
     */
    public static String getBaseBalance(String address) {
        com.cosmos.bank.v1beta1.QueryGrpc.QueryBlockingStub queryBlockingStub = com.cosmos.bank.v1beta1.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        com.cosmos.bank.v1beta1.QueryProto.QueryBalanceRequest build = com.cosmos.bank.v1beta1.QueryProto.QueryBalanceRequest.newBuilder().setAddress(address).setDenom(BASE_DENOM).build();
        com.cosmos.bank.v1beta1.QueryProto.QueryBalanceResponse balance1 = queryBlockingStub.balance(build);
        CoinProto.Coin balance = balance1.getBalance();
        return balance.getAmount();
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    public static ProfileProto.Profile getAuthInfo(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryProfileRequest build = ProfileQueryProto.QueryProfileRequest.newBuilder().setAddress(address).build();
        ProfileProto.Profile profile = stub.queryProfile(build).getProfile();
        return profile;
    }


    /**
     * 获取用户头像
     *
     * @return
     */
    public static String getAuthHeard(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryProfileAvatarRequest build = ProfileQueryProto.QueryProfileAvatarRequest.newBuilder().setAddress(address).build();
        String profile = stub.queryProfileAvatar(build).getAvatar();
        return profile;
    }

    /**
     * 获取帖子图片
     *
     * @return
     */
    public static String getPostImages(String id) {
       QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryPaidPostImageRequest build = PostQueryProto.QueryPaidPostImageRequest.newBuilder().setImageId(id).build();
        String profile = stub.queryPaidPostImage(build).getImage();
        return profile;
    }


    /**
     * 获取话题头像
     *
     * @return
     */
    public static String getTopicHeard(String id) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryTopicImageRequest build = PostQueryProto.QueryTopicImageRequest.newBuilder().setTopicId(id).build();
        String profile = stub.queryTopicImage(build).getAvatar();
        return profile;
    }


    /**
     * 获取首页帖子
     *
     * @return
     */
    public static PostQueryProto.QueryHomePostsResponse getHomePosts() {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryHomePostsRequest build = PostQueryProto.QueryHomePostsRequest.newBuilder().build();
        PostQueryProto.QueryHomePostsResponse queryHomePostsResponse = stub.queryHomePosts(build);

        return queryHomePostsResponse;
    }


    /**
     * 获取关注人帖子
     *
     * @return
     */
    public static PostQueryProto.QueryFollowingPostsResponse QueryFollowingPosts(int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        String address = WalletDaoUtils.getCurrent().getAddress();
        PostQueryProto.QueryFollowingPostsRequest build = PostQueryProto.QueryFollowingPostsRequest.newBuilder()
                .setPage(page)
                .setAddress(address).build();
        PostQueryProto.QueryFollowingPostsResponse queryFollowingPostsResponse = stub.queryFollowingPosts(build);
        return queryFollowingPostsResponse;
    }

    /**
     * 获取关注话题
     *
     * @return
     */
    public static ArrayList<PostQueryProto.TopicResponse> QueryFollowingTopics() {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryFollowingTopicsRequest build = PostQueryProto.QueryFollowingTopicsRequest.newBuilder().setAddress(WalletDaoUtils.getCurrent().getAddress()).build();
        List<PostQueryProto.TopicResponse> topicsList = stub.queryFollowingTopics(build).getTopicsList();
        ArrayList<PostQueryProto.TopicResponse> postResponses = new ArrayList<>(topicsList);
        return postResponses;
    }

    /**
     * 获取第一页帖子
     *
     * @return
     */
    public static ArrayList<PostQueryProto.PostResponse> getHomeFirstPosts(int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryFirstPageHomePostsRequest build = PostQueryProto.QueryFirstPageHomePostsRequest.newBuilder().setPage(page).build();
        List<PostQueryProto.PostResponse> postsList = stub.queryFirstPageHomePosts(build).getPostsList();
        ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>(postsList);
        return postResponses;
    }

    /**
     * 模糊搜索话题
     *
     * @return
     */
    public static ArrayList<PostQueryProto.TopicResponse> getTopic(String topic) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.SearchTopicsRequest build = PostQueryProto.SearchTopicsRequest.newBuilder().setMatching(topic).build();
        List<PostQueryProto.TopicResponse> topicsList = stub.searchTopics(build).getTopicsList();
        ArrayList<PostQueryProto.TopicResponse> postResponses = new ArrayList<>(topicsList);
        return postResponses;
    }

    /**
     * 获取自己发全部帖子
     *
     * @return
     */
    public static ArrayList<PostQueryProto.PostResponse> getMyPosts(String address, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryUserCreatedPostsRequest build = PostQueryProto.QueryUserCreatedPostsRequest.newBuilder()
                .setPage(page)
                .setAddress(address).build();
        List<PostQueryProto.PostResponse> postsList = stub.queryUserCreatedPosts(build).getPostsList();
        ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>(postsList);
        return postResponses;
    }


    /**
     * 获取喜欢帖子
     *
     * @return
     */
    public static ArrayList<PostQueryProto.PostResponse> getLikePosts(String address, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.LikesIMadeRequest build = PostQueryProto.LikesIMadeRequest.newBuilder().setPage(page).setAddress(address).build();
        List<PostQueryProto.PostResponse> postsList = stub.likesIMade(build).getPostsList();
        ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>(postsList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));

        return postResponses;
    }

    /**
     * 获取收藏帖子
     *
     * @return
     */
    public static ArrayList<PostQueryProto.PostResponse> getSavePosts(String address, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.SavesIMadeRequest build = PostQueryProto.SavesIMadeRequest.newBuilder().setPage(page).setAddress(address).build();
        List<PostQueryProto.PostResponse> postsList = stub.savesIMade(build).getPostsList();
        ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>(postsList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }


    /**
     * 获取评论
     *
     * @return
     */
    public static ArrayList<PostQueryProto.CommentReceivedResponse> getCommentsByWallet() {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryCommentsReceivedRequest build = PostQueryProto.QueryCommentsReceivedRequest.newBuilder().setAddress(WalletDaoUtils.getCurrent().getAddress()).build();
        List<PostQueryProto.CommentReceivedResponse> commentsList = stub.queryCommentsReceived(build).getCommentsList();
        ArrayList<PostQueryProto.CommentReceivedResponse> postResponses = new ArrayList<>(commentsList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }


    /**
     * 获取评论
     *
     * @return
     */
    public static ArrayList<PostQueryProto.CommentResponse> getCommentsById(String id, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryCommentsRequest build = PostQueryProto.QueryCommentsRequest.newBuilder().setPage(page).setId(id).build();
        List<PostQueryProto.CommentResponse> commentsList = stub.queryComments(build).getCommentsList();
        ArrayList<PostQueryProto.CommentResponse> postResponses = new ArrayList<>(commentsList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }

    /**
     * 获取全部帖子
     *
     * @return
     */
//    public static ArrayList<PostQueryProto.PostResponse> getLikesReceivedPosts() {
//        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
//        PostQueryProto.LikesReceivedRequest build = PostQueryProto.LikesReceivedRequest.newBuilder().setWallet(WalletDaoUtils.getCurrent().getAddress()).build();
//        List<PostQueryProto.LikesReceived> likesReceivedList = stub.likesReceived(build).getLikesReceivedList();
//
//        ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>();
//
//        for (PostQueryProto.LikesReceived likesReceived : likesReceivedList) {
//            PostQueryProto.Post.newBuilder().setCreator(likesReceived.get()).setPostId(likesReceived.getPostId()).build();
//          PostQueryProto.PostResponse.newBuilder().setPost().build();
//            postResponses.add(postResponse)
//        }
//
//
//        return postResponses;
//    }


    /**
     * 查询是否关注
     *
     * @return
     */
    public static boolean getIsFollow(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryFollowingRequest build = ProfileQueryProto.QueryFollowingRequest.newBuilder().setAddress(address).build();
        List<ProfileProto.Profile> profilesList = stub.queryFollowing(build).getProfilesList();
        return !profilesList.isEmpty();

    }

    /**
     * 查询我关注的所有人
     *
     * @return
     */
    public static ArrayList<ProfileProto.Profile> getFollowList(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryFollowersRequest build = ProfileQueryProto.QueryFollowersRequest.newBuilder().setAddress(address).build();
        List<ProfileProto.Profile> profilesList = stub.queryFollowers(build).getProfilesList();
        ArrayList<ProfileProto.Profile> postResponses = new ArrayList<>(profilesList);
        return postResponses;

    }

    /**
     * 查询我的消息
     *
     * @return
     */
    public static ArrayList<PostQueryProto.ActivitiesReceivedResponse> getMsgList(int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryActivitiesReceivedRequest build = PostQueryProto.QueryActivitiesReceivedRequest.newBuilder().setPage(page).setAddress(WalletDaoUtils.getCurrent().getAddress()).build();
        List<PostQueryProto.ActivitiesReceivedResponse> activitiesReceivedList = stub.queryActivitiesReceived(build).getActivitiesReceivedList();
        ArrayList<PostQueryProto.ActivitiesReceivedResponse> activitiesReceiveds = new ArrayList<>(activitiesReceivedList);
        return activitiesReceiveds;
    }

    /**
     * 查询我当前消息的数量
     *
     * @return
     */
    public static long getMsgCount(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryActivitiesReceivedCountRequest build = ProfileQueryProto.QueryActivitiesReceivedCountRequest.newBuilder().setAddress(address).build();
        long count = stub.queryActivitiesReceivedCount(build).getCount();
        return count;

    }


    /**
     * 查询用户
     *
     * @return
     */
    public static ArrayList<ProfileProto.Profile> getUserList(String name) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.SearchUsersRequest build = ProfileQueryProto.SearchUsersRequest.newBuilder().setMatching(name).build();
        List<ProfileProto.Profile> usersList = stub.searchUsers(build).getUsersList();
        ArrayList<ProfileProto.Profile> userSearches = new ArrayList<>(usersList);
        return userSearches;
    }

    /**
     * 查询用户
     *
     * @return
     */
    public static ArrayList<ProfileProto.Profile> getAtUserList(String name) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryGetMentionSuggestionsRequest build = ProfileQueryProto.QueryGetMentionSuggestionsRequest.newBuilder().setAddress(WalletDaoUtils.getCurrent().getAddress()).setMatching(name).build();
        List<ProfileProto.Profile> usersList = stub.getMentionSuggestions(build).getProfilesList();
        ArrayList<ProfileProto.Profile> userSearches = new ArrayList<>(usersList);
        return userSearches;
    }

    /**
     * 查询关注人
     *
     * @return
     */
    public static ArrayList<ProfileProto.Profile> queryFollowing(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryFollowingRequest build = ProfileQueryProto.QueryFollowingRequest.newBuilder().setAddress(address).build();
        List<ProfileProto.Profile> usersList = stub.queryFollowing(build).getProfilesList();
        ArrayList<ProfileProto.Profile> userSearches = new ArrayList<>(usersList);
        return userSearches;
    }

    /**
     * 查询粉丝
     *
     * @return
     */
    public static ArrayList<ProfileProto.Profile> queryFollowers(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryFollowersRequest build = ProfileQueryProto.QueryFollowersRequest.newBuilder().setAddress(address).build();
        List<ProfileProto.Profile> usersList = stub.queryFollowers(build).getProfilesList();
        ArrayList<ProfileProto.Profile> userSearches = new ArrayList<>(usersList);
        return userSearches;
    }

    /**
     * 获取分类
     *
     * @return
     */
    public static ArrayList<PostQueryProto.CategoryResponse> getCategories() {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryCategoriesRequest build = PostQueryProto.QueryCategoriesRequest.newBuilder().build();
        List<PostQueryProto.CategoryResponse> categoriesList = stub.queryCategories(build).getCategoriesList();
        ArrayList<PostQueryProto.CategoryResponse> postResponses = new ArrayList<>(categoriesList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }

    /**
     * 通过分类获取话题
     *
     * @return
     */
    public static ArrayList<PostQueryProto.TopicResponse> getTopicsByCategory(String id, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryTopicsByCategoryRequest build = PostQueryProto.QueryTopicsByCategoryRequest.newBuilder()
                .setPage(page)
                .setCategoryId(id).build();
        try {
            List<PostQueryProto.TopicResponse> topicsList = stub.queryTopicsByCategory(build).getResponse().getTopicsList();
            ArrayList<PostQueryProto.TopicResponse> postResponses = new ArrayList<>(topicsList);
            return postResponses;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 通过分类获取帖子
     *
     * @return
     */
    public static ArrayList<PostQueryProto.PostResponse> getCategoryPosts(String id, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryCategoryPostsRequest build = PostQueryProto.QueryCategoryPostsRequest.newBuilder()
                .setPage(page)
                .setCategoryId(id).build();
        try {
            List<PostQueryProto.PostResponse> postsList = stub.queryCategoryPosts(build).getResponse().getPostsList();
            ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>(postsList);
            return postResponses;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 通过话题获取帖子
     *
     * @return
     */
    public static ArrayList<PostQueryProto.PostResponse> getPostsByTopics(String id, int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryTopicPostsRequest build = PostQueryProto.QueryTopicPostsRequest.newBuilder()
                .setPage(page)
                .setTopicId(id).build();
        try {
            List<PostQueryProto.PostResponse> postsList = stub.queryTopicPosts(build).getPostsList();
            ArrayList<PostQueryProto.PostResponse> postResponses = new ArrayList<>(postsList);
            return postResponses;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 榜单
     *
     * @return
     */
    public static ArrayList<PostQueryProto.TopicResponse> getTrendingKeywords(int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryTrendingKeywordsRequest build = PostQueryProto.QueryTrendingKeywordsRequest.newBuilder().setPage(page).build();
        List<PostQueryProto.TopicResponse> commentsList = stub.queryTrendingKeywords(build).getTopicsList();
        ArrayList<PostQueryProto.TopicResponse> postResponses = new ArrayList<>(commentsList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }


    /**
     * 榜单
     *
     * @return
     */
    public static ArrayList<PostQueryProto.TopicResponse> getTrendingTopics(int page) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryTrendingTopicsRequest build = PostQueryProto.QueryTrendingTopicsRequest.newBuilder().setPage(page).build();
        List<PostQueryProto.TopicResponse> commentsList = stub.queryTrendingTopics(build).getTopicsList();
        ArrayList<PostQueryProto.TopicResponse> postResponses = new ArrayList<>(commentsList);
//        Log.e("Cosmos query", "getSavePosts: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }

    /**
     * 查询投票选项
     *
     * @param id
     * @return
     */
    public static String queryVoteOption(String id) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryVoteOptionRequest build = PostQueryProto.QueryVoteOptionRequest
                .newBuilder()
                .setAddress(WalletDaoUtils.getCurrent().getAddress())
                .setPostId(id)
                .build();
        String idStr = stub.queryVoteOption(build).getOptionId();
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                long decodedId = ByteBuffer.wrap(idStr.getBytes()).getLong();
                String numericStr = Long.toString(decodedId);
                return numericStr;
            }
        } catch (Exception e) {
            return "-1";
        }
        return "-1";
    }

    /**
     * 查询是否关注话题
     *
     * @param id
     * @return
     */
    public static Boolean queryIsFollowingTopic(String id) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryIsFollowingTopicRequest build = PostQueryProto.QueryIsFollowingTopicRequest
                .newBuilder()
                .setAddress(WalletDaoUtils.getCurrent().getAddress())
                .setTopicId(id).build();
        boolean isFollowing = stub.queryIsFollowingTopic(build).getIsFollowing();
        return isFollowing;
    }

    /**
     * 查询未分类话题
     *
     * @return
     */
    public static ArrayList<PostQueryProto.TopicResponse> queryUncategorizedTopics(int page, int size) {
        QueryGrpc.QueryBlockingStub stub = QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        PostQueryProto.QueryUncategorizedTopicsRequest build = PostQueryProto.QueryUncategorizedTopicsRequest
                .newBuilder()
                .setAddress(WalletDaoUtils.getCurrent().getAddress())
                .setPage(page)
                .setLimit(size)
                .build();
        List<PostQueryProto.TopicResponse> topicsList = stub.queryUncategorizedTopics(build).getTopicsList();
        ArrayList<PostQueryProto.TopicResponse> postResponses = new ArrayList<>(topicsList);
        Log.e("Cosmos query", "queryUncategorizedTopics: " + JsonUtils.listToJson(postResponses));
        return postResponses;
    }


    /**
     * 查询用户关注
     *
     * @return
     */
    public static long queryFollowRelationship(String address) {
        com.Tlock.io.profile.QueryGrpc.QueryBlockingStub stub = com.Tlock.io.profile.QueryGrpc.newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ProfileQueryProto.QueryFollowRelationshipRequest build = ProfileQueryProto.QueryFollowRelationshipRequest
                .newBuilder()
                .setAddressA(WalletDaoUtils.getCurrent().getAddress())
                .setAddressB(address)
                .build();
        long relationship = stub.queryFollowRelationship(build).getRelationship();

        return relationship;
    }


    /**
     * 查询用户关注
     *
     * @return
     */
    @SuppressLint("CheckResult")
    public static ServiceProto.GetTxResponse queryHash(String hash) {
        ServiceGrpc.ServiceBlockingStub stub = newBlockingStub(channel).withDeadlineAfter(30L, TimeUnit.SECONDS);
        ServiceProto.GetTxRequest build = ServiceProto.GetTxRequest.newBuilder().setHash(hash).build();
        return stub.getTx(build);
    }


    /**********************************************查询   end**********************************************************/

}
