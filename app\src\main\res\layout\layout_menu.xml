<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_300"
    android:layout_height="match_parent"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_list_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_26"
        android:text="@string/wallet_list"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_18"
     />


    <View
        style="@style/gray_horizontal_line_view"
        android:id="@+id/line1"
        android:layout_marginBottom="@dimen/dp_18"
        android:layout_below="@id/tv_list_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_wallet"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/tv_create"
        android:layout_below="@+id/line1"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@color/white" />

    <TextView
        android:id="@+id/tv_create"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:background="@drawable/btn_black_6"
        android:gravity="center"
        android:padding="@dimen/dp_15"
        android:text="Add wallet"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />
</RelativeLayout>
