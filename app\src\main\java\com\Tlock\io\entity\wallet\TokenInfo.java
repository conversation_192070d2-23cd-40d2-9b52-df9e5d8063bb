package com.Tlock.io.entity.wallet;


import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;


import org.greenrobot.greendao.annotation.Generated;

/**
 * @ClassName TokenInfo
 * <AUTHOR>
 * @Data 2021/11/16 15:52
 * @Desc
 */
@Entity
public class TokenInfo {
    @Id(autoincrement = true)
    private Long id;

    private Long walletId;
    private String name;
    private String symbol;
    private String decimals;
    private String count;
    private String balanceOf;
    private String price;
    private String imgUrl;
    private String address;
    private String walletAddress;
    private int chainID;
    @Generated(hash = 1457305956)
    public TokenInfo(Long id, Long walletId, String name, String symbol,
            String decimals, String count, String balanceOf, String price,
            String imgUrl, String address, String walletAddress, int chainID) {
        this.id = id;
        this.walletId = walletId;
        this.name = name;
        this.symbol = symbol;
        this.decimals = decimals;
        this.count = count;
        this.balanceOf = balanceOf;
        this.price = price;
        this.imgUrl = imgUrl;
        this.address = address;
        this.walletAddress = walletAddress;
        this.chainID = chainID;
    }
    @Generated(hash = 2142441333)
    public TokenInfo() {
    }
    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Long getWalletId() {
        return this.walletId;
    }
    public void setWalletId(Long walletId) {
        this.walletId = walletId;
    }
    public String getName() {
        return this.name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getSymbol() {
        return this.symbol;
    }
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    public String getDecimals() {
        return this.decimals;
    }
    public void setDecimals(String decimals) {
        this.decimals = decimals;
    }
    public String getCount() {
        return this.count;
    }
    public void setCount(String count) {
        this.count = count;
    }
    public String getBalanceOf() {
        return this.balanceOf;
    }
    public void setBalanceOf(String balanceOf) {
        this.balanceOf = balanceOf;
    }
    public String getPrice() {
        return this.price;
    }
    public void setPrice(String price) {
        this.price = price;
    }
    public String getImgUrl() {
        return this.imgUrl;
    }
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
    public String getAddress() {
        return this.address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    public String getWalletAddress() {
        return this.walletAddress;
    }
    public void setWalletAddress(String walletAddress) {
        this.walletAddress = walletAddress;
    }
    public int getChainID() {
        return this.chainID;
    }
    public void setChainID(int chainID) {
        this.chainID = chainID;
    }

}
