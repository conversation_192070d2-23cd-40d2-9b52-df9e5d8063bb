[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_create_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_create_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_home_tab.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_home_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_tg.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_tg.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_custom_input_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\custom_input_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_status_at.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_status_at.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_iv_bird.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_iv_bird.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_progressdlg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\progressdlg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_crypto.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_crypto.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_notifications_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_notifications_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_wallet_edit_name.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_wallet_edit_name.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_forward.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_forward.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_netwarmingdlg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\netwarmingdlg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_warning.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_warning.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_white_10.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_white_10.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_heart_1.gif.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\heart_1.gif"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_layout_toast.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\layout_toast.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_rounded_corner_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\rounded_corner_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_layer_h5_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\layer_h5_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_gray_line_13.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_gray_line_13.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\raw_wallet_data.txt.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\raw\\wallet_data.txt"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_app_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\app_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\anim_pop_slide_in_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\anim\\pop_slide_in_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_payer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_payer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_post1.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_post1.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_topic_heard_default.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\topic_heard_default.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_home_default.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_home_default.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_bg_deep_gray_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\bg_deep_gray_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_setting.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_setting.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_kline_info.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_kline_info.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_edit_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_edit_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_progressbar_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\progressbar_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_common_waiting_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\common_waiting_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_white_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_white_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_msg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_msg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_like.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_like.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_edit.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_edit.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_gif_collect.gif.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\gif_collect.gif"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_current.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_current.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_transfer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_transfer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_at.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_at.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\anim_pop_slide_out_top.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\anim\\pop_slide_out_top.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_blue_add1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_blue_add1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_check_mnemonics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_check_mnemonics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_hot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_hot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_hot.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_hot.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_heard_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_heard_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_post_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_post_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_black_down.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_black_down.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_post_quote.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_post_quote.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_add_image.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_add_image.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_status_save.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_status_save.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_member_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_member_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_eye_open.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_eye_open.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_transfer_history_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_transfer_history_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_chirp_medium_500.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\chirp_medium_500.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic6.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic6.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_alpha_red_2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_alpha_red_2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_default_16.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_default_16.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_dark_4.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_dark_4.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_content_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_content_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_layout_pop_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\layout_pop_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_show_more.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_show_more.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_status_like.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_status_like.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_select_black.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_select_black.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_input_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_input_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_wallet_order.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_wallet_order.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_right.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_right.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_check_pwd.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_check_pwd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_btn_gray_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\btn_gray_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_backup_mnemonics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_backup_mnemonics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_layout_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\layout_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic0.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic0.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_new_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_new_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_test1.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_test1.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_deep_blue_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_deep_blue_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_mine.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_mine.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_follow.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_follow.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_transaction.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_transaction.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_wallet.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_wallet.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_change_pwd.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_change_pwd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_mnemonics.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_mnemonics.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_whale_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_whale_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_clear.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_clear.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_white_16.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_white_16.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_report.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_report.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\raw_trust_js.js.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\raw\\trust_js.js"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_backup_private_key.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_backup_private_key.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_status_callback.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_status_callback.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_x.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_x.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_gold_vip.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_gold_vip.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_load_error_page.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\load_error_page.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_home.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_home.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_copy_gray.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_copy_gray.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_scan.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_scan.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_post_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_post_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_transfer_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_transfer_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_bg_gray_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\bg_gray_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_m_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_m_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_proposal_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_proposal_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_user.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_user.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_bg_gray_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\bg_gray_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_white_2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_white_2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_eye_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_eye_close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_transparent_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_transparent_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_copy_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_copy_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_like_post.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_like_post.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_network.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_network.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_updatedialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\updatedialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_task_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_task_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_proposal_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_proposal_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_total_token.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_total_token.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_gray_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_gray_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_comment.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_comment.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_send_token.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_send_token.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_transfer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_transfer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_think_face.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_think_face.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_share_pic.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_share_pic.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\anim_pop_slide_out_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\anim\\pop_slide_out_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_font_family.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\font_family.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_chirp_regular_400.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\chirp_regular_400.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_to_backup_mnemonics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_to_backup_mnemonics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_post_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_post_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_copy_url.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\copy_url.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_delete_wallet.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_delete_wallet.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_wallet_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_wallet_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_favorite.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_favorite.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_shape_gray_line_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\shape_gray_line_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_blue_line_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_blue_line_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_red_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_red_close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_my.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_my.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_post_like.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_post_like.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_private_key.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_private_key.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_manger.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_manger.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_post_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_post_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_round_del.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_round_del.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_notice.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_notice.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_category_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_category_select.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_deep_gray_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_deep_gray_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\raw_data.txt.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\raw\\data.txt"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_deep_gray_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_deep_gray_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_search_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_search_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_copy_url0.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_copy_url0.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\xml_provider_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\xml\\provider_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_post_share.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_post_share.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic_list_default.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic_list_default.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_back_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_back_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_gray_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_gray_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_btn_gray_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\btn_gray_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_gray_shade_10.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_gray_shade_10.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_chirp_heavy_800.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\chirp_heavy_800.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_tlk_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\tlk_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\anim_pop_scale_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\anim\\pop_scale_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_share.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_share.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_topic_tab.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_topic_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_notifications.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_notifications.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_save_select.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_save_select.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_transfer_confirm.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_transfer_confirm.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_end_time.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_end_time.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_tip_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\tip_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_collect.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_collect.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_repost.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_repost.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_rotate_loading_progressbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\rotate_loading_progressbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_h5_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_h5_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_img_wallet.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\img_wallet.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_recommend.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_recommend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_select_vote.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_select_vote.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_check_finger.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_check_finger.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_close_post.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_close_post.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_fill_rectangle_trancelucence_4.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_fill_rectangle_trancelucence_4.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_post.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_post.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_history.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_history.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_m.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_m.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_transaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_yellow_border_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_yellow_border_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic_select.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic_select.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_status_follow.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_status_follow.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_governance.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_governance.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_scan_white.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_scan_white.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_toast_center.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\toast_center.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_task_roport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_task_roport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_roboto_medium_numbers.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\roboto_medium_numbers.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_select_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_select_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_btn_blue_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\btn_blue_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_gif_loading.gif.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\gif_loading.gif"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_ooo.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\ooo.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_layout_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\layout_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_comment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_mnemonic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_mnemonic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_img_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\img_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_bg_gray_13.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\bg_gray_13.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_wallet_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_wallet_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_setting_proposal.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_setting_proposal.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_h5_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_h5_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_quote.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_quote.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_right_gray.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_right_gray.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_menu.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_menu.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_deep_gray_line_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_deep_gray_line_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_allowance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_allowance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_gray_more.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_gray_more.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic_list.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic_list.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_like_select.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_like_select.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_share_post.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_share_post.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_toast_blue_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\toast_blue_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_light_red_2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_light_red_2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\raw_user_name_heard.txt.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\raw\\user_name_heard.txt"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_check_mnemonics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_check_mnemonics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_custom_navi_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\custom_navi_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_private.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_private.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_btn_black_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\btn_black_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_hot_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_hot_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_send.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_send.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_post_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_post_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_topic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_topic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_search_deep_gray.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_search_deep_gray.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_edit_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_edit_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_post_save.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_post_save.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_bg_deep_gray_13.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\bg_deep_gray_13.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_receive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_receive.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_reset_time.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_reset_time.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_custom_search_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\custom_search_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_yellow_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_yellow_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\anim_pop_slide_in_top.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\anim\\pop_slide_in_top.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_dark_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_dark_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\menu_navigation_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\menu\\navigation_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_user_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_user_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_wallet_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_wallet_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_received.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_received.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\font_font_family_bold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\font\\font_family_bold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_more.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_more.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic7.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic7.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_index.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\index.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_proposal.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_proposal.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_tip_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_tip_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_post.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_post.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_tok.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_tok.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_delete.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_delete.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_check.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_check.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_yellow_shade_10.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_yellow_shade_10.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_hot_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_hot_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_show_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_show_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_layout_gas_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\layout_gas_select.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_warn_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\warn_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_backup_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_backup_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_hot_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_hot_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_bg_light_gray_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\bg_light_gray_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_progress_indeterminate_horizontal_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\progress_indeterminate_horizontal_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_rounded_corner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\rounded_corner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_yellow_alpha_shade_10.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_yellow_alpha_shade_10.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_dapp_brower.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_dapp_brower.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_btn_blue_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\btn_blue_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_close_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_close_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_transaction_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_transaction_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_light_gray_60.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_light_gray_60.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_topic_class_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_topic_class_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_post_image_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_post_image_select.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_hot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_hot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_user_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_user_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_refresh.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\refresh.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_asset_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_asset_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\raw_user_info.txt.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\raw\\user_info.txt"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_share_address.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_share_address.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_topic2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_topic2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_successful.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_successful.webp"}, {"merged": "com.Tlock.io.app-merged_res-53:/layout_item_post1.xml.flat", "source": "com.Tlock.io.app-main-57:/layout/item_post1.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_classify.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_classify.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_fingerprint.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_fingerprint.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_community.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_community.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_pop_two_button_tip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\pop_two_button_tip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_topic_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_topic_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\raw_word_list.txt.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\raw\\word_list.txt"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_h5.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_h5.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_shape_white_bottom_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\shape_white_bottom_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_progress_indeterminate_horizontal_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\progress_indeterminate_horizontal_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable-v24_btn_black_6.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable-v24\\btn_black_6.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_blue_add.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_blue_add.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_custom_edit_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\custom_edit_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_item_category_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\item_category_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_item_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\item_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_activity_m_task_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\activity_m_task_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_delete_img.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_delete_img.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_pop_close.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_pop_close.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\mipmap-xxhdpi_icon_allowance.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\mipmap-xxhdpi\\icon_allowance.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\anim_pop_scale_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\anim\\pop_scale_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\drawable_bg_random_heard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\drawable\\bg_random_heard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-merged_res-53:\\layout_fragment_index.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\7.5\\com.Tlock.io.app-main-57:\\layout\\fragment_index.xml"}]