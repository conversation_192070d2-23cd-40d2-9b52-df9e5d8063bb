// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.fragment;

import android.view.View;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.cy.tablayoutniubility.TabLayoutScroll;
import java.lang.IllegalStateException;
import java.lang.Override;

public class HotFragment_ViewBinding implements Unbinder {
  private HotFragment target;

  @UiThread
  public HotFragment_ViewBinding(HotFragment target, View source) {
    this.target = target;

    target.tabLayoutLine = Utils.findRequiredViewAsType(source, R.id.tablayout, "field 'tabLayoutLine'", TabLayoutScroll.class);
    target.viewPager2 = Utils.findRequiredViewAsType(source, R.id.view_pager, "field 'viewPager2'", ViewPager2.class);
    target.mRoot = Utils.findRequiredViewAsType(source, R.id.root, "field 'mRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    HotFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.tabLayoutLine = null;
    target.viewPager2 = null;
    target.mRoot = null;
  }
}
