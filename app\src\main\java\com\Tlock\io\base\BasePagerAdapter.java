package com.Tlock.io.base;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.ArrayList;

public class BasePagerAdapter extends FragmentPagerAdapter {
  private ArrayList<Fragment> list;
  private ArrayList<String> titles;
    public BasePagerAdapter(@NonNull FragmentManager fm) {
        super(fm);
    }

    public BasePagerAdapter(@NonNull FragmentManager fm, int behavior) {
        super(fm, behavior);
    }
    public BasePagerAdapter(@NonNull FragmentManager fm, ArrayList<Fragment> list,ArrayList<String> titles) {
        super(fm);
        this.list=list;
        this.titles=titles;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        String name = titles.get(position);
        return name;
    }

    @Override
    public Fragment getItem(int position) {
        return list.get(position);
    }

    public void refresh(){
        notifyDataSetChanged();
    }
}
