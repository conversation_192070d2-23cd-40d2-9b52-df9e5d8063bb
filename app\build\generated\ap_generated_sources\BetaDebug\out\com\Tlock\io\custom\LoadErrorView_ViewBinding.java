// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.custom;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class LoadErrorView_ViewBinding implements Unbinder {
  private LoadErrorView target;

  @UiThread
  public LoadErrorView_ViewBinding(LoadErrorView target) {
    this(target, target);
  }

  @UiThread
  public LoadErrorView_ViewBinding(LoadErrorView target, View source) {
    this.target = target;

    target.mIvErrorImg = Utils.findRequiredViewAsType(source, R.id.iv_error_img, "field 'mIvErrorImg'", ImageView.class);
    target.mErrorMsg = Utils.findRequiredViewAsType(source, R.id.tv_error_msg, "field 'mErrorMsg'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    LoadErrorView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvErrorImg = null;
    target.mErrorMsg = null;
  }
}
