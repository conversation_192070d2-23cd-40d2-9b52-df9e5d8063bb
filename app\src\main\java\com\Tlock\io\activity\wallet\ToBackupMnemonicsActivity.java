package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;

import butterknife.BindView;
import butterknife.OnClick;

public class ToBackupMnemonicsActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.rl_toolbar)
    RelativeLayout mRlToolbar;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.tv_1)
    TextView mTv1;
    @BindView(R.id.tv_backup)
    TextView mTvBackup;


    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String data) {
        Intent intent = new Intent(context, ToBackupMnemonicsActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_to_backup_mnemonics;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        mTv1.setText("Your 12/24-word recovery phrase is the ONLY way to restore access to your wallet and funds.\n\n" +
                "• Write it down on paper and store it securely (never digitally)\n" +
                "• Keep it private - anyone with this phrase can steal your assets\n" +
                "• Make multiple copies and store them in separate safe locations\n" +
                "• Never share it with anyone, including wallet support staff\n" +
                "• This phrase cannot be recovered if lost you will permanently lose access to your funds\n\n" +
                "We cannot restore your recovery phrase if you lose it. You are solely responsible for keeping it safe.");
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {

    }

    @OnClick(R.id.tv_backup)
    public void onViewClicked() {
        String data = getIntent().getStringExtra("data");
        BackupMnemonicsActivity.start(getActivity(), data, 1);
    }
}