// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ShareAddressActivity_ViewBinding implements Unbinder {
  private ShareAddressActivity target;

  private View view7f090311;

  private View view7f090365;

  @UiThread
  public ShareAddressActivity_ViewBinding(ShareAddressActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public ShareAddressActivity_ViewBinding(final ShareAddressActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvAddress = Utils.findRequiredViewAsType(source, R.id.iv_address, "field 'mIvAddress'", ImageView.class);
    target.mTvAddress = Utils.findRequiredViewAsType(source, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    target.mLlImg = Utils.findRequiredViewAsType(source, R.id.ll_img, "field 'mLlImg'", LinearLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_copy_address, "method 'onViewClicked'");
    view7f090311 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_share_address, "method 'onViewClicked'");
    view7f090365 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    ShareAddressActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvAddress = null;
    target.mTvAddress = null;
    target.mLlImg = null;

    view7f090311.setOnClickListener(null);
    view7f090311 = null;
    view7f090365.setOnClickListener(null);
    view7f090365 = null;
  }
}
