<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".activity.cosmos.SearchActivity">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:padding="@dimen/dp_10"
        android:src="@mipmap/icon_back_black" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_alignBaseline="@id/iv_back"
        android:layout_marginRight="@dimen/dp_15"
        android:layout_toRightOf="@id/iv_back"
        android:background="@drawable/btn_gray_60">

        <EditText
            android:id="@+id/ed_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dp_15"
            android:layout_toLeftOf="@id/iv_clear"
            android:background="@null"
            android:hint="Search for users or topics"
            android:maxLines="1"
            android:textColorHint="@color/hint_color"
            android:singleLine="true"
            android:textSize="@dimen/sp_14" />

        <ImageView
            android:id="@+id/iv_clear"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp_8"
            android:src="@mipmap/icon_round_del"
            android:visibility="gone" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/ll_history"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_back"
        android:layout_marginTop="@dimen/dp_10"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_15"
        android:paddingRight="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="Search History"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_clear_history"
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_30"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp_6"
            android:src="@mipmap/icon_close_black" />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_history"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/ll_history"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_5"
        android:visibility="gone" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_topic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_history"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_5"
        android:visibility="gone" />

    <View
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@id/rv_topic"
        android:background="#EFF3F4"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_user"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/line1"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_5"
        android:visibility="gone" />

</RelativeLayout>