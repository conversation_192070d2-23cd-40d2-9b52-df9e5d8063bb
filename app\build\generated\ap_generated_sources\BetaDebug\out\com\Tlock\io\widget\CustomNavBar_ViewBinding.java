// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.constraintlayout.widget.ConstraintLayout;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CustomNavBar_ViewBinding implements Unbinder {
  private CustomNavBar target;

  @UiThread
  public CustomNavBar_ViewBinding(CustomNavBar target) {
    this(target, target);
  }

  @UiThread
  public CustomNavBar_ViewBinding(CustomNavBar target, View source) {
    this.target = target;

    target.mIvLeft = Utils.findRequiredViewAsType(source, R.id.iv_left, "field 'mIvLeft'", ImageView.class);
    target.mTvLeft = Utils.findRequiredViewAsType(source, R.id.tv_left, "field 'mTvLeft'", TextView.class);
    target.mLlLeft = Utils.findRequiredViewAsType(source, R.id.ll_left, "field 'mLlLeft'", LinearLayout.class);
    target.mTvMid = Utils.findRequiredViewAsType(source, R.id.tv_mid, "field 'mTvMid'", TextView.class);
    target.mIvRight = Utils.findRequiredViewAsType(source, R.id.iv_right, "field 'mIvRight'", ImageView.class);
    target.mTvRight = Utils.findRequiredViewAsType(source, R.id.tv_right, "field 'mTvRight'", TextView.class);
    target.mLlRight = Utils.findRequiredViewAsType(source, R.id.ll_right, "field 'mLlRight'", LinearLayout.class);
    target.mClNavBar = Utils.findRequiredViewAsType(source, R.id.cl_nav_bar, "field 'mClNavBar'", ConstraintLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    CustomNavBar target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvLeft = null;
    target.mTvLeft = null;
    target.mLlLeft = null;
    target.mTvMid = null;
    target.mIvRight = null;
    target.mTvRight = null;
    target.mLlRight = null;
    target.mClNavBar = null;
  }
}
