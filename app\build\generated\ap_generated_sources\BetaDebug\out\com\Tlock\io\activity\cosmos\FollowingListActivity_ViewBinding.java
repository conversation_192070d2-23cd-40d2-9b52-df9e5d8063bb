// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.MeasureRecyclerView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class FollowingListActivity_ViewBinding implements Unbinder {
  private FollowingListActivity target;

  @UiThread
  public FollowingListActivity_ViewBinding(FollowingListActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public FollowingListActivity_ViewBinding(FollowingListActivity target, View source) {
    this.target = target;

    target.mRecyclerView = Utils.findRequiredViewAsType(source, R.id.recyclerView_community, "field 'mRecyclerView'", MeasureRecyclerView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    FollowingListActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRecyclerView = null;
  }
}
