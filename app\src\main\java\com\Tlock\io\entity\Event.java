package com.Tlock.io.entity;

public class Event {

    private String messgae;
    private String data;
    private int position;
    private long time;
    private Object obj;

    public Event(String messgae) {
        this.messgae = messgae;
    }
    public Event(Object obj) {
        this.obj = obj;
    }

    public String getMessgae() {
        return messgae;
    }

    public Event(String messgae, String data) {
        this.messgae = messgae;
        this.data = data;
    }

    public Event(String messgae, Object obj) {
        this.messgae = messgae;
        this.obj = obj;
    }
    public Event(String messgae, Object obj,int position) {
        this.messgae = messgae;
        this.obj = obj;
        this.position = position;

    }

    public Event(String messgae, String data, int position) {
        this.messgae = messgae;
        this.data = data;
        this.position = position;
    }

    public Event(String messgae, int position) {
        this.messgae = messgae;
        this.position = position;
    }

    public Event(String messgae, long time) {
        this.messgae = messgae;
        this.time = time;
    }

    public Object getObj() {
        return obj;
    }

    public void setObj(Object obj) {
        this.obj = obj;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public void setMessgae(String messgae) {
        this.messgae = messgae;
    }

    public String getData() {
        return data;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public void setData(String data) {
        this.data = data;
    }
}