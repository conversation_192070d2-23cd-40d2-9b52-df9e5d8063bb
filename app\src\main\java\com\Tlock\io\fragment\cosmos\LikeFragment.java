package com.Tlock.io.fragment.cosmos;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.ContentInfoActivity;
import com.Tlock.io.activity.cosmos.PostQuoteActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseFragment;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.PostItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.NewCosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.pop.PopPostForward;
import com.Tlock.io.widget.pop.PopPostMore;
import com.Tlock.io.widget.pop.PopPostShare;
import com.lxj.xpopup.XPopup;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

import butterknife.BindView;


public class LikeFragment extends BaseFragment {

    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    private BaseRecyclerViewAdapter<PostQueryProto.PostResponse> adapter;
    private ETHWallet current;
    private int position = 0;
    private String address = "";

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_post_like;
    }

    public LikeFragment(int position, String address) {
        this.position = position;
        this.address = address;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        current = WalletDaoUtils.getCurrent();
        initRecycleView();
    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getContext(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.PostResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                PostItemView itemView = new PostItemView(getContext());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.PostResponse data, View view) {
                ((PostItemView) view).setData(data);
                ((PostItemView) view).setCallback(new PostItemView.Callback() {

                    @Override
                    public void quote() {
                        PopPostForward popPostMore = new PopPostForward(getActivity(), data.getPost().getId(), "");
                        popPostMore.setCallBack(new PopPostForward.CallBack() {

                            @Override
                            public void repost(String id) {
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.postRepost(WalletDaoUtils.getCurrent().getAddress(), id);

                                    }
                                });
                            }

                            @Override
                            public void quote(String id) {
                                PostQuoteActivity.start(getActivity(), id);
                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
                    }

                    @Override
                    public void share() {
                        PopPostShare popPostMore = new PopPostShare(getActivity());
                        popPostMore.setCallBack(new PopPostShare.CallBack() {

                            @Override
                            public void follow(String handle) {

                            }

                            @Override
                            public void report(String id) {

                            }

                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();

                    }

                    @Override
                    public void review(String id, String handle) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), true);
                    }


                    @Override
                    public void praise(String id) {
                        //点赞
                        if (current != null) {
                            showToast("Liked");

                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    NewCosmosUtils.postLike(current.getAddress(), id);
                                }
                            });
                        }

                    }

                    @Override
                    public void collect(String id) {
                        //收藏
                        if (current != null) {
                            showToast("Added to bookmarks");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    NewCosmosUtils.savePost(current.getAddress(), id);
                                }
                            });
                        }


                    }

                    @Override
                    public void more(String data, String handle, String address) {
                        PopPostMore popPostMore = new PopPostMore(getActivity(), data, handle);
                        popPostMore.setCallBack(new PopPostMore.CallBack() {
                            @Override
                            public void follow(String handle) {
                                showToast("Following");
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.follow(current.getAddress(), address);
                                    }
                                });
                            }

                            @Override
                            public void report(String id) {
                                //TODO 新页面
                                showToast("");

                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
                    }

                    @Override
                    public void info(PostQueryProto.PostResponse data) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
                    }

                    @Override
                    public void resetProfile(PostQueryProto.PostResponse postResponse) {
                        adapter.getList().set(position, postResponse);
                    }
                });

            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.PostResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.PostResponse data, View view) {
                EventBus.getDefault().postSticky(new Event("info.Post", data));
                ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
            }
        });
    }

    @Override
    protected void loadData() {
        getPostList();
    }

    @Override
    protected void getData() {
        super.getData();
        getPostList();

    }

    public void getListData(int position) {
        this.position = position;
        if (adapter != null && adapter.getList().isEmpty()) {
            getPostList();
        }

    }

    public void getPostList() {

        //  修改成实际的
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.PostResponse> datas = new ArrayList<>();
                switch (position) {
                    case 0:
                        if (current != null) {
                            datas = CosmosUtils.getMyPosts(address, page);
                        }
                        break;
                    case 1:
                        if (current != null) {
                            datas = CosmosUtils.getLikePosts(address,page);
                        }
                        break;
                    case 2:
                        if (current != null) {
                            datas = CosmosUtils.getSavePosts(address,page);
                        }
                        break;
                }
                ArrayList<PostQueryProto.PostResponse> finalDatas = datas;
                mRecyclerView.post(new Runnable() {
                    @Override
                    public void run() {
                        if (adapter != null) {
                            adapter.addListNoChange(finalDatas,page);
                        }
                        finishRefresh();
                    }
                });


            }
        });

    }

}