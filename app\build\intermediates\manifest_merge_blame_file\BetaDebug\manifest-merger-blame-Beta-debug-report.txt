1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Tlock.io"
4    android:versionCode="148"
5    android:versionName="0.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->D:\cs\tlk\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\cs\tlk\app\src\main\AndroidManifest.xml
10
11    <uses-permission
11-->D:\cs\tlk\app\src\main\AndroidManifest.xml:6:5-8:36
12        android:name="android.permission.BROADCAST_PACKAGE_ADDED"
12-->D:\cs\tlk\app\src\main\AndroidManifest.xml:7:9-66
13        android:required="false" />
13-->D:\cs\tlk\app\src\main\AndroidManifest.xml:8:9-33
14    <uses-permission
14-->D:\cs\tlk\app\src\main\AndroidManifest.xml:9:5-11:36
15        android:name="android.permission.BROADCAST_PACKAGE_CHANGED"
15-->D:\cs\tlk\app\src\main\AndroidManifest.xml:10:9-68
16        android:required="false" />
16-->D:\cs\tlk\app\src\main\AndroidManifest.xml:11:9-33
17    <uses-permission
17-->D:\cs\tlk\app\src\main\AndroidManifest.xml:12:5-14:36
18        android:name="android.permission.BROADCAST_PACKAGE_INSTALL"
18-->D:\cs\tlk\app\src\main\AndroidManifest.xml:13:9-68
19        android:required="false" />
19-->D:\cs\tlk\app\src\main\AndroidManifest.xml:14:9-33
20    <uses-permission
20-->D:\cs\tlk\app\src\main\AndroidManifest.xml:15:5-17:36
21        android:name="android.permission.BROADCAST_PACKAGE_REPLACED"
21-->D:\cs\tlk\app\src\main\AndroidManifest.xml:16:9-69
22        android:required="false" />
22-->D:\cs\tlk\app\src\main\AndroidManifest.xml:17:9-33
23    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
23-->D:\cs\tlk\app\src\main\AndroidManifest.xml:18:5-84
23-->D:\cs\tlk\app\src\main\AndroidManifest.xml:18:22-81
24    <uses-permission
24-->D:\cs\tlk\app\src\main\AndroidManifest.xml:19:5-21:36
25        android:name="android.permission.WAKE_LOCK"
25-->D:\cs\tlk\app\src\main\AndroidManifest.xml:20:9-52
26        android:required="false" />
26-->D:\cs\tlk\app\src\main\AndroidManifest.xml:21:9-33
27    <uses-permission
27-->D:\cs\tlk\app\src\main\AndroidManifest.xml:22:5-24:36
28        android:name="android.permission.REORDER_TASKS"
28-->D:\cs\tlk\app\src\main\AndroidManifest.xml:23:9-56
29        android:required="false" />
29-->D:\cs\tlk\app\src\main\AndroidManifest.xml:24:9-33
30    <uses-permission
30-->D:\cs\tlk\app\src\main\AndroidManifest.xml:25:5-27:36
31        android:name="android.permission.DISABLE_KEYGUARD"
31-->D:\cs\tlk\app\src\main\AndroidManifest.xml:26:9-59
32        android:required="false" />
32-->D:\cs\tlk\app\src\main\AndroidManifest.xml:27:9-33
33    <uses-permission
33-->D:\cs\tlk\app\src\main\AndroidManifest.xml:28:5-30:36
34        android:name="android.permission.SYSTEM_ALERT_WINDOW"
34-->D:\cs\tlk\app\src\main\AndroidManifest.xml:29:9-62
35        android:required="false" />
35-->D:\cs\tlk\app\src\main\AndroidManifest.xml:30:9-33
36    <uses-permission
36-->D:\cs\tlk\app\src\main\AndroidManifest.xml:31:5-33:36
37        android:name="android.permission.SYSTEM_OVERLAY_WINDOW"
37-->D:\cs\tlk\app\src\main\AndroidManifest.xml:32:9-64
38        android:required="false" />
38-->D:\cs\tlk\app\src\main\AndroidManifest.xml:33:9-33
39    <uses-permission
39-->D:\cs\tlk\app\src\main\AndroidManifest.xml:34:5-36:36
40        android:name="android.permission.INTERNET"
40-->D:\cs\tlk\app\src\main\AndroidManifest.xml:35:9-51
41        android:required="false" />
41-->D:\cs\tlk\app\src\main\AndroidManifest.xml:36:9-33
42    <uses-permission
42-->D:\cs\tlk\app\src\main\AndroidManifest.xml:37:5-39:36
43        android:name="android.permission.ACCESS_NETWORK_STATE"
43-->D:\cs\tlk\app\src\main\AndroidManifest.xml:38:9-63
44        android:required="false" />
44-->D:\cs\tlk\app\src\main\AndroidManifest.xml:39:9-33
45    <uses-permission
45-->D:\cs\tlk\app\src\main\AndroidManifest.xml:40:5-42:36
46        android:name="android.permission.READ_PHONE_STATE"
46-->D:\cs\tlk\app\src\main\AndroidManifest.xml:41:9-59
47        android:required="false" />
47-->D:\cs\tlk\app\src\main\AndroidManifest.xml:42:9-33
48    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
48-->D:\cs\tlk\app\src\main\AndroidManifest.xml:43:5-81
48-->D:\cs\tlk\app\src\main\AndroidManifest.xml:43:22-78
49    <uses-permission
49-->D:\cs\tlk\app\src\main\AndroidManifest.xml:44:5-46:36
50        android:name="android.permission.REQUEST_DELETE_PACKAGES"
50-->D:\cs\tlk\app\src\main\AndroidManifest.xml:45:9-66
51        android:required="false" />
51-->D:\cs\tlk\app\src\main\AndroidManifest.xml:46:9-33
52    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
52-->D:\cs\tlk\app\src\main\AndroidManifest.xml:47:5-90
52-->D:\cs\tlk\app\src\main\AndroidManifest.xml:47:22-87
53    <uses-permission
53-->D:\cs\tlk\app\src\main\AndroidManifest.xml:48:5-50:38
54        android:name="android.permission.READ_EXTERNAL_STORAGE"
54-->D:\cs\tlk\app\src\main\AndroidManifest.xml:49:9-64
55        android:maxSdkVersion="32" />
55-->D:\cs\tlk\app\src\main\AndroidManifest.xml:50:9-35
56    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
56-->D:\cs\tlk\app\src\main\AndroidManifest.xml:51:5-76
56-->D:\cs\tlk\app\src\main\AndroidManifest.xml:51:22-73
57    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
57-->D:\cs\tlk\app\src\main\AndroidManifest.xml:52:5-75
57-->D:\cs\tlk\app\src\main\AndroidManifest.xml:52:22-72
58    <uses-permission
58-->D:\cs\tlk\app\src\main\AndroidManifest.xml:53:5-55:36
59        android:name="android.permission.ACCESS_WIFI_STATE"
59-->D:\cs\tlk\app\src\main\AndroidManifest.xml:54:9-60
60        android:required="false" />
60-->D:\cs\tlk\app\src\main\AndroidManifest.xml:55:9-33
61    <uses-permission
61-->D:\cs\tlk\app\src\main\AndroidManifest.xml:40:5-42:36
62        android:name="android.permission.READ_PHONE_STATE"
62-->D:\cs\tlk\app\src\main\AndroidManifest.xml:41:9-59
63        android:required="false" />
63-->D:\cs\tlk\app\src\main\AndroidManifest.xml:42:9-33
64    <uses-permission
64-->D:\cs\tlk\app\src\main\AndroidManifest.xml:59:5-61:36
65        android:name="android.permission.READ_SETTINGS"
65-->D:\cs\tlk\app\src\main\AndroidManifest.xml:60:9-56
66        android:required="false" />
66-->D:\cs\tlk\app\src\main\AndroidManifest.xml:61:9-33
67    <uses-permission
67-->D:\cs\tlk\app\src\main\AndroidManifest.xml:62:5-64:36
68        android:name="android.permission.ACCESS_COARSE_LOCATION"
68-->D:\cs\tlk\app\src\main\AndroidManifest.xml:63:9-65
69        android:required="false" />
69-->D:\cs\tlk\app\src\main\AndroidManifest.xml:64:9-33
70    <uses-permission
70-->D:\cs\tlk\app\src\main\AndroidManifest.xml:65:5-68:47
71        android:name="android.permission.WRITE_SETTINGS"
71-->D:\cs\tlk\app\src\main\AndroidManifest.xml:66:9-57
72        android:required="false" />
72-->D:\cs\tlk\app\src\main\AndroidManifest.xml:67:9-33
73
74    <permission
75        android:name="com.Tlock.io.openadsdk.permission.TT_PANGOLIN"
75-->D:\cs\tlk\app\src\main\AndroidManifest.xml:71:9-73
76        android:protectionLevel="signature" />
76-->D:\cs\tlk\app\src\main\AndroidManifest.xml:72:9-44
77
78    <uses-permission android:name="com.Tlock.io.openadsdk.permission.TT_PANGOLIN" />
78-->D:\cs\tlk\app\src\main\AndroidManifest.xml:74:22-86
79    <uses-permission
79-->D:\cs\tlk\app\src\main\AndroidManifest.xml:75:5-78:47
80        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
80-->D:\cs\tlk\app\src\main\AndroidManifest.xml:76:9-68
81        android:required="true" /> <!-- 硬件加速对X5视频播放非常重要，建议开启 -->
81-->D:\cs\tlk\app\src\main\AndroidManifest.xml:77:9-32
82    <uses-permission
82-->D:\cs\tlk\app\src\main\AndroidManifest.xml:79:5-81:36
83        android:name="android.permission.GET_TASKS"
83-->D:\cs\tlk\app\src\main\AndroidManifest.xml:80:9-52
84        android:required="false" />
84-->D:\cs\tlk\app\src\main\AndroidManifest.xml:81:9-33
85    <uses-permission
85-->D:\cs\tlk\app\src\main\AndroidManifest.xml:82:5-85:47
86        android:name="android.permission.READ_LOGS"
86-->D:\cs\tlk\app\src\main\AndroidManifest.xml:83:9-52
87        android:required="false" />
87-->D:\cs\tlk\app\src\main\AndroidManifest.xml:84:9-33
88    <uses-permission
88-->D:\cs\tlk\app\src\main\AndroidManifest.xml:86:5-88:36
89        android:name="android.permission.VIBRATE"
89-->D:\cs\tlk\app\src\main\AndroidManifest.xml:87:9-50
90        android:required="false" />
90-->D:\cs\tlk\app\src\main\AndroidManifest.xml:88:9-33
91    <uses-permission
91-->D:\cs\tlk\app\src\main\AndroidManifest.xml:89:5-91:36
92        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
92-->D:\cs\tlk\app\src\main\AndroidManifest.xml:90:9-67
93        android:required="false" />
93-->D:\cs\tlk\app\src\main\AndroidManifest.xml:91:9-33
94    <uses-permission
94-->D:\cs\tlk\app\src\main\AndroidManifest.xml:92:5-94:36
95        android:name="android.permission.BLUETOOTH"
95-->D:\cs\tlk\app\src\main\AndroidManifest.xml:93:9-52
96        android:required="false" />
96-->D:\cs\tlk\app\src\main\AndroidManifest.xml:94:9-33
97
98    <uses-feature android:name="android.hardware.Camera" />
98-->D:\cs\tlk\app\src\main\AndroidManifest.xml:96:5-60
98-->D:\cs\tlk\app\src\main\AndroidManifest.xml:96:19-57
99    <uses-feature
99-->D:\cs\tlk\app\src\main\AndroidManifest.xml:97:5-99:36
100        android:name="android.hardware.camera.autofocus"
100-->D:\cs\tlk\app\src\main\AndroidManifest.xml:98:9-57
101        android:required="true" />
101-->D:\cs\tlk\app\src\main\AndroidManifest.xml:99:9-33
102
103    <uses-permission
103-->D:\cs\tlk\app\src\main\AndroidManifest.xml:101:5-103:36
104        android:name="android.permission.CHANGE_NETWORK_STATE"
104-->D:\cs\tlk\app\src\main\AndroidManifest.xml:102:9-63
105        android:required="false" />
105-->D:\cs\tlk\app\src\main\AndroidManifest.xml:103:9-33
106    <uses-permission
106-->D:\cs\tlk\app\src\main\AndroidManifest.xml:104:5-106:36
107        android:name="android.permission.ACCESS_FINE_LOCATION"
107-->D:\cs\tlk\app\src\main\AndroidManifest.xml:105:9-63
108        android:required="false" />
108-->D:\cs\tlk\app\src\main\AndroidManifest.xml:106:9-33
109    <uses-permission
109-->D:\cs\tlk\app\src\main\AndroidManifest.xml:107:5-109:36
110        android:name="android.permission.CHANGE_WIFI_STATE"
110-->D:\cs\tlk\app\src\main\AndroidManifest.xml:108:9-60
111        android:required="false" />
111-->D:\cs\tlk\app\src\main\AndroidManifest.xml:109:9-33
112    <uses-permission
112-->D:\cs\tlk\app\src\main\AndroidManifest.xml:110:5-112:36
113        android:name="android.permission.RECEIVE_BOOT_COMPLETED"
113-->D:\cs\tlk\app\src\main\AndroidManifest.xml:111:9-65
114        android:required="false" />
114-->D:\cs\tlk\app\src\main\AndroidManifest.xml:112:9-33
115    <uses-permission
115-->D:\cs\tlk\app\src\main\AndroidManifest.xml:113:5-115:36
116        android:name="com.android.launcher.permission.READ_SETTINGS"
116-->D:\cs\tlk\app\src\main\AndroidManifest.xml:114:9-69
117        android:required="false" />
117-->D:\cs\tlk\app\src\main\AndroidManifest.xml:115:9-33
118    <uses-permission
118-->D:\cs\tlk\app\src\main\AndroidManifest.xml:116:5-118:36
119        android:name="android.permission.BROADCAST_STICKY"
119-->D:\cs\tlk\app\src\main\AndroidManifest.xml:117:9-59
120        android:required="false" />
120-->D:\cs\tlk\app\src\main\AndroidManifest.xml:118:9-33
121    <uses-permission
121-->D:\cs\tlk\app\src\main\AndroidManifest.xml:119:5-121:36
122        android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"
122-->D:\cs\tlk\app\src\main\AndroidManifest.xml:120:9-79
123        android:required="false" /> <!-- 悬浮窗权限 -->
123-->D:\cs\tlk\app\src\main\AndroidManifest.xml:121:9-33
124    <uses-permission
124-->D:\cs\tlk\app\src\main\AndroidManifest.xml:28:5-30:36
125        android:name="android.permission.SYSTEM_ALERT_WINDOW"
125-->D:\cs\tlk\app\src\main\AndroidManifest.xml:29:9-62
126        android:required="false" /> <!-- 魅族推送配置 start -->
126-->D:\cs\tlk\app\src\main\AndroidManifest.xml:30:9-33
127    <!-- 兼容 flyme5.0 以下版本，魅族内部集成 pushSDK 必填，不然无法收到消息 -->
128    <uses-permission
128-->D:\cs\tlk\app\src\main\AndroidManifest.xml:126:5-128:36
129        android:name="com.meizu.flyme.push.permission.RECEIVE"
129-->D:\cs\tlk\app\src\main\AndroidManifest.xml:127:9-63
130        android:required="false" />
130-->D:\cs\tlk\app\src\main\AndroidManifest.xml:128:9-33
131    <uses-permission
131-->D:\cs\tlk\app\src\main\AndroidManifest.xml:86:5-88:36
132        android:name="android.permission.VIBRATE"
132-->D:\cs\tlk\app\src\main\AndroidManifest.xml:87:9-50
133        android:required="false" />
133-->D:\cs\tlk\app\src\main\AndroidManifest.xml:88:9-33
134    <uses-permission android:name="android.permission.CAMERA" />
134-->D:\cs\tlk\app\src\main\AndroidManifest.xml:132:5-65
134-->D:\cs\tlk\app\src\main\AndroidManifest.xml:132:22-62
135    <uses-permission android:name="android.permission.FLASHLIGHT" />
135-->D:\cs\tlk\app\src\main\AndroidManifest.xml:133:5-69
135-->D:\cs\tlk\app\src\main\AndroidManifest.xml:133:22-66
136
137    <permission
138        android:name="com.Tlock.io.push.permission.MESSAGE"
138-->D:\cs\tlk\app\src\main\AndroidManifest.xml:136:9-64
139        android:protectionLevel="signature"
139-->D:\cs\tlk\app\src\main\AndroidManifest.xml:137:9-44
140        android:required="false" />
140-->D:\cs\tlk\app\src\main\AndroidManifest.xml:138:9-33
141
142    <uses-permission
143        android:name="com.Tlock.io.push.permission.MESSAGE"
143-->D:\cs\tlk\app\src\main\AndroidManifest.xml:141:9-64
144        android:required="false" /> <!-- 兼容 flyme3.0 配置权限 -->
144-->D:\cs\tlk\app\src\main\AndroidManifest.xml:142:9-33
145    <uses-permission
145-->D:\cs\tlk\app\src\main\AndroidManifest.xml:143:5-145:36
146        android:name="com.meizu.c2dm.permission.RECEIVE"
146-->D:\cs\tlk\app\src\main\AndroidManifest.xml:144:9-57
147        android:required="false" />
147-->D:\cs\tlk\app\src\main\AndroidManifest.xml:145:9-33
148
149    <permission
150        android:name="com.Tlock.io.permission.C2D_MESSAGE"
150-->D:\cs\tlk\app\src\main\AndroidManifest.xml:148:9-63
151        android:protectionLevel="signature"
151-->D:\cs\tlk\app\src\main\AndroidManifest.xml:149:9-44
152        android:required="false" />
152-->D:\cs\tlk\app\src\main\AndroidManifest.xml:150:9-33
153
154    <uses-permission
155        android:name="com.Tlock.io.permission.C2D_MESSAGE"
155-->D:\cs\tlk\app\src\main\AndroidManifest.xml:153:9-63
156        android:required="false" /> <!-- 魅族推送配置 end -->
156-->D:\cs\tlk\app\src\main\AndroidManifest.xml:154:9-33
157    <!-- Oppo推送配置 start -->
158    <uses-permission
158-->D:\cs\tlk\app\src\main\AndroidManifest.xml:156:5-158:36
159        android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE"
159-->D:\cs\tlk\app\src\main\AndroidManifest.xml:157:9-70
160        android:required="false" /> <!-- Oppo推送配置 end -->
160-->D:\cs\tlk\app\src\main\AndroidManifest.xml:158:9-33
161    <!-- Mi推送配置 start -->
162    <permission
163        android:name="com.Tlock.io.permission.MIPUSH_RECEIVE"
163-->D:\cs\tlk\app\src\main\AndroidManifest.xml:161:9-66
164        android:protectionLevel="signature"
164-->D:\cs\tlk\app\src\main\AndroidManifest.xml:162:9-44
165        android:required="false" />
165-->D:\cs\tlk\app\src\main\AndroidManifest.xml:163:9-33
166
167    <uses-permission
168        android:name="com.Tlock.io.permission.MIPUSH_RECEIVE"
168-->D:\cs\tlk\app\src\main\AndroidManifest.xml:166:9-66
169        android:required="false" /> <!-- Mi推送配置 end -->
169-->D:\cs\tlk\app\src\main\AndroidManifest.xml:167:9-33
170    <!-- 桌面角标权限start -->
171    <!-- 华为 -->
172    <uses-permission
172-->D:\cs\tlk\app\src\main\AndroidManifest.xml:170:5-172:36
173        android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"
173-->D:\cs\tlk\app\src\main\AndroidManifest.xml:171:9-75
174        android:required="false" /> <!-- 三星 -->
174-->D:\cs\tlk\app\src\main\AndroidManifest.xml:172:9-33
175    <uses-permission
175-->D:\cs\tlk\app\src\main\AndroidManifest.xml:173:5-175:36
176        android:name="com.sec.android.provider.badge.permission.READ"
176-->D:\cs\tlk\app\src\main\AndroidManifest.xml:174:9-70
177        android:required="false" />
177-->D:\cs\tlk\app\src\main\AndroidManifest.xml:175:9-33
178    <uses-permission
178-->D:\cs\tlk\app\src\main\AndroidManifest.xml:176:5-178:36
179        android:name="com.sec.android.provider.badge.permission.WRITE"
179-->D:\cs\tlk\app\src\main\AndroidManifest.xml:177:9-71
180        android:required="false" /> <!-- OPPO -->
180-->D:\cs\tlk\app\src\main\AndroidManifest.xml:178:9-33
181    <uses-permission
181-->D:\cs\tlk\app\src\main\AndroidManifest.xml:179:5-181:36
182        android:name="com.oppo.launcher.permission.READ_SETTINGS"
182-->D:\cs\tlk\app\src\main\AndroidManifest.xml:180:9-66
183        android:required="false" />
183-->D:\cs\tlk\app\src\main\AndroidManifest.xml:181:9-33
184    <uses-permission
184-->D:\cs\tlk\app\src\main\AndroidManifest.xml:182:5-184:36
185        android:name="com.oppo.launcher.permission.WRITE_SETTINGS"
185-->D:\cs\tlk\app\src\main\AndroidManifest.xml:183:9-67
186        android:required="false" /> <!-- 联想ZUK -->
186-->D:\cs\tlk\app\src\main\AndroidManifest.xml:184:9-33
187    <uses-permission
187-->D:\cs\tlk\app\src\main\AndroidManifest.xml:185:5-187:36
188        android:name="android.permission.READ_APP_BADGE"
188-->D:\cs\tlk\app\src\main\AndroidManifest.xml:186:9-57
189        android:required="false" /> <!-- HTC -->
189-->D:\cs\tlk\app\src\main\AndroidManifest.xml:187:9-33
190    <uses-permission
190-->D:\cs\tlk\app\src\main\AndroidManifest.xml:188:5-190:36
191        android:name="com.htc.launcher.permission.READ_SETTINGS"
191-->D:\cs\tlk\app\src\main\AndroidManifest.xml:189:9-65
192        android:required="false" />
192-->D:\cs\tlk\app\src\main\AndroidManifest.xml:190:9-33
193    <uses-permission
193-->D:\cs\tlk\app\src\main\AndroidManifest.xml:191:5-193:36
194        android:name="com.htc.launcher.permission.UPDATE_SHORTCUT"
194-->D:\cs\tlk\app\src\main\AndroidManifest.xml:192:9-67
195        android:required="false" /> <!-- 用于申请调用A-GPS模块 -->
195-->D:\cs\tlk\app\src\main\AndroidManifest.xml:193:9-33
196    <uses-permission
196-->D:\cs\tlk\app\src\main\AndroidManifest.xml:194:5-196:36
197        android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"
197-->D:\cs\tlk\app\src\main\AndroidManifest.xml:195:9-73
198        android:required="false" />
198-->D:\cs\tlk\app\src\main\AndroidManifest.xml:196:9-33
199    <uses-permission
199-->D:\cs\tlk\app\src\main\AndroidManifest.xml:197:5-199:36
200        android:name="android.permission.USE_FINGERPRINT"
200-->D:\cs\tlk\app\src\main\AndroidManifest.xml:198:9-58
201        android:required="false" />
201-->D:\cs\tlk\app\src\main\AndroidManifest.xml:199:9-33
202    <uses-permission
202-->D:\cs\tlk\app\src\main\AndroidManifest.xml:200:5-202:36
203        android:name="android.permission.FOREGROUND_SERVICE"
203-->D:\cs\tlk\app\src\main\AndroidManifest.xml:201:9-61
204        android:required="false" />
204-->D:\cs\tlk\app\src\main\AndroidManifest.xml:202:9-33
205    <uses-permission
205-->D:\cs\tlk\app\src\main\AndroidManifest.xml:203:5-205:36
206        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
206-->D:\cs\tlk\app\src\main\AndroidManifest.xml:204:9-66
207        android:required="false" />
207-->D:\cs\tlk\app\src\main\AndroidManifest.xml:205:9-33
208    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
208-->D:\cs\tlk\app\src\main\AndroidManifest.xml:206:5-208:53
208-->D:\cs\tlk\app\src\main\AndroidManifest.xml:207:9-61
209    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
209-->[com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:15:5-17:47
209-->[com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:16:9-70
210
211    <queries>
211-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:14:5-27:15
212        <intent>
212-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:15:9-17:18
213            <action android:name="android.intent.action.PICK" />
213-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:16:13-65
213-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:16:21-62
214        </intent>
215        <intent>
215-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:18:9-20:18
216            <action android:name="android.intent.action.GET_CONTENT" />
216-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:19:13-72
216-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:19:21-69
217        </intent>
218        <intent>
218-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:21:9-23:18
219            <action android:name="android.media.action.IMAGE_CAPTURE" />
219-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:22:13-73
219-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:22:21-70
220        </intent>
221        <intent>
221-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:24:9-26:18
222            <action android:name="com.android.camera.action.CROP" />
222-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:25:13-69
222-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:25:21-66
223        </intent>
224    </queries>
225
226    <permission
226-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
227        android:name="com.Tlock.io.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
227-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
228        android:protectionLevel="signature" />
228-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
229
230    <uses-permission android:name="com.Tlock.io.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
230-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
230-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
231
232    <uses-feature android:name="android.hardware.camera" />
232-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:14:5-60
232-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:14:19-57
233
234    <application
234-->D:\cs\tlk\app\src\main\AndroidManifest.xml:210:5-405:19
235        android:name="com.Tlock.io.app.AppApplication"
235-->D:\cs\tlk\app\src\main\AndroidManifest.xml:211:9-43
236        android:allowBackup="true"
236-->D:\cs\tlk\app\src\main\AndroidManifest.xml:212:9-35
237        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
237-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
238        android:debuggable="true"
239        android:extractNativeLibs="false"
240        android:hardwareAccelerated="true"
240-->D:\cs\tlk\app\src\main\AndroidManifest.xml:213:9-43
241        android:icon="@mipmap/app_icon"
241-->D:\cs\tlk\app\src\main\AndroidManifest.xml:214:9-40
242        android:label="@string/app_name"
242-->D:\cs\tlk\app\src\main\AndroidManifest.xml:215:9-41
243        android:largeHeap="true"
243-->D:\cs\tlk\app\src\main\AndroidManifest.xml:216:9-33
244        android:requestLegacyExternalStorage="true"
244-->D:\cs\tlk\app\src\main\AndroidManifest.xml:217:9-52
245        android:roundIcon="@mipmap/app_icon"
245-->D:\cs\tlk\app\src\main\AndroidManifest.xml:218:9-45
246        android:supportsRtl="true"
246-->D:\cs\tlk\app\src\main\AndroidManifest.xml:219:9-35
247        android:theme="@style/AppTheme"
247-->D:\cs\tlk\app\src\main\AndroidManifest.xml:220:9-40
248        android:usesCleartextTraffic="true" >
248-->D:\cs\tlk\app\src\main\AndroidManifest.xml:221:9-44
249        <activity
249-->D:\cs\tlk\app\src\main\AndroidManifest.xml:223:9-225:40
250            android:name="com.Tlock.io.activity.cosmos.Test1Activity"
250-->D:\cs\tlk\app\src\main\AndroidManifest.xml:224:13-58
251            android:exported="false" />
251-->D:\cs\tlk\app\src\main\AndroidManifest.xml:225:13-37
252        <activity
252-->D:\cs\tlk\app\src\main\AndroidManifest.xml:226:9-228:40
253            android:name="com.Tlock.io.activity.cosmos.AllowanceActivity"
253-->D:\cs\tlk\app\src\main\AndroidManifest.xml:227:13-62
254            android:exported="false" />
254-->D:\cs\tlk\app\src\main\AndroidManifest.xml:228:13-37
255        <activity
255-->D:\cs\tlk\app\src\main\AndroidManifest.xml:229:9-231:40
256            android:name="com.Tlock.io.activity.cosmos.EditTopicActivity"
256-->D:\cs\tlk\app\src\main\AndroidManifest.xml:230:13-62
257            android:exported="false" />
257-->D:\cs\tlk\app\src\main\AndroidManifest.xml:231:13-37
258        <activity
258-->D:\cs\tlk\app\src\main\AndroidManifest.xml:232:9-234:40
259            android:name="com.Tlock.io.activity.cosmos.MangerActivity"
259-->D:\cs\tlk\app\src\main\AndroidManifest.xml:233:13-59
260            android:exported="false" />
260-->D:\cs\tlk\app\src\main\AndroidManifest.xml:234:13-37
261        <activity
261-->D:\cs\tlk\app\src\main\AndroidManifest.xml:235:9-237:40
262            android:name="com.Tlock.io.activity.cosmos.TransactionListActivity"
262-->D:\cs\tlk\app\src\main\AndroidManifest.xml:236:13-68
263            android:exported="false" />
263-->D:\cs\tlk\app\src\main\AndroidManifest.xml:237:13-37
264        <activity
264-->D:\cs\tlk\app\src\main\AndroidManifest.xml:238:9-240:40
265            android:name="com.Tlock.io.activity.cosmos.TransactionInfoActivity"
265-->D:\cs\tlk\app\src\main\AndroidManifest.xml:239:13-68
266            android:exported="false" />
266-->D:\cs\tlk\app\src\main\AndroidManifest.xml:240:13-37
267        <activity
267-->D:\cs\tlk\app\src\main\AndroidManifest.xml:241:9-243:40
268            android:name="com.Tlock.io.activity.cosmos.TransferInfoActivity"
268-->D:\cs\tlk\app\src\main\AndroidManifest.xml:242:13-65
269            android:exported="false" />
269-->D:\cs\tlk\app\src\main\AndroidManifest.xml:243:13-37
270        <activity
270-->D:\cs\tlk\app\src\main\AndroidManifest.xml:244:9-246:40
271            android:name="com.Tlock.io.activity.cosmos.ReceiveActivity"
271-->D:\cs\tlk\app\src\main\AndroidManifest.xml:245:13-60
272            android:exported="false" />
272-->D:\cs\tlk\app\src\main\AndroidManifest.xml:246:13-37
273        <activity
273-->D:\cs\tlk\app\src\main\AndroidManifest.xml:247:9-249:40
274            android:name="com.Tlock.io.activity.cosmos.TransferActivity"
274-->D:\cs\tlk\app\src\main\AndroidManifest.xml:248:13-61
275            android:exported="false" />
275-->D:\cs\tlk\app\src\main\AndroidManifest.xml:249:13-37
276        <activity
276-->D:\cs\tlk\app\src\main\AndroidManifest.xml:250:9-252:40
277            android:name="com.Tlock.io.activity.cosmos.TransferHistoryActivity"
277-->D:\cs\tlk\app\src\main\AndroidManifest.xml:251:13-68
278            android:exported="false" />
278-->D:\cs\tlk\app\src\main\AndroidManifest.xml:252:13-37
279        <activity
279-->D:\cs\tlk\app\src\main\AndroidManifest.xml:253:9-255:40
280            android:name="com.Tlock.io.activity.cosmos.WalletInfoActivity"
280-->D:\cs\tlk\app\src\main\AndroidManifest.xml:254:13-63
281            android:exported="false" />
281-->D:\cs\tlk\app\src\main\AndroidManifest.xml:255:13-37
282        <activity
282-->D:\cs\tlk\app\src\main\AndroidManifest.xml:256:9-258:40
283            android:name="com.Tlock.io.activity.cosmos.SearchActivity"
283-->D:\cs\tlk\app\src\main\AndroidManifest.xml:257:13-59
284            android:exported="false" />
284-->D:\cs\tlk\app\src\main\AndroidManifest.xml:258:13-37
285        <activity
285-->D:\cs\tlk\app\src\main\AndroidManifest.xml:259:9-261:40
286            android:name="com.Tlock.io.activity.cosmos.EditProfileActivity"
286-->D:\cs\tlk\app\src\main\AndroidManifest.xml:260:13-64
287            android:exported="false" />
287-->D:\cs\tlk\app\src\main\AndroidManifest.xml:261:13-37
288        <activity
288-->D:\cs\tlk\app\src\main\AndroidManifest.xml:262:9-265:46
289            android:name="com.Tlock.io.activity.cosmos.UserInfoActivity"
289-->D:\cs\tlk\app\src\main\AndroidManifest.xml:263:13-61
290            android:exported="false"
290-->D:\cs\tlk\app\src\main\AndroidManifest.xml:264:13-37
291            android:launchMode="singleTop" />
291-->D:\cs\tlk\app\src\main\AndroidManifest.xml:265:13-43
292        <activity
292-->D:\cs\tlk\app\src\main\AndroidManifest.xml:266:9-268:40
293            android:name="com.Tlock.io.activity.cosmos.MTaskDetailActivity"
293-->D:\cs\tlk\app\src\main\AndroidManifest.xml:267:13-64
294            android:exported="false" />
294-->D:\cs\tlk\app\src\main\AndroidManifest.xml:268:13-37
295        <activity
295-->D:\cs\tlk\app\src\main\AndroidManifest.xml:269:9-271:40
296            android:name="com.Tlock.io.activity.cosmos.MTaskListActivity"
296-->D:\cs\tlk\app\src\main\AndroidManifest.xml:270:13-62
297            android:exported="false" />
297-->D:\cs\tlk\app\src\main\AndroidManifest.xml:271:13-37
298        <activity
298-->D:\cs\tlk\app\src\main\AndroidManifest.xml:272:9-274:40
299            android:name="com.Tlock.io.activity.cosmos.MTopicDetailActivity"
299-->D:\cs\tlk\app\src\main\AndroidManifest.xml:273:13-65
300            android:exported="false" />
300-->D:\cs\tlk\app\src\main\AndroidManifest.xml:274:13-37
301        <activity
301-->D:\cs\tlk\app\src\main\AndroidManifest.xml:275:9-277:40
302            android:name="com.Tlock.io.activity.cosmos.MTopicSearchActivity"
302-->D:\cs\tlk\app\src\main\AndroidManifest.xml:276:13-65
303            android:exported="false" />
303-->D:\cs\tlk\app\src\main\AndroidManifest.xml:277:13-37
304        <activity
304-->D:\cs\tlk\app\src\main\AndroidManifest.xml:278:9-280:40
305            android:name="com.Tlock.io.activity.cosmos.FollowingListActivity"
305-->D:\cs\tlk\app\src\main\AndroidManifest.xml:279:13-66
306            android:exported="false" />
306-->D:\cs\tlk\app\src\main\AndroidManifest.xml:280:13-37
307        <activity
307-->D:\cs\tlk\app\src\main\AndroidManifest.xml:281:9-283:40
308            android:name="com.Tlock.io.activity.cosmos.CollectListActivity"
308-->D:\cs\tlk\app\src\main\AndroidManifest.xml:282:13-64
309            android:exported="false" />
309-->D:\cs\tlk\app\src\main\AndroidManifest.xml:283:13-37
310        <activity
310-->D:\cs\tlk\app\src\main\AndroidManifest.xml:284:9-286:40
311            android:name="com.Tlock.io.activity.wallet.WalletOrderActivity"
311-->D:\cs\tlk\app\src\main\AndroidManifest.xml:285:13-64
312            android:exported="false" />
312-->D:\cs\tlk\app\src\main\AndroidManifest.xml:286:13-37
313        <activity
313-->D:\cs\tlk\app\src\main\AndroidManifest.xml:287:9-290:46
314            android:name="com.Tlock.io.activity.cosmos.MMemberListActivity"
314-->D:\cs\tlk\app\src\main\AndroidManifest.xml:288:13-64
315            android:exported="false"
315-->D:\cs\tlk\app\src\main\AndroidManifest.xml:289:13-37
316            android:launchMode="singleTop" />
316-->D:\cs\tlk\app\src\main\AndroidManifest.xml:290:13-43
317        <activity
317-->D:\cs\tlk\app\src\main\AndroidManifest.xml:291:9-293:40
318            android:name="com.Tlock.io.activity.cosmos.TopicActivity"
318-->D:\cs\tlk\app\src\main\AndroidManifest.xml:292:13-58
319            android:exported="false" />
319-->D:\cs\tlk\app\src\main\AndroidManifest.xml:293:13-37
320        <activity
320-->D:\cs\tlk\app\src\main\AndroidManifest.xml:294:9-296:40
321            android:name="com.Tlock.io.activity.cosmos.PostQuoteActivity"
321-->D:\cs\tlk\app\src\main\AndroidManifest.xml:295:13-62
322            android:exported="false" />
322-->D:\cs\tlk\app\src\main\AndroidManifest.xml:296:13-37
323        <activity
323-->D:\cs\tlk\app\src\main\AndroidManifest.xml:297:9-300:46
324            android:name="com.Tlock.io.activity.cosmos.ContentInfoActivity"
324-->D:\cs\tlk\app\src\main\AndroidManifest.xml:298:13-64
325            android:exported="false"
325-->D:\cs\tlk\app\src\main\AndroidManifest.xml:299:13-37
326            android:launchMode="singleTop" />
326-->D:\cs\tlk\app\src\main\AndroidManifest.xml:300:13-43
327        <activity
327-->D:\cs\tlk\app\src\main\AndroidManifest.xml:301:9-303:40
328            android:name="com.Tlock.io.activity.cosmos.TopicDetailActivity"
328-->D:\cs\tlk\app\src\main\AndroidManifest.xml:302:13-64
329            android:exported="false" />
329-->D:\cs\tlk\app\src\main\AndroidManifest.xml:303:13-37
330        <activity
330-->D:\cs\tlk\app\src\main\AndroidManifest.xml:305:9-308:52
331            android:name="com.Tlock.io.activity.wallet.WalletSettingActivity"
331-->D:\cs\tlk\app\src\main\AndroidManifest.xml:306:13-66
332            android:exported="false"
332-->D:\cs\tlk\app\src\main\AndroidManifest.xml:307:13-37
333            android:screenOrientation="portrait" />
333-->D:\cs\tlk\app\src\main\AndroidManifest.xml:308:13-49
334        <activity
334-->D:\cs\tlk\app\src\main\AndroidManifest.xml:309:9-317:20
335            android:name="com.Tlock.io.activity.wallet.NewWalletActivity"
335-->D:\cs\tlk\app\src\main\AndroidManifest.xml:310:13-62
336            android:exported="true"
336-->D:\cs\tlk\app\src\main\AndroidManifest.xml:311:13-36
337            android:launchMode="singleTask"
337-->D:\cs\tlk\app\src\main\AndroidManifest.xml:312:13-44
338            android:screenOrientation="portrait"
338-->D:\cs\tlk\app\src\main\AndroidManifest.xml:313:13-49
339            android:theme="@style/SplashTheme"
339-->D:\cs\tlk\app\src\main\AndroidManifest.xml:314:13-47
340            android:windowSoftInputMode="adjustPan" >
340-->D:\cs\tlk\app\src\main\AndroidManifest.xml:315:13-52
341        </activity>
342        <activity
342-->D:\cs\tlk\app\src\main\AndroidManifest.xml:319:9-330:20
343            android:name="com.Tlock.io.MainActivity"
343-->D:\cs\tlk\app\src\main\AndroidManifest.xml:320:13-41
344            android:exported="true"
344-->D:\cs\tlk\app\src\main\AndroidManifest.xml:321:13-36
345            android:launchMode="singleTask"
345-->D:\cs\tlk\app\src\main\AndroidManifest.xml:322:13-44
346            android:screenOrientation="portrait"
346-->D:\cs\tlk\app\src\main\AndroidManifest.xml:323:13-49
347            android:theme="@style/SplashTheme"
347-->D:\cs\tlk\app\src\main\AndroidManifest.xml:324:13-47
348            android:windowSoftInputMode="adjustPan" >
348-->D:\cs\tlk\app\src\main\AndroidManifest.xml:325:13-52
349            <intent-filter>
349-->D:\cs\tlk\app\src\main\AndroidManifest.xml:326:13-329:29
350                <action android:name="android.intent.action.MAIN" />
350-->D:\cs\tlk\app\src\main\AndroidManifest.xml:327:17-69
350-->D:\cs\tlk\app\src\main\AndroidManifest.xml:327:25-66
351
352                <category android:name="android.intent.category.LAUNCHER" />
352-->D:\cs\tlk\app\src\main\AndroidManifest.xml:328:17-77
352-->D:\cs\tlk\app\src\main\AndroidManifest.xml:328:27-74
353            </intent-filter>
354        </activity>
355        <activity
355-->D:\cs\tlk\app\src\main\AndroidManifest.xml:333:9-336:58
356            android:name="com.Tlock.io.activity.cosmos.PostActivity"
356-->D:\cs\tlk\app\src\main\AndroidManifest.xml:334:13-57
357            android:screenOrientation="portrait"
357-->D:\cs\tlk\app\src\main\AndroidManifest.xml:335:13-49
358            android:windowSoftInputMode="adjustResize" />
358-->D:\cs\tlk\app\src\main\AndroidManifest.xml:336:13-55
359
360        <!-- <activity -->
361        <!-- android:name=".widget.web.DappBrowerActivity" -->
362        <!-- android:screenOrientation="portrait" -->
363        <!-- android:theme="@style/AppTheme" -->
364        <!-- android:usesCleartextTraffic="true" /> -->
365
366        <activity
366-->D:\cs\tlk\app\src\main\AndroidManifest.xml:345:9-347:52
367            android:name="com.Tlock.io.activity.wallet.AssetDetailsActivity"
367-->D:\cs\tlk\app\src\main\AndroidManifest.xml:346:13-65
368            android:screenOrientation="portrait" />
368-->D:\cs\tlk\app\src\main\AndroidManifest.xml:347:13-49
369        <activity
369-->D:\cs\tlk\app\src\main\AndroidManifest.xml:350:9-352:52
370            android:name="com.Tlock.io.activity.wallet.InputWalletActivity"
370-->D:\cs\tlk\app\src\main\AndroidManifest.xml:351:13-64
371            android:screenOrientation="portrait" />
371-->D:\cs\tlk\app\src\main\AndroidManifest.xml:352:13-49
372        <activity
372-->D:\cs\tlk\app\src\main\AndroidManifest.xml:353:9-355:52
373            android:name="com.Tlock.io.activity.wallet.BackupActivity"
373-->D:\cs\tlk\app\src\main\AndroidManifest.xml:354:13-59
374            android:screenOrientation="portrait" />
374-->D:\cs\tlk\app\src\main\AndroidManifest.xml:355:13-49
375        <activity
375-->D:\cs\tlk\app\src\main\AndroidManifest.xml:357:9-359:52
376            android:name="com.Tlock.io.activity.wallet.ToBackupMnemonicsActivity"
376-->D:\cs\tlk\app\src\main\AndroidManifest.xml:358:13-70
377            android:screenOrientation="portrait" />
377-->D:\cs\tlk\app\src\main\AndroidManifest.xml:359:13-49
378        <activity
378-->D:\cs\tlk\app\src\main\AndroidManifest.xml:360:9-362:52
379            android:name="com.Tlock.io.activity.wallet.BackupPrivateKeyActivity"
379-->D:\cs\tlk\app\src\main\AndroidManifest.xml:361:13-69
380            android:screenOrientation="portrait" />
380-->D:\cs\tlk\app\src\main\AndroidManifest.xml:362:13-49
381        <activity
381-->D:\cs\tlk\app\src\main\AndroidManifest.xml:363:9-365:52
382            android:name="com.Tlock.io.activity.wallet.CheckMnemonicsActivity"
382-->D:\cs\tlk\app\src\main\AndroidManifest.xml:364:13-67
383            android:screenOrientation="portrait" />
383-->D:\cs\tlk\app\src\main\AndroidManifest.xml:365:13-49
384        <activity
384-->D:\cs\tlk\app\src\main\AndroidManifest.xml:366:9-368:52
385            android:name="com.Tlock.io.activity.wallet.BackupMnemonicsActivity"
385-->D:\cs\tlk\app\src\main\AndroidManifest.xml:367:13-68
386            android:screenOrientation="portrait" />
386-->D:\cs\tlk\app\src\main\AndroidManifest.xml:368:13-49
387        <activity
387-->D:\cs\tlk\app\src\main\AndroidManifest.xml:369:9-371:52
388            android:name="com.Tlock.io.activity.wallet.CreateWalletActivity"
388-->D:\cs\tlk\app\src\main\AndroidManifest.xml:370:13-65
389            android:screenOrientation="portrait" />
389-->D:\cs\tlk\app\src\main\AndroidManifest.xml:371:13-49
390        <activity
390-->D:\cs\tlk\app\src\main\AndroidManifest.xml:373:9-375:52
391            android:name="com.Tlock.io.activity.wallet.ShareAddressActivity"
391-->D:\cs\tlk\app\src\main\AndroidManifest.xml:374:13-65
392            android:screenOrientation="portrait" />
392-->D:\cs\tlk\app\src\main\AndroidManifest.xml:375:13-49
393
394        <!-- <activity -->
395        <!-- android:name=".activity.H5Activity" -->
396        <!-- android:screenOrientation="portrait" /> -->
397
398
399        <!-- <service -->
400        <!-- android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService" -->
401        <!-- android:label="dexopt" /> -->
402
403        <provider
404            android:name="androidx.core.content.FileProvider"
404-->D:\cs\tlk\app\src\main\AndroidManifest.xml:386:13-62
405            android:authorities="com.Tlock.io.fileProvider"
405-->D:\cs\tlk\app\src\main\AndroidManifest.xml:387:13-64
406            android:exported="false"
406-->D:\cs\tlk\app\src\main\AndroidManifest.xml:388:13-37
407            android:grantUriPermissions="true" >
407-->D:\cs\tlk\app\src\main\AndroidManifest.xml:389:13-47
408            <meta-data
408-->D:\cs\tlk\app\src\main\AndroidManifest.xml:390:13-392:58
409                android:name="android.support.FILE_PROVIDER_PATHS"
409-->D:\cs\tlk\app\src\main\AndroidManifest.xml:391:17-67
410                android:resource="@xml/provider_paths" />
410-->D:\cs\tlk\app\src\main\AndroidManifest.xml:392:17-55
411        </provider>
412
413        <meta-data
413-->D:\cs\tlk\app\src\main\AndroidManifest.xml:395:9-397:35
414            android:name="design_width_in_dp"
414-->D:\cs\tlk\app\src\main\AndroidManifest.xml:396:13-46
415            android:value="375" />
415-->D:\cs\tlk\app\src\main\AndroidManifest.xml:397:13-32
416        <meta-data
416-->D:\cs\tlk\app\src\main\AndroidManifest.xml:398:9-400:35
417            android:name="design_height_in_dp"
417-->D:\cs\tlk\app\src\main\AndroidManifest.xml:399:13-47
418            android:value="812" />
418-->D:\cs\tlk\app\src\main\AndroidManifest.xml:400:13-32
419        <meta-data
419-->D:\cs\tlk\app\src\main\AndroidManifest.xml:401:9-403:43
420            android:name="com.Tlock.io.widget.QiNiuModule"
420-->D:\cs\tlk\app\src\main\AndroidManifest.xml:402:13-59
421            android:value="GlideModule" />
421-->D:\cs\tlk\app\src\main\AndroidManifest.xml:403:13-40
422        <meta-data
422-->[com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:11:9-13:36
423            android:name="android.notch_support"
423-->[com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:12:13-49
424            android:value="true" /> <!-- PermissonActivity -->
424-->[com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:13:13-33
425        <activity
425-->[com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:15:9-17:75
426            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
426-->[com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:16:13-78
427            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
427-->[com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:17:13-72
428
429        <provider
429-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:30:9-38:20
430            android:name="com.sl.utakephoto.TakePhotoProvider"
430-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:31:13-63
431            android:authorities="com.Tlock.io.fileProvider"
431-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:32:13-64
432            android:exported="false"
432-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:33:13-37
433            android:grantUriPermissions="true" >
433-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:34:13-47
434            <meta-data
434-->D:\cs\tlk\app\src\main\AndroidManifest.xml:390:13-392:58
435                android:name="android.support.FILE_PROVIDER_PATHS"
435-->D:\cs\tlk\app\src\main\AndroidManifest.xml:391:17-67
436                android:resource="@xml/take_file_path" />
436-->D:\cs\tlk\app\src\main\AndroidManifest.xml:392:17-55
437        </provider>
438
439        <activity
439-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:40:9-43:49
440            android:name="com.sl.utakephoto.crop.CropActivity"
440-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:41:13-63
441            android:configChanges="keyboardHidden|orientation|screenSize"
441-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:42:13-74
442            android:theme="@style/Theme.Crop" />
442-->[io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:43:13-46
443
444        <meta-data
444-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
445            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
445-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
446            android:value="GlideModule" />
446-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
447
448        <provider
448-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
449            android:name="com.squareup.picasso.PicassoProvider"
449-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
450            android:authorities="com.Tlock.io.com.squareup.picasso"
450-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
451            android:exported="false" />
451-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
452        <provider
452-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
453            android:name="androidx.startup.InitializationProvider"
453-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:25:13-67
454            android:authorities="com.Tlock.io.androidx-startup"
454-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:26:13-68
455            android:exported="false" >
455-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:27:13-37
456            <meta-data
456-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
457                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
457-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
458                android:value="androidx.startup" />
458-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
459        </provider>
460
461        <receiver
461-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
462            android:name="androidx.profileinstaller.ProfileInstallReceiver"
462-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
463            android:directBootAware="false"
463-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
464            android:enabled="true"
464-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
465            android:exported="true"
465-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
466            android:permission="android.permission.DUMP" >
466-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
467            <intent-filter>
467-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
468                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
468-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
468-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
469            </intent-filter>
470            <intent-filter>
470-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
471                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
471-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
471-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
472            </intent-filter>
473            <intent-filter>
473-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
474                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
474-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
474-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
475            </intent-filter>
476            <intent-filter>
476-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
477                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
477-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
477-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
478            </intent-filter>
479        </receiver>
480
481        <activity
481-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:23:9-28:67
482            android:name="com.yzq.zxinglibrary.android.CaptureActivity"
482-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:24:13-72
483            android:configChanges="orientation|screenSize"
483-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:25:13-59
484            android:screenOrientation="portrait"
484-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:26:13-49
485            android:theme="@style/Theme.AppCompat.NoActionBar"
485-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:27:13-63
486            android:windowSoftInputMode="adjustPan|stateHidden" />
486-->[com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:28:13-64
487
488        <provider
488-->[com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:12:9-16:43
489            android:name="me.jessyan.autosize.InitProvider"
489-->[com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:13:13-60
490            android:authorities="com.Tlock.io.autosize-init-provider"
490-->[com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:14:13-74
491            android:exported="false"
491-->[com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:15:13-37
492            android:multiprocess="true" />
492-->[com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:16:13-40
493
494        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
494-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
494-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
495        <service
495-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
496            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
496-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
497            android:process=":filedownloader" />
497-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
498    </application>
499
500</manifest>
