package com.Tlock.io.entity.wallet;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "OPERATION_BEAN".
*/
public class OperationBeanDao extends AbstractDao<OperationBean, Long> {

    public static final String TABLENAME = "OPERATION_BEAN";

    /**
     * Properties of entity OperationBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property PostId = new Property(1, String.class, "postId", false, "POST_ID");
        public final static Property Address = new Property(2, String.class, "address", false, "ADDRESS");
        public final static Property IsLike = new Property(3, boolean.class, "isLike", false, "IS_LIKE");
        public final static Property IsComment = new Property(4, boolean.class, "isComment", false, "IS_COMMENT");
        public final static Property IsShare = new Property(5, boolean.class, "isShare", false, "IS_SHARE");
        public final static Property IsCollect = new Property(6, boolean.class, "isCollect", false, "IS_COLLECT");
    }


    public OperationBeanDao(DaoConfig config) {
        super(config);
    }
    
    public OperationBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"OPERATION_BEAN\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"POST_ID\" TEXT," + // 1: postId
                "\"ADDRESS\" TEXT," + // 2: address
                "\"IS_LIKE\" INTEGER NOT NULL ," + // 3: isLike
                "\"IS_COMMENT\" INTEGER NOT NULL ," + // 4: isComment
                "\"IS_SHARE\" INTEGER NOT NULL ," + // 5: isShare
                "\"IS_COLLECT\" INTEGER NOT NULL );"); // 6: isCollect
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_OPERATION_BEAN_POST_ID ON \"OPERATION_BEAN\"" +
                " (\"POST_ID\" ASC);");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"OPERATION_BEAN\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, OperationBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String postId = entity.getPostId();
        if (postId != null) {
            stmt.bindString(2, postId);
        }
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(3, address);
        }
        stmt.bindLong(4, entity.getIsLike() ? 1L: 0L);
        stmt.bindLong(5, entity.getIsComment() ? 1L: 0L);
        stmt.bindLong(6, entity.getIsShare() ? 1L: 0L);
        stmt.bindLong(7, entity.getIsCollect() ? 1L: 0L);
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, OperationBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String postId = entity.getPostId();
        if (postId != null) {
            stmt.bindString(2, postId);
        }
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(3, address);
        }
        stmt.bindLong(4, entity.getIsLike() ? 1L: 0L);
        stmt.bindLong(5, entity.getIsComment() ? 1L: 0L);
        stmt.bindLong(6, entity.getIsShare() ? 1L: 0L);
        stmt.bindLong(7, entity.getIsCollect() ? 1L: 0L);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public OperationBean readEntity(Cursor cursor, int offset) {
        OperationBean entity = new OperationBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // postId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // address
            cursor.getShort(offset + 3) != 0, // isLike
            cursor.getShort(offset + 4) != 0, // isComment
            cursor.getShort(offset + 5) != 0, // isShare
            cursor.getShort(offset + 6) != 0 // isCollect
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, OperationBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setPostId(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAddress(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setIsLike(cursor.getShort(offset + 3) != 0);
        entity.setIsComment(cursor.getShort(offset + 4) != 0);
        entity.setIsShare(cursor.getShort(offset + 5) != 0);
        entity.setIsCollect(cursor.getShort(offset + 6) != 0);
     }
    
    @Override
    protected final Long updateKeyAfterInsert(OperationBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(OperationBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(OperationBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
