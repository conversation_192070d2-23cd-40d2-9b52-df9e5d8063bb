package com.Tlock.io.activity.cosmos;

import static android.Manifest.permission.READ_MEDIA_IMAGES;
import static android.Manifest.permission.READ_MEDIA_VIDEO;

import android.Manifest;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.profile.ProfileTXProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomEditBox;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.sl.utakephoto.compress.CompressConfig;
import com.sl.utakephoto.compress.CompressImage;
import com.sl.utakephoto.compress.CompressImageImpl;
import com.sl.utakephoto.crop.CropOptions;
import com.sl.utakephoto.utils.IntentUtils;
import com.sl.utakephoto.utils.TUriUtils;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Collections;

import butterknife.BindView;
import butterknife.OnClick;

public class EditProfileActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_avatar)
    ImageView mIvAvatar;
    @BindView(R.id.ed_nike_name)
    CustomEditBox mEdNikeName;
    @BindView(R.id.ed_user_name)
    CustomEditBox mEdUserName;
    @BindView(R.id.ed_bio)
    CustomEditBox mEdBio;
    @BindView(R.id.ed_Location)
    CustomEditBox mEdLocation;
    @BindView(R.id.ed_web_site)
    CustomEditBox mEdWebSite;
    @BindView(R.id.tv_save)
    TextView mTvSave;
    @BindView(R.id.iv_avatar1)
    ImageView mIvAvatar1;
    @BindView(R.id.tv_error)
    TextView mTvError;
    private String base64String;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context) {
        Intent intent = new Intent(context, EditProfileActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_edit_profile;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mTvError.setText("Names can't include @, #, $ or be only spaces.");
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {
        getAuthInfo();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            ETHWallet current = WalletDaoUtils.getCurrent();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                private ProfileProto.Profile authInfo;

                @Override
                public void run() {
                    authInfo = CosmosUtils.getAuthInfo(current.getAddress());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mEdUserName.setEditText(authInfo.getUserHandle());
                            mEdNikeName.setEditText(authInfo.getNickname().isEmpty() ? authInfo.getUserHandle() : authInfo.getNickname());
                            mEdBio.setEditText(authInfo.getBio());
                            mEdLocation.setEditText(authInfo.getLocation());
                            mEdWebSite.setEditText(authInfo.getWebsite());

                            if (TextUtils.isEmpty(authInfo.getAvatar())) {

                                TextAvatarDrawable a = new TextAvatarDrawable(authInfo.getUserHandle().substring(0, 1));
                                mIvAvatar.setImageDrawable(a);
                            } else {
                                if (authInfo.getAvatar().startsWith("http")) {
                                    Glide.with(getActivity()).load(authInfo.getAvatar()).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(authInfo.getAvatar()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvAvatar);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authInfo.getAvatar());
                                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvAvatar);
                                }

                            }


                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.tv_save, R.id.iv_avatar})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_save:
                if (mEdBio.getEditText().length() > 80) {
                    showToast("Bio length must be less than 80");
                    return;
                }
                if (mEdLocation.getEditText().length() > 20) {
                    showToast("Location length must be less than 20");
                    return;
                }
                if (mEdWebSite.getEditText().length() > 100) {
                    showToast("Website length must be less than 100");
                    return;
                }
                String editText = mEdNikeName.getEditText();
                if (editText.contains("#") || editText.contains("@") || editText.contains("$") || editText.isEmpty()) {
                    mTvError.setVisibility(View.VISIBLE);
                    return;
                }
                finish();
                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        ProfileTXProto.ProfileOptions.Builder pro = ProfileTXProto.ProfileOptions.newBuilder();
                        if (!TextUtils.isEmpty(base64String)) {
                            pro.setAvatar(base64String);
                        }
                        if (!TextUtils.isEmpty(mEdBio.getEditText())) {
                            pro.setBio(mEdBio.getEditText());
                        }
                        if (!TextUtils.isEmpty(mEdLocation.getEditText())) {
                            pro.setLocation(mEdLocation.getEditText());
                        }
                        if (!TextUtils.isEmpty(mEdWebSite.getEditText())) {
                            pro.setWebsite(mEdWebSite.getEditText());
                        }
                        if (!TextUtils.isEmpty(mEdNikeName.getEditText().trim())) {
                            pro.setNickname(mEdNikeName.getEditText());
                        }
                        if (!TextUtils.isEmpty(mEdUserName.getEditText())) {
                            pro.setUserHandle(mEdUserName.getEditText());
                        }
                        CosmosUtils.profileUpdata(pro.build(), WalletDaoUtils.getCurrent().getAddress());
                        mTvSave.post(new Runnable() {
                            @Override
                            public void run() {
                                showToast("Save success");
                            }
                        });
                    }
                });

                break;
            case R.id.iv_avatar:
                checkPermissions();
                break;
        }
    }

    private Bitmap getBitmapFromUri(Uri uri) {
        try {
            ContentResolver cr = getActivity().getContentResolver();
            InputStream is = cr.openInputStream(uri);
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inSampleSize = 2; // 压缩图片，避免内存溢出
            return BitmapFactory.decodeStream(is, null, options);
        } catch (FileNotFoundException e) {
        }
        return null;
    }


    private static final int REQUEST_CODE_PERMISSIONS = 100;
    private final String[] REQUIRED_PERMISSIONS = {
            READ_MEDIA_IMAGES,
            READ_MEDIA_VIDEO,
            Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
    };

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, REQUIRED_PERMISSIONS[0]) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS);
        } else {
            openGallery(); // 已有权限，直接打开相册
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openGallery();
                }
            } else {
                openGallery();
            }

        }
    }

    private ActivityResultLauncher<String> galleryLauncher = registerForActivityResult(
            new ActivityResultContracts.GetContent(),
            uri -> handleImageResult(uri)
    );

    private void openGallery() {
        galleryLauncher.launch("image/*");
    }

    private void handleImageResult(Uri uri) {
        if (uri != null) {
            crop(uri);
        }
    }

    private void setCompose(Uri uri) {
        CompressImageImpl.of(getActivity(), new CompressConfig.Builder()
                .setFocusAlpha(false)//是否支持透明度
                .setLeastCompressSize(200)//最小压缩尺寸
                .create(), Collections.singletonList(uri), new CompressImage.CompressListener() {
            @Override
            public void onStart() {

            }

            @Override
            public void onSuccess(Uri uri) {
                Bitmap bitmap = getBitmapFromUri(uri);
                base64String = BitmapUtils.bitmapToBase64(bitmap);
                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(base64String);
                Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                .centerCrop()
                                .dontTransform())
                        .apply(RequestOptions.circleCropTransform().circleCrop())
                        .into(mIvAvatar);
            }

            @Override
            public void onError(Throwable obj) {

            }
        }).compress();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 101 && resultCode == Activity.RESULT_OK) {
            setCompose(tempUri);
        }
    }

    private Uri tempUri;

    private void crop(Uri takePhotoUri) {
        CropOptions cropOptions = new CropOptions.Builder()
                .setAspectX(1)
                .setAspectY(1)
                .setOutputX(150)
                .setOutputY(150)
                .setWithOwnCrop(true)//使用系统裁剪还是自带裁剪
                .create();
        tempUri = TUriUtils.getTempSchemeUri(getActivity());
        Intent cropIntentWithOtherApp = IntentUtils.getCropIntent(takePhotoUri, tempUri, cropOptions);
        startActivityForResult(cropIntentWithOtherApp, 101);
    }
}