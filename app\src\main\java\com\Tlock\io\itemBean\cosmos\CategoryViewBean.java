package com.Tlock.io.itemBean.cosmos;

import static com.lxj.xpopup.util.XPopupUtils.dp2px;

import android.content.Context;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;

import butterknife.BindView;

/**
 * @ClassName MnemonicViewBean
 * <AUTHOR>
 * @Data 2021/11/10 17:21
 * @Desc
 */

public class CategoryViewBean extends BaseView {
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.rl_root)
    RelativeLayout mRlRoot;
    @BindView(R.id.tv_index)
    TextView mTvIndex;

    public CategoryViewBean(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_mnemonic;
    }

    public void setData(String world, boolean isSelected) {
        mTvTitle.setText(world);
        mTvIndex.setVisibility(GONE);
        mRlRoot.setPadding(20,10,20,10);
//        mRlRoot.setPadding(dp2px(getContext(),20),dp2px(getContext(),10),dp2px(getContext(),20),dp2px(getContext(),10));
        if (isSelected) {
            mRlRoot.setBackground(getResources().getDrawable(R.drawable.item_category_select));
            mTvTitle.setTextColor(getResources().getColor(R.color.blue_text));
        } else {
            mRlRoot.setBackground(getResources().getDrawable(R.drawable.item_category_default));
            mTvTitle.setTextColor(getResources().getColor(R.color.cosmos_black));


        }
    }

}
