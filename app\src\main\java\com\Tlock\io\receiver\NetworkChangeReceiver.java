package com.Tlock.io.receiver;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;

/**
 * Description:用于监听网络变化
 */
public class NetworkChangeReceiver extends BroadcastReceiver {


    private static NetworkChangeReceiver receiver = new NetworkChangeReceiver();

    public static void register(Application application) {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ANDROID_NET_CHANGE_ACTION);
        application.registerReceiver(receiver, intentFilter);
    }


    private static final String ANDROID_NET_CHANGE_ACTION = "android.net.conn.CONNECTIVITY_CHANGE";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent != null && TextUtils.equals(intent.getAction(), ANDROID_NET_CHANGE_ACTION)) {
//            MyLocationService.start(context);
        }
    }
}
