// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopEdit_ViewBinding implements Unbinder {
  private PopEdit target;

  @UiThread
  public PopEdit_ViewBinding(PopEdit target) {
    this(target, target);
  }

  @UiThread
  public PopEdit_ViewBinding(PopEdit target, View source) {
    this.target = target;

    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTvConfirm = Utils.findRequiredViewAsType(source, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    target.mEdCount = Utils.findRequiredViewAsType(source, R.id.ed_count, "field 'mEdCount'", EditText.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mTvFingererprint = Utils.findRequiredViewAsType(source, R.id.tv_fingererprint, "field 'mTvFingererprint'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopEdit target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mTvConfirm = null;
    target.mEdCount = null;
    target.mLine1 = null;
    target.mTvFingererprint = null;
  }
}
