// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopWhaleListAttch_ViewBinding implements Unbinder {
  private PopWhaleListAttch target;

  @UiThread
  public PopWhaleListAttch_ViewBinding(PopWhaleListAttch target) {
    this(target, target);
  }

  @UiThread
  public PopWhaleListAttch_ViewBinding(PopWhaleListAttch target, View source) {
    this.target = target;

    target.mRvWallet = Utils.findRequiredViewAsType(source, R.id.rv_wallet, "field 'mRvWallet'", RecyclerView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopWhaleListAttch target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRvWallet = null;
  }
}
