package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;


public class MTaskDetailActivity extends BaseActivity {

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context
    ) {
        Intent intent = new Intent(context, MTaskDetailActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_manger;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {




    }



    @Override
    protected void loadData() {

    }

}