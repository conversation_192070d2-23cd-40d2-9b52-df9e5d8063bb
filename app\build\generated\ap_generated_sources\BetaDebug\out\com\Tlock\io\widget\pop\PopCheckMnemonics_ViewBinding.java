// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopCheckMnemonics_ViewBinding implements Unbinder {
  private PopCheckMnemonics target;

  private View view7f090338;

  @UiThread
  public PopCheckMnemonics_ViewBinding(PopCheckMnemonics target) {
    this(target, target);
  }

  @UiThread
  public PopCheckMnemonics_ViewBinding(final PopCheckMnemonics target, View source) {
    this.target = target;

    View view;
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTvCaptureTip = Utils.findRequiredViewAsType(source, R.id.tv_capture_tip, "field 'mTvCaptureTip'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_know, "field 'mTvKnow' and method 'onBindClick'");
    target.mTvKnow = Utils.castView(view, R.id.tv_know, "field 'mTvKnow'", TextView.class);
    view7f090338 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopCheckMnemonics target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mTvCaptureTip = null;
    target.mTvKnow = null;

    view7f090338.setOnClickListener(null);
    view7f090338 = null;
  }
}
