// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.fragment;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MineFragment_ViewBinding implements Unbinder {
  private MineFragment target;

  private View view7f090072;

  private View view7f09014b;

  private View view7f09022a;

  private View view7f0902f1;

  private View view7f0900ea;

  private View view7f0902f2;

  private View view7f090241;

  private View view7f090233;

  private View view7f09023b;

  private View view7f090237;

  private View view7f09023e;

  private View view7f090232;

  private View view7f090380;

  private View view7f090177;

  private View view7f09023c;

  private View view7f090255;

  @UiThread
  public MineFragment_ViewBinding(final MineFragment target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.btn_updata, "field 'mBtnUpdata' and method 'onBindClick'");
    target.mBtnUpdata = Utils.castView(view, R.id.btn_updata, "field 'mBtnUpdata'", TextView.class);
    view7f090072 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_heard, "field 'mIvHeard' and method 'onBindClick'");
    target.mIvHeard = Utils.castView(view, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    view7f09014b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvManger = Utils.findRequiredViewAsType(source, R.id.iv_manger, "field 'mIvManger'", ImageView.class);
    target.mTvName = Utils.findRequiredViewAsType(source, R.id.tv_name, "field 'mTvName'", TextView.class);
    target.mTvHandle = Utils.findRequiredViewAsType(source, R.id.tv_handle, "field 'mTvHandle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.review_count, "field 'mReviewCount' and method 'onBindClick'");
    target.mReviewCount = Utils.castView(view, R.id.review_count, "field 'mReviewCount'", TextView.class);
    view7f09022a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_1, "field 'mTv1' and method 'onBindClick'");
    target.mTv1 = Utils.castView(view, R.id.tv_1, "field 'mTv1'", TextView.class);
    view7f0902f1 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.fans_count, "field 'mFansCount' and method 'onBindClick'");
    target.mFansCount = Utils.castView(view, R.id.fans_count, "field 'mFansCount'", TextView.class);
    view7f0900ea = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_2, "field 'mTv2' and method 'onBindClick'");
    target.mTv2 = Utils.castView(view, R.id.tv_2, "field 'mTv2'", TextView.class);
    view7f0902f2 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_info, "field 'mRlInfo' and method 'onBindClick'");
    target.mRlInfo = Utils.castView(view, R.id.rl_info, "field 'mRlInfo'", RelativeLayout.class);
    view7f090241 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mIvBalance = Utils.findRequiredViewAsType(source, R.id.iv_balance, "field 'mIvBalance'", ImageView.class);
    target.mTvBalance = Utils.findRequiredViewAsType(source, R.id.tv_balance, "field 'mTvBalance'", TextView.class);
    target.mTvData = Utils.findRequiredViewAsType(source, R.id.tv_data, "field 'mTvData'", TextView.class);
    target.mIvRight = Utils.findRequiredViewAsType(source, R.id.iv_right, "field 'mIvRight'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_balance, "field 'mRlBalance' and method 'onBindClick'");
    target.mRlBalance = Utils.castView(view, R.id.rl_balance, "field 'mRlBalance'", RelativeLayout.class);
    view7f090233 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLine2 = Utils.findRequiredView(source, R.id.line2, "field 'mLine2'");
    target.mIvFavorite = Utils.findRequiredViewAsType(source, R.id.iv_favorite, "field 'mIvFavorite'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_favorite, "field 'mRlFavorite' and method 'onBindClick'");
    target.mRlFavorite = Utils.castView(view, R.id.rl_favorite, "field 'mRlFavorite'", RelativeLayout.class);
    view7f09023b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvCollect = Utils.findRequiredViewAsType(source, R.id.iv_collect, "field 'mIvCollect'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_collect, "field 'mRlCollect' and method 'onBindClick'");
    target.mRlCollect = Utils.castView(view, R.id.rl_collect, "field 'mRlCollect'", RelativeLayout.class);
    view7f090237 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLine3 = Utils.findRequiredView(source, R.id.line3, "field 'mLine3'");
    target.mIvGovernance = Utils.findRequiredViewAsType(source, R.id.iv_governance, "field 'mIvGovernance'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_governance, "field 'mRlGovernance' and method 'onBindClick'");
    target.mRlGovernance = Utils.castView(view, R.id.rl_governance, "field 'mRlGovernance'", RelativeLayout.class);
    view7f09023e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLine4 = Utils.findRequiredView(source, R.id.line4, "field 'mLine4'");
    target.mIvModerator = Utils.findRequiredViewAsType(source, R.id.iv_Moderator, "field 'mIvModerator'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_Moderator, "field 'mRlModerator' and method 'onBindClick'");
    target.mRlModerator = Utils.castView(view, R.id.rl_Moderator, "field 'mRlModerator'", RelativeLayout.class);
    view7f090232 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLine5 = Utils.findRequiredView(source, R.id.line5, "field 'mLine5'");
    view = Utils.findRequiredView(source, R.id.tv_wallet_address, "field 'mTvWalletAddress' and method 'onBindClick'");
    target.mTvWalletAddress = Utils.castView(view, R.id.tv_wallet_address, "field 'mTvWalletAddress'", TextView.class);
    view7f090380 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvLevel = Utils.findRequiredViewAsType(source, R.id.tv_level, "field 'mTvLevel'", FontTextView.class);
    target.mIvVip = Utils.findRequiredViewAsType(source, R.id.iv_vip, "field 'mIvVip'", ImageView.class);
    target.mIconCheck = Utils.findRequiredViewAsType(source, R.id.icon_check, "field 'mIconCheck'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.iv_wallet, "field 'mIvWallet' and method 'onBindClick'");
    target.mIvWallet = Utils.castView(view, R.id.iv_wallet, "field 'mIvWallet'", ImageView.class);
    view7f090177 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvFree = Utils.findRequiredViewAsType(source, R.id.iv_free, "field 'mIvFree'", ImageView.class);
    target.mTvFree = Utils.findRequiredViewAsType(source, R.id.tv_free, "field 'mTvFree'", TextView.class);
    target.mTvFreeData = Utils.findRequiredViewAsType(source, R.id.tv_free_data, "field 'mTvFreeData'", TextView.class);
    target.mIvFree1 = Utils.findRequiredViewAsType(source, R.id.iv_free_1, "field 'mIvFree1'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_free, "field 'mRlFree' and method 'onBindClick'");
    target.mRlFree = Utils.castView(view, R.id.rl_free, "field 'mRlFree'", RelativeLayout.class);
    view7f09023c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvTransaction = Utils.findRequiredViewAsType(source, R.id.iv_transaction, "field 'mIvTransaction'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_transaction, "field 'mRlTransaction' and method 'onBindClick'");
    target.mRlTransaction = Utils.castView(view, R.id.rl_transaction, "field 'mRlTransaction'", RelativeLayout.class);
    view7f090255 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    MineFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mBtnUpdata = null;
    target.mIvHeard = null;
    target.mIvManger = null;
    target.mTvName = null;
    target.mTvHandle = null;
    target.mReviewCount = null;
    target.mTv1 = null;
    target.mFansCount = null;
    target.mTv2 = null;
    target.mRlInfo = null;
    target.mLine1 = null;
    target.mIvBalance = null;
    target.mTvBalance = null;
    target.mTvData = null;
    target.mIvRight = null;
    target.mRlBalance = null;
    target.mLine2 = null;
    target.mIvFavorite = null;
    target.mRlFavorite = null;
    target.mIvCollect = null;
    target.mRlCollect = null;
    target.mLine3 = null;
    target.mIvGovernance = null;
    target.mRlGovernance = null;
    target.mLine4 = null;
    target.mIvModerator = null;
    target.mRlModerator = null;
    target.mLine5 = null;
    target.mTvWalletAddress = null;
    target.mTvLevel = null;
    target.mIvVip = null;
    target.mIconCheck = null;
    target.mIvWallet = null;
    target.mIvFree = null;
    target.mTvFree = null;
    target.mTvFreeData = null;
    target.mIvFree1 = null;
    target.mRlFree = null;
    target.mIvTransaction = null;
    target.mRlTransaction = null;

    view7f090072.setOnClickListener(null);
    view7f090072 = null;
    view7f09014b.setOnClickListener(null);
    view7f09014b = null;
    view7f09022a.setOnClickListener(null);
    view7f09022a = null;
    view7f0902f1.setOnClickListener(null);
    view7f0902f1 = null;
    view7f0900ea.setOnClickListener(null);
    view7f0900ea = null;
    view7f0902f2.setOnClickListener(null);
    view7f0902f2 = null;
    view7f090241.setOnClickListener(null);
    view7f090241 = null;
    view7f090233.setOnClickListener(null);
    view7f090233 = null;
    view7f09023b.setOnClickListener(null);
    view7f09023b = null;
    view7f090237.setOnClickListener(null);
    view7f090237 = null;
    view7f09023e.setOnClickListener(null);
    view7f09023e = null;
    view7f090232.setOnClickListener(null);
    view7f090232 = null;
    view7f090380.setOnClickListener(null);
    view7f090380 = null;
    view7f090177.setOnClickListener(null);
    view7f090177 = null;
    view7f09023c.setOnClickListener(null);
    view7f09023c = null;
    view7f090255.setOnClickListener(null);
    view7f090255 = null;
  }
}
