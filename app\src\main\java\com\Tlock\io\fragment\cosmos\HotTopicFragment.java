package com.Tlock.io.fragment.cosmos;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.TopicDetailActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.HotTopicItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import java.util.ArrayList;

import butterknife.BindView;

public class HotTopicFragment extends LazyLoadBaseFragment {

    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    @BindView(R.id.root)
    RelativeLayout mRoot;

    int from = 0;

    private BaseRecyclerViewAdapter<PostQueryProto.TopicResponse> adapter;
    private ETHWallet current;

    public HotTopicFragment(int position) {
        this.from = position;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_hot_topic;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        current = WalletDaoUtils.getCurrent();
        initRecycleView();
    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        adapter = new BaseRecyclerViewAdapter<>(getContext(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.TopicResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                HotTopicItemView itemView = new HotTopicItemView(getContext());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.TopicResponse data, View view) {
                ((HotTopicItemView) view).setData(data, position,from);
                ((HotTopicItemView) view).setCallback(new HotTopicItemView.Callback() {
                    @Override
                    public void resetProfile(PostQueryProto.TopicResponse data) {
                        adapter.getList().set(position, data);
                    }
                });
            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.TopicResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.TopicResponse data, View view) {
                TopicDetailActivity.start(getActivity(), JsonUtils.objectToJson(data));
            }
        });
    }

    @Override
    protected void loadData() {
        if (from == 1) {
            getTrendingKeywordsList();
        } else {
            getTrendingTopicsList();
        }
    }

    @Override
    protected void getData() {
        super.getData();
        if (from == 1) {
            getTrendingKeywordsList();
        } else {
            getTrendingTopicsList();
        }
    }

    public void getTrendingKeywordsList() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.TopicResponse> hotTopics72 = CosmosUtils.getTrendingKeywords(page);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (adapter != null) {
                            adapter.addListNoChange(hotTopics72, page);
                        }
                        finishRefresh();
                    }
                });
            }
        });
    }


    public void getTrendingTopicsList() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.TopicResponse> hotTopics72 = CosmosUtils.getTrendingTopics(page);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (adapter != null) {
                            adapter.addListNoChange(hotTopics72, page);
                        }
                        finishRefresh();
                    }
                });
            }
        });
    }
    @Override
    public void onResume() {
        super.onResume();
        current = WalletDaoUtils.getCurrent();
    }

}