package com.Tlock.io.activity.cosmos;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.core.os.CancellationSignal;

import com.Tlock.io.R;
import com.Tlock.io.activity.wallet.BackupMnemonicsActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.profile.ProfileQueryProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.pop.PopCopyPrivateKey;
import com.Tlock.io.widget.pop.PopEdit;
import com.Tlock.io.widget.pop.PopTip;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;

import org.greenrobot.eventbus.EventBus;

import butterknife.BindView;
import butterknife.OnClick;

public class WalletInfoActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_balance)
    TextView mTvBalance;
    @BindView(R.id.tv_address)
    TextView mTvAddress;
    private ProfileProto.Profile authInfo;
    private ETHWallet current;
    private CancellationSignal cancellationSignal;
    private int outputType = 1; //1.私钥 2.助记词 3.删除

    /**
     * @param context
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, WalletInfoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_wallet_info;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {

        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mCustomNavBar.setOnRightClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                TransferActivity.start(getActivity(), 2);
            }
        });
    }


    @Override
    protected void loadData() {

        getAuthInfo();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            current = WalletDaoUtils.getCurrent();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    authInfo = CosmosUtils.getAuthInfo(current.getAddress());
                    String baseBalance = CosmosUtils.getBaseBalance(current.getAddress());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mCustomNavBar.setMidTitle(authInfo.getNickname());
                            mTvAddress.setText(current.getAddress());
                            String division = BigDecimalUtils.division(baseBalance, "1000000");
                            mTvBalance.setText(BigDecimalUtils.saveDecimals(division, 2) + " TOK");
                            if (TextUtils.isEmpty(authInfo.getAvatar())) {
                                TextAvatarDrawable a = new TextAvatarDrawable(authInfo.getUserHandle().substring(0, 1));
                                // 应用到 ImageView
                                mIvHeard.setBackground(a);
                            } else {
                                if (authInfo.getAvatar().startsWith("http")) {
                                    Glide.with(getActivity()).load(authInfo.getAvatar()).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(authInfo.getAvatar()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authInfo.getAvatar());
                                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                }
                            }
                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.tv_address, R.id.rl_send, R.id.rl_received, R.id.rl_name, R.id.rl_history, R.id.rl_private_key, R.id.rl_mnemonic, R.id.rl_delete})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_address:
                CopyUtils.copyToClipboard(current.getAddress());
                break;
            case R.id.rl_send:
                TransferActivity.start(getActivity());
                break;
            case R.id.rl_received:
                ReceiveActivity.start(getActivity());
                break;
            case R.id.rl_name:
                //TODO 修改名称弹窗
                break;
            case R.id.rl_history:
                TransferHistoryActivity.start(getActivity());
                break;
            case R.id.rl_private_key:
                // 备份弹窗
                outputType = 1;
                showNote();
                break;
            case R.id.rl_mnemonic:
                outputType = 2;
                if (WalletDaoUtils.getCurrent().getMnemonic()==null|| TextUtils.isEmpty(WalletDaoUtils.getCurrent().getMnemonic())){
                    ToastUtil.toastView("There is no mnemonic phrase");
                    return;
                }
                showNote();
                break;
            case R.id.rl_delete:
                outputType = 3;
                showCheckPassword();
                break;
        }
    }

    /**
     * 显示内容
     */
    private void showNote() {
        PopTip popTip = new PopTip(getActivity(),
                "Warning: Make sure no one can see your screen. Exposure of your private key via camera or screen recording may result in permanent loss of wallet access.",
                "Tlock Note",
                "Cancel",
                "Confirm",
                true,true);
        popTip.setCallback(new PopTip.Callback() {
            @Override
            public void notShow() {
            }

            @Override
            public void confirm() {
                showCheckPassword();
            }
        });
        new XPopup.Builder(getActivity())
                .hasShadowBg(true)
                .isDestroyOnDismiss(true)
                .asCustom(popTip).show();

    }

    private void showCheckPassword() {

        PopEdit pop = new PopEdit(getActivity(), "", "Authenticate Password", true);
        pop.setCallback(new PopEdit.Callback() {
            @Override
            public void notShow() {

            }

            @Override
            public void confirm(String title) {
                if (title.equals(WalletDaoUtils.getCurrent().getPassword())) {
                    switch (outputType) {
                        case 1:
                            showPrivateKey();
                            break;
                        case 2:
                            BackupMnemonicsActivity.start(getActivity(), JsonUtils.objectToJson(WalletDaoUtils.getCurrent()), 2);
                            break;
                        case 3:
                            WalletDaoUtils.deleteWallet(WalletDaoUtils.getCurrent().getId());
                            ToastUtil.toastView("Wallet deleted successfully");
                            EventBus.getDefault().postSticky(new Event(EventConstant.WALLET));
                            finish();
                            break;
                    }
                } else {
                    ToastUtil.toastView("Password error");
                }
            }

            @Override
            public void finger() {
                startFingerprintAuth();

            }
        });
        new XPopup.Builder(getActivity())
                .hasShadowBg(true)
                .isDestroyOnDismiss(true)
                .asCustom(pop).show();
    }


    private void showPrivateKey() {
        PopCopyPrivateKey popCopyPrivateKey = new PopCopyPrivateKey(getActivity(), WalletDaoUtils.getCurrent().getPrivateKey());
        new XPopup.Builder(getActivity())
                .hasShadowBg(true)
                .isDestroyOnDismiss(true)
                .asCustom(popCopyPrivateKey).show();
    }

    @SuppressLint("RestrictedApi")
    public void startFingerprintAuth() {
    FingerprintManagerCompat fingerprintManager = FingerprintManagerCompat.from(this);
        cancellationSignal = new CancellationSignal();

        fingerprintManager.authenticate(
                null, // 可选的CryptoObject（用于加密验证结果）
                0,
                cancellationSignal,
                new FingerprintManagerCompat.AuthenticationCallback() {
                    @Override
                    public void onAuthenticationError(int errMsgId, CharSequence errString) {
                        // 验证错误（如多次失败）
                        showToast("Validation error：" + errString);
                    }

                    @Override
                    public void onAuthenticationSucceeded(FingerprintManagerCompat.AuthenticationResult result) {
                        // 验证成功
                        switch (outputType) {
                            case 1:
                                showPrivateKey();
                                break;
                            case 2:
                                BackupMnemonicsActivity.start(getActivity(), JsonUtils.objectToJson(WalletDaoUtils.getCurrent()), 2);
                                break;
                            case 3:
                                WalletDaoUtils.deleteWallet(WalletDaoUtils.getCurrent().getId());
                                ToastUtil.toastView("Wallet deleted successfully");
                                EventBus.getDefault().postSticky(new Event(EventConstant.WALLET));
                                finish();
                                break;
                        }

                    }

                    @Override
                    public void onAuthenticationFailed() {
                        // 单次验证失败
                        showToast("Fingerprint mismatch");
                    }
                },
                null
        );
    }
}