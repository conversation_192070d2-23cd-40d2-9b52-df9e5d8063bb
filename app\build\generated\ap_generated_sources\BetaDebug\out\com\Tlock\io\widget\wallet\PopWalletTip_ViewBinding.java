// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopWalletTip_ViewBinding implements Unbinder {
  private PopWalletTip target;

  private View view7f090308;

  private View view7f090320;

  private View view7f09030d;

  @UiThread
  public PopWalletTip_ViewBinding(PopWalletTip target) {
    this(target, target);
  }

  @UiThread
  public PopWalletTip_ViewBinding(final PopWalletTip target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.tv_backup, "field 'mTvBackup' and method 'onViewClicked'");
    target.mTvBackup = Utils.castView(view, R.id.tv_backup, "field 'mTvBackup'", TextView.class);
    view7f090308 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_delete, "method 'onViewClicked'");
    view7f090320 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_cancle, "method 'onViewClicked'");
    view7f09030d = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopWalletTip target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvBackup = null;

    view7f090308.setOnClickListener(null);
    view7f090308 = null;
    view7f090320.setOnClickListener(null);
    view7f090320 = null;
    view7f09030d.setOnClickListener(null);
    view7f09030d = null;
  }
}
