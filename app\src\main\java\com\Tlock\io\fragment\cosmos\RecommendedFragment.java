package com.Tlock.io.fragment.cosmos;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.ContentInfoActivity;
import com.Tlock.io.activity.cosmos.PostQuoteActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.PostItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.NewCosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.pop.PopPostForward;
import com.Tlock.io.widget.pop.PopPostMore;
import com.Tlock.io.widget.pop.PopPostShare;
import com.cosmos.gov.v1.QueryProto;
import com.lxj.xpopup.XPopup;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;

public class RecommendedFragment extends LazyLoadBaseFragment {

    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    @BindView(R.id.root)
    RelativeLayout mRoot;

    int position = 0;
    ArrayList<String> pageList = new ArrayList<>();
    private HashMap<String, Integer> opMap = new HashMap<>();//1点赞 2收藏 3点赞收藏

    private BaseRecyclerViewAdapter<PostQueryProto.PostResponse> adapter;
    private ETHWallet current;
    private boolean isScrolling = false;
    private ArrayList<PostQueryProto.PostResponse> pendingPosts = new ArrayList<>(); // 缓存待添加的数据

    public RecommendedFragment(int position) {
        this.position = position;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_recommend;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
        current = WalletDaoUtils.getCurrent();
        initRecycleView();
    }

    private void initRecycleView() {
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setDrawingCacheEnabled(true);
        mRecyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        // 优化：启用预取和滑动优化
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        layoutManager.setSmoothScrollbarEnabled(true);
        mRecyclerView.setLayoutManager(layoutManager);

        // 优化：设置RecycledViewPool
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        mRecyclerView.setRecycledViewPool(recycledViewPool);


        // 优化滑动监听 - 减少不必要的操作，并添加50%自动加载功能
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (position == 1) return;
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // 停止滑动
                    isScrolling = false;
                    // 使用Handler延迟处理，避免频繁更新
                    if (!pendingPosts.isEmpty()) {
                        recyclerView.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!isScrolling && !pendingPosts.isEmpty()) {
                                    adapter.addListNoChange(pendingPosts, page);
                                    pendingPosts.clear();
                                }
                            }
                        }, 100); // 延迟100ms执行
                    }
                } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    // 只在开始拖拽时设置滑动状态
                    isScrolling = true;
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                // 检查是否需要自动加载下一页（滑动50%时触发）
                if (dy > 0 && !isLoading && !isLastPage) { // 向下滑动且未在加载且不是最后一页
                    LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                    if (layoutManager != null) {
                        int visibleItemCount = layoutManager.getChildCount();
                        int totalItemCount = layoutManager.getItemCount();
                        int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();

                        // 计算滑动进度：当前可见的第一个item位置 + 可见item数量 >= 总数量的50%
                        int scrolledItems = firstVisibleItemPosition + visibleItemCount;
                        double scrollProgress = (double) scrolledItems / totalItemCount;

                        // 当滑动超过50%时自动加载下一页
                        if (scrollProgress >= 0.5) {
                            isLoading = true;
                            page++;
                            // 根据不同的position调用不同的加载方法
                            switch (position) {
                                case 0:
                                    getPostList();
                                    break;
                                case 2:
                                    getPostList1();
                                    break;
                            }
                        }
                    }
                }
            }
        });
        adapter = new BaseRecyclerViewAdapter<>(getContext(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.PostResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                PostItemView itemView = new PostItemView(getContext());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.PostResponse data, View view) {
                ((PostItemView) view).setData(data);
                ((PostItemView) view).setDefaultImg(opMap.getOrDefault(data.getPost().getId(), 0));
                ((PostItemView) view).setCallback(new PostItemView.Callback() {

                    @Override
                    public void quote() {
                        PopPostForward popPostMore = new PopPostForward(getActivity(), data.getPost().getId(), "");
                        popPostMore.setCallBack(new PopPostForward.CallBack() {

                            @Override
                            public void repost(String id) {
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        mRecyclerView.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                showToast("Reposted success");
                                            }
                                        });
                                        CosmosUtils.postRepost(WalletDaoUtils.getCurrent().getAddress(), id);

                                    }
                                });
                            }

                            @Override
                            public void quote(String id) {
                                PostQuoteActivity.start(getActivity(), id);

                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();

                    }

                    @Override
                    public void share() {
                        PopPostShare popPostMore = new PopPostShare(getActivity());
                        popPostMore.setCallBack(new PopPostShare.CallBack() {

                            @Override
                            public void follow(String handle) {

                            }

                            @Override
                            public void report(String id) {

                            }

                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();

                    }

                    @Override
                    public void review(String id, String handle) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), true);
                    }


                    @Override
                    public void praise(String id) {
                        Integer i = opMap.get(id);
                        if (i == null || i == 0) {
                            opMap.put(id, 1);

                        } else if (i == 2) {
                            opMap.put(id, 3);
                        }
                        //点赞
                        if (current != null) {
                            showToast("Liked");
                            PostQueryProto.PostResponse item = adapter.getList().get(position);
                            PostProto.Post updatedPost = item.getPost().toBuilder()
                                    .setLikeCount(item.getPost().getLikeCount() + 1)
                                    .build();
                            adapter.getList().set(position, item.toBuilder().setPost(updatedPost).build());
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    NewCosmosUtils.postLike(current.getAddress(), id);
                                }
                            });
                        }

                    }

                    @Override
                    public void collect(String id) {
                        Integer i = opMap.get(id);
                        if (i == null || i == 0) {
                            opMap.put(id, 2);
                        } else if (i == 1) {
                            opMap.put(id, 3);
                        }
                        //收藏
                        if (current != null) {
                            showToast("Added to bookmarks");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    NewCosmosUtils.savePost(current.getAddress(), id);
                                }
                            });
                        }

                    }

                    @Override
                    public void more(String data, String handle, String address) {
                        PopPostMore popPostMore = new PopPostMore(getActivity(), data, handle);
                        popPostMore.setCallBack(new PopPostMore.CallBack() {
                            @Override
                            public void follow(String handle) {
                                showToast("Follow success");
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.follow(current.getAddress(), address);
                                    }
                                });
                            }

                            @Override
                            public void report(String id) {
                                //TODO 新页面
                                showToast("Report");
                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
                    }

                    @Override
                    public void info(PostQueryProto.PostResponse data) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
                    }

                    @Override
                    public void resetProfile(PostQueryProto.PostResponse postResponse) {
                        adapter.getList().set(position, postResponse);
                    }


                });
            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.PostResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.PostResponse data, View view) {
                ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
            }
        });
    }

    @Override
    protected void loadData() {
        switch (position) {
            case 0:
                getPostList();
                break;
            case 1:
                getPostListFlow();
                break;
            case 2:
                getPostList1();
                break;

        }
    }

    @Override
    protected void getData() {
        super.getData();
        switch (position) {
            case 0:
                getPostList();
                break;
            case 1:
                getPostListFlow();

                break;
            case 2:
                getPostList1();
                break;

        }
    }

    public void getPostList() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                PostQueryProto.QueryHomePostsResponse homePosts = CosmosUtils.getHomePosts();
                final String p = homePosts.getPage() + "";
                final ArrayList<PostQueryProto.PostResponse> homePost = new ArrayList<>(homePosts.getPostsList());
                // 预处理数据，减轻主线程负担
                final boolean shouldAddData = adapter != null && !pageList.contains(p);

                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        isLoading = false;
                        if (homePost.isEmpty()) {
                            isLastPage = true;
                            showToast("All the data have been loaded");
                            finishRefresh();
                            return;
                        }

                        if (shouldAddData) {
                            pageList.add(p);
                            if (isScrolling) {
                                pendingPosts.addAll(homePost);
                            } else {
                                pendingPosts = new ArrayList<>();
                                adapter.addListNoChange(homePost, page);
                            }
                        }
                        finishRefresh();
                    }
                });
            }
        });
    }

    public void getPostListFlow() {
        mRefreshLayout.setEnableLoadMore(false);
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                isLoading = false;
                PostQueryProto.QueryFollowingPostsResponse queryFollowingPostsResponse = CosmosUtils.QueryFollowingPosts(page);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (queryFollowingPostsResponse.getPostsList().size() == 0) {
                            isLastPage = true; // 数据为空，标记为最后一页
                            showError("Go follow users you're interested in!");
                            finishRefresh();
                            return;
                        }
                        hideError();
                        pendingPosts = new ArrayList<>();
                        // 直接更新数据
                        adapter.addListNoChange(new ArrayList<>(queryFollowingPostsResponse.getPostsList()), 1);
                        finishRefresh();

                    }
                });
            }
        });


    }

    /**
     * 刷新当前页面数据
     * 重置分页参数并重新加载数据
     */
    public void refreshData() {
        if (isLoading) return; // 防止重复刷新

        // 重置分页参数
        page = 1;
        isLastPage = false;
        isLoading = true;

        // 清空当前数据
        if (adapter != null) {
            adapter.setList(new ArrayList<>());
        }

        // 清空页面列表缓存
        pageList.clear();
        pendingPosts.clear();

        // 隐藏错误页面
        hideError();

        // 根据不同position调用对应的数据加载方法
        switch (position) {
            case 0:
                getPostList();
                break;
            case 1:
                getPostListFlow();
                break;
            case 2:
                getPostList1();
                break;
        }
    }


    public void getPostList1() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.PostResponse> homePost = CosmosUtils.getHomeFirstPosts(page);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
//                        Log.e(TAG, "getPostList: " + JsonUtils.listToJson(homePost));
                        if (homePost.isEmpty()) {
                            isLastPage = true; // 数据为空，标记为最后一页
                            showToast("All the data have been loaded");
                            finishRefresh();
                            return;
                        }
                        isLoading = false;
                        if (adapter != null) {
                            if (isScrolling) {
                                // 如果正在滑动，缓存数据
                                pendingPosts.addAll(homePost);
                            } else {
                                pendingPosts = new ArrayList<>();
                                // 直接更新数据
                                adapter.addListNoChange(homePost, page);
                            }
                        }
                        finishRefresh();
                    }
                });

            }
        });


    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onVipCallback(Event event) {
//        if (event.getMessgae().equalsIgnoreCase("PostQueryProto.Post")) {
//
//            getPostList();
//        }
    }

    @Override
    public void onResume() {
        super.onResume();
        current = WalletDaoUtils.getCurrent();

    }


    private int id = 0;
    private List<PostTXProto.MsgCreatePost> anyList = new ArrayList<>();

    public void sendProposal() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.sendProposal("1");


            }
        });
    }

    public void sendGovVote() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                QueryProto.QueryProposalsResponse queryProposalsResponse = CosmosUtils.lastVoteProposal();
                if (queryProposalsResponse != null && queryProposalsResponse.getProposalsCount() > 0) {
                    CosmosUtils.sendGovVote(queryProposalsResponse.getProposals(0).getId() + "");
                }
            }
        });
    }

    public void submitDeposit() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                QueryProto.QueryProposalsResponse queryProposalsResponse = CosmosUtils.lastDepositProposal();
                if (queryProposalsResponse != null && queryProposalsResponse.getProposalsCount() > 0) {
                    CosmosUtils.submitDeposit(queryProposalsResponse.getProposals(0).getId() + "", "1000000");
                }
            }
        });
    }


}
