// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopWhaleList_ViewBinding implements Unbinder {
  private PopWhaleList target;

  @UiThread
  public PopWhaleList_ViewBinding(PopWhaleList target) {
    this(target, target);
  }

  @UiThread
  public PopWhaleList_ViewBinding(PopWhaleList target, View source) {
    this.target = target;

    target.mRvWallet = Utils.findRequiredViewAsType(source, R.id.rv_wallet, "field 'mRvWallet'", RecyclerView.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mLine2 = Utils.findRequiredView(source, R.id.line2, "field 'mLine2'");
    target.mRoot = Utils.findRequiredViewAsType(source, R.id.root, "field 'mRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopWhaleList target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRvWallet = null;
    target.mLine1 = null;
    target.mLine2 = null;
    target.mRoot = null;
  }
}
