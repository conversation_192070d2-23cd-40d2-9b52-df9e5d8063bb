// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomInputBox;
import java.lang.IllegalStateException;
import java.lang.Override;

public class InputWalletActivity_ViewBinding implements Unbinder {
  private InputWalletActivity target;

  private View view7f090341;

  private View view7f090334;

  private View view7f090332;

  @UiThread
  public InputWalletActivity_ViewBinding(InputWalletActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public InputWalletActivity_ViewBinding(final InputWalletActivity target, View source) {
    this.target = target;

    View view;
    target.mIvBack = Utils.findRequiredViewAsType(source, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    target.mIvScan = Utils.findRequiredViewAsType(source, R.id.iv_scan, "field 'mIvScan'", ImageView.class);
    target.mRlToolbar = Utils.findRequiredViewAsType(source, R.id.rl_toolbar, "field 'mRlToolbar'", RelativeLayout.class);
    target.mTvInfo = Utils.findRequiredViewAsType(source, R.id.tv_info, "field 'mTvInfo'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_mnemonics, "field 'mTvMnemonics' and method 'onViewClicked'");
    target.mTvMnemonics = Utils.castView(view, R.id.tv_mnemonics, "field 'mTvMnemonics'", TextView.class);
    view7f090341 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_key, "field 'mTvKey' and method 'onViewClicked'");
    target.mTvKey = Utils.castView(view, R.id.tv_key, "field 'mTvKey'", TextView.class);
    view7f090334 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mLlType = Utils.findRequiredViewAsType(source, R.id.ll_type, "field 'mLlType'", LinearLayout.class);
    target.mEdInput = Utils.findRequiredViewAsType(source, R.id.ed_input, "field 'mEdInput'", EditText.class);
    target.mEtWalletName = Utils.findRequiredViewAsType(source, R.id.et_wallet_name, "field 'mEtWalletName'", CustomInputBox.class);
    target.mEtPwd = Utils.findRequiredViewAsType(source, R.id.et_pwd, "field 'mEtPwd'", CustomInputBox.class);
    target.mEtPwdConfirm = Utils.findRequiredViewAsType(source, R.id.et_pwd_confirm, "field 'mEtPwdConfirm'", CustomInputBox.class);
    view = Utils.findRequiredView(source, R.id.tv_input, "field 'mTvInput' and method 'onViewClicked'");
    target.mTvInput = Utils.castView(view, R.id.tv_input, "field 'mTvInput'", TextView.class);
    view7f090332 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mTvError = Utils.findRequiredViewAsType(source, R.id.tv_error, "field 'mTvError'", TextView.class);
    target.mTvAlready = Utils.findRequiredViewAsType(source, R.id.tv_already, "field 'mTvAlready'", TextView.class);
    target.mTvPwdError = Utils.findRequiredViewAsType(source, R.id.tv_pwd_error, "field 'mTvPwdError'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    InputWalletActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mIvScan = null;
    target.mRlToolbar = null;
    target.mTvInfo = null;
    target.mTvMnemonics = null;
    target.mTvKey = null;
    target.mLlType = null;
    target.mEdInput = null;
    target.mEtWalletName = null;
    target.mEtPwd = null;
    target.mEtPwdConfirm = null;
    target.mTvInput = null;
    target.mTvError = null;
    target.mTvAlready = null;
    target.mTvPwdError = null;

    view7f090341.setOnClickListener(null);
    view7f090341 = null;
    view7f090334.setOnClickListener(null);
    view7f090334 = null;
    view7f090332.setOnClickListener(null);
    view7f090332 = null;
  }
}
