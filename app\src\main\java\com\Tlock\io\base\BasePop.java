package com.Tlock.io.base;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.PopupWindow.OnDismissListener;


import com.Tlock.io.R;

import butterknife.ButterKnife;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;

/**
 * 本类的主要功能是 :  弹窗基类
 * <p>
 * 使用:
 * 继承基类,创建对象
 *
 */
@SuppressLint("NewApi")
public abstract class BasePop implements OnDismissListener {
    // private View targetView;
    protected PopupWindow popupWindow;
    protected OnClickListener onClickListener;
    protected OnClickListener onClickSureListener;// 确定
    protected OnClickListener onClickCancelListener; // 取消
    protected OnItemClickListener itemClickListener;
    protected OnDismissListener onDismissListener;
    protected OnBackPressListener onBackPressListener;//返回键监听
    protected Context mContext;

    public BasePop(Context context) {
        // this.targetView = targetView;
        this.mContext = context;
        //View view = LayoutInflater.from(context).inflate(getContentViewId(), null);
        LayoutInflater mLayoutInflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View view = mLayoutInflater.inflate(getContentViewId(), null);
        //绑定ButterKnife
        ButterKnife.bind(this, view);
        popupWindow = new PopupWindow(view, LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT) {
            @Override
            public void dismiss() {
                if (onBackPressListener == null) {
                    super.dismiss();
                    return;
                }
                StackTraceElement[] stackTrace = new Exception().getStackTrace();
                //按了返回键; 并且需要自己处理
                boolean b = stackTrace.length >= 2 && "dispatchKeyEvent".equals(stackTrace[1].getMethodName()) && onBackPressListener.onBackPressed(this);
                if (!b) {
                    super.dismiss();
                }
            }
        };
        popupWindow.setOnDismissListener(this);
        initData(view, context);
    }

//    public BasePop(Context context) {
//        this.mContext = context;
//        View view = LayoutInflater.from(context).inflate(getContentViewId(), null);
//        //绑定ButterKnife
//        ButterKnife.bind(view);
//        popupWindow = new PopupWindow(view, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
//        popupWindow.setOnDismissListener(this);
//        initData(view, context);
//    }


    public Context getContext() {

        return mContext;
    }

    public void setOnBackPressListener(OnBackPressListener onBackPressListener) {
        this.onBackPressListener = onBackPressListener;
    }

    public void setOnDismissListener(OnDismissListener onDismissListener) {
        this.onDismissListener = onDismissListener;
    }

    protected abstract int getContentViewId();

    public abstract void initData(View layout, Context context);


    /**
     * 隐藏菜单
     */
    public void dismiss() {
        popupWindow.dismiss();
        // Log.e("wsssssssss");
        // popupWindow.setOnDismissListener(new OnDismissListener()
        // {
        //
        // @Override
        // public void onDismiss()
        // {
        // popupWindow.dismiss();
        // Log.e("wsssssssss");
        //
        // }
        // });
    }

    public void setItemClickListener(OnItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    //点击监听
    public void setOnClickListener(OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
    }

    //确定
    public void setOnClickSureListener(OnClickListener onClickListener) {
        this.onClickSureListener = onClickListener;
    }

    //取消
    public void setOnClickCancelListener(OnClickListener onClickListener) {
        this.onClickCancelListener = onClickListener;
    }

    public void onDismiss() {
        popupWindow.dismiss();
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }
/**************************************************************************************************************************/
    /**
     * 从bottom向上移入屏幕动画
     *
     * @param container 需要动画的view
     */
    protected void startBottomInAnimate(View container) {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.pop_slide_in_bottom);
        container.startAnimation(animation);
    }

    /**
     * 从bottom向下移出屏幕动画
     *
     * @param container 需要动画的view
     */
    protected void startBottomOutAnimate(View container) {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.pop_slide_out_bottom);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        dismiss();
                    }
                });
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        container.startAnimation(animation);
    }

    /**
     * 从top向上移出屏幕动画
     *
     * @param container 需要动画的view
     */
    protected void startTopOutAnimate(View container) {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.pop_slide_out_top);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        dismiss();
                    }
                });
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        container.startAnimation(animation);
    }

    /**
     * 从top向下移入屏幕动画
     *
     * @param container 需要动画的view
     */
    protected void startTopInAnimate(View container) {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.pop_slide_in_top);
        container.startAnimation(animation);
    }

    /**
     * 从top向上移出屏幕动画
     *
     * @param container 需要动画的view
     */
    protected void startScaleInAnimate(View container) {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.pop_scale_in);
        container.startAnimation(animation);
    }

    /**
     * 从top向下移入屏幕动画
     *
     * @param container 需要动画的view
     */
    protected void startScaleoutAnimate(View container) {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.pop_scale_out);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        dismiss();
                    }
                });

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        container.startAnimation(animation);
    }
/**************************************************************************************************************************/


    /**
     * 只显示内容区域
     *
     * @param anchor
     * @param xoff
     * @param yoff
     * @param popHeight 视图高度
     */
    public void showAsDropDownCommon(View anchor, int popHeight, int xoff, int yoff) {

//        popupWindow.setWidth(anchor.getWidth());

        popupWindow.setWidth(anchor.getWidth());
        popupWindow.setHeight(LinearLayout.LayoutParams.WRAP_CONTENT);
        popupWindow.setFocusable(true);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setTouchable(true); // 设置PopupWindow可触摸
        popupWindow.showAsDropDown(anchor, xoff, yoff);
        popupWindow.update();
    }
    /**
     * 只显示内容区域
     *
     * @param anchor
     * @param xoff
     * @param yoff
     */
    public void showAsDropDownCommon(View anchor,  int xoff, int yoff) {

//        popupWindow.setWidth(anchor.getWidth());
        popupWindow.setWidth(LinearLayout.LayoutParams.MATCH_PARENT);
        popupWindow.setHeight(LinearLayout.LayoutParams.MATCH_PARENT);
        popupWindow.setFocusable(true);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setTouchable(true); // 设置PopupWindow可触摸
        popupWindow.showAsDropDown(anchor, xoff, yoff);
        popupWindow.update();
    }

    /**
     * 展示在屏幕中间
     */
    public void showAtCenter() {
        popupWindow.setBackgroundDrawable(new BitmapDrawable());
        // 使其聚集
        popupWindow.setFocusable(true);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setTouchable(true); // 设置PopupWindow可触摸

        popupWindow.showAtLocation(((Activity) mContext).getWindow().getDecorView(), Gravity.CENTER, 0, 0);
        popupWindow.update();
    }

    /**
     * 下拉式 弹出 pop菜单 parent 右下角
     */
    @SuppressWarnings("deprecation")
    public void showAsDropDown(View anchor) {
        // 这个是为了点击“返回Back”也能使其消失
        popupWindow.setBackgroundDrawable(new BitmapDrawable());
        // 设置允许在外点击消失
        popupWindow.setOutsideTouchable(false);
        // 使其聚集
        popupWindow.setFocusable(true);
        popupWindow.setTouchable(true); // 设置PopupWindow可触摸
        //兼容api24
        if (anchor != null && Build.VERSION.SDK_INT >= 24) {
            //获取屏幕真实高度
            int screenHeightReal = getScreenHeightReal(mContext);
            Rect rect = new Rect();
            anchor.getGlobalVisibleRect(rect);
            //保留1像素的线宽
            int h = screenHeightReal - rect.top - 1;
            popupWindow.setHeight(h);
        }
        // 设置弹出位置
        popupWindow.showAsDropDown(anchor);
        // 刷新状态
        popupWindow.update();
    }

    @SuppressWarnings("deprecation")
    public void showPopUp(View parent) {
        popupWindow.setFocusable(true);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());
        int[] location = new int[2];
        parent.getLocationOnScreen(location);
        popupWindow.showAtLocation(parent, Gravity.TOP, 15, location[1] - popupWindow.getHeight());
        popupWindow.update();
    }

    /**
     * <p>Indicate whether this popup window is showing on screen.</p>
     *
     * @return true if the popup is showing, false otherwise
     */
    public boolean isShowing() {
        return popupWindow.isShowing();
    }

    /**
     * 获取屏幕全高度
     * @param context
     * @return
     */
    public static int getScreenHeightReal(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        DisplayMetrics metrics = new DisplayMetrics();
        display.getMetrics(metrics);
        Point size = new Point();
        display.getRealSize(size);

        return size.y;
    }
}
