// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PostQuoteActivity_ViewBinding implements Unbinder {
  private PostQuoteActivity target;

  private View view7f090133;

  private View view7f09034e;

  @UiThread
  public PostQuoteActivity_ViewBinding(PostQuoteActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public PostQuoteActivity_ViewBinding(final PostQuoteActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_back, "field 'mIvBack' and method 'onBindClick'");
    target.mIvBack = Utils.castView(view, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    view7f090133 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_post, "field 'mTvPost' and method 'onBindClick'");
    target.mTvPost = Utils.castView(view, R.id.tv_post, "field 'mTvPost'", TextView.class);
    view7f09034e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mEdComment = Utils.findRequiredViewAsType(source, R.id.ed_Comment, "field 'mEdComment'", EditText.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTvContent = Utils.findRequiredViewAsType(source, R.id.tv_content, "field 'mTvContent'", TextView.class);
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
    target.mIvHeardQuote = Utils.findRequiredViewAsType(source, R.id.iv_heard_quote, "field 'mIvHeardQuote'", ImageView.class);
    target.mTvAccountName = Utils.findRequiredViewAsType(source, R.id.tv_account_name, "field 'mTvAccountName'", FontTextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PostQuoteActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mTvPost = null;
    target.mIvHeard = null;
    target.mEdComment = null;
    target.mTvTitle = null;
    target.mTvContent = null;
    target.mMain = null;
    target.mIvHeardQuote = null;
    target.mTvAccountName = null;
    target.mTvTime = null;

    view7f090133.setOnClickListener(null);
    view7f090133 = null;
    view7f09034e.setOnClickListener(null);
    view7f09034e = null;
  }
}
