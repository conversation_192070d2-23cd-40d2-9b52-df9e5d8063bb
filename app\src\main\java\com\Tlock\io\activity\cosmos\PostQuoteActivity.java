package com.Tlock.io.activity.cosmos;

import static com.Tlock.io.utils.DateUtil.friendly_time_3;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;

import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.OnClick;
import kotlin.Triple;

public class PostQuoteActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.tv_post)
    TextView mTvPost;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.ed_Comment)
    EditText mEdComment;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.tv_content)
    TextView mTvContent;
    @BindView(R.id.main)
    RelativeLayout mMain;
    @BindView(R.id.iv_heard_quote)
    ImageView mIvHeardQuote;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    private String id;


    private boolean isEditing = false; // 防止重复触发
    private Pattern pattern = Pattern.compile("#\\w+");
    private Pattern mentionPattern = Pattern.compile("@\\w+");
    private ArrayList<String> topicsList = new ArrayList<>();
    private ArrayList<String> mentionsList = new ArrayList<>(); // 存储提及的 ArrayList
    private String previousText = "";
    private PostQueryProto.PostResponse quoteData;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context, String id) {
        Intent intent = new Intent(context, PostQuoteActivity.class);
        intent.putExtra("id", id);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_post_quote;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mEdComment.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                previousText = charSequence.toString();
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {


            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (isEditing) return;

                isEditing = true;

                String currentText = editable.toString();
                int cursorPosition = mEdComment.getSelectionStart();

                // 1. 检测删除操作
                if (currentText.length() < previousText.length()) {
                    // 识别删除范围
                    int diff = previousText.length() - currentText.length();
                    // 确定删除的起始位置
                    int deleteStart = cursorPosition;
                    int deleteEnd = cursorPosition + diff;

                    // 获取删除后的文本中光标位置
                    // 分析删除的位置是否在话题或提及内
                    // 获取话题位置
                    ArrayList<int[]> hashtagSpans = getAllSpans(currentText, pattern);
                    ArrayList<int[]> mentionSpans = getAllSpans(currentText, mentionPattern);

                    boolean withinHashtag = false;
                    int targetHashtagStart = -1;
                    int targetHashtagEnd = -1;

                    for (int[] span : hashtagSpans) {
                        if (cursorPosition > span[0] && cursorPosition <= span[1]) {
                            withinHashtag = true;
                            targetHashtagStart = span[0];
                            targetHashtagEnd = span[1];
                            break;
                        }
                    }

                    boolean withinMention = false;
                    int targetMentionStart = -1;
                    int targetMentionEnd = -1;

                    if (!withinHashtag) { // 如果不在话题内，再检查提及
                        for (int[] span : mentionSpans) {
                            if (cursorPosition > span[0] && cursorPosition <= span[1]) {
                                withinMention = true;
                                targetMentionStart = span[0];
                                targetMentionEnd = span[1];
                                break;
                            }
                        }
                    }

                    if (withinHashtag) {
                        // 删除整个话题
                        Editable editableContent = mEdComment.getText();
                        editableContent.delete(targetHashtagStart - 1, targetHashtagEnd);
                        cursorPosition = targetHashtagStart - 1;
                        mEdComment.setSelection(cursorPosition);

                        // 更新 currentText after deletion
                        currentText = editableContent.toString();
                    } else if (withinMention) {
                        // 删除整个提及
                        Editable editableContent = mEdComment.getText();
                        editableContent.delete(targetMentionStart - 1, targetMentionEnd);
                        cursorPosition = targetMentionStart - 1;
                        mEdComment.setSelection(cursorPosition);

                        // 更新 currentText after deletion
                        currentText = editableContent.toString();
                    }
                }

                // 2. 自动在#和@前添加空格
                if (cursorPosition > 0 && (currentText.charAt(cursorPosition - 1) == '#' || currentText.charAt(cursorPosition - 1) == '@')) {
                    if (cursorPosition == 1 || currentText.charAt(cursorPosition - 2) != ' ') {
                        editable.replace(cursorPosition - 1, cursorPosition, " " + currentText.charAt(cursorPosition - 1));
                        cursorPosition += 1; // 调整光标位置
                        mEdComment.setSelection(cursorPosition);
                        currentText = editable.toString(); // 更新文本内容
                    }
                }

                // 3. 进行颜色高亮并收集话题和提及
                SpannableStringBuilder spannable = new SpannableStringBuilder(currentText);
                Matcher hashtagMatcher = pattern.matcher(currentText);
                Matcher mentionMatcher = mentionPattern.matcher(currentText);
                ArrayList<String> currentHashtags = new ArrayList<>();
                ArrayList<String> currentMentions = new ArrayList<>();

                // 处理话题
                while (hashtagMatcher.find()) {
                    String hashtag = hashtagMatcher.group(); // 获取匹配的 #标签
                    currentHashtags.add(hashtag.replace("#", "")); // 添加到当前话题列表

                    // 设置字体颜色为蓝色
                    editable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.cosmos_topic)),
                            hashtagMatcher.start(),
                            hashtagMatcher.end(),
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                // 处理提及
                while (mentionMatcher.find()) {
                    String mention = mentionMatcher.group(); // 获取匹配的 @标签
                    currentMentions.add(mention.replace("@", "")); // 添加到当前提及列表

                    // 设置字体颜色为绿色
                    editable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.cosmos_topic)),
                            mentionMatcher.start(),
                            mentionMatcher.end(),
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                // 4. 更新 EditText 内容

                try {
                    mEdComment.setSelection(cursorPosition); // 保持光标位置
                } catch (IndexOutOfBoundsException e) {
                    // 处理可能的异常，如光标位置超出范围
                    mEdComment.setSelection(spannable.length());
                }


                // 5. 更新话题和提及列表
                updateLists(currentHashtags, currentMentions);

                previousText = currentText; // 更新前一个文本状态

                isEditing = false;
            }
        });
        try {
            String userInfo = SpUtil.getUserInfo();
            ProfileProto.Profile profile = JsonUtils.jsonToObject(userInfo, ProfileProto.Profile.class);
            if (TextUtils.isEmpty(profile.getAvatar())) {
                TextAvatarDrawable a = new TextAvatarDrawable(profile.getUserHandle().substring(0, 1));
                mIvHeard.setImageDrawable(a);
            } else {
                if (profile.getAvatar().startsWith("http")) {
                    Glide.with(getActivity()).load(profile.getAvatar()).apply(new RequestOptions()
                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                    .signature(new ObjectKey(profile.getAvatar()))
                                    .centerCrop()
                                    .format(DecodeFormat.PREFER_RGB_565)
                                    .dontTransform())
                            .apply(RequestOptions.circleCropTransform().circleCrop())
                            .into(mIvHeard);
                } else {
                    Bitmap bitmap2 = BitmapUtils.base64ToBitmap(profile.getAvatar());
                    Glide.with(getActivity()).asBitmap().load(bitmap2).apply(new RequestOptions()
                                    .centerCrop()
                                    .dontTransform())
                            .apply(RequestOptions.circleCropTransform().circleCrop())
                            .into(mIvHeard);


                }
            }
        } catch (Exception e) {
//            Log.e(TAG, "initView: " + e.getMessage());
        }
    }

    private void setQuote(PostQueryProto.PostResponse quoteData) {

        if (quoteData.getPost().getTitle().isEmpty()) {
            mTvTitle.setVisibility(View.GONE);
        }
        mTvTitle.setText(quoteData.getPost().getTitle());
        mTvAccountName.setText(quoteData.getProfile().getNickname());
        mTvContent.setText(quoteData.getPost().getContent());
        Bitmap bitmap1 = BitmapUtils.base64ToBitmap(quoteData.getProfile().getAvatar());
        Glide.with(getActivity()).load(bitmap1)
                .apply(new RequestOptions()
                        .centerCrop()
                        .dontTransform())
                .transition(DrawableTransitionOptions.withCrossFade(500))
                .circleCrop()
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .into(mIvHeardQuote);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_3(quoteData.getPost().getTimestamp() + "000"));
        }

    }


    /**
     * 获取所有匹配的标记的起始和结束索引
     *
     * @param text    当前文本
     * @param pattern 正则表达式模式
     * @return ArrayList<int [ ]> 每个 int[] 包含 [start, end] 索引
     */
    private ArrayList<int[]> getAllSpans(String text, Pattern pattern) {
        ArrayList<int[]> spans = new ArrayList<>();
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            spans.add(new int[]{matcher.start(), matcher.end()});
        }
        return spans;
    }


    /**
     * 更新话题和提及列表并显示
     *
     * @param currentHashtags 当前文本中的话题列表
     * @param currentMentions 当前文本中的提及列表
     */
    private void updateLists(ArrayList<String> currentHashtags, ArrayList<String> currentMentions) {
        // 更新话题列表
        topicsList.clear();
        topicsList.addAll(currentHashtags);

        // 更新提及列表
        mentionsList.clear();
        mentionsList.addAll(currentMentions);

    }

    @Override
    protected void loadData() {
        id = getIntent().getStringExtra("id");
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                PostQueryProto.PostResponse postResponse = CosmosUtils.queryPost(id);
                ProfileProto.Profile authInfo = CosmosUtils.getAuthInfo(postResponse.getPost().getCreator());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        PostQueryProto.PostResponse build = postResponse.toBuilder().setProfile(authInfo).build();
                        setQuote(build);
                    }
                });
            }
        });
    }

    @OnClick({R.id.iv_back, R.id.tv_post})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_post:
                finish();
                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        CosmosUtils.postQuote(mEdComment.getText().toString(), WalletDaoUtils.getCurrent().getAddress(), id);
                    }
                });
                break;
        }
    }
}