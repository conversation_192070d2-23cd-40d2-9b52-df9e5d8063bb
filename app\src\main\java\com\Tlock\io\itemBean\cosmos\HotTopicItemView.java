package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.UserUtil.dip2px;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class HotTopicItemView extends BaseView {


    @BindView(R.id.iv_hot_topic)
    ImageView mIvHotTopic;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.tv_topic)
    TextView mTvTopic;
    @BindView(R.id.tv_count)
    TextView mTvCount;
//    @BindView(R.id.tv_index)
//    TextView mTvIndex;
    private PostQueryProto.TopicResponse data;
    private String id = "";
    String templateRed = "<font color='red' size='5'>%d</font>，%d";

    public HotTopicItemView(Context context) {
        super(context);
    }

    public HotTopicItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public HotTopicItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.item_hot_topic;
    }

    public void setData(PostQueryProto.TopicResponse data, int position, int from) {
        if (id.equalsIgnoreCase(data.getId())) {
            return;
        } else {
//            Log.e("TAG", "对比不同数据: " + "这是记录的id:" + id + "    这个传入  " + data.getId() + "    这个是真实数据" + JsonUtils.objectToJson(data));

        }
        id = data.getId();
        this.data = data;
//        mTvIndex.setText( + "");
        mIvHotTopic.setVisibility(GONE);
        SpannableString spannable;
        String indexStr=(position + 1)+"  ";


        if (data.getTitle().isEmpty()) {
            spannable = new SpannableString(indexStr + data.getName());
        } else {
            spannable = new SpannableString(indexStr + data.getTitle());
        }
        int indexColor = getResources().getColor(R.color.cosmos_default);
        if (position < 3) {
            indexColor = getResources().getColor(R.color.red_text);
        }
        spannable.setSpan(
                new ForegroundColorSpan(indexColor),
                0, indexStr.length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        );
        spannable.setSpan(
                new AbsoluteSizeSpan(16, true), // true表示单位为dp
                0, indexStr.length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        );
        mTvTitle.setText(spannable);


        mTvCount.setText(data.getSummary());

        mTvTopic.setText(BigDecimalUtils.saveDecimals(from == 1 ? data.getTrendingKeywordsScore() + "" : data.getScore() + "", 0));

        getAuthInfo();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String authStr = CosmosUtils.getTopicHeard(data.getId());
                    mIvHotTopic.post(new Runnable() {
                        @Override
                        public void run() {
                            if (!TextUtils.isEmpty(authStr)) {
                                mIvHotTopic.setVisibility(VISIBLE);
                                if (callback != null) {
                                    PostQueryProto.TopicResponse build = data.toBuilder().setImage(authStr).build();
                                    callback.resetProfile(build);
                                }
                                int radiusPx = dip2px(getContext(), 3);
                                RequestOptions options = new RequestOptions()
                                        .bitmapTransform(new RoundedCorners(radiusPx)); // 统一设置圆角

                                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authStr);
                                Glide.with(getContext()).load(bitmap1)
                                        .apply(new RequestOptions()
                                                .centerCrop()
                                                .dontTransform())
                                        .apply(options)
                                        .transition(DrawableTransitionOptions.withCrossFade(500))
                                        .into(mIvHotTopic);

                            } else {
                                mIvHotTopic.setVisibility(GONE);

                            }
                        }
                    });
                }
            });
        }
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void resetProfile(PostQueryProto.TopicResponse data);
    }

}
