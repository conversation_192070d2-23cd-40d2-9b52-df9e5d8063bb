package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.cosmos.TopicTabBean;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TopicTabView extends BaseView {


    @BindView(R.id.iv_line)
    ImageView mIvLine;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.rl_item)
    RelativeLayout mRlItem;
    @BindView(R.id.iv_1)
    ImageView mIv1;

    public TopicTabView(Context context) {
        super(context);
    }

    public TopicTabView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TopicTabView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_topic_tab;
    }

    public void setData(TopicTabBean data, int position) {
        mTvTitle.setText(data.getTopicName());
        mIvLine.setVisibility(data.isSelect() ? VISIBLE : GONE);
        mRlItem.setBackground(data.isSelect() ? getResources().getDrawable(R.color.white) : getResources().getDrawable(R.color.gray_color));
        switch (position) {
            case 0:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic0));
                break;
            case 1:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic1));
                break;
            case 2:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic2));
                break;
            case 3:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic3));
                break;
            case 4:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic4));
                break;
            case 5:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic5));
                break;
            case 6:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic6));
                break;
            case 7:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic7));
                break;
        }

    }
    public void setSelect(boolean select) {
        mIvLine.setVisibility(select ? VISIBLE : GONE);
        mRlItem.setBackground(select ? getResources().getDrawable(R.color.white) : getResources().getDrawable(R.color.gray_color));

    }

    public void setHeight(int height) {
        ViewGroup.LayoutParams layoutParams = mRlItem.getLayoutParams();
        layoutParams.height = height;
        mRlItem.setLayoutParams(layoutParams);
    }

}
