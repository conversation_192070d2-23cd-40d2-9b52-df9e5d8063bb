package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;

import butterknife.BindView;

/**
 * @ClassName LPHistoryBean
 * <AUTHOR>
 * @Data 2022/3/24 15:41
 * @Desc
 */

public class PostTopicItemView extends BaseView {


    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.ll_root)
    RelativeLayout mLlRoot;
    @BindView(R.id.tv_nike_name)
    TextView mTvNikeName;
    @BindView(R.id.tv_handle)
    TextView mTvHandle;
    @BindView(R.id.iv_click)
    ImageView mIvClick;
    private String address = "";

    public PostTopicItemView(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_post_topic;
    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    public void setData(ProfileProto.Profile data) {

        if (TextUtils.isEmpty(data.getNickname())) {
            mTvTitle.setVisibility(VISIBLE);
            mIvHeard.setVisibility(GONE);
            mTvNikeName.setVisibility(GONE);
            mTvHandle.setVisibility(GONE);
            mTvTitle.setText(data.getUserHandle());
        } else {
            mTvTitle.setVisibility(GONE);
            mIvHeard.setVisibility(VISIBLE);
            mTvNikeName.setVisibility(VISIBLE);
            mTvHandle.setVisibility(VISIBLE);
            mTvNikeName.setText(data.getNickname());
            mTvHandle.setText(data.getUserHandle());
            if (address != null && address.equals(data.getWalletAddress())) {
                return;
            }
            address = data.getWalletAddress();
            Glide.with(getContext()).load(getResources().getDrawable(R.drawable.shape_transparent_60)).into(mIvHeard);
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String heardStr = CosmosUtils.getAuthHeard(data.getWalletAddress());
                    mIvHeard.post(new Runnable() {
                        @Override
                        public void run() {
                            if (TextUtils.isEmpty(heardStr)) {
                                TextAvatarDrawable a = new TextAvatarDrawable(data.getUserHandle().substring(0, 1));
                                // 应用到 ImageView
                                mIvHeard.setBackground(a);
                            } else {
                                if (heardStr.startsWith("http")) {
                                    Glide.with(getContext()).load(heardStr).apply(new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(new ObjectKey(data.getWalletAddress())).centerCrop().format(DecodeFormat.PREFER_RGB_565).dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).transition(DrawableTransitionOptions.withCrossFade(500)).into(mIvHeard);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(heardStr);
                                    Glide.with(getContext()).load(bitmap1).apply(new RequestOptions().centerCrop().dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).transition(DrawableTransitionOptions.withCrossFade(500)).placeholder(getResources().getDrawable(R.drawable.shape_transparent_60)).into(mIvHeard);
                                }
                            }
                        }
                    });
                }
            });

        }


    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public void setData(ProfileProto.Profile data, boolean hasClick) {
        mIvHeard.setVisibility(VISIBLE);
        mTvTitle.setVisibility(GONE);
        mTvNikeName.setVisibility(VISIBLE);
        mTvHandle.setVisibility(VISIBLE);
        mTvNikeName.setText(data.getNickname().isEmpty() ? data.getUserHandle() : data.getNickname());
        mTvHandle.setText(data.getUserHandle());
        TextAvatarDrawable a = new TextAvatarDrawable(data.getUserHandle().substring(0, 1));
        mIvHeard.setImageDrawable(a);
        mTvTitle.setText(data.getUserHandle());
        if (!TextUtils.isEmpty(data.getNickname())) {
            address = data.getWalletAddress();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String heardStr = CosmosUtils.getAuthHeard(data.getWalletAddress());
                    mIvHeard.post(new Runnable() {
                        @Override
                        public void run() {
                            if (TextUtils.isEmpty(heardStr)) {
                                TextAvatarDrawable a = new TextAvatarDrawable(data.getUserHandle().substring(0, 1));
                                // 应用到 ImageView
                                mIvHeard.setImageDrawable(a);
                            } else {
                                if (heardStr.startsWith("http")) {
                                    Glide.with(getContext()).load(heardStr).apply(new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(new ObjectKey(data.getWalletAddress())).centerCrop().format(DecodeFormat.PREFER_RGB_565).dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).transition(DrawableTransitionOptions.withCrossFade(500)).into(mIvHeard);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(heardStr);
                                    Glide.with(getContext()).load(bitmap1).apply(new RequestOptions().centerCrop().dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).transition(DrawableTransitionOptions.withCrossFade(500)).into(mIvHeard);
                                }
                            }
                        }
                    });
                }
            });

        }


    }

}
