package com.Tlock.io.entity.wallet;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Generated;

/**
 * @ClassName TransactionInfo
 * <AUTHOR>
 * @Data 2021/11/18 11:14
 * @Desc
 */

@Entity
public class Transfer {
    @Id(autoincrement = true)
    private Long id;

    @Property(nameInDb = "tokenId")
    private Long tokenId;
    private String getAddress;
    private String tokenAddress;
    private String name;
    private String symbol;
    private String imgUrl;
    private int decimals;
    private int logIndex;//log值
    private String count;
    private String payAddress;
    private String time;
    private String gasPrice;
    private String gasLimit;
    private String miners;//矿工费
    private String tokenBalance;
    private String transferHash;
    private int payStatus;//1买 2卖 3授权
    private boolean status;

    public boolean equals(Object obj) {//覆盖Object里的该方法
        if (!(obj instanceof Transfer))
            return false;
        Transfer o = (Transfer) obj;
        return this.tokenId == o.getTokenId()
                && this.getAddress.equalsIgnoreCase(o.getGetAddress())
//                && this.tokenAddress.equalsIgnoreCase(o.getTokenAddress())
                && this.decimals == getDecimals()
                && this.logIndex == getLogIndex()
                && this.count.equalsIgnoreCase(o.getCount())
                && this.payAddress.equalsIgnoreCase(o.getPayAddress())
                && this.time.equalsIgnoreCase(o.getTime())
                && this.gasPrice.equalsIgnoreCase(o.getGasPrice())
                && this.gasLimit.equalsIgnoreCase(o.getGasLimit())
                && this.miners.equalsIgnoreCase(o.getMiners())
                && this.transferHash.equalsIgnoreCase(o.getTransferHash())
                && this.payStatus == o.getPayStatus()
                && this.status == o.getStatus();

    }

    @Generated(hash = 1704612723)
    public Transfer(Long id, Long tokenId, String getAddress, String tokenAddress,
                    String name, String symbol, String imgUrl, int decimals, int logIndex,
                    String count, String payAddress, String time, String gasPrice,
                    String gasLimit, String miners, String tokenBalance,
                    String transferHash, int payStatus, boolean status) {
        this.id = id;
        this.tokenId = tokenId;
        this.getAddress = getAddress;
        this.tokenAddress = tokenAddress;
        this.name = name;
        this.symbol = symbol;
        this.imgUrl = imgUrl;
        this.decimals = decimals;
        this.logIndex = logIndex;
        this.count = count;
        this.payAddress = payAddress;
        this.time = time;
        this.gasPrice = gasPrice;
        this.gasLimit = gasLimit;
        this.miners = miners;
        this.tokenBalance = tokenBalance;
        this.transferHash = transferHash;
        this.payStatus = payStatus;
        this.status = status;
    }

    @Generated(hash = 137042952)
    public Transfer() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTokenId() {
        return this.tokenId;
    }

    public void setTokenId(Long tokenId) {
        this.tokenId = tokenId;
    }

    public String getGetAddress() {
        return this.getAddress;
    }

    public void setGetAddress(String getAddress) {
        this.getAddress = getAddress;
    }

    public String getTokenAddress() {
        return this.tokenAddress;
    }

    public void setTokenAddress(String tokenAddress) {
        this.tokenAddress = tokenAddress;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSymbol() {
        return this.symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getImgUrl() {
        return this.imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public int getDecimals() {
        return this.decimals;
    }

    public void setDecimals(int decimals) {
        this.decimals = decimals;
    }

    public int getLogIndex() {
        return this.logIndex;
    }

    public void setLogIndex(int logIndex) {
        this.logIndex = logIndex;
    }

    public String getCount() {
        return this.count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getPayAddress() {
        return this.payAddress;
    }

    public void setPayAddress(String payAddress) {
        this.payAddress = payAddress;
    }

    public String getTime() {
        return this.time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getGasPrice() {
        return this.gasPrice;
    }

    public void setGasPrice(String gasPrice) {
        this.gasPrice = gasPrice;
    }

    public String getGasLimit() {
        return this.gasLimit;
    }

    public void setGasLimit(String gasLimit) {
        this.gasLimit = gasLimit;
    }

    public String getMiners() {
        return this.miners;
    }

    public void setMiners(String miners) {
        this.miners = miners;
    }

    public String getTokenBalance() {
        return this.tokenBalance;
    }

    public void setTokenBalance(String tokenBalance) {
        this.tokenBalance = tokenBalance;
    }

    public String getTransferHash() {
        return this.transferHash;
    }

    public void setTransferHash(String transferHash) {
        this.transferHash = transferHash;
    }

    public int getPayStatus() {
        return this.payStatus;
    }

    public void setPayStatus(int payStatus) {
        this.payStatus = payStatus;
    }

    public boolean getStatus() {
        return this.status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

}
