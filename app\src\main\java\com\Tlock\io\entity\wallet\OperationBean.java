package com.Tlock.io.entity.wallet;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Index;
import org.greenrobot.greendao.annotation.Generated;

@Entity
public class OperationBean {
    @Id(autoincrement = true)
    private Long id;
    @Index
    private String postId;
    private String address;
    private boolean isLike;
    private boolean isComment;
    private boolean isShare;
    private boolean isCollect;
    @Generated(hash = 107430226)
    public OperationBean(Long id, String postId, String address, boolean isLike,
            boolean isComment, boolean isShare, boolean isCollect) {
        this.id = id;
        this.postId = postId;
        this.address = address;
        this.isLike = isLike;
        this.isComment = isComment;
        this.isShare = isShare;
        this.isCollect = isCollect;
    }
    @Generated(hash = 2008627548)
    public OperationBean() {
    }
    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getPostId() {
        return this.postId;
    }
    public void setPostId(String postId) {
        this.postId = postId;
    }
    public String getAddress() {
        return this.address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    public boolean getIsLike() {
        return this.isLike;
    }
    public void setIsLike(boolean isLike) {
        this.isLike = isLike;
    }
    public boolean getIsComment() {
        return this.isComment;
    }
    public void setIsComment(boolean isComment) {
        this.isComment = isComment;
    }
    public boolean getIsShare() {
        return this.isShare;
    }
    public void setIsShare(boolean isShare) {
        this.isShare = isShare;
    }
    public boolean getIsCollect() {
        return this.isCollect;
    }
    public void setIsCollect(boolean isCollect) {
        this.isCollect = isCollect;
    }


}
