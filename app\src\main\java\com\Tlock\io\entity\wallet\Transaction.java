package com.Tlock.io.entity.wallet;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Index;

@Entity

public class Transaction {

    @Id(autoincrement = true)
    private Long id;
    @Index
    private String walletAddress;
    private String function;
    private String gasLimit;
    private String payer;
    private String blockHeight;
    private String hash;
    private String data;
    private String gasWanted;
    private String gesUsed;
    private String time;
    private String sequence;

    @Generated(hash = 574549166)
    public Transaction(Long id, String walletAddress, String function,
                       String gasLimit, String payer, String blockHeight, String hash,
                       String data, String gasWanted, String gesUsed, String time,
                       String sequence) {
        this.id = id;
        this.walletAddress = walletAddress;
        this.function = function;
        this.gasLimit = gasLimit;
        this.payer = payer;
        this.blockHeight = blockHeight;
        this.hash = hash;
        this.data = data;
        this.gasWanted = gasWanted;
        this.gesUsed = gesUsed;
        this.time = time;
        this.sequence = sequence;
    }

    @Generated(hash = 750986268)
    public Transaction() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWalletAddress() {
        return this.walletAddress;
    }

    public void setWalletAddress(String walletAddress) {
        this.walletAddress = walletAddress;
    }

    public String getFunction() {
        return this.function;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getGasLimit() {
        return this.gasLimit;
    }

    public void setGasLimit(String gasLimit) {
        this.gasLimit = gasLimit;
    }

    public String getPayer() {
        return this.payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public String getBlockHeight() {
        return this.blockHeight;
    }

    public void setBlockHeight(String blockHeight) {
        this.blockHeight = blockHeight;
    }

    public String getHash() {
        return this.hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getData() {
        return this.data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getGasWanted() {
        return this.gasWanted;
    }

    public void setGasWanted(String gasWanted) {
        this.gasWanted = gasWanted;
    }

    public String getGesUsed() {
        return this.gesUsed;
    }

    public void setGesUsed(String gesUsed) {
        this.gesUsed = gesUsed;
    }

    public String getTime() {
        return this.time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getSequence() {
        return this.sequence;
    }

    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

}
