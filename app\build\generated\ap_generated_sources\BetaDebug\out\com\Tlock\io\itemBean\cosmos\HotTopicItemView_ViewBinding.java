// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class HotTopicItemView_ViewBinding implements Unbinder {
  private HotTopicItemView target;

  @UiThread
  public HotTopicItemView_ViewBinding(HotTopicItemView target) {
    this(target, target);
  }

  @UiThread
  public HotTopicItemView_ViewBinding(HotTopicItemView target, View source) {
    this.target = target;

    target.mIvHotTopic = Utils.findRequiredViewAsType(source, R.id.iv_hot_topic, "field 'mIvHotTopic'", ImageView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTvTopic = Utils.findRequiredViewAsType(source, R.id.tv_topic, "field 'mTvTopic'", TextView.class);
    target.mTvCount = Utils.findRequiredViewAsType(source, R.id.tv_count, "field 'mTvCount'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    HotTopicItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvHotTopic = null;
    target.mTvTitle = null;
    target.mTvTopic = null;
    target.mTvCount = null;
  }
}
