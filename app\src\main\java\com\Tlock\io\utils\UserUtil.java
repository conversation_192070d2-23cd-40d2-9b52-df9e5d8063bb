package com.Tlock.io.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Vibrator;
import android.text.TextUtils;

import com.Tlock.io.entity.wallet.ETHWallet;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Set;

/**
 * 本类的主要功能是 :  用户工具类
 *
 * <AUTHOR>  2020/5/9 16:38
 */

public class UserUtil {

//    /**
//     * 获取用户登录信息
//     *
//     * @return LoginBean
//     */
//    public static LoginBean getLoginBean() {
//
//        LoginBean loginBean = null;
//        LoginBeanDAO first = LitePal.findFirst(LoginBeanDAO.class);
//        if (first != null) {
//            String jsonItem =
//                    first.getJsonItem();
//            if (!TextUtils.isEmpty(jsonItem)) {
//                loginBean = JsonUtils.jsonToObject(jsonItem, LoginBean.class);
//            }
//        }
//        return loginBean == null ? new LoginBean() : loginBean;
//    }

//    /**
//     * 获取用户登录信息
//     *
//     * @return LoginBean
//     */
//    public static LoginBean.UserBean getUserBean() {
//        LoginBean.UserBean user = getLoginBean().getUser();
//        return user == null ? new LoginBean.UserBean() : user;
//    }
//
//    /**
//     * 是否是自己的,根据id对比
//     *
//     * @param userId_ id
//     * @return true
//     */
//    public static boolean isSelf(Long userId_) {
//        if (userId_ == null) {
//            return false;
//        }
//        LoginBean.UserBean userBean = UserUtil.getUserBean();
//        Long id = (long) userBean.getId();
//        return userId_.equals(id);
//    }

    /**
     * 保留两位小数
     *
     * @param num 数字
     * @return .00
     */
    public static String remain_dot_2(String num) {
        if (TextUtils.isEmpty(num)) {
            return "0.00";
        }
        double d = 0.00d;
        try {
            d = Double.parseDouble(num);
        } catch (Exception e) {
            e.printStackTrace();
        }

        java.text.DecimalFormat df = new java.text.DecimalFormat("#.00");
        String formatNum = df.format(d);
        if (formatNum.startsWith(".")) {
            formatNum = "0".concat(formatNum);
        }
        return formatNum;
    }


    /**
     * 空的话返回  "replaceStr参数",不空的话返回原字符串
     *
     * @param str        原字符串
     * @param replaceStr 替换成的字符串
     * @return 替换后的字符串
     */
    public static String emptyReplace(String str, String replaceStr) {
        if (TextUtils.isEmpty(str))
            return replaceStr;

        return str;
    }

    /**
     * 是否包含 searchWord--->originWord中是否包含searchWord
     *
     * @param originWord 原字符串
     * @param searchWord 搜索字符串
     * @return 是否包含
     */
    public static boolean isContains(String originWord, String searchWord) {
        if (originWord == null) {
            return false;
        }
        return originWord.contains(searchWord);
    }


    /**
     * 是否为邮件
     *
     * @param email 地址
     * @return true/false
     */
    public static boolean isEmail(String email) {
        if (TextUtils.isEmpty(email)) {
            return false;
        }
        String[] split = email.split("@");
        for (int i = 0; i < split.length; i++) {
            if ((i + 1) < split.length) {
                if (!TextUtils.isEmpty(split[i].trim()) && !TextUtils.isEmpty(split[i + 1].trim())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 卸载应用         unInstallApk(getActivity(),"com.jingkai.lawpad");
     *
     * @param context 上下文
     * @param apk_pkg 包名
     */
    public static void unInstallApk(Context context, String apk_pkg) {
        try {
            Intent intent = new Intent(Intent.ACTION_DELETE);
            intent.setData(Uri.parse("package:" + apk_pkg));
            context.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 是否安装了某应用
     *
     * @param context     上下文
     * @param apk_package 包名
     * @return true/false
     */
    public static boolean isInstallApp(Context context, String apk_package) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(
                    apk_package, 0);
        } catch (PackageManager.NameNotFoundException e) {
            packageInfo = null;
            e.printStackTrace();
        }
        return packageInfo != null;

    }

    /**
     * 手机振动
     *
     * @param context 上下文
     * @param time    振动时长
     */
    public static void vibrate(Context context, long time) {
        if (context == null) {
            return;
        }

        Vibrator vibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        if (vibrator == null) {
            return;
        }
        vibrator.vibrate(time);
    }


    /**
     * 过滤空值
     *
     * @param map
     */
    public static LinkedHashMap removeEmptyValue(LinkedHashMap map) {
        Set set = map.keySet();
        try {
            for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
                Object obj = (Object) iterator.next();
                Object value = (Object) map.get(obj);
                String str = (String) value;
                if (TextUtils.isEmpty(str)) {
                    iterator.remove();
                }
            }
        } catch (Exception e) {

        }
        return map;
    }

    /**
     * 将dip或dp值转换为px值，保证尺寸大小不变
     */

    public static int dip2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;

        return (int) (dipValue * scale + 0.5f);

    }

    /**
     * 将sp值转换为px值，保证文字大小不变
     */

    public static int sp2px(Context context, float spValue) {
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;

        return (int) (spValue * fontScale + 0.5f);

    }

    public static String encryptToSHA(String info) {
        byte[] digesta = null;
        try {
            MessageDigest alga = MessageDigest.getInstance("SHA-1");
            alga.update(info.getBytes());
            digesta = alga.digest();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        String rs = byte2hex(digesta);
        return rs;
    }

    public static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs;
    }

    public static boolean isControl(ETHWallet current) {
        if (current.getAddress().equalsIgnoreCase("******************************************")
                || current.getAddress().equalsIgnoreCase("******************************************")
                || current.getAddress().equalsIgnoreCase("******************************************")
                || current.getAddress().equalsIgnoreCase("******************************************")) {
            return true;
        }
        return false;
    }
}
