// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopPostMore_ViewBinding implements Unbinder {
  private PopPostMore target;

  private View view7f090329;

  private View view7f090300;

  @UiThread
  public PopPostMore_ViewBinding(PopPostMore target) {
    this(target, target);
  }

  @UiThread
  public PopPostMore_ViewBinding(final PopPostMore target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.tv_follow, "field 'mTvFollow' and method 'onBindClick'");
    target.mTvFollow = Utils.castView(view, R.id.tv_follow, "field 'mTvFollow'", TextView.class);
    view7f090329 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_Report, "field 'mTvReport' and method 'onBindClick'");
    target.mTvReport = Utils.castView(view, R.id.tv_Report, "field 'mTvReport'", TextView.class);
    view7f090300 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopPostMore target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvFollow = null;
    target.mTvReport = null;

    view7f090329.setOnClickListener(null);
    view7f090329 = null;
    view7f090300.setOnClickListener(null);
    view7f090300 = null;
  }
}
