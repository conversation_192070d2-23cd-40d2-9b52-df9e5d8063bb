// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.itemBean.cosmos.PostInfoItemView;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ContentInfoActivity_ViewBinding implements Unbinder {
  private ContentInfoActivity target;

  private View view7f090071;

  @UiThread
  public ContentInfoActivity_ViewBinding(ContentInfoActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public ContentInfoActivity_ViewBinding(final ContentInfoActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mPostLayout = Utils.findRequiredViewAsType(source, R.id.postLayout, "field 'mPostLayout'", PostInfoItemView.class);
    target.mTvCount = Utils.findRequiredViewAsType(source, R.id.tv_count, "field 'mTvCount'", TextView.class);
    target.mRvComment = Utils.findRequiredViewAsType(source, R.id.rv_comment, "field 'mRvComment'", RecyclerView.class);
    target.mScrollView = Utils.findRequiredViewAsType(source, R.id.scroll_view, "field 'mScrollView'", NestedScrollView.class);
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_user_heard, "field 'mIvHeard'", ImageView.class);
    target.mEdComment = Utils.findRequiredViewAsType(source, R.id.ed_Comment, "field 'mEdComment'", EditText.class);
    view = Utils.findRequiredView(source, R.id.btn_send_Comment, "field 'mBtnSendComment' and method 'onBindClick'");
    target.mBtnSendComment = Utils.castView(view, R.id.btn_send_Comment, "field 'mBtnSendComment'", TextView.class);
    view7f090071 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLlComment = Utils.findRequiredViewAsType(source, R.id.ll_comment, "field 'mLlComment'", RelativeLayout.class);
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    ContentInfoActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mPostLayout = null;
    target.mTvCount = null;
    target.mRvComment = null;
    target.mScrollView = null;
    target.mIvHeard = null;
    target.mEdComment = null;
    target.mBtnSendComment = null;
    target.mLlComment = null;
    target.mMain = null;

    view7f090071.setOnClickListener(null);
    view7f090071 = null;
  }
}
