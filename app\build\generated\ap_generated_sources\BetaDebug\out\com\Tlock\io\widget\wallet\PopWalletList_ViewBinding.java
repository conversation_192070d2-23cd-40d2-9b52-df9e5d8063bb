// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopWalletList_ViewBinding implements Unbinder {
  private PopWalletList target;

  private View view7f09013a;

  private View view7f090346;

  private View view7f090313;

  @UiThread
  public PopWalletList_ViewBinding(PopWalletList target) {
    this(target, target);
  }

  @UiThread
  public PopWalletList_ViewBinding(final PopWalletList target, View source) {
    this.target = target;

    View view;
    target.mRlTitle = Utils.findRequiredViewAsType(source, R.id.rl_title, "field 'mRlTitle'", RelativeLayout.class);
    target.mRvWallet = Utils.findRequiredViewAsType(source, R.id.rv_wallet, "field 'mRvWallet'", RecyclerView.class);
    view = Utils.findRequiredView(source, R.id.iv_close, "field 'mIvClose' and method 'onViewClicked'");
    target.mIvClose = Utils.castView(view, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    view7f09013a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_order, "field 'mTvOrder' and method 'onViewClicked'");
    target.mTvOrder = Utils.castView(view, R.id.tv_order, "field 'mTvOrder'", TextView.class);
    view7f090346 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mTvListTitle = Utils.findRequiredViewAsType(source, R.id.tv_list_title, "field 'mTvListTitle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_create, "method 'onViewClicked'");
    view7f090313 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopWalletList target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRlTitle = null;
    target.mRvWallet = null;
    target.mIvClose = null;
    target.mTvOrder = null;
    target.mTvListTitle = null;

    view7f09013a.setOnClickListener(null);
    view7f09013a = null;
    view7f090346.setOnClickListener(null);
    view7f090346 = null;
    view7f090313.setOnClickListener(null);
    view7f090313 = null;
  }
}
