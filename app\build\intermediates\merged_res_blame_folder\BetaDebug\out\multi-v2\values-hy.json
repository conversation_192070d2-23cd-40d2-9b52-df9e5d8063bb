{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,378,478,588,677,783,900,982,1062,1153,1246,1341,1435,1535,1628,1723,1817,1908,1999,2082,2188,2294,2393,2503,2611,2712,2882,8084", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "373,473,583,672,778,895,977,1057,1148,1241,1336,1430,1530,1623,1718,1812,1903,1994,2077,2183,2289,2388,2498,2606,2707,2877,2974,8162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,395,525,606,670,767,852,914,1001,1065,1126,1193,1254,1308,1430,1487,1547,1601,1682,1817,1901,1986,2092,2167,2242,2337,2404,2470,2544,2624,2710,2781,2857,2933,3010,3098,3178,3274,3370,3444,3522,3622,3673,3742,3829,3920,3982,4046,4109,4214,4315,4415,4520", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "215,295,390,520,601,665,762,847,909,996,1060,1121,1188,1249,1303,1425,1482,1542,1596,1677,1812,1896,1981,2087,2162,2237,2332,2399,2465,2539,2619,2705,2776,2852,2928,3005,3093,3173,3269,3365,3439,3517,3617,3668,3737,3824,3915,3977,4041,4104,4209,4310,4410,4515,4600"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2979,3779,3874,4004,4085,4149,4246,4331,4393,4480,4544,4605,4672,4733,4787,4909,4966,5026,5080,5161,5296,5380,5465,5571,5646,5721,5816,5883,5949,6023,6103,6189,6260,6336,6412,6489,6577,6657,6753,6849,6923,7001,7101,7152,7221,7308,7399,7461,7525,7588,7693,7794,7894,7999", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "265,3054,3869,3999,4080,4144,4241,4326,4388,4475,4539,4600,4667,4728,4782,4904,4961,5021,5075,5156,5291,5375,5460,5566,5641,5716,5811,5878,5944,6018,6098,6184,6255,6331,6407,6484,6572,6652,6748,6844,6918,6996,7096,7147,7216,7303,7394,7456,7520,7583,7688,7789,7889,7994,8079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3059,3159,3264,3362,3461,3566,3668,8167", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3154,3259,3357,3456,3561,3663,3774,8263"}}]}]}