package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.Editable;
import android.text.Spannable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.CommentItemView;
import com.Tlock.io.itemBean.cosmos.PostInfoItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.NewCosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.pop.PopPostForward;
import com.Tlock.io.widget.pop.PopPostMore;
import com.Tlock.io.widget.pop.PopPostShare;
import com.Tlock.io.widget.pop.PopWhaleListAttch;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BasePopupView;
import com.lxj.xpopup.impl.AttachListPopupView;
import com.lxj.xpopup.impl.LoadingPopupView;

import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.OnClick;

public class ContentInfoActivity extends BaseActivity {

    public PostQueryProto.PostResponse data;
    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.postLayout)
    PostInfoItemView mPostLayout;
    @BindView(R.id.tv_count)
    TextView mTvCount;
    @BindView(R.id.rv_comment)
    RecyclerView mRvComment;
    @BindView(R.id.scroll_view)
    NestedScrollView mScrollView;
    @BindView(R.id.iv_user_heard)
    ImageView mIvHeard;
    @BindView(R.id.ed_Comment)
    EditText mEdComment;
    @BindView(R.id.btn_send_Comment)
    TextView mBtnSendComment;
    @BindView(R.id.ll_comment)
    RelativeLayout mLlComment;
    @BindView(R.id.main)
    RelativeLayout mMain;
    private String id;
    private BaseRecyclerViewAdapter<PostQueryProto.CommentResponse> adapter;
    private ETHWallet current;
    private PostProto.Post post;
    private ArrayList<PostQueryProto.CommentResponse> commentList;
    private boolean isComment;
    private final boolean isEditing = false; // 防止重复触发
    private final Pattern mentionPattern = Pattern.compile("@\\w+");
    private final ArrayList<String> mentionsList = new ArrayList<>(); // 存储提及的 ArrayList
    private String previousText = "";
    private BasePopupView searchPop;
    private PopWhaleListAttch popWhaleList;
    private AttachListPopupView attachList;
    private boolean isDown = false;
    private LoadingPopupView loadingPopup;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String id, boolean isComment
    ) {
        Intent intent = new Intent(context, ContentInfoActivity.class);
        intent.putExtra("id", id);
        intent.putExtra("isComment", isComment);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_content_info;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        current = WalletDaoUtils.getCurrent();

//        loadingPopup = (LoadingPopupView) new XPopup.Builder(getActivity())
//                .dismissOnBackPressed(false)
//                .isLightNavigationBar(false)
//                .hasShadowBg(true)
//                .shadowBgColor(getResources().getColor(R.color.white))
//                .asLoading("Loading", LoadingPopupView.Style.ProgressBar)
//                .show();
        mScrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                View child = mScrollView.getChildAt(0);
                if (child == null) return;
                if (isDown) return;
                int childHeight = child.getHeight();
                int viewHeight = mScrollView.getHeight();

                if (scrollY >= (childHeight - viewHeight)) {
                    page++;
                    getCommentData();
                }
            }
        });

        initRecyclerView();
        setSearchData();
        id = getIntent().getStringExtra("id");
        isComment = getIntent().getBooleanExtra("isComment", false);
        mPostLayout.setCallback(new PostInfoItemView.Callback() {
            @Override
            public void quote() {
                PopPostForward popPostMore = new PopPostForward(getActivity(), data.getPost().getId(), "");
                popPostMore.setCallBack(new PopPostForward.CallBack() {
                    @Override
                    public void repost(String id) {
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                popPostMore.dismiss();
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.postRepost(WalletDaoUtils.getCurrent().getAddress(), id);
                                    }
                                });
                            }
                        });
                    }

                    @Override
                    public void quote(String id) {
                        popPostMore.dismiss();

                        PostQuoteActivity.start(getActivity(), id);
                    }
                });
                new XPopup.Builder(getActivity())
                        .hasShadowBg(true)
                        .isDestroyOnDismiss(true)
                        .asCustom(popPostMore).show();
            }

            @Override
            public void share() {
                PopPostShare popPostMore = new PopPostShare(getActivity());
                popPostMore.setCallBack(new PopPostShare.CallBack() {

                    @Override
                    public void follow(String handle) {

                    }

                    @Override
                    public void report(String id) {

                    }

                });
                new XPopup.Builder(getActivity())
                        .hasShadowBg(true)
                        .isDestroyOnDismiss(true)
                        .asCustom(popPostMore).show();

            }

            @Override
            public void review(String id, String handle) {
//                ContentInfoActivity.start(getActivity(), id, true);
                show(mEdComment);
            }


            @Override
            public void praise(String id) {
                //点赞
                if (current != null) {
                    showToast("Liked");
                    AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                        @Override
                        public void run() {
                            NewCosmosUtils.postLike(current.getAddress(), id);
                        }
                    });
                }

            }

            @Override
            public void collect(String id) {
                //收藏
                if (current != null) {
                    showToast("Added to bookmarks");
                    AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                        @Override
                        public void run() {
                            NewCosmosUtils.savePost(current.getAddress(), id);
                        }
                    });
                }
            }

            @Override
            public void more(String data, String handle, String address) {
                PopPostMore popPostMore = new PopPostMore(getActivity(), data, handle);
                popPostMore.setCallBack(new PopPostMore.CallBack() {
                    @Override
                    public void follow(String handle) {
                        showToast("Following");
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                CosmosUtils.follow(current.getAddress(), address);
                            }
                        });
                    }

                    @Override
                    public void report(String id) {
                        //TODO 新页面
                        showToast("Report");
                    }
                });
                new XPopup.Builder(getActivity())
                        .hasShadowBg(true)
                        .isDestroyOnDismiss(true)
                        .asCustom(popPostMore).show();
            }

            @Override
            public void info(PostQueryProto.PostResponse data) {
                ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
            }
        });

        mEdComment.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                previousText = charSequence.toString();
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable.toString().length() > 300) {
                    showToast("The content cannot exceed 300 characters");
                }
                if (editable.toString().isEmpty()) {
                    searchPop.dismiss();
                }
                if (editable.toString().endsWith(" ")) {
                    searchPop.dismiss();
                }
                if (isEditing) return;


                String currentText = editable.toString();
                int cursorPosition = mEdComment.getSelectionStart();

                // 1. 检测删除操作
                if (currentText.length() < previousText.length()) {
                    // 识别删除范围
                    int diff = previousText.length() - currentText.length();
                    // 确定删除的起始位置
                    int deleteStart = cursorPosition;
                    int deleteEnd = cursorPosition + diff;

                    // 获取删除后的文本中光标位置
                    // 分析删除的位置是否在话题或提及内
                    // 获取话题位置
                    ArrayList<int[]> mentionSpans = getAllSpans(currentText, mentionPattern);

                    boolean withinHashtag = false;
                    int targetHashtagStart = -1;
                    int targetHashtagEnd = -1;

                    boolean withinMention = false;
                    int targetMentionStart = -1;
                    int targetMentionEnd = -1;

                    if (!withinHashtag) { // 如果不在话题内，再检查提及
                        for (int[] span : mentionSpans) {
                            if (cursorPosition > span[0] && cursorPosition <= span[1]) {
                                withinMention = true;
                                targetMentionStart = span[0];
                                targetMentionEnd = span[1];
                                break;
                            }
                        }
                    }

                    if (withinHashtag) {
                        // 删除整个话题
                        Editable editableContent = mEdComment.getText();
//                        Log.e(TAG, "withinHashtag: " + targetHashtagStart + " " + targetHashtagEnd);
                        editableContent.delete(targetHashtagStart, targetHashtagEnd);
                        cursorPosition = targetHashtagStart;
                        mEdComment.setSelection(cursorPosition);
                        // 更新 currentText after deletion
                        currentText = editableContent.toString();
                    } else if (withinMention) {
                        // 删除整个提及
                        Editable editableContent = mEdComment.getText();
//                        Log.e(TAG, "withinMention: " + targetMentionStart + " " + targetMentionEnd);

                        editableContent.delete(targetMentionStart, targetMentionEnd);
                        cursorPosition = targetMentionStart;
                        mEdComment.setSelection(cursorPosition);

                        // 更新 currentText after deletion
                        currentText = editableContent.toString();
                    }
                }

                // 2. 自动在#和@前添加空格
                if (cursorPosition > 0 && (currentText.charAt(cursorPosition - 1) == '#' || currentText.charAt(cursorPosition - 1) == '@')) {
                    if (cursorPosition == 1 || currentText.charAt(cursorPosition - 2) != ' ') {
                        editable.replace(cursorPosition - 1, cursorPosition, " " + currentText.charAt(cursorPosition - 1));
                        cursorPosition += 1; // 调整光标位置
                        mEdComment.setSelection(cursorPosition);
                        currentText = editable.toString(); // 更新文本内容
                    }
                }

                // 3. 进行颜色高亮并收集话题和提及
                Matcher mentionMatcher = mentionPattern.matcher(currentText);
                ArrayList<String> currentMentions = new ArrayList<>();
                // 处理提及
                while (mentionMatcher.find()) {
                    String mention = mentionMatcher.group(); // 获取匹配的 @标签
                    currentMentions.add(mention.replace("@", "").trim()); // 添加到当前提及列表

                    // 设置字体颜色为绿色
                    editable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.cosmos_topic)),
                            mentionMatcher.start(),
                            mentionMatcher.end(),
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                // 4. 更新 EditText 内容

                try {
                    mEdComment.setSelection(cursorPosition); // 保持光标位置
                    String str = mEdComment.getText().toString();
                    int i = str.lastIndexOf(' ', cursorPosition - 1);
                    String substring = str.substring(i, cursorPosition);
//                    Log.e(TAG, "这是截取的内容: " + substring + "   cursorPosition  " + cursorPosition + " i  " + i);

                    if (substring.contains("@")) {
                        getAtData(substring.replace("@", "").trim());
                    }
                } catch (IndexOutOfBoundsException e) {
                    // 处理可能的异常，如光标位置超出范围
//                    mEdContent.setSelection(spannable.length());
                }

                // 5. 更新话题和提及列表
                updateLists(currentMentions);

                previousText = currentText; // 更新前一个文本状态


            }
        });
        try {
            String userInfo = SpUtil.getUserInfo();
            ProfileProto.Profile profile = JsonUtils.jsonToObject(userInfo, ProfileProto.Profile.class);
            if (TextUtils.isEmpty(profile.getAvatar())) {
                TextAvatarDrawable a = new TextAvatarDrawable(profile.getUserHandle().substring(0, 1));
                mIvHeard.setImageDrawable(a);
            } else {
                if (profile.getAvatar().startsWith("http")) {
                    Glide.with(getActivity()).load(profile.getAvatar()).apply(new RequestOptions()
                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                    .signature(new ObjectKey(profile.getAvatar()))
                                    .centerCrop()
                                    .format(DecodeFormat.PREFER_RGB_565)
                                    .dontTransform())
                            .apply(RequestOptions.circleCropTransform().circleCrop())
                            .into(mIvHeard);
                } else {
                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(profile.getAvatar());
                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                    .centerCrop()
                                    .dontTransform())
                            .apply(RequestOptions.circleCropTransform().circleCrop())
                            .into(mIvHeard);
                }
            }
        } catch (Exception e) {
//            Log.e(TAG, "initView: " + e.getMessage());
        }
    }

    private void setSearchData() {
        popWhaleList = new PopWhaleListAttch(getActivity(), new ArrayList<>());
        popWhaleList.setCallBack(new PopWhaleListAttch.CallBack() {
            @Override
            public void change(ProfileProto.Profile data) {
                int cursorPosition = mEdComment.getSelectionStart();
                String str = mEdComment.getText().toString();
                int i = str.lastIndexOf(' ', cursorPosition - 1);
                String old_sub = str.substring(i + 2, cursorPosition);
//                Log.e(TAG, "substring:" + old_sub);
                replaceTag(data.getUserHandle());
                mEdComment.requestFocus();
            }
        });
        searchPop = new XPopup.Builder(getActivity())
                .atView(mLlComment)
                .dismissOnTouchOutside(false)
                .isViewMode(true)      //开启View实现
                .isRequestFocus(false) //不强制焦点
                .hasShadowBg(false)
                .hasBlurBg(false)
                .positionByWindowCenter(true)
                .asCustom(popWhaleList);

    }

    private void getAtData(String math) {
//        Log.e(TAG, "math: " + math);
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<ProfileProto.Profile> userList = new ArrayList<>();
                userList = CosmosUtils.getAtUserList(math);
                if (userList.isEmpty()) {
                    return;
                }

                ArrayList<ProfileProto.Profile> finalUserList = userList;
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (searchPop.isDismiss()) {
                            searchPop.show();
                        }
//                        Log.e(TAG, "run: " + JsonUtils.listToJson(finalUserList));
                        popWhaleList.setBeans(finalUserList);
//                        searchPop.show();
                    }
                });
            }
        });
    }

    public void replaceTag(String selectedText) {
        Editable editable = mEdComment.getText();
        if (editable == null) return;

        int cursorPos = mEdComment.getSelectionStart();
        if (cursorPos <= 0) return;

        // 找到光标前的最近一个@或#
        int lastAt = editable.toString().lastIndexOf('@', cursorPos - 1);
        int lastHash = editable.toString().lastIndexOf('#', cursorPos - 1);
        int tagPos = Math.max(lastAt, lastHash);
        if (tagPos == -1) return;

        char symbol = editable.charAt(tagPos);
        if (symbol != '@' && symbol != '#') return;

        // 找到@或#后的空格或文本末尾
        cursorPos = editable.toString().indexOf(' ', tagPos);
        if (cursorPos == -1) {
            cursorPos = editable.length();
        }

        // 删除旧的内容
        editable.delete(tagPos + 1, cursorPos);

        // 插入新的内容和一个空格
        editable.insert(tagPos + 1, selectedText + " ");
        int a = tagPos + 1 + selectedText.length() + 1;
//        Log.e(TAG, "replaceTag: " + a);
        // 移动光标到新插入内容的末尾
        mEdComment.setSelection(a);


    }

    /**
     * 获取所有匹配的标记的起始和结束索引
     *
     * @param text    当前文本
     * @param pattern 正则表达式模式
     * @return ArrayList<int [ ]> 每个 int[] 包含 [start, end] 索引
     */
    private ArrayList<int[]> getAllSpans(String text, Pattern pattern) {
        ArrayList<int[]> spans = new ArrayList<>();
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            spans.add(new int[]{matcher.start(), matcher.end()});
        }
        return spans;
    }

    /**
     * 更新话题和提及列表并显示
     *
     * @param currentMentions 当前文本中的提及列表
     */
    private void updateLists(ArrayList<String> currentMentions) {
        // 更新提及列表
        mentionsList.clear();
        mentionsList.addAll(currentMentions);

    }

    private void initRecyclerView() {
        mRvComment.setNestedScrollingEnabled(false);
        mRvComment.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.CommentResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                CommentItemView itemView = new CommentItemView(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.CommentResponse data, View view) {
                CommentItemView commentItemView = ((CommentItemView) view);
                commentItemView.setData(data, position, 1);
                commentItemView.setCallback(new CommentItemView.Callback() {

                    @Override
                    public void share() {

                    }

                    @Override
                    public void review(String id, String handle) {
                        ContentInfoActivity.this.id = id;
                        show(mEdComment);
                        mEdComment.setText("");
                        mEdComment.setHint("Replay  @" + handle);
                    }


                    @Override
                    public void praise(String id) {
                        showToast("Liked");

                        if (current != null) {
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    CosmosUtils.postLike(current.getAddress(), id);
                                }
                            });
                        }
                    }

                    @Override
                    public void collect(String id) {
                        showToast("Added to bookmarks");
                        if (current != null) {
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    CosmosUtils.savePost(current.getAddress(), id);
                                }
                            });
                        }
                    }

                    @Override
                    public void info(PostQueryProto.CommentResponse data) {

                    }


                    @Override
                    public void showMore(String id, int position, int layer) {
                        ContentInfoActivity.this.id = id;
                        getCommentChild(commentItemView, position, layer);
                        commentItemView.setHide();
                    }

                });

            }
        });
        mRvComment.setAdapter(adapter);
        //点击切换

    }

    @Override
    protected void loadData() {
        getCommentData();

    }

    /**
     * 获取评论数据
     */
    public void getCommentChild(CommentItemView view, int parentPosition, int layer) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                commentList = CosmosUtils.getCommentsById(id, page);
                PostQueryProto.PostResponse postResponse = CosmosUtils.queryPost(id);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ArrayList<PostQueryProto.CommentResponse> commentList1 = view.getCommentList();
                        if (commentList1.size() == 0) {
                            commentList1.addAll(commentList);
                        } else {
                            commentList1.addAll(parentPosition + 1, commentList);
                        }
                        view.setCommentList(commentList1, parentPosition, layer);

                    }
                });
            }
        });
    }

    public void getCommentData() {

        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                commentList = CosmosUtils.getCommentsById(id, page);
                isDown = commentList.size() < 10;
                data = CosmosUtils.queryPost(id);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.addListNoChange(commentList, page);
                        mPostLayout.setData(data);
                        if (adapter.getList().size() == 0) {
                            mTvCount.setVisibility(View.GONE);
                        } else {
                            mTvCount.setText(adapter.getList().size() + " comments");
                            mTvCount.setVisibility(View.VISIBLE);
                        }
                        if (isComment) {
                            mScrollView.post(new Runnable() {
                                @Override
                                public void run() {
                                    isComment = false;
                                    mScrollView.smoothScrollTo(0, mTvCount.getTop());
                                }
                            });
                        }
                    }
                });
            }
        });
    }


    @OnClick({R.id.btn_send_Comment})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.btn_send_Comment:
                if (current != null) {
                    PostProto.Post post1 = PostProto.Post.newBuilder()
                            .setTimestamp(System.currentTimeMillis() / 1000)
                            .setCreator(current.getAddress())
                            .setContent(mEdComment.getText().toString()).build();
                    String userInfo = SpUtil.getUserInfo();
                    ProfileProto.Profile profile = JsonUtils.jsonToObject(userInfo, ProfileProto.Profile.class);
                    PostQueryProto.CommentResponse.Builder builder = PostQueryProto.CommentResponse.newBuilder()
                            .setPost(post1)
                            .setProfile(profile);
                    PostQueryProto.CommentResponse build = builder.build();
                    ArrayList<PostQueryProto.CommentResponse> list = adapter.getList();
                    list.add(0, build);
                    adapter.setList(list);
                    String string = mEdComment.getText().toString();
                    hideKeyboard();
                    mEdComment.setText("");
                    AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                        @Override
                        public void run() {
                            CosmosUtils.postComment(string, current.getAddress(), id, mentionsList);
                            mEdComment.post(new Runnable() {
                                @Override
                                public void run() {
                                    showToast("Comment success");
                                }
                            });
                        }
                    });
                }
                break;
        }
    }

    public void hideKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }

    }


    public void show(EditText editText) {
        editText.setFocusable(true);
        editText.setFocusableInTouchMode(true);
        editText.requestFocus();
        InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
        }
    }
}