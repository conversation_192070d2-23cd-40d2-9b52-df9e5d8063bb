package com.Tlock.io.fragment;


import android.os.Bundle;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.fragment.cosmos.HotTopicFragment;
import com.Tlock.io.post.PostQueryProto;
import com.cy.tablayoutniubility.FragPageAdapterVp2;
import com.cy.tablayoutniubility.TabAdapter;
import com.cy.tablayoutniubility.TabLayoutScroll;
import com.cy.tablayoutniubility.TabMediatorVp2;
import com.cy.tablayoutniubility.TabViewHolder;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class HotFragment extends LazyLoadBaseFragment {


    @BindView(R.id.tablayout)
    TabLayoutScroll tabLayoutLine;
    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;
    @BindView(R.id.root)
    RelativeLayout mRoot;

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_hot;
    }


    private BaseRecyclerViewAdapter<PostQueryProto.PostResponse> adapter;
    private ETHWallet current;


    @Override
    protected void initView(Bundle savedInstanceState) {
        //初始化dex列表
        FragPageAdapterVp2<String> fragmentPageAdapter = new FragPageAdapterVp2<String>(this) {

            @Override
            public Fragment createFragment(String bean, int position) {
                return new HotTopicFragment(position);
            }

            @Override
            public void bindDataToTab(TabViewHolder holder, int position, String bean, boolean isSelected) {
                TextView textView = holder.getView(R.id.tv);
                if (isSelected) {
                    textView.setTextColor(getResources().getColor(R.color.cosmos_black));
                } else {
                    textView.setTextColor(getResources().getColor(R.color.cosmos_default));
                }
                textView.setText(bean);
            }

            @Override
            public int getTabLayoutID(int position, String bean) {
                return R.layout.item_home_tab;
            }
        };
        TabAdapter<String> tabAdapter = new TabMediatorVp2<String>(tabLayoutLine, viewPager2).setAdapter(fragmentPageAdapter);

        List<String> list = new ArrayList<>();
        list.add("Topics");
        list.add("Keywords");
        fragmentPageAdapter.add(list);
        tabAdapter.add(list);
    }


    @Override
    protected void loadData() {
    }

    @Override
    protected void getData() {
        super.getData();
    }

}
