{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,309,413,530,611,677,768,834,895,985,1052,1113,1182,1244,1298,1405,1464,1525,1579,1653,1773,1858,1942,2047,2118,2188,2275,2342,2408,2481,2561,2656,2725,2801,2881,2950,3045,3128,3218,3313,3387,3461,3554,3608,3675,3761,3846,3908,3972,4035,4137,4242,4335,4441", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,103,116,80,65,90,65,60,89,66,60,68,61,53,106,58,60,53,73,119,84,83,104,70,69,86,66,65,72,79,94,68,75,79,68,94,82,89,94,73,73,92,53,66,85,84,61,63,62,101,104,92,105,79", "endOffsets": "221,304,408,525,606,672,763,829,890,980,1047,1108,1177,1239,1293,1400,1459,1520,1574,1648,1768,1853,1937,2042,2113,2183,2270,2337,2403,2476,2556,2651,2720,2796,2876,2945,3040,3123,3213,3308,3382,3456,3549,3603,3670,3756,3841,3903,3967,4030,4132,4237,4330,4436,4516"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3006,3818,3922,4039,4120,4186,4277,4343,4404,4494,4561,4622,4691,4753,4807,4914,4973,5034,5088,5162,5282,5367,5451,5556,5627,5697,5784,5851,5917,5990,6070,6165,6234,6310,6390,6459,6554,6637,6727,6822,6896,6970,7063,7117,7184,7270,7355,7417,7481,7544,7646,7751,7844,7950", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,82,103,116,80,65,90,65,60,89,66,60,68,61,53,106,58,60,53,73,119,84,83,104,70,69,86,66,65,72,79,94,68,75,79,68,94,82,89,94,73,73,92,53,66,85,84,61,63,62,101,104,92,105,79", "endOffsets": "271,3084,3917,4034,4115,4181,4272,4338,4399,4489,4556,4617,4686,4748,4802,4909,4968,5029,5083,5157,5277,5362,5446,5551,5622,5692,5779,5846,5912,5985,6065,6160,6229,6305,6385,6454,6549,6632,6722,6817,6891,6965,7058,7112,7179,7265,7350,7412,7476,7539,7641,7746,7839,7945,8025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3089,3188,3290,3392,3495,3596,3698,8117", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3183,3285,3387,3490,3591,3693,3813,8213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,490,596,685,790,911,994,1076,1167,1260,1354,1448,1548,1641,1736,1830,1921,2012,2098,2208,2312,2415,2523,2631,2736,2901,8030", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "379,485,591,680,785,906,989,1071,1162,1255,1349,1443,1543,1636,1731,1825,1916,2007,2093,2203,2307,2410,2518,2626,2731,2896,3001,8112"}}]}]}