package com.Tlock.io.itemBean.wallet;

import android.content.Context;
import android.view.Gravity;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;

import butterknife.BindView;

/**
 * @ClassName MnemonicViewBean
 * <AUTHOR>
 * @Data 2021/11/10 17:21
 * @Desc
 */

public class MnemonicViewBean extends BaseView {
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.rl_root)
    RelativeLayout mRlRoot;
    @BindView(R.id.tv_index)
    TextView mTvIndex;
    private boolean isSelected = false;

    public MnemonicViewBean(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_mnemonic;
    }

    public void setData(String world, int index, int type) {
        mTvTitle.setText(world);
        mTvIndex.setText(index + "");
        if (type != 0){
            mRlRoot.setBackground(getResources().getDrawable(R.color.colorPrimaryDark));
            mTvIndex.setVisibility(VISIBLE);
            mTvTitle.setGravity(Gravity.LEFT);
        }
    }

    public void setSelected(boolean isSelected) {
        mRlRoot.setBackground(isSelected ? getResources().getDrawable(R.drawable.shape_deep_gray_line_6) : getResources().getDrawable(R.drawable.shape_deep_gray_6));
    }

    public boolean getSelect() {
        return isSelected;
    }

    public boolean click() {
        mRlRoot.setBackground(!isSelected ? getResources().getDrawable(R.drawable.shape_deep_gray_line_6) : getResources().getDrawable(R.drawable.shape_deep_gray_6));
        isSelected = !isSelected;
        return isSelected;
    }

}
