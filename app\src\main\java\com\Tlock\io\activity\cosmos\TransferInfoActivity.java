package com.Tlock.io.activity.cosmos;

import static com.Tlock.io.utils.cosmos.CosmosUtils.BASE_DENOM;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.Transfer;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.DateUtil;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.NewCosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.cosmos.base.v1beta1.CoinProto;
import com.cosmos.tx.v1beta1.TxProto;
import com.google.protobuf.Any;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class TransferInfoActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_send)
    ImageView mIvSend;
    @BindView(R.id.tv1)
    TextView mTv1;
    @BindView(R.id.tv_count)
    TextView mTvCount;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.tv_gas_title)
    TextView mTvGasTitle;
    @BindView(R.id.tv_gas)
    TextView mTvGas;
    @BindView(R.id.tv_From_title)
    TextView mTvFromTitle;
    @BindView(R.id.tv_from)
    TextView mTvFrom;
    @BindView(R.id.iv_from)
    ImageView mIvFrom;
    @BindView(R.id.tv_to_title)
    TextView mTvToTitle;
    @BindView(R.id.tv_to)
    TextView mTvTo;
    @BindView(R.id.iv_to)
    ImageView mIvTo;
    @BindView(R.id.tv_cancel)
    TextView mTvCancel;
    @BindView(R.id.tv_confirm)
    TextView mTvConfirm;
    @BindView(R.id.ll_btn)
    LinearLayout mLlBtn;
    @BindView(R.id.tv_hash_title)
    TextView mTvHashTitle;
    @BindView(R.id.tv_hash)
    TextView mTvHash;
    @BindView(R.id.iv_hash)
    ImageView mIvHash;
    @BindView(R.id.tv_time)
    TextView mTvTime;

    private ETHWallet current;
    private int from;
    private Transfer transfer;
    private com.cosmos.bank.v1beta1.TxProto.MsgSend build;

    /**
     * @param context
     * @param from    1转账 ,2详情
     */
    public static void start(Context context, int from, String data) {
        Intent intent = new Intent(context, TransferInfoActivity.class);
        intent.putExtra("from", from);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_transfer_info;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        from = getIntent().getIntExtra("from", 1);
        current = WalletDaoUtils.getCurrent();
        String data = getIntent().getStringExtra("data");
        transfer = JsonUtils.jsonToObject(data, Transfer.class);
        mTvCount.setText(BigDecimalUtils.saveDecimals(BigDecimalUtils.uTok2Tok(transfer.getCount()),2) + " TOK");
        mTvFrom.setText(transfer.getPayAddress());
        mTvTo.setText(transfer.getGetAddress());

        if (from == 2) {
            //详情
            mIvHash.setVisibility(View.VISIBLE);
            mTvHash.setVisibility(View.VISIBLE);
            mTvHashTitle.setVisibility(View.VISIBLE);
            mTvHash.setText(transfer.getTransferHash());
            mLlBtn.setVisibility(View.GONE);
            if (transfer.getGasLimit() != null){
                String string1 = BigDecimalUtils.uTok2Tok(transfer.getGasLimit());
                String string = BigDecimalUtils.save4Valid(string1, 4);
                mTvGas.setText(string + " TOK");
            }else{
                mTvGas.setText( "-- TOK");
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mTvTime.setText(DateUtil.formatDate(Long.parseLong(transfer.getTime()), "yyyy/MM/dd HH:mm:ss"));
            }
        }

    }


    @Override
    protected void loadData() {
        params.clear();
        if (from == 1) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    com.cosmos.bank.v1beta1.TxProto.MsgSend.Builder builder = com.cosmos.bank.v1beta1.TxProto.MsgSend.newBuilder();
                    builder.setFromAddress(transfer.getPayAddress());
                    CoinProto.Coin coin = CoinProto.Coin.newBuilder().setAmount(transfer.getCount()).setDenom(BASE_DENOM).build();
                    builder.addAmount(coin);
                    builder.setToAddress(transfer.getGetAddress());
                    build = builder.build();
                    List<Any> anyList = new ArrayList<>();
                    anyList.add(
                            Any.newBuilder().setTypeUrl("/cosmos.bank.v1beta1.MsgSend")
                                    .setValue(build.toByteString()).build()
                    );
                    TxProto.Fee initFee = CosmosUtils.getFee(anyList);
                    long gasLimit = initFee.getGasLimit();
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mTvGas.setText(gasLimit + " " + BASE_DENOM);
                            transfer.setGasLimit(gasLimit + "");
                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.iv_from, R.id.iv_to, R.id.tv_cancel, R.id.iv_hash, R.id.tv_confirm})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_from:
                CopyUtils.copyToClipboard(mTvFrom.getText().toString());
                break;
            case R.id.iv_to:
                CopyUtils.copyToClipboard(mTvTo.getText().toString());
                break;
            case R.id.iv_hash:
                TransactionInfoActivity.start(getActivity(), transfer.getTransferHash());
                break;
            case R.id.tv_cancel:
                finish();
                break;
            case R.id.tv_confirm:
                finish();
                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                    @Override
                    public void run() {
                        String str_hash = NewCosmosUtils.sendBase(build);
                        //保存交易记录
                        transfer.setTransferHash(str_hash);
                        transfer.setTime(System.currentTimeMillis() + "");
                        transfer.setPayStatus(2);
                        WalletDaoUtils.insertNewTransfer(transfer);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtil.toastView("Send success");
                            }
                        });
                    }
                });


                break;
        }
    }
}