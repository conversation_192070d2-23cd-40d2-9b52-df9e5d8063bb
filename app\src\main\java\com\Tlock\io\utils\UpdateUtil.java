package com.Tlock.io.utils;


import android.annotation.TargetApi;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import com.Tlock.io.R;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.VersionBean;
import com.Tlock.io.manger.ActivitiesManager;
import com.Tlock.io.widget.NumberProgressView;
import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadListener;
import com.liulishuo.filedownloader.FileDownloader;

import org.greenrobot.eventbus.EventBus;

import java.io.File;


/**
 * 本类的主要功能是 :  更新
 *  注意:ActivitiesManager.getInstance().pushActivity(this);
 */
public class UpdateUtil implements DialogInterface.OnKeyListener {
    private Context mContext;
    private static final String TAG = "UpdateUtil";
    private Dialog dialog_update;
    private Dialog dialog_warn;
    private Dialog dialog_updateing;
    private VersionBean updateInfoBean;


    @Override
    public boolean onKey(DialogInterface dialogInterface, int i, KeyEvent keyEvent) {
        if (i == KeyEvent.KEYCODE_BACK) {
            if (dialog_update == dialogInterface) {

                if (updateInfoBean.isRequireUpdate()) {
                    //强制
                    Toast.makeText(mContext, "更新到最新版,才能正常使用", Toast.LENGTH_SHORT).show();
                    if (dialog_update != null) {
                        dialog_update.dismiss();
                    }
                    ActivitiesManager.getInstance().popAllActivity();
                } else {
                    if (dialog_update != null && dialog_update.isShowing()) {
                        dialog_update.dismiss();
                    }
                    //非强制更新情况下返回取消监听
                    if (cancelClickListener != null) {
                        cancelClickListener.onCancelClick(dialog_update, updateInfoBean);
                    }
                }

                // ((Activity) mContext).finish();
            } else if (dialog_updateing == dialogInterface) {
                Toast.makeText(mContext, "正在下载无法退出", Toast.LENGTH_SHORT).show();
            }
            return true;
        }
        return false;

    }

    public UpdateUtil(Context mContext) {
        this.mContext = mContext;
    }

    //    public static final String ACCOUNT_DIR = MyApplication.getInstance().getApplicationContext().getExternalCacheDir().getAbsolutePath();
//    public static final String APK_PATH = "apk_cache/";

//    public static final String ACCOUNT_DIR = MyApplication.getInstance().getPath();
//    public static final String APK_PATH = "apk_cache/";

    //    File f = new File(ACCOUNT_DIR + APK_PATH);
//    File downloadFile = new File(f.getAbsoluteFile() + File.separator + "temp.apk");
    File dir = null;//下载目录

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public void upDateApp(final VersionBean updateInfoBean) {
        this.updateInfoBean = updateInfoBean;
        if (updateInfoBean == null) {
            return;
        }
        if (TextUtils.isEmpty(updateInfoBean.getAppUrl())) {
            return;
        }
        //        获取存储位置的路径
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
//            dir = App.getInstance().getContext().getExternalCacheDir();
            String path = FilePathUtil.getAppExternalPublicDir(FilePathUtil.APK_DIR);
            if (path == null) {

                Toast.makeText(mContext,
                        R.string.no_sdcard, Toast.LENGTH_LONG).show();
                return;
            }
            dir = new File(path);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            Log.e(TAG, "upDateApp: " + path);
        } else {
            Toast.makeText(mContext,
                    R.string.no_sdcard, Toast.LENGTH_LONG).show();
            return;
        }
        //先清除缓存目录下的文件
        clearAPPCache();

        if (!dir.exists()) {
            dir.mkdirs();
        }
        dialog_update = new Dialog(mContext, R.style.update_dialog);
        dialog_update.setOnKeyListener(this);
        View view = LayoutInflater.from(mContext).inflate(R.layout.updatedialog, null);
        dialog_update.setContentView(view);
        TextView tv_queding = (TextView) view.findViewById(R.id.tv_queding);
        final TextView tv_quxiao = (TextView) view.findViewById(R.id.tv_quxiao);
        TextView tv_pop_normal_content = (TextView) view.findViewById(R.id.tv_pop_normal_content);
        if (!TextUtils.isEmpty(updateInfoBean.getAppDesc()))
            tv_pop_normal_content.setText(updateInfoBean.getAppDesc());

        tv_queding.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // 先判断有没有权限
                    if (!Environment.isExternalStorageManager()) {
                        EventBus.getDefault().post(new Event(EventConstant.UPDATA_SETTING));
                        return;
                    }
                }
                //是wifi链接
                if (NetworkUtils.isWifiConnected(mContext)) {
                    gotoUpdate(updateInfoBean);
                    return;
                }
                dialog_warn = new Dialog(mContext, R.style.update_dialog);
                View view_warming = LayoutInflater.from(mContext).inflate(R.layout.netwarmingdlg, null);
                dialog_warn.setContentView(view_warming);
                TextView tv_warn_cancle = (TextView) view_warming.findViewById(R.id.tv_warn_cancle);
                TextView tv_warn_continue = (TextView) view_warming.findViewById(R.id.tv_warn_continue);
                tv_warn_cancle.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        dialog_warn.dismiss();
                    }
                });
                tv_warn_continue.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        dialog_warn.dismiss();
                        gotoUpdate(updateInfoBean);
                    }
                });
                dialog_warn.show();

            }
        });

        tv_quxiao.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (updateInfoBean.isRequireUpdate()) {
                    //强制
                    Toast.makeText(mContext, "更新到最新版,才能正常使用", Toast.LENGTH_SHORT).show();
                    if (dialog_update != null) {
                        dialog_update.dismiss();
                    }
                    ActivitiesManager.getInstance().popAllActivity();

                } else {
                    if (dialog_update != null && dialog_update.isShowing()) {
                        dialog_update.dismiss();
                    }
                    //非强制更新情况下取消监听
                    if (cancelClickListener != null) {
                        cancelClickListener.onCancelClick(dialog_update, updateInfoBean);
                    }
                }

            }
        });

        dialog_update.setCancelable(false);
        dialog_update.show();
    }

    public void gotoUpdate(final VersionBean updateInfoBean) {

        File file = new File("/storage/emulated/0/download/com.tlock.io");
        final File downloadFile = new File(file,"tlock" + ".apk");


        final String url = updateInfoBean.getAppUrl();
        dialog_update.dismiss();
        dialog_updateing = new Dialog(mContext, R.style.update_dialog);
        View view_update = LayoutInflater.from(mContext).inflate(R.layout.progressdlg, null);
        dialog_updateing.setContentView(view_update);
        final NumberProgressView progressBar = (NumberProgressView) view_update.findViewById(R.id.pb_update);
//        progressBar.setMax(100);
        progressBar.setProgress(0);
        final TextView tv_status = (TextView) view_update.findViewById(R.id.tv_status);

        dialog_updateing.setCanceledOnTouchOutside(false);
        dialog_updateing.setOnKeyListener(this);
        dialog_updateing.show();
        FileDownloader.getImpl().create(url)
                .setPath(downloadFile.getAbsolutePath(), false)
                .setListener(new FileDownloadListener() {
                    @Override
                    protected void pending(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                        tv_status.setText("连接中...");

                    }

                    @Override
                    protected void connected(BaseDownloadTask task, String etag, boolean isContinue, int soFarBytes, int totalBytes) {
                        Log.i(TAG, "connected: "+etag);
                    }

                    @Override
                    protected void progress(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                        tv_status.setText("下载中...");
                        progressBar.setProgress((int) (((float) soFarBytes / totalBytes) * 100));
                        int width = progressBar.getWidth();
                        RelativeLayout.LayoutParams layoutParams1 = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                        layoutParams1.setMargins(width*((int) (((float) soFarBytes / totalBytes) )),0, 0, 5);
                    }

                    @Override
                    protected void blockComplete(BaseDownloadTask task) {
                        Log.i(TAG, "blockComplete: ");
                    }

                    @Override
                    protected void retry(final BaseDownloadTask task, final Throwable ex, final int retryingTimes, final int soFarBytes) {
                        Log.i(TAG, "blockComplete: ");
                    }

                    @Override
                    protected void completed(BaseDownloadTask task) {
                        tv_status.setText("下载完成");
                        progressBar.setProgress(100);
                        dialog_updateing.dismiss();
                        if (downloadFile.exists()) {

                            //适配 暂时用不到 因为在清单文件配置了

//                            if (Build.VERSION.SDK_INT >= 26) {
//                                boolean b = mContext.getPackageManager().canRequestPackageInstalls();
//                                if (b) {
//                                    Intent intent = new Intent(Intent.ACTION_VIEW);
//                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                                    intent.setDataAndType(Uri.fromFile(downloadFile),
//                                            "application/vnd.android.package-archive");
//                                    mContext.startActivity(intent);//安装应用的逻辑(写自己的就可以)
//                                } else {
//                                            //请求安装未知应用来源的权限
//                                    ActivityCompat.requestPermissions((Activity) mContext, new String[]{ Manifest.permission.REQUEST_INSTALL_PACKAGES },12);
//                                }
//                            } else {
//                                Intent intent = new Intent(Intent.ACTION_VIEW);
//                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                                intent.setDataAndType(Uri.fromFile(downloadFile),
//                                        "application/vnd.android.package-archive");
//                                mContext.startActivity(intent);
//                            }


                            Intent intent = new Intent(Intent.ACTION_VIEW);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            if (Build.VERSION.SDK_INT >= 24) {
                                //判读版本是否在7.0以上
                                //参数1 上下文, 参数2 Provider主机地址 和配置文件中保持一致   参数3  共享的文件
                                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                                Uri apkUri = FileProvider.getUriForFile(mContext, "com.tlock.io.fileProvider", downloadFile);
                                //添加这一句表示对目标应用临时授权该Uri所代表的文件
                                intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
                            } else {
                                intent.setDataAndType(Uri.fromFile(downloadFile),
                                        "application/vnd.android.package-archive");
                            }
                            mContext.startActivity(intent);
                            ActivitiesManager.getInstance().popAllActivity();
                        }
                    }

                    @Override
                    protected void paused(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void error(BaseDownloadTask task, Throwable e) {
                        dialog_updateing.dismiss();
                        dialog_update.show();
                        Log.i(TAG, "error: "+e.getMessage());
                        Toast.makeText(mContext, "更新出错: "+e.getMessage(), Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    protected void warn(BaseDownloadTask task) {
                    }
                }).start();
    }

    public static String getPrintSize(int size) {
        java.text.DecimalFormat df = new java.text.DecimalFormat("0.00");
        double size1 = Double.valueOf(size);
        //如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (size1 < 1024) {
            return size + "B";
        } else {
            size1 = size1 / 1024;
        }

        if (size1 < 1024) {
            return df.format(size1) + "KB";
        } else {
            size1 = size1 / 1024;
        }


        if (size1 < 1024) {
            //因为如果以MB为单位的话，要保留最后1位小数，
            //因此，把此数乘以100之后再取余
            Log.e("==》", size1 + "");
            return df.format(size1) + "MB";
        } else {
            //否则如果要以GB为单位的，先除于1024再作同样的处理
            size1 = size1 * 100 / 1024;
            return size1 / 100 + "."
                    + size1 % 100 + "GB";
        }
    }

    public static void delFolder(String folderPath) {
        try {
            delAllFile(folderPath); // 删除完里面所有内容
            String filePath = folderPath;
            filePath = filePath;
            File myFilePath = new File(filePath);
            myFilePath.delete(); // 删除空文件夹
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void clearAPPCache() {
        try {
            // 1、清除APP cache目录下的内容
//            File appCacheDir = mContext.getCacheDir();
//            delFolder(appCacheDir.getAbsolutePath());
            if (Environment.MEDIA_MOUNTED.equals(Environment
                    .getExternalStorageState())
                    || !Environment.isExternalStorageRemovable()) {
                //Environment.isExternalStorageRemovable()，通过此方法我们可以知道手机上的存储卡是手机自带的还是外边可插拔的SD卡
                // 2、清除APP外部cache目录下的内容
//                File appExCacheDir = mContext.getExternalCacheDir();
//                if (appExCacheDir != null) {
//                    delFolder(appExCacheDir.getAbsolutePath());
//                }
                // 3、清除APP在外部自定义的缓存目录
                File file = new File("/storage/emulated/0/download/com.TLK.io");
                if (file != null) {
                    delFolder(file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        }
        if (!file.isDirectory()) {
            return flag;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {
            if (path.endsWith(File.separator)) {
                temp = new File(path + tempList[i]);
            } else {
                temp = new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                temp.delete();
            }
            if (temp.isDirectory()) {
                delAllFile(path + File.separator + tempList[i]);// 先删除文件夹里面的文件
                delFolder(path + File.separator + tempList[i]);// 再删除空文件夹
                flag = true;
            }
        }
        return flag;
    }

    private OnCancelClickListener cancelClickListener;

    public void setOnCancelClickListener(OnCancelClickListener cancelClickListener) {
        this.cancelClickListener = cancelClickListener;
    }

    /**
     * Listener that is called when this popup window is dismissed.
     */
    public interface OnCancelClickListener {
        /**
         * 非强制更新,点击取消更新按钮的监听
         */
        void onCancelClick(Dialog dialog_update, VersionBean updateInfoBean);
    }
}
