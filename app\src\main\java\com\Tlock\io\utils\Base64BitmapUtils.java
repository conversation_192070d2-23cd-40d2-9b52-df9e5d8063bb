package com.Tlock.io.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Base64与Bitmap转换工具类
 * 所有转换操作都在子线程中执行，避免阻塞主线程
 * 
 * <AUTHOR> Assistant
 * @date 2025/01/09
 */
public class Base64BitmapUtils {
    
    private static final String TAG = "Base64BitmapUtils";
    
    // 线程池，用于执行后台任务
    private static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(3);
    
    // 主线程Handler，用于回调结果
    private static final Handler MAIN_HANDLER = new Handler(Looper.getMainLooper());
    
    /**
     * Base64转Bitmap的回调接口
     */
    public interface Base64ToBitmapCallback {
        /**
         * 转换成功回调（在主线程中执行）
         * @param bitmap 转换后的Bitmap对象
         */
        void onSuccess(Bitmap bitmap);
        
        /**
         * 转换失败回调（在主线程中执行）
         * @param error 错误信息
         */
        void onError(String error);
    }
    
    /**
     * Bitmap转Base64的回调接口
     */
    public interface BitmapToBase64Callback {
        /**
         * 转换成功回调（在主线程中执行）
         * @param base64String 转换后的Base64字符串
         */
        void onSuccess(String base64String);
        
        /**
         * 转换失败回调（在主线程中执行）
         * @param error 错误信息
         */
        void onError(String error);
    }
    
    /**
     * 在子线程中将Base64字符串转换为Bitmap
     * 
     * @param base64String Base64编码的图片字符串
     * @param callback 转换结果回调
     */
    public static void base64ToBitmap(String base64String, Base64ToBitmapCallback callback) {
        if (base64String == null || base64String.trim().isEmpty()) {
            if (callback != null) {
                MAIN_HANDLER.post(() -> callback.onError("Base64字符串不能为空"));
            }
            return;
        }
        
        if (callback == null) {
            Log.w(TAG, "回调接口为null，无法返回结果");
            return;
        }
        
        EXECUTOR.execute(() -> {
            try {
                // 清理Base64字符串（移除可能的前缀）
                String cleanBase64 = cleanBase64String(base64String);
                
                // 解码Base64字符串为字节数组
                byte[] decodedBytes = Base64.decode(cleanBase64, Base64.DEFAULT);
                
                // 将字节数组转换为Bitmap
                Bitmap bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
                
                if (bitmap != null) {
                    // 在主线程中回调成功结果
                    MAIN_HANDLER.post(() -> callback.onSuccess(bitmap));
                } else {
                    // 在主线程中回调失败结果
                    MAIN_HANDLER.post(() -> callback.onError("无法解析图片数据"));
                }
                
            } catch (IllegalArgumentException e) {
                Log.e(TAG, "Base64解码失败", e);
                MAIN_HANDLER.post(() -> callback.onError("Base64格式错误: " + e.getMessage()));
            } catch (OutOfMemoryError e) {
                Log.e(TAG, "内存不足", e);
                MAIN_HANDLER.post(() -> callback.onError("图片过大，内存不足"));
            } catch (Exception e) {
                Log.e(TAG, "转换失败", e);
                MAIN_HANDLER.post(() -> callback.onError("转换失败: " + e.getMessage()));
            }
        });
    }
    
    /**
     * 在子线程中将Base64字符串转换为Bitmap（带压缩选项）
     * 
     * @param base64String Base64编码的图片字符串
     * @param maxWidth 最大宽度，0表示不限制
     * @param maxHeight 最大高度，0表示不限制
     * @param callback 转换结果回调
     */
    public static void base64ToBitmapWithResize(String base64String, int maxWidth, int maxHeight, 
                                               Base64ToBitmapCallback callback) {
        if (base64String == null || base64String.trim().isEmpty()) {
            if (callback != null) {
                MAIN_HANDLER.post(() -> callback.onError("Base64字符串不能为空"));
            }
            return;
        }
        
        if (callback == null) {
            Log.w(TAG, "回调接口为null，无法返回结果");
            return;
        }
        
        EXECUTOR.execute(() -> {
            try {
                // 清理Base64字符串
                String cleanBase64 = cleanBase64String(base64String);
                
                // 解码Base64字符串为字节数组
                byte[] decodedBytes = Base64.decode(cleanBase64, Base64.DEFAULT);
                
                // 先获取图片尺寸信息，不加载到内存
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length, options);
                
                // 计算压缩比例
                int inSampleSize = calculateInSampleSize(options, maxWidth, maxHeight);
                
                // 实际解码图片
                options.inJustDecodeBounds = false;
                options.inSampleSize = inSampleSize;
                options.inPreferredConfig = Bitmap.Config.RGB_565; // 使用更少内存的配置
                
                Bitmap bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length, options);
                
                if (bitmap != null) {
                    // 在主线程中回调成功结果
                    MAIN_HANDLER.post(() -> callback.onSuccess(bitmap));
                } else {
                    // 在主线程中回调失败结果
                    MAIN_HANDLER.post(() -> callback.onError("无法解析图片数据"));
                }
                
            } catch (IllegalArgumentException e) {
                Log.e(TAG, "Base64解码失败", e);
                MAIN_HANDLER.post(() -> callback.onError("Base64格式错误: " + e.getMessage()));
            } catch (OutOfMemoryError e) {
                Log.e(TAG, "内存不足", e);
                MAIN_HANDLER.post(() -> callback.onError("图片过大，内存不足"));
            } catch (Exception e) {
                Log.e(TAG, "转换失败", e);
                MAIN_HANDLER.post(() -> callback.onError("转换失败: " + e.getMessage()));
            }
        });
    }
    
    /**
     * 在子线程中将Bitmap转换为Base64字符串
     * 
     * @param bitmap 要转换的Bitmap对象
     * @param format 压缩格式（JPEG, PNG, WEBP）
     * @param quality 压缩质量（0-100，仅对JPEG有效）
     * @param callback 转换结果回调
     */
    public static void bitmapToBase64(Bitmap bitmap, Bitmap.CompressFormat format, int quality,
                                     BitmapToBase64Callback callback) {
        if (bitmap == null || bitmap.isRecycled()) {
            if (callback != null) {
                MAIN_HANDLER.post(() -> callback.onError("Bitmap不能为空或已被回收"));
            }
            return;
        }
        
        if (callback == null) {
            Log.w(TAG, "回调接口为null，无法返回结果");
            return;
        }
        
        EXECUTOR.execute(() -> {
            ByteArrayOutputStream outputStream = null;
            try {
                outputStream = new ByteArrayOutputStream();
                
                // 压缩Bitmap到输出流
                boolean compressResult = bitmap.compress(format, quality, outputStream);
                
                if (!compressResult) {
                    MAIN_HANDLER.post(() -> callback.onError("Bitmap压缩失败"));
                    return;
                }
                
                // 转换为字节数组
                byte[] byteArray = outputStream.toByteArray();
                
                // 编码为Base64字符串
                String base64String = Base64.encodeToString(byteArray, Base64.DEFAULT);
                
                // 在主线程中回调成功结果
                MAIN_HANDLER.post(() -> callback.onSuccess(base64String));
                
            } catch (OutOfMemoryError e) {
                Log.e(TAG, "内存不足", e);
                MAIN_HANDLER.post(() -> callback.onError("内存不足，无法完成转换"));
            } catch (Exception e) {
                Log.e(TAG, "转换失败", e);
                MAIN_HANDLER.post(() -> callback.onError("转换失败: " + e.getMessage()));
            } finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (Exception e) {
                        Log.e(TAG, "关闭输出流失败", e);
                    }
                }
            }
        });
    }
    
    /**
     * 清理Base64字符串，移除可能的前缀
     * 
     * @param base64String 原始Base64字符串
     * @return 清理后的Base64字符串
     */
    private static String cleanBase64String(String base64String) {
        if (base64String == null) {
            return "";
        }
        
        // 移除可能的data:image前缀
        if (base64String.startsWith("data:image/")) {
            int commaIndex = base64String.indexOf(",");
            if (commaIndex != -1) {
                return base64String.substring(commaIndex + 1);
            }
        }
        
        return base64String.trim();
    }
    
    /**
     * 计算图片压缩比例
     * 
     * @param options BitmapFactory.Options对象
     * @param reqWidth 目标宽度
     * @param reqHeight 目标高度
     * @return 压缩比例
     */
    private static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        if (reqWidth <= 0 && reqHeight <= 0) {
            return 1;
        }
        
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        
        return inSampleSize;
    }
    
    /**
     * 释放线程池资源（在应用退出时调用）
     */
    public static void shutdown() {
        if (!EXECUTOR.isShutdown()) {
            EXECUTOR.shutdown();
        }
    }
}
