package com.Tlock.io.entity;


/**
 *
 */
public class FileUploadHeader {

    private String fileLocalPath;

    private String Server;
    private String Date;
    private String TransferEncoding;
    private String Connection;
    private String SetCookie;
    private String Expires;
    private String XContentTypeOptions;
    private String XXSSProtection;
    private String XFrameOptions;
    private String CacheControl;
    private String Pragma;

    public String getFileLocalPath() {
        return fileLocalPath;
    }

    public void setFileLocalPath(String fileLocalPath) {
        this.fileLocalPath = fileLocalPath;
    }

    public String getServer() {
        return Server;
    }

    public void setServer(String server) {
        Server = server;
    }

    public String getDate() {
        return Date;
    }

    public void setDate(String date) {
        Date = date;
    }

    public String getTransferEncoding() {
        return TransferEncoding;
    }

    public void setTransferEncoding(String transferEncoding) {
        TransferEncoding = transferEncoding;
    }

    public String getConnection() {
        return Connection;
    }

    public void setConnection(String connection) {
        Connection = connection;
    }

    public String getSetCookie() {
        return SetCookie;
    }

    public void setSetCookie(String setCookie) {
        SetCookie = setCookie;
    }

    public String getExpires() {
        return Expires;
    }

    public void setExpires(String expires) {
        Expires = expires;
    }

    public String getXContentTypeOptions() {
        return XContentTypeOptions;
    }

    public void setXContentTypeOptions(String XContentTypeOptions) {
        this.XContentTypeOptions = XContentTypeOptions;
    }

    public String getXXSSProtection() {
        return XXSSProtection;
    }

    public void setXXSSProtection(String XXSSProtection) {
        this.XXSSProtection = XXSSProtection;
    }

    public String getXFrameOptions() {
        return XFrameOptions;
    }

    public void setXFrameOptions(String XFrameOptions) {
        this.XFrameOptions = XFrameOptions;
    }

    public String getCacheControl() {
        return CacheControl;
    }

    public void setCacheControl(String cacheControl) {
        CacheControl = cacheControl;
    }

    public String getPragma() {
        return Pragma;
    }

    public void setPragma(String pragma) {
        Pragma = pragma;
    }
}
