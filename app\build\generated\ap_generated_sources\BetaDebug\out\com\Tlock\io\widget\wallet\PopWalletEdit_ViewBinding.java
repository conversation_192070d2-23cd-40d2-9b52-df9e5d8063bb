// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopWalletEdit_ViewBinding implements Unbinder {
  private PopWalletEdit target;

  private View view7f090140;

  private View view7f090349;

  private View view7f090304;

  private View view7f090071;

  private View view7f090320;

  @UiThread
  public PopWalletEdit_ViewBinding(PopWalletEdit target) {
    this(target, target);
  }

  @UiThread
  public PopWalletEdit_ViewBinding(final PopWalletEdit target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_close, "field 'mIvClose' and method 'onViewClicked'");
    target.mIvClose = Utils.castView(view, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    view7f090140 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_name, "field 'mTvName' and method 'onViewClicked'");
    target.mTvName = Utils.castView(view, R.id.tv_name, "field 'mTvName'", TextView.class);
    view7f090349 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_address, "field 'mTvAddress' and method 'onViewClicked'");
    target.mTvAddress = Utils.castView(view, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    view7f090304 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.btn_backup, "field 'mBtnBackup' and method 'onViewClicked'");
    target.mBtnBackup = Utils.castView(view, R.id.btn_backup, "field 'mBtnBackup'", TextView.class);
    view7f090071 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_delete, "field 'mTvDelete' and method 'onViewClicked'");
    target.mTvDelete = Utils.castView(view, R.id.tv_delete, "field 'mTvDelete'", TextView.class);
    view7f090320 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopWalletEdit target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvClose = null;
    target.mTvName = null;
    target.mTvAddress = null;
    target.mBtnBackup = null;
    target.mTvDelete = null;

    view7f090140.setOnClickListener(null);
    view7f090140 = null;
    view7f090349.setOnClickListener(null);
    view7f090349 = null;
    view7f090304.setOnClickListener(null);
    view7f090304 = null;
    view7f090071.setOnClickListener(null);
    view7f090071 = null;
    view7f090320.setOnClickListener(null);
    view7f090320 = null;
  }
}
