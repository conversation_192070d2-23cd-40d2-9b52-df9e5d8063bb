{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e2803939f49d234b1bb2c9c5c4d3bbd9\\transformed\\jetified-zxing-2.2.8\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,96,142,181,251,296,338,397", "endColumns": "40,45,38,69,44,41,58,56", "endOffsets": "91,137,176,246,291,333,392,449"}, "to": {"startLines": "2,3,11,12,13,14,15,39", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,96,525,564,634,679,721,2088", "endColumns": "40,45,38,69,44,41,58,56", "endOffsets": "91,137,559,629,674,716,775,2140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3fe0ffb90c4cc2173002321ae627b6d3\\transformed\\jetified-XPopup-2.10.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,100,157,198,241,294", "endColumns": "44,56,40,42,52,58", "endOffsets": "95,152,193,236,289,348"}, "to": {"startLines": "33,34,35,36,37,38", "startColumns": "4,4,4,4,4,4", "startOffsets": "1790,1835,1892,1933,1976,2029", "endColumns": "44,56,40,42,52,58", "endOffsets": "1830,1887,1928,1971,2024,2083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e396990e4cedac72d7ee6505fbd880a1\\transformed\\jetified-SmartRefreshHeader-1.1.0-alpha-26\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,108,162,219,271,324,381", "endColumns": "52,53,56,51,52,56,56", "endOffsets": "103,157,214,266,319,376,433"}, "to": {"startLines": "4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "142,195,249,306,358,411,468", "endColumns": "52,53,56,51,52,56,56", "endOffsets": "190,244,301,353,406,463,520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\924afc56e026b49ad40635f04d8fbc7e\\transformed\\jetified-SmartRefreshLayout-1.1.0-alpha-6\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,255,306,357,410,465,519,575,629,680,731,784,838,894,948,1004", "endColumns": "107,91,50,50,52,54,53,55,53,50,50,52,53,55,53,55,60", "endOffsets": "158,250,301,352,405,460,514,570,624,675,726,779,833,889,943,999,1060"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "780,888,980,1031,1082,1135,1190,1244,1300,1354,1405,1456,1509,1563,1619,1673,1729", "endColumns": "107,91,50,50,52,54,53,55,53,50,50,52,53,55,53,55,60", "endOffsets": "883,975,1026,1077,1130,1185,1239,1295,1349,1400,1451,1504,1558,1614,1668,1724,1785"}}]}]}