package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.DateUtil.friendly_time_2;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.vectordrawable.graphics.drawable.Animatable2Compat;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.UserInfoActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.signature.ObjectKey;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;
import io.noties.markwon.Markwon;
import io.noties.markwon.image.glide.GlideImagesPlugin;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class CommentItemView extends BaseView {


    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_handle)
    FontTextView mTvHandle;
    @BindView(R.id.tv_content)
    FontTextView mTvContent;
    @BindView(R.id.iv_post)
    ImageView mIvPost;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.tv_isShow)
    TextView mTvIsShow;
    @BindView(R.id.iv_review)
    ImageView mIvReview;
    @BindView(R.id.tv_review)
    TextView mTvReview;
    @BindView(R.id.ll_review)
    RelativeLayout mLlReview;
    @BindView(R.id.iv_praise)
    ImageView mIvPraise;
    @BindView(R.id.tv_praise)
    TextView mTvPraise;
    @BindView(R.id.ll_praise)
    RelativeLayout mLlPraise;
    @BindView(R.id.constraint)
    LinearLayout mConstraint;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.ll_comment_child)
    LinearLayout mLlCommentChild;
    private ArrayList<PostQueryProto.CommentResponse> commentList;

    public CommentItemView(Context context) {
        super(context);
    }

    public CommentItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CommentItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public PostProto.Post post;
    public int position;
    public ProfileProto.Profile profile;
    public ProfileProto.Profile tagProfile;
    public PostQueryProto.CommentResponse data;
    public PostProto.Post data1;
    private int child_page = 1;

    @Override
    protected int getLayoutId() {
        return R.layout.item_comment;
    }

    public void setData(PostQueryProto.CommentResponse data, int position, int layer) {
        this.data = data;
        this.position = position;
        this.post = data.getPost();
        this.profile = data.getProfile();
        this.tagProfile = data.getTargetProfile();
        mLlCommentChild.removeAllViews();
        Glide.with(getContext()).load(getResources().getDrawable(R.drawable.shape_transparent_60))
                .into(mIvHeard);
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                String heardStr = CosmosUtils.getAuthHeard(post.getCreator());
                mIvPraise.post(new Runnable() {
                    @Override
                    public void run() {
                        if (TextUtils.isEmpty(heardStr)) {
                            TextAvatarDrawable a = new TextAvatarDrawable(profile.getUserHandle().substring(0, 1));
                            mIvHeard.setImageDrawable(a);
                        } else {
                            if (heardStr.startsWith("http")) {
                                Glide.with(getContext()).load(profile.getAvatar()).apply(new RequestOptions()
                                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                .signature(new ObjectKey(heardStr))
                                                .centerCrop()
                                                .format(DecodeFormat.PREFER_RGB_565)
                                                .dontTransform())
                                        .apply(RequestOptions.circleCropTransform().circleCrop())
                                        .into(mIvHeard);
                            } else {
                                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(heardStr);
                                try {
                                    Glide.with(getContext()).load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .transition(DrawableTransitionOptions.withCrossFade(500))
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                } catch (Exception e) {
                                }

                            }

                        }
                    }
                });
            }
        });


        Markwon markdwon = Markwon.builder(mTvAccountName.getContext())
                .usePlugin(GlideImagesPlugin.create(mTvAccountName.getContext()))
                .build();
        markdwon.setMarkdown(mTvContent, post.getContent());
        mTvHandle.setVisibility(layer == 2 ? VISIBLE : GONE);
//        mLlCommentChild.setVisibility(layer == 1 ? VISIBLE : GONE);
        if (layer == 2) {
            mTvHandle.setText("@ " + post.getParentId());
        }
        if (post.getCommentCount() > 0) {
            mTvIsShow.setVisibility(VISIBLE);
        } else {
            mTvIsShow.setVisibility(GONE);
        }

        mTvIsShow.setText(post.getCommentCount() + " Replies In Total >");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_2(post.getTimestamp() + "000"));
        }
        mTvAccountName.setText(profile.getNickname().isEmpty()?profile.getUserHandle():profile.getNickname());
        mTvHandle.setText("Reply :" + tagProfile.getNickname());
        mTvReview.setText(post.getCommentCount() != 0 ? post.getCommentCount() + "" : "");
        mTvPraise.setText(post.getLikeCount() != 0 ? post.getLikeCount() + "" : "");
        if (post.getImagesUrlCount() != 0 && !TextUtils.isEmpty(post.getImagesUrl(0))) {
            mIvPost.setVisibility(VISIBLE);
            Glide.with(mTvPraise.getContext()).load(post.getImagesUrl(0)).apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .centerCrop()
                            .signature(new ObjectKey(post.getImagesUrl(0)))
                            .dontTransform())
                    .into(mIvPost);

        } else {
            mIvPost.setVisibility(GONE);
        }

    }

    public ArrayList<PostQueryProto.CommentResponse> getCommentList() {
        return commentList == null ? new ArrayList<>() : commentList;
    }

    public void setHideLine() {
        mLine1.setVisibility(GONE);

    }

    public void setCommentList(ArrayList<PostQueryProto.CommentResponse> list, int position, int layer) {
        mLlCommentChild.setVisibility(VISIBLE);
        mLlCommentChild.removeAllViews();
        mLlCommentChild.getRootView().requestLayout();
        commentList = list;

        for (int i = 0; i < list.size(); i++) {
            PostQueryProto.CommentResponse postResponse = list.get(i);
            CommentItemView commentItemView = new CommentItemView(getContext(), null, 0);
            commentItemView.setData(postResponse, i, 2);
            commentItemView.setHideLine();
            if (i <= position && layer == 2) {
                commentItemView.setHide();
            }
            int finalI = i;
            commentItemView.setCallback(new Callback() {
                @Override
                public void share() {

                }

                @Override
                public void review(String id, String handle) {
                    if (callback != null) {
                        callback.review(id, handle);
                    }
                }

                @Override
                public void praise(String id) {
                    if (callback != null) {
                        callback.praise(id);
                    }
                }

                @Override
                public void collect(String id) {

                }

                @Override
                public void info(PostQueryProto.CommentResponse data) {

                }

                @Override
                public void showMore(String id, int position, int layer) {
                    if (callback != null) {
                        callback.showMore(id, finalI, 2);
                    }
                }
            });
            mLlCommentChild.addView(commentItemView);
        }

    }


    public void setData(PostQueryProto.PostResponse data) {
        this.post = data.getPost();
        this.profile = data.getProfile();
        Markwon markdwon = Markwon.builder(mTvAccountName.getContext())
                .usePlugin(GlideImagesPlugin.create(mTvAccountName.getContext()))
                .build();
        markdwon.setMarkdown(mTvContent, post.getContent());


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_2(post.getTimestamp() + "000"));
        }
        mTvAccountName.setText(profile.getNickname());
        mTvReview.setText(post.getCommentCount() != 0 ? post.getCommentCount() + "" : "");
        mTvPraise.setText(post.getLikeCount() != 0 ? post.getLikeCount() + "" : "");
        if (post.getImagesUrlCount() != 0 && !TextUtils.isEmpty(post.getImagesUrl(0))) {
            mIvPost.setVisibility(VISIBLE);
            Glide.with(mTvPraise.getContext()).load(post.getImagesUrl(0)).apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .centerCrop()
                            .signature(new ObjectKey(post.getImagesUrl(0)))
                            .dontTransform())
                    .into(mIvPost);

        } else {
            mIvPost.setVisibility(GONE);
        }

    }


    public void setCommentCount() {
        String string1 = mTvReview.getText().toString();
        if (TextUtils.isDigitsOnly(string1)) {
            int i = Integer.parseInt(string1);
            mTvReview.setText((i + 1) + "");
        } else {
            mTvReview.setText("1");
        }
    }

    public void setHide() {
        mTvIsShow.setVisibility(GONE);

    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @OnClick({R.id.ll_review, R.id.ll_praise, R.id.iv_heard, R.id.tv_content, R.id.tv_isShow, R.id.rl_comment})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_heard:
                UserInfoActivity.start(getContext(), profile.getWalletAddress());
                break;
            case R.id.tv_content:
                if (callback != null)
                    callback.info(data);
                break;
            case R.id.rl_comment:
            case R.id.ll_review:
                if (callback != null)
                    callback.review(post.getId(), profile.getUserHandle());
                break;
            case R.id.ll_praise:
                Glide.with(getContext())
                        .asGif()
                        .load(R.drawable.heart_1) // 替换为你的GIF资源ID
                        .listener(new RequestListener<GifDrawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
                                resource.setLoopCount(1);
                                // 加载静态图片
                                resource.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {
                                    @Override
                                    public void onAnimationEnd(Drawable drawable) {
                                        // 加载静态图片
                                        Glide.with(mIvPraise)
                                                .load(R.mipmap.icon_post_like_select)
                                                .into(mIvPraise);
                                    }
                                });
                                return false;
                            }
                        })
                        .into(mIvPraise);
                String string = mTvPraise.getText().toString();
                if (TextUtils.isDigitsOnly(string) && !TextUtils.isEmpty(string)) {
                    int i = Integer.parseInt(string);
                    mTvPraise.setText((i + 1) + "");
                } else {
                    mTvPraise.setText("1");
                }
                if (callback != null && !post.getId().isEmpty())
                    callback.praise(post.getId());
                break;

            case R.id.tv_isShow:
                if (callback != null)
                    callback.showMore(post.getId(), position, 1);
                break;
        }
    }

    public interface Callback {
        void share();

        void review(String id, String handle);

        void praise(String id);

        void collect(String id);

        void info(PostQueryProto.CommentResponse data);

        void showMore(String id, int position, int layer);
    }
}
