package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.PostItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.NewCosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.pop.PopPostForward;
import com.Tlock.io.widget.pop.PopPostMore;
import com.Tlock.io.widget.pop.PopPostShare;
import com.lxj.xpopup.XPopup;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

import butterknife.BindView;

public class CollectListActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.recyler_view)
    RecyclerView mRecyclerView;
    private ArrayList<PostQueryProto.PostResponse> dappBeans = new ArrayList<>();
    private BaseRecyclerViewAdapter<PostQueryProto.PostResponse> adapter;
    private ETHWallet current;
    private int from;

    /**
     * @param context
     * @param from    1喜欢 ,2收藏
     */
    public static void start(Context context, int from) {
        Intent intent = new Intent(context, CollectListActivity.class);
        intent.putExtra("from", from);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_tip_list;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        from = getIntent().getIntExtra("from", 1);
        current = WalletDaoUtils.getCurrent();
        initRecycleView();
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        adapter = new BaseRecyclerViewAdapter<>(getActivity(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.PostResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                PostItemView itemView = new PostItemView(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.PostResponse data, View view) {
                ((PostItemView) view).setData(data);
                ((PostItemView) view).setDefaultImg(from);
                ((PostItemView) view).setCallback(new PostItemView.Callback() {

                    @Override
                    public void quote() {
                        PopPostForward popPostMore = new PopPostForward(getActivity(), data.getPost().getId(), "");
                        popPostMore.setCallBack(new PopPostForward.CallBack() {

                            @Override
                            public void repost(String id) {
                                showToast("Reposted");
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.postRepost(WalletDaoUtils.getCurrent().getAddress(), id);
                                    }
                                });
                            }

                            @Override
                            public void quote(String id) {
                                PostQuoteActivity.start(getActivity(), id);
                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
                    }

                    @Override
                    public void share() {
                        PopPostShare popPostMore = new PopPostShare(getActivity());
                        popPostMore.setCallBack(new PopPostShare.CallBack() {

                            @Override
                            public void follow(String handle) {

                            }

                            @Override
                            public void report(String id) {

                            }

                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();

                    }

                    @Override
                    public void review(String id, String handle) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), true);
                    }


                    @Override
                    public void praise(String id) {
                        //点赞
                        if (current != null) {
                            showToast("Liked");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    NewCosmosUtils.postLike(current.getAddress(), id);

                                }
                            });
                        }

                    }

                    @Override
                    public void collect(String id) {
                        //收藏
                        if (current != null) {
                            showToast("Added to bookmarks");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    NewCosmosUtils.savePost(current.getAddress(), id);
                                }
                            });

                        }


                    }

                    @Override
                    public void more(String data, String handle, String address) {
                        PopPostMore popPostMore = new PopPostMore(getActivity(), data, handle);
                        popPostMore.setCallBack(new PopPostMore.CallBack() {
                            @Override
                            public void follow(String handle) {
                                showToast("Following");
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.follow(current.getAddress(), address);
                                    }
                                });
                            }

                            @Override
                            public void report(String id) {
                                //TODO 新页面
                                showToast("举报");

                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popPostMore).show();
                    }

                    @Override
                    public void info(PostQueryProto.PostResponse data) {
                        ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
                    }

                    @Override
                    public void resetProfile(PostQueryProto.PostResponse postResponse) {
                        adapter.getList().set(position, postResponse);
                    }
                });
            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.PostResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.PostResponse data, View view) {
                EventBus.getDefault().postSticky(new Event("info.Post", data));
                ContentInfoActivity.start(getActivity(), data.getPost().getId(), false);
            }
        });
    }

    @Override
    protected void getData() {
        super.getData();
        loadData();
    }

    @Override
    protected void loadData() {
        params.clear();
        mCustomNavBar.setMidTitle(from == 1 ? "Likes" : "Bookmarks");
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.PostResponse> datas = new ArrayList<>();
                if (from == 1) {
                    datas = CosmosUtils.getLikePosts(WalletDaoUtils.getCurrent().getAddress(),page);
                } else if (from == 2) {
                    datas = CosmosUtils.getSavePosts(WalletDaoUtils.getCurrent().getAddress(),page);
                }
                ArrayList<PostQueryProto.PostResponse> finalDatas = datas;
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (adapter != null) {
                            adapter.addListNoChange(finalDatas,page);
                        }
                        finishRefresh();
                    }
                });

            }
        });

    }
}