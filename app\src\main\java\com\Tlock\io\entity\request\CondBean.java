package com.Tlock.io.entity.request;

import java.util.List;

public class CondBean {
    private List<?> rules;
    private List<?> groups;
    private Object groupOp;

    public Object getGroupOp() {
        return groupOp;
    }

    public void setGroupOp(Object groupOp) {
        this.groupOp = groupOp;
    }

    public List getRules() {
        return rules;
    }

    public void setRules(List rules) {
        this.rules = rules;
    }

    public List getGroups() {
        return groups;
    }

    public void setGroups(List groups) {
        this.groups = groups;
    }
}
