// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: entity/post.proto
// Protobuf Java Version: 4.28.3

package com.Tlock.io.entity.post;

public final class PostProto {
  private PostProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      PostProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code PostType}
   */
  public enum PostType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ORIGINAL = 0;</code>
     */
    ORIGINAL(0),
    /**
     * <code>ARTICLE = 1;</code>
     */
    ARTICLE(1),
    /**
     * <code>ADVERTISEMENT = 2;</code>
     */
    ADVERTISEMENT(2),
    /**
     * <code>QUOTE = 3;</code>
     */
    QUOTE(3),
    /**
     * <code>REPOST = 4;</code>
     */
    REPOST(4),
    /**
     * <code>POLL = 5;</code>
     */
    POLL(5),
    /**
     * <code>COMMENT = 6;</code>
     */
    COMMENT(6),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        PostType.class.getName());
    }
    /**
     * <code>ORIGINAL = 0;</code>
     */
    public static final int ORIGINAL_VALUE = 0;
    /**
     * <code>ARTICLE = 1;</code>
     */
    public static final int ARTICLE_VALUE = 1;
    /**
     * <code>ADVERTISEMENT = 2;</code>
     */
    public static final int ADVERTISEMENT_VALUE = 2;
    /**
     * <code>QUOTE = 3;</code>
     */
    public static final int QUOTE_VALUE = 3;
    /**
     * <code>REPOST = 4;</code>
     */
    public static final int REPOST_VALUE = 4;
    /**
     * <code>POLL = 5;</code>
     */
    public static final int POLL_VALUE = 5;
    /**
     * <code>COMMENT = 6;</code>
     */
    public static final int COMMENT_VALUE = 6;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static PostType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static PostType forNumber(int value) {
      switch (value) {
        case 0: return ORIGINAL;
        case 1: return ARTICLE;
        case 2: return ADVERTISEMENT;
        case 3: return QUOTE;
        case 4: return REPOST;
        case 5: return POLL;
        case 6: return COMMENT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<PostType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        PostType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<PostType>() {
            public PostType findValueByNumber(int number) {
              return PostType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.Tlock.io.entity.post.PostProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final PostType[] VALUES = values();

    public static PostType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private PostType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:PostType)
  }

  public interface PostOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Post)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    java.lang.String getId();
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>.PostType post_type = 2;</code>
     * @return The enum numeric value on the wire for postType.
     */
    int getPostTypeValue();
    /**
     * <code>.PostType post_type = 2;</code>
     * @return The postType.
     */
    com.Tlock.io.entity.post.PostProto.PostType getPostType();

    /**
     * <code>string parent_id = 3;</code>
     * @return The parentId.
     */
    java.lang.String getParentId();
    /**
     * <code>string parent_id = 3;</code>
     * @return The bytes for parentId.
     */
    com.google.protobuf.ByteString
        getParentIdBytes();

    /**
     * <code>string title = 4;</code>
     * @return The title.
     */
    java.lang.String getTitle();
    /**
     * <code>string title = 4;</code>
     * @return The bytes for title.
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <code>string content = 5;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <code>string content = 5;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();

    /**
     * <code>string creator = 6;</code>
     * @return The creator.
     */
    java.lang.String getCreator();
    /**
     * <code>string creator = 6;</code>
     * @return The bytes for creator.
     */
    com.google.protobuf.ByteString
        getCreatorBytes();

    /**
     * <code>int64 timestamp = 7;</code>
     * @return The timestamp.
     */
    long getTimestamp();

    /**
     * <code>repeated string image_ids = 8;</code>
     * @return A list containing the imageIds.
     */
    java.util.List<java.lang.String>
        getImageIdsList();
    /**
     * <code>repeated string image_ids = 8;</code>
     * @return The count of imageIds.
     */
    int getImageIdsCount();
    /**
     * <code>repeated string image_ids = 8;</code>
     * @param index The index of the element to return.
     * @return The imageIds at the given index.
     */
    java.lang.String getImageIds(int index);
    /**
     * <code>repeated string image_ids = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the imageIds at the given index.
     */
    com.google.protobuf.ByteString
        getImageIdsBytes(int index);

    /**
     * <code>repeated string images_url = 9;</code>
     * @return A list containing the imagesUrl.
     */
    java.util.List<java.lang.String>
        getImagesUrlList();
    /**
     * <code>repeated string images_url = 9;</code>
     * @return The count of imagesUrl.
     */
    int getImagesUrlCount();
    /**
     * <code>repeated string images_url = 9;</code>
     * @param index The index of the element to return.
     * @return The imagesUrl at the given index.
     */
    java.lang.String getImagesUrl(int index);
    /**
     * <code>repeated string images_url = 9;</code>
     * @param index The index of the value to return.
     * @return The bytes of the imagesUrl at the given index.
     */
    com.google.protobuf.ByteString
        getImagesUrlBytes(int index);

    /**
     * <code>repeated string videos_url = 10;</code>
     * @return A list containing the videosUrl.
     */
    java.util.List<java.lang.String>
        getVideosUrlList();
    /**
     * <code>repeated string videos_url = 10;</code>
     * @return The count of videosUrl.
     */
    int getVideosUrlCount();
    /**
     * <code>repeated string videos_url = 10;</code>
     * @param index The index of the element to return.
     * @return The videosUrl at the given index.
     */
    java.lang.String getVideosUrl(int index);
    /**
     * <code>repeated string videos_url = 10;</code>
     * @param index The index of the value to return.
     * @return The bytes of the videosUrl at the given index.
     */
    com.google.protobuf.ByteString
        getVideosUrlBytes(int index);

    /**
     * <code>string quote = 11;</code>
     * @return The quote.
     */
    java.lang.String getQuote();
    /**
     * <code>string quote = 11;</code>
     * @return The bytes for quote.
     */
    com.google.protobuf.ByteString
        getQuoteBytes();

    /**
     * <code>uint64 like_count = 12;</code>
     * @return The likeCount.
     */
    long getLikeCount();

    /**
     * <code>uint64 comment_count = 13;</code>
     * @return The commentCount.
     */
    long getCommentCount();

    /**
     * <code>uint64 repost_count = 14;</code>
     * @return The repostCount.
     */
    long getRepostCount();

    /**
     * <code>uint64 save_count = 15;</code>
     * @return The saveCount.
     */
    long getSaveCount();

    /**
     * <code>uint64 score = 16;</code>
     * @return The score.
     */
    long getScore();

    /**
     * <code>int64 homePostsUpdate = 17;</code>
     * @return The homePostsUpdate.
     */
    long getHomePostsUpdate();

    /**
     * <code>.Poll poll = 18;</code>
     * @return Whether the poll field is set.
     */
    boolean hasPoll();
    /**
     * <code>.Poll poll = 18;</code>
     * @return The poll.
     */
    com.Tlock.io.entity.post.PostProto.Poll getPoll();
    /**
     * <code>.Poll poll = 18;</code>
     */
    com.Tlock.io.entity.post.PostProto.PollOrBuilder getPollOrBuilder();
  }
  /**
   * <pre>
   * Post defines the structure of a post
   * </pre>
   *
   * Protobuf type {@code Post}
   */
  public static final class Post extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Post)
      PostOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Post.class.getName());
    }
    // Use Post.newBuilder() to construct.
    private Post(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Post() {
      id_ = "";
      postType_ = 0;
      parentId_ = "";
      title_ = "";
      content_ = "";
      creator_ = "";
      imageIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      imagesUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      videosUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      quote_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.entity.post.PostProto.internal_static_Post_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.entity.post.PostProto.internal_static_Post_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.entity.post.PostProto.Post.class, com.Tlock.io.entity.post.PostProto.Post.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int POST_TYPE_FIELD_NUMBER = 2;
    private int postType_ = 0;
    /**
     * <code>.PostType post_type = 2;</code>
     * @return The enum numeric value on the wire for postType.
     */
    @java.lang.Override public int getPostTypeValue() {
      return postType_;
    }
    /**
     * <code>.PostType post_type = 2;</code>
     * @return The postType.
     */
    @java.lang.Override public com.Tlock.io.entity.post.PostProto.PostType getPostType() {
      com.Tlock.io.entity.post.PostProto.PostType result = com.Tlock.io.entity.post.PostProto.PostType.forNumber(postType_);
      return result == null ? com.Tlock.io.entity.post.PostProto.PostType.UNRECOGNIZED : result;
    }

    public static final int PARENT_ID_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object parentId_ = "";
    /**
     * <code>string parent_id = 3;</code>
     * @return The parentId.
     */
    @java.lang.Override
    public java.lang.String getParentId() {
      java.lang.Object ref = parentId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        parentId_ = s;
        return s;
      }
    }
    /**
     * <code>string parent_id = 3;</code>
     * @return The bytes for parentId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getParentIdBytes() {
      java.lang.Object ref = parentId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        parentId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TITLE_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object title_ = "";
    /**
     * <code>string title = 4;</code>
     * @return The title.
     */
    @java.lang.Override
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      }
    }
    /**
     * <code>string title = 4;</code>
     * @return The bytes for title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONTENT_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object content_ = "";
    /**
     * <code>string content = 5;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      }
    }
    /**
     * <code>string content = 5;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CREATOR_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object creator_ = "";
    /**
     * <code>string creator = 6;</code>
     * @return The creator.
     */
    @java.lang.Override
    public java.lang.String getCreator() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creator_ = s;
        return s;
      }
    }
    /**
     * <code>string creator = 6;</code>
     * @return The bytes for creator.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCreatorBytes() {
      java.lang.Object ref = creator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 7;
    private long timestamp_ = 0L;
    /**
     * <code>int64 timestamp = 7;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int IMAGE_IDS_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList imageIds_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <code>repeated string image_ids = 8;</code>
     * @return A list containing the imageIds.
     */
    public com.google.protobuf.ProtocolStringList
        getImageIdsList() {
      return imageIds_;
    }
    /**
     * <code>repeated string image_ids = 8;</code>
     * @return The count of imageIds.
     */
    public int getImageIdsCount() {
      return imageIds_.size();
    }
    /**
     * <code>repeated string image_ids = 8;</code>
     * @param index The index of the element to return.
     * @return The imageIds at the given index.
     */
    public java.lang.String getImageIds(int index) {
      return imageIds_.get(index);
    }
    /**
     * <code>repeated string image_ids = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the imageIds at the given index.
     */
    public com.google.protobuf.ByteString
        getImageIdsBytes(int index) {
      return imageIds_.getByteString(index);
    }

    public static final int IMAGES_URL_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList imagesUrl_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <code>repeated string images_url = 9;</code>
     * @return A list containing the imagesUrl.
     */
    public com.google.protobuf.ProtocolStringList
        getImagesUrlList() {
      return imagesUrl_;
    }
    /**
     * <code>repeated string images_url = 9;</code>
     * @return The count of imagesUrl.
     */
    public int getImagesUrlCount() {
      return imagesUrl_.size();
    }
    /**
     * <code>repeated string images_url = 9;</code>
     * @param index The index of the element to return.
     * @return The imagesUrl at the given index.
     */
    public java.lang.String getImagesUrl(int index) {
      return imagesUrl_.get(index);
    }
    /**
     * <code>repeated string images_url = 9;</code>
     * @param index The index of the value to return.
     * @return The bytes of the imagesUrl at the given index.
     */
    public com.google.protobuf.ByteString
        getImagesUrlBytes(int index) {
      return imagesUrl_.getByteString(index);
    }

    public static final int VIDEOS_URL_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList videosUrl_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <code>repeated string videos_url = 10;</code>
     * @return A list containing the videosUrl.
     */
    public com.google.protobuf.ProtocolStringList
        getVideosUrlList() {
      return videosUrl_;
    }
    /**
     * <code>repeated string videos_url = 10;</code>
     * @return The count of videosUrl.
     */
    public int getVideosUrlCount() {
      return videosUrl_.size();
    }
    /**
     * <code>repeated string videos_url = 10;</code>
     * @param index The index of the element to return.
     * @return The videosUrl at the given index.
     */
    public java.lang.String getVideosUrl(int index) {
      return videosUrl_.get(index);
    }
    /**
     * <code>repeated string videos_url = 10;</code>
     * @param index The index of the value to return.
     * @return The bytes of the videosUrl at the given index.
     */
    public com.google.protobuf.ByteString
        getVideosUrlBytes(int index) {
      return videosUrl_.getByteString(index);
    }

    public static final int QUOTE_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object quote_ = "";
    /**
     * <code>string quote = 11;</code>
     * @return The quote.
     */
    @java.lang.Override
    public java.lang.String getQuote() {
      java.lang.Object ref = quote_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        quote_ = s;
        return s;
      }
    }
    /**
     * <code>string quote = 11;</code>
     * @return The bytes for quote.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getQuoteBytes() {
      java.lang.Object ref = quote_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        quote_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LIKE_COUNT_FIELD_NUMBER = 12;
    private long likeCount_ = 0L;
    /**
     * <code>uint64 like_count = 12;</code>
     * @return The likeCount.
     */
    @java.lang.Override
    public long getLikeCount() {
      return likeCount_;
    }

    public static final int COMMENT_COUNT_FIELD_NUMBER = 13;
    private long commentCount_ = 0L;
    /**
     * <code>uint64 comment_count = 13;</code>
     * @return The commentCount.
     */
    @java.lang.Override
    public long getCommentCount() {
      return commentCount_;
    }

    public static final int REPOST_COUNT_FIELD_NUMBER = 14;
    private long repostCount_ = 0L;
    /**
     * <code>uint64 repost_count = 14;</code>
     * @return The repostCount.
     */
    @java.lang.Override
    public long getRepostCount() {
      return repostCount_;
    }

    public static final int SAVE_COUNT_FIELD_NUMBER = 15;
    private long saveCount_ = 0L;
    /**
     * <code>uint64 save_count = 15;</code>
     * @return The saveCount.
     */
    @java.lang.Override
    public long getSaveCount() {
      return saveCount_;
    }

    public static final int SCORE_FIELD_NUMBER = 16;
    private long score_ = 0L;
    /**
     * <code>uint64 score = 16;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }

    public static final int HOMEPOSTSUPDATE_FIELD_NUMBER = 17;
    private long homePostsUpdate_ = 0L;
    /**
     * <code>int64 homePostsUpdate = 17;</code>
     * @return The homePostsUpdate.
     */
    @java.lang.Override
    public long getHomePostsUpdate() {
      return homePostsUpdate_;
    }

    public static final int POLL_FIELD_NUMBER = 18;
    private com.Tlock.io.entity.post.PostProto.Poll poll_;
    /**
     * <code>.Poll poll = 18;</code>
     * @return Whether the poll field is set.
     */
    @java.lang.Override
    public boolean hasPoll() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Poll poll = 18;</code>
     * @return The poll.
     */
    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.Poll getPoll() {
      return poll_ == null ? com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance() : poll_;
    }
    /**
     * <code>.Poll poll = 18;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.PollOrBuilder getPollOrBuilder() {
      return poll_ == null ? com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance() : poll_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
      }
      if (postType_ != com.Tlock.io.entity.post.PostProto.PostType.ORIGINAL.getNumber()) {
        output.writeEnum(2, postType_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(parentId_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, parentId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, title_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(content_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, content_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, creator_);
      }
      if (timestamp_ != 0L) {
        output.writeInt64(7, timestamp_);
      }
      for (int i = 0; i < imageIds_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, imageIds_.getRaw(i));
      }
      for (int i = 0; i < imagesUrl_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, imagesUrl_.getRaw(i));
      }
      for (int i = 0; i < videosUrl_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, videosUrl_.getRaw(i));
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(quote_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, quote_);
      }
      if (likeCount_ != 0L) {
        output.writeUInt64(12, likeCount_);
      }
      if (commentCount_ != 0L) {
        output.writeUInt64(13, commentCount_);
      }
      if (repostCount_ != 0L) {
        output.writeUInt64(14, repostCount_);
      }
      if (saveCount_ != 0L) {
        output.writeUInt64(15, saveCount_);
      }
      if (score_ != 0L) {
        output.writeUInt64(16, score_);
      }
      if (homePostsUpdate_ != 0L) {
        output.writeInt64(17, homePostsUpdate_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(18, getPoll());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
      }
      if (postType_ != com.Tlock.io.entity.post.PostProto.PostType.ORIGINAL.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, postType_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(parentId_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, parentId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, title_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(content_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, content_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(creator_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, creator_);
      }
      if (timestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, timestamp_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < imageIds_.size(); i++) {
          dataSize += computeStringSizeNoTag(imageIds_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getImageIdsList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < imagesUrl_.size(); i++) {
          dataSize += computeStringSizeNoTag(imagesUrl_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getImagesUrlList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < videosUrl_.size(); i++) {
          dataSize += computeStringSizeNoTag(videosUrl_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getVideosUrlList().size();
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(quote_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, quote_);
      }
      if (likeCount_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(12, likeCount_);
      }
      if (commentCount_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(13, commentCount_);
      }
      if (repostCount_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, repostCount_);
      }
      if (saveCount_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(15, saveCount_);
      }
      if (score_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(16, score_);
      }
      if (homePostsUpdate_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(17, homePostsUpdate_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, getPoll());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.entity.post.PostProto.Post)) {
        return super.equals(obj);
      }
      com.Tlock.io.entity.post.PostProto.Post other = (com.Tlock.io.entity.post.PostProto.Post) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (postType_ != other.postType_) return false;
      if (!getParentId()
          .equals(other.getParentId())) return false;
      if (!getTitle()
          .equals(other.getTitle())) return false;
      if (!getContent()
          .equals(other.getContent())) return false;
      if (!getCreator()
          .equals(other.getCreator())) return false;
      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (!getImageIdsList()
          .equals(other.getImageIdsList())) return false;
      if (!getImagesUrlList()
          .equals(other.getImagesUrlList())) return false;
      if (!getVideosUrlList()
          .equals(other.getVideosUrlList())) return false;
      if (!getQuote()
          .equals(other.getQuote())) return false;
      if (getLikeCount()
          != other.getLikeCount()) return false;
      if (getCommentCount()
          != other.getCommentCount()) return false;
      if (getRepostCount()
          != other.getRepostCount()) return false;
      if (getSaveCount()
          != other.getSaveCount()) return false;
      if (getScore()
          != other.getScore()) return false;
      if (getHomePostsUpdate()
          != other.getHomePostsUpdate()) return false;
      if (hasPoll() != other.hasPoll()) return false;
      if (hasPoll()) {
        if (!getPoll()
            .equals(other.getPoll())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + POST_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + postType_;
      hash = (37 * hash) + PARENT_ID_FIELD_NUMBER;
      hash = (53 * hash) + getParentId().hashCode();
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
      hash = (37 * hash) + CREATOR_FIELD_NUMBER;
      hash = (53 * hash) + getCreator().hashCode();
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestamp());
      if (getImageIdsCount() > 0) {
        hash = (37 * hash) + IMAGE_IDS_FIELD_NUMBER;
        hash = (53 * hash) + getImageIdsList().hashCode();
      }
      if (getImagesUrlCount() > 0) {
        hash = (37 * hash) + IMAGES_URL_FIELD_NUMBER;
        hash = (53 * hash) + getImagesUrlList().hashCode();
      }
      if (getVideosUrlCount() > 0) {
        hash = (37 * hash) + VIDEOS_URL_FIELD_NUMBER;
        hash = (53 * hash) + getVideosUrlList().hashCode();
      }
      hash = (37 * hash) + QUOTE_FIELD_NUMBER;
      hash = (53 * hash) + getQuote().hashCode();
      hash = (37 * hash) + LIKE_COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLikeCount());
      hash = (37 * hash) + COMMENT_COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCommentCount());
      hash = (37 * hash) + REPOST_COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRepostCount());
      hash = (37 * hash) + SAVE_COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSaveCount());
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
      hash = (37 * hash) + HOMEPOSTSUPDATE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getHomePostsUpdate());
      if (hasPoll()) {
        hash = (37 * hash) + POLL_FIELD_NUMBER;
        hash = (53 * hash) + getPoll().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.entity.post.PostProto.Post parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.entity.post.PostProto.Post parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.post.PostProto.Post parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.entity.post.PostProto.Post prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Post defines the structure of a post
     * </pre>
     *
     * Protobuf type {@code Post}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Post)
        com.Tlock.io.entity.post.PostProto.PostOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Post_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Post_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.entity.post.PostProto.Post.class, com.Tlock.io.entity.post.PostProto.Post.Builder.class);
      }

      // Construct using com.Tlock.io.entity.post.PostProto.Post.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getPollFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = "";
        postType_ = 0;
        parentId_ = "";
        title_ = "";
        content_ = "";
        creator_ = "";
        timestamp_ = 0L;
        imageIds_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        imagesUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        videosUrl_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        quote_ = "";
        likeCount_ = 0L;
        commentCount_ = 0L;
        repostCount_ = 0L;
        saveCount_ = 0L;
        score_ = 0L;
        homePostsUpdate_ = 0L;
        poll_ = null;
        if (pollBuilder_ != null) {
          pollBuilder_.dispose();
          pollBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Post_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Post getDefaultInstanceForType() {
        return com.Tlock.io.entity.post.PostProto.Post.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Post build() {
        com.Tlock.io.entity.post.PostProto.Post result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Post buildPartial() {
        com.Tlock.io.entity.post.PostProto.Post result = new com.Tlock.io.entity.post.PostProto.Post(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.entity.post.PostProto.Post result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.postType_ = postType_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.parentId_ = parentId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.title_ = title_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.content_ = content_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.creator_ = creator_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.timestamp_ = timestamp_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          imageIds_.makeImmutable();
          result.imageIds_ = imageIds_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          imagesUrl_.makeImmutable();
          result.imagesUrl_ = imagesUrl_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          videosUrl_.makeImmutable();
          result.videosUrl_ = videosUrl_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.quote_ = quote_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.likeCount_ = likeCount_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.commentCount_ = commentCount_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.repostCount_ = repostCount_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.saveCount_ = saveCount_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.score_ = score_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.homePostsUpdate_ = homePostsUpdate_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.poll_ = pollBuilder_ == null
              ? poll_
              : pollBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.entity.post.PostProto.Post) {
          return mergeFrom((com.Tlock.io.entity.post.PostProto.Post)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.entity.post.PostProto.Post other) {
        if (other == com.Tlock.io.entity.post.PostProto.Post.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.postType_ != 0) {
          setPostTypeValue(other.getPostTypeValue());
        }
        if (!other.getParentId().isEmpty()) {
          parentId_ = other.parentId_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getTitle().isEmpty()) {
          title_ = other.title_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getContent().isEmpty()) {
          content_ = other.content_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getCreator().isEmpty()) {
          creator_ = other.creator_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (other.getTimestamp() != 0L) {
          setTimestamp(other.getTimestamp());
        }
        if (!other.imageIds_.isEmpty()) {
          if (imageIds_.isEmpty()) {
            imageIds_ = other.imageIds_;
            bitField0_ |= 0x00000080;
          } else {
            ensureImageIdsIsMutable();
            imageIds_.addAll(other.imageIds_);
          }
          onChanged();
        }
        if (!other.imagesUrl_.isEmpty()) {
          if (imagesUrl_.isEmpty()) {
            imagesUrl_ = other.imagesUrl_;
            bitField0_ |= 0x00000100;
          } else {
            ensureImagesUrlIsMutable();
            imagesUrl_.addAll(other.imagesUrl_);
          }
          onChanged();
        }
        if (!other.videosUrl_.isEmpty()) {
          if (videosUrl_.isEmpty()) {
            videosUrl_ = other.videosUrl_;
            bitField0_ |= 0x00000200;
          } else {
            ensureVideosUrlIsMutable();
            videosUrl_.addAll(other.videosUrl_);
          }
          onChanged();
        }
        if (!other.getQuote().isEmpty()) {
          quote_ = other.quote_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.getLikeCount() != 0L) {
          setLikeCount(other.getLikeCount());
        }
        if (other.getCommentCount() != 0L) {
          setCommentCount(other.getCommentCount());
        }
        if (other.getRepostCount() != 0L) {
          setRepostCount(other.getRepostCount());
        }
        if (other.getSaveCount() != 0L) {
          setSaveCount(other.getSaveCount());
        }
        if (other.getScore() != 0L) {
          setScore(other.getScore());
        }
        if (other.getHomePostsUpdate() != 0L) {
          setHomePostsUpdate(other.getHomePostsUpdate());
        }
        if (other.hasPoll()) {
          mergePoll(other.getPoll());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                postType_ = input.readEnum();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                parentId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                title_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                content_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                creator_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                timestamp_ = input.readInt64();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureImageIdsIsMutable();
                imageIds_.add(s);
                break;
              } // case 66
              case 74: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureImagesUrlIsMutable();
                imagesUrl_.add(s);
                break;
              } // case 74
              case 82: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureVideosUrlIsMutable();
                videosUrl_.add(s);
                break;
              } // case 82
              case 90: {
                quote_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 96: {
                likeCount_ = input.readUInt64();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 104: {
                commentCount_ = input.readUInt64();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                repostCount_ = input.readUInt64();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 120: {
                saveCount_ = input.readUInt64();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 128: {
                score_ = input.readUInt64();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 136: {
                homePostsUpdate_ = input.readInt64();
                bitField0_ |= 0x00010000;
                break;
              } // case 136
              case 146: {
                input.readMessage(
                    getPollFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object id_ = "";
      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int postType_ = 0;
      /**
       * <code>.PostType post_type = 2;</code>
       * @return The enum numeric value on the wire for postType.
       */
      @java.lang.Override public int getPostTypeValue() {
        return postType_;
      }
      /**
       * <code>.PostType post_type = 2;</code>
       * @param value The enum numeric value on the wire for postType to set.
       * @return This builder for chaining.
       */
      public Builder setPostTypeValue(int value) {
        postType_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.PostType post_type = 2;</code>
       * @return The postType.
       */
      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.PostType getPostType() {
        com.Tlock.io.entity.post.PostProto.PostType result = com.Tlock.io.entity.post.PostProto.PostType.forNumber(postType_);
        return result == null ? com.Tlock.io.entity.post.PostProto.PostType.UNRECOGNIZED : result;
      }
      /**
       * <code>.PostType post_type = 2;</code>
       * @param value The postType to set.
       * @return This builder for chaining.
       */
      public Builder setPostType(com.Tlock.io.entity.post.PostProto.PostType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        postType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.PostType post_type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPostType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        postType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object parentId_ = "";
      /**
       * <code>string parent_id = 3;</code>
       * @return The parentId.
       */
      public java.lang.String getParentId() {
        java.lang.Object ref = parentId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          parentId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string parent_id = 3;</code>
       * @return The bytes for parentId.
       */
      public com.google.protobuf.ByteString
          getParentIdBytes() {
        java.lang.Object ref = parentId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          parentId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string parent_id = 3;</code>
       * @param value The parentId to set.
       * @return This builder for chaining.
       */
      public Builder setParentId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        parentId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string parent_id = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearParentId() {
        parentId_ = getDefaultInstance().getParentId();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string parent_id = 3;</code>
       * @param value The bytes for parentId to set.
       * @return This builder for chaining.
       */
      public Builder setParentIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        parentId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <code>string title = 4;</code>
       * @return The title.
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string title = 4;</code>
       * @return The bytes for title.
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string title = 4;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        title_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>string title = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        title_ = getDefaultInstance().getTitle();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>string title = 4;</code>
       * @param value The bytes for title to set.
       * @return This builder for chaining.
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        title_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <code>string content = 5;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string content = 5;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string content = 5;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        content_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>string content = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        content_ = getDefaultInstance().getContent();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <code>string content = 5;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        content_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object creator_ = "";
      /**
       * <code>string creator = 6;</code>
       * @return The creator.
       */
      public java.lang.String getCreator() {
        java.lang.Object ref = creator_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          creator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string creator = 6;</code>
       * @return The bytes for creator.
       */
      public com.google.protobuf.ByteString
          getCreatorBytes() {
        java.lang.Object ref = creator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          creator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string creator = 6;</code>
       * @param value The creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreator(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        creator_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreator() {
        creator_ = getDefaultInstance().getCreator();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <code>string creator = 6;</code>
       * @param value The bytes for creator to set.
       * @return This builder for chaining.
       */
      public Builder setCreatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        creator_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private long timestamp_ ;
      /**
       * <code>int64 timestamp = 7;</code>
       * @return The timestamp.
       */
      @java.lang.Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <code>int64 timestamp = 7;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {

        timestamp_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>int64 timestamp = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000040);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList imageIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureImageIdsIsMutable() {
        if (!imageIds_.isModifiable()) {
          imageIds_ = new com.google.protobuf.LazyStringArrayList(imageIds_);
        }
        bitField0_ |= 0x00000080;
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @return A list containing the imageIds.
       */
      public com.google.protobuf.ProtocolStringList
          getImageIdsList() {
        imageIds_.makeImmutable();
        return imageIds_;
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @return The count of imageIds.
       */
      public int getImageIdsCount() {
        return imageIds_.size();
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @param index The index of the element to return.
       * @return The imageIds at the given index.
       */
      public java.lang.String getImageIds(int index) {
        return imageIds_.get(index);
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the imageIds at the given index.
       */
      public com.google.protobuf.ByteString
          getImageIdsBytes(int index) {
        return imageIds_.getByteString(index);
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @param index The index to set the value at.
       * @param value The imageIds to set.
       * @return This builder for chaining.
       */
      public Builder setImageIds(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureImageIdsIsMutable();
        imageIds_.set(index, value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @param value The imageIds to add.
       * @return This builder for chaining.
       */
      public Builder addImageIds(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureImageIdsIsMutable();
        imageIds_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @param values The imageIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllImageIds(
          java.lang.Iterable<java.lang.String> values) {
        ensureImageIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, imageIds_);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearImageIds() {
        imageIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string image_ids = 8;</code>
       * @param value The bytes of the imageIds to add.
       * @return This builder for chaining.
       */
      public Builder addImageIdsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureImageIdsIsMutable();
        imageIds_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList imagesUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureImagesUrlIsMutable() {
        if (!imagesUrl_.isModifiable()) {
          imagesUrl_ = new com.google.protobuf.LazyStringArrayList(imagesUrl_);
        }
        bitField0_ |= 0x00000100;
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @return A list containing the imagesUrl.
       */
      public com.google.protobuf.ProtocolStringList
          getImagesUrlList() {
        imagesUrl_.makeImmutable();
        return imagesUrl_;
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @return The count of imagesUrl.
       */
      public int getImagesUrlCount() {
        return imagesUrl_.size();
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @param index The index of the element to return.
       * @return The imagesUrl at the given index.
       */
      public java.lang.String getImagesUrl(int index) {
        return imagesUrl_.get(index);
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @param index The index of the value to return.
       * @return The bytes of the imagesUrl at the given index.
       */
      public com.google.protobuf.ByteString
          getImagesUrlBytes(int index) {
        return imagesUrl_.getByteString(index);
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @param index The index to set the value at.
       * @param value The imagesUrl to set.
       * @return This builder for chaining.
       */
      public Builder setImagesUrl(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureImagesUrlIsMutable();
        imagesUrl_.set(index, value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @param value The imagesUrl to add.
       * @return This builder for chaining.
       */
      public Builder addImagesUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureImagesUrlIsMutable();
        imagesUrl_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @param values The imagesUrl to add.
       * @return This builder for chaining.
       */
      public Builder addAllImagesUrl(
          java.lang.Iterable<java.lang.String> values) {
        ensureImagesUrlIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, imagesUrl_);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearImagesUrl() {
        imagesUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string images_url = 9;</code>
       * @param value The bytes of the imagesUrl to add.
       * @return This builder for chaining.
       */
      public Builder addImagesUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureImagesUrlIsMutable();
        imagesUrl_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList videosUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureVideosUrlIsMutable() {
        if (!videosUrl_.isModifiable()) {
          videosUrl_ = new com.google.protobuf.LazyStringArrayList(videosUrl_);
        }
        bitField0_ |= 0x00000200;
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @return A list containing the videosUrl.
       */
      public com.google.protobuf.ProtocolStringList
          getVideosUrlList() {
        videosUrl_.makeImmutable();
        return videosUrl_;
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @return The count of videosUrl.
       */
      public int getVideosUrlCount() {
        return videosUrl_.size();
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @param index The index of the element to return.
       * @return The videosUrl at the given index.
       */
      public java.lang.String getVideosUrl(int index) {
        return videosUrl_.get(index);
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @param index The index of the value to return.
       * @return The bytes of the videosUrl at the given index.
       */
      public com.google.protobuf.ByteString
          getVideosUrlBytes(int index) {
        return videosUrl_.getByteString(index);
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @param index The index to set the value at.
       * @param value The videosUrl to set.
       * @return This builder for chaining.
       */
      public Builder setVideosUrl(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureVideosUrlIsMutable();
        videosUrl_.set(index, value);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @param value The videosUrl to add.
       * @return This builder for chaining.
       */
      public Builder addVideosUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureVideosUrlIsMutable();
        videosUrl_.add(value);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @param values The videosUrl to add.
       * @return This builder for chaining.
       */
      public Builder addAllVideosUrl(
          java.lang.Iterable<java.lang.String> values) {
        ensureVideosUrlIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, videosUrl_);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearVideosUrl() {
        videosUrl_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string videos_url = 10;</code>
       * @param value The bytes of the videosUrl to add.
       * @return This builder for chaining.
       */
      public Builder addVideosUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureVideosUrlIsMutable();
        videosUrl_.add(value);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object quote_ = "";
      /**
       * <code>string quote = 11;</code>
       * @return The quote.
       */
      public java.lang.String getQuote() {
        java.lang.Object ref = quote_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          quote_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string quote = 11;</code>
       * @return The bytes for quote.
       */
      public com.google.protobuf.ByteString
          getQuoteBytes() {
        java.lang.Object ref = quote_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          quote_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string quote = 11;</code>
       * @param value The quote to set.
       * @return This builder for chaining.
       */
      public Builder setQuote(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        quote_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>string quote = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuote() {
        quote_ = getDefaultInstance().getQuote();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <code>string quote = 11;</code>
       * @param value The bytes for quote to set.
       * @return This builder for chaining.
       */
      public Builder setQuoteBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        quote_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private long likeCount_ ;
      /**
       * <code>uint64 like_count = 12;</code>
       * @return The likeCount.
       */
      @java.lang.Override
      public long getLikeCount() {
        return likeCount_;
      }
      /**
       * <code>uint64 like_count = 12;</code>
       * @param value The likeCount to set.
       * @return This builder for chaining.
       */
      public Builder setLikeCount(long value) {

        likeCount_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 like_count = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeCount() {
        bitField0_ = (bitField0_ & ~0x00000800);
        likeCount_ = 0L;
        onChanged();
        return this;
      }

      private long commentCount_ ;
      /**
       * <code>uint64 comment_count = 13;</code>
       * @return The commentCount.
       */
      @java.lang.Override
      public long getCommentCount() {
        return commentCount_;
      }
      /**
       * <code>uint64 comment_count = 13;</code>
       * @param value The commentCount to set.
       * @return This builder for chaining.
       */
      public Builder setCommentCount(long value) {

        commentCount_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 comment_count = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommentCount() {
        bitField0_ = (bitField0_ & ~0x00001000);
        commentCount_ = 0L;
        onChanged();
        return this;
      }

      private long repostCount_ ;
      /**
       * <code>uint64 repost_count = 14;</code>
       * @return The repostCount.
       */
      @java.lang.Override
      public long getRepostCount() {
        return repostCount_;
      }
      /**
       * <code>uint64 repost_count = 14;</code>
       * @param value The repostCount to set.
       * @return This builder for chaining.
       */
      public Builder setRepostCount(long value) {

        repostCount_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 repost_count = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearRepostCount() {
        bitField0_ = (bitField0_ & ~0x00002000);
        repostCount_ = 0L;
        onChanged();
        return this;
      }

      private long saveCount_ ;
      /**
       * <code>uint64 save_count = 15;</code>
       * @return The saveCount.
       */
      @java.lang.Override
      public long getSaveCount() {
        return saveCount_;
      }
      /**
       * <code>uint64 save_count = 15;</code>
       * @param value The saveCount to set.
       * @return This builder for chaining.
       */
      public Builder setSaveCount(long value) {

        saveCount_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 save_count = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearSaveCount() {
        bitField0_ = (bitField0_ & ~0x00004000);
        saveCount_ = 0L;
        onChanged();
        return this;
      }

      private long score_ ;
      /**
       * <code>uint64 score = 16;</code>
       * @return The score.
       */
      @java.lang.Override
      public long getScore() {
        return score_;
      }
      /**
       * <code>uint64 score = 16;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(long value) {

        score_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 score = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00008000);
        score_ = 0L;
        onChanged();
        return this;
      }

      private long homePostsUpdate_ ;
      /**
       * <code>int64 homePostsUpdate = 17;</code>
       * @return The homePostsUpdate.
       */
      @java.lang.Override
      public long getHomePostsUpdate() {
        return homePostsUpdate_;
      }
      /**
       * <code>int64 homePostsUpdate = 17;</code>
       * @param value The homePostsUpdate to set.
       * @return This builder for chaining.
       */
      public Builder setHomePostsUpdate(long value) {

        homePostsUpdate_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>int64 homePostsUpdate = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearHomePostsUpdate() {
        bitField0_ = (bitField0_ & ~0x00010000);
        homePostsUpdate_ = 0L;
        onChanged();
        return this;
      }

      private com.Tlock.io.entity.post.PostProto.Poll poll_;
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.entity.post.PostProto.Poll, com.Tlock.io.entity.post.PostProto.Poll.Builder, com.Tlock.io.entity.post.PostProto.PollOrBuilder> pollBuilder_;
      /**
       * <code>.Poll poll = 18;</code>
       * @return Whether the poll field is set.
       */
      public boolean hasPoll() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>.Poll poll = 18;</code>
       * @return The poll.
       */
      public com.Tlock.io.entity.post.PostProto.Poll getPoll() {
        if (pollBuilder_ == null) {
          return poll_ == null ? com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance() : poll_;
        } else {
          return pollBuilder_.getMessage();
        }
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      public Builder setPoll(com.Tlock.io.entity.post.PostProto.Poll value) {
        if (pollBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          poll_ = value;
        } else {
          pollBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      public Builder setPoll(
          com.Tlock.io.entity.post.PostProto.Poll.Builder builderForValue) {
        if (pollBuilder_ == null) {
          poll_ = builderForValue.build();
        } else {
          pollBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      public Builder mergePoll(com.Tlock.io.entity.post.PostProto.Poll value) {
        if (pollBuilder_ == null) {
          if (((bitField0_ & 0x00020000) != 0) &&
            poll_ != null &&
            poll_ != com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance()) {
            getPollBuilder().mergeFrom(value);
          } else {
            poll_ = value;
          }
        } else {
          pollBuilder_.mergeFrom(value);
        }
        if (poll_ != null) {
          bitField0_ |= 0x00020000;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      public Builder clearPoll() {
        bitField0_ = (bitField0_ & ~0x00020000);
        poll_ = null;
        if (pollBuilder_ != null) {
          pollBuilder_.dispose();
          pollBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      public com.Tlock.io.entity.post.PostProto.Poll.Builder getPollBuilder() {
        bitField0_ |= 0x00020000;
        onChanged();
        return getPollFieldBuilder().getBuilder();
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      public com.Tlock.io.entity.post.PostProto.PollOrBuilder getPollOrBuilder() {
        if (pollBuilder_ != null) {
          return pollBuilder_.getMessageOrBuilder();
        } else {
          return poll_ == null ?
              com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance() : poll_;
        }
      }
      /**
       * <code>.Poll poll = 18;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.Tlock.io.entity.post.PostProto.Poll, com.Tlock.io.entity.post.PostProto.Poll.Builder, com.Tlock.io.entity.post.PostProto.PollOrBuilder> 
          getPollFieldBuilder() {
        if (pollBuilder_ == null) {
          pollBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.Tlock.io.entity.post.PostProto.Poll, com.Tlock.io.entity.post.PostProto.Poll.Builder, com.Tlock.io.entity.post.PostProto.PollOrBuilder>(
                  getPoll(),
                  getParentForChildren(),
                  isClean());
          poll_ = null;
        }
        return pollBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Post)
    }

    // @@protoc_insertion_point(class_scope:Post)
    private static final com.Tlock.io.entity.post.PostProto.Post DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.entity.post.PostProto.Post();
    }

    public static com.Tlock.io.entity.post.PostProto.Post getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Post>
        PARSER = new com.google.protobuf.AbstractParser<Post>() {
      @java.lang.Override
      public Post parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Post> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Post> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.Post getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PollOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Poll)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 totalVotes = 1;</code>
     * @return The totalVotes.
     */
    long getTotalVotes();

    /**
     * <code>int64 votingStart = 2;</code>
     * @return The votingStart.
     */
    long getVotingStart();

    /**
     * <code>int64 votingEnd = 3;</code>
     * @return The votingEnd.
     */
    long getVotingEnd();

    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    java.util.List<com.Tlock.io.entity.post.PostProto.Vote> 
        getVoteList();
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    com.Tlock.io.entity.post.PostProto.Vote getVote(int index);
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    int getVoteCount();
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    java.util.List<? extends com.Tlock.io.entity.post.PostProto.VoteOrBuilder> 
        getVoteOrBuilderList();
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    com.Tlock.io.entity.post.PostProto.VoteOrBuilder getVoteOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code Poll}
   */
  public static final class Poll extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Poll)
      PollOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Poll.class.getName());
    }
    // Use Poll.newBuilder() to construct.
    private Poll(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Poll() {
      vote_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.entity.post.PostProto.internal_static_Poll_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.entity.post.PostProto.internal_static_Poll_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.entity.post.PostProto.Poll.class, com.Tlock.io.entity.post.PostProto.Poll.Builder.class);
    }

    public static final int TOTALVOTES_FIELD_NUMBER = 1;
    private long totalVotes_ = 0L;
    /**
     * <code>int64 totalVotes = 1;</code>
     * @return The totalVotes.
     */
    @java.lang.Override
    public long getTotalVotes() {
      return totalVotes_;
    }

    public static final int VOTINGSTART_FIELD_NUMBER = 2;
    private long votingStart_ = 0L;
    /**
     * <code>int64 votingStart = 2;</code>
     * @return The votingStart.
     */
    @java.lang.Override
    public long getVotingStart() {
      return votingStart_;
    }

    public static final int VOTINGEND_FIELD_NUMBER = 3;
    private long votingEnd_ = 0L;
    /**
     * <code>int64 votingEnd = 3;</code>
     * @return The votingEnd.
     */
    @java.lang.Override
    public long getVotingEnd() {
      return votingEnd_;
    }

    public static final int VOTE_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<com.Tlock.io.entity.post.PostProto.Vote> vote_;
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.Tlock.io.entity.post.PostProto.Vote> getVoteList() {
      return vote_;
    }
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.Tlock.io.entity.post.PostProto.VoteOrBuilder> 
        getVoteOrBuilderList() {
      return vote_;
    }
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    @java.lang.Override
    public int getVoteCount() {
      return vote_.size();
    }
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.Vote getVote(int index) {
      return vote_.get(index);
    }
    /**
     * <code>repeated .Vote vote = 4;</code>
     */
    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.VoteOrBuilder getVoteOrBuilder(
        int index) {
      return vote_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (totalVotes_ != 0L) {
        output.writeInt64(1, totalVotes_);
      }
      if (votingStart_ != 0L) {
        output.writeInt64(2, votingStart_);
      }
      if (votingEnd_ != 0L) {
        output.writeInt64(3, votingEnd_);
      }
      for (int i = 0; i < vote_.size(); i++) {
        output.writeMessage(4, vote_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (totalVotes_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, totalVotes_);
      }
      if (votingStart_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, votingStart_);
      }
      if (votingEnd_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, votingEnd_);
      }
      for (int i = 0; i < vote_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, vote_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.entity.post.PostProto.Poll)) {
        return super.equals(obj);
      }
      com.Tlock.io.entity.post.PostProto.Poll other = (com.Tlock.io.entity.post.PostProto.Poll) obj;

      if (getTotalVotes()
          != other.getTotalVotes()) return false;
      if (getVotingStart()
          != other.getVotingStart()) return false;
      if (getVotingEnd()
          != other.getVotingEnd()) return false;
      if (!getVoteList()
          .equals(other.getVoteList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TOTALVOTES_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTotalVotes());
      hash = (37 * hash) + VOTINGSTART_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVotingStart());
      hash = (37 * hash) + VOTINGEND_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVotingEnd());
      if (getVoteCount() > 0) {
        hash = (37 * hash) + VOTE_FIELD_NUMBER;
        hash = (53 * hash) + getVoteList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.entity.post.PostProto.Poll parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.entity.post.PostProto.Poll parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.post.PostProto.Poll parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.entity.post.PostProto.Poll prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Poll}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Poll)
        com.Tlock.io.entity.post.PostProto.PollOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Poll_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Poll_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.entity.post.PostProto.Poll.class, com.Tlock.io.entity.post.PostProto.Poll.Builder.class);
      }

      // Construct using com.Tlock.io.entity.post.PostProto.Poll.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        totalVotes_ = 0L;
        votingStart_ = 0L;
        votingEnd_ = 0L;
        if (voteBuilder_ == null) {
          vote_ = java.util.Collections.emptyList();
        } else {
          vote_ = null;
          voteBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Poll_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Poll getDefaultInstanceForType() {
        return com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Poll build() {
        com.Tlock.io.entity.post.PostProto.Poll result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Poll buildPartial() {
        com.Tlock.io.entity.post.PostProto.Poll result = new com.Tlock.io.entity.post.PostProto.Poll(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.Tlock.io.entity.post.PostProto.Poll result) {
        if (voteBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            vote_ = java.util.Collections.unmodifiableList(vote_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.vote_ = vote_;
        } else {
          result.vote_ = voteBuilder_.build();
        }
      }

      private void buildPartial0(com.Tlock.io.entity.post.PostProto.Poll result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.totalVotes_ = totalVotes_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.votingStart_ = votingStart_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.votingEnd_ = votingEnd_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.entity.post.PostProto.Poll) {
          return mergeFrom((com.Tlock.io.entity.post.PostProto.Poll)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.entity.post.PostProto.Poll other) {
        if (other == com.Tlock.io.entity.post.PostProto.Poll.getDefaultInstance()) return this;
        if (other.getTotalVotes() != 0L) {
          setTotalVotes(other.getTotalVotes());
        }
        if (other.getVotingStart() != 0L) {
          setVotingStart(other.getVotingStart());
        }
        if (other.getVotingEnd() != 0L) {
          setVotingEnd(other.getVotingEnd());
        }
        if (voteBuilder_ == null) {
          if (!other.vote_.isEmpty()) {
            if (vote_.isEmpty()) {
              vote_ = other.vote_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureVoteIsMutable();
              vote_.addAll(other.vote_);
            }
            onChanged();
          }
        } else {
          if (!other.vote_.isEmpty()) {
            if (voteBuilder_.isEmpty()) {
              voteBuilder_.dispose();
              voteBuilder_ = null;
              vote_ = other.vote_;
              bitField0_ = (bitField0_ & ~0x00000008);
              voteBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getVoteFieldBuilder() : null;
            } else {
              voteBuilder_.addAllMessages(other.vote_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                totalVotes_ = input.readInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                votingStart_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                votingEnd_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                com.Tlock.io.entity.post.PostProto.Vote m =
                    input.readMessage(
                        com.Tlock.io.entity.post.PostProto.Vote.parser(),
                        extensionRegistry);
                if (voteBuilder_ == null) {
                  ensureVoteIsMutable();
                  vote_.add(m);
                } else {
                  voteBuilder_.addMessage(m);
                }
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long totalVotes_ ;
      /**
       * <code>int64 totalVotes = 1;</code>
       * @return The totalVotes.
       */
      @java.lang.Override
      public long getTotalVotes() {
        return totalVotes_;
      }
      /**
       * <code>int64 totalVotes = 1;</code>
       * @param value The totalVotes to set.
       * @return This builder for chaining.
       */
      public Builder setTotalVotes(long value) {

        totalVotes_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int64 totalVotes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalVotes() {
        bitField0_ = (bitField0_ & ~0x00000001);
        totalVotes_ = 0L;
        onChanged();
        return this;
      }

      private long votingStart_ ;
      /**
       * <code>int64 votingStart = 2;</code>
       * @return The votingStart.
       */
      @java.lang.Override
      public long getVotingStart() {
        return votingStart_;
      }
      /**
       * <code>int64 votingStart = 2;</code>
       * @param value The votingStart to set.
       * @return This builder for chaining.
       */
      public Builder setVotingStart(long value) {

        votingStart_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 votingStart = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVotingStart() {
        bitField0_ = (bitField0_ & ~0x00000002);
        votingStart_ = 0L;
        onChanged();
        return this;
      }

      private long votingEnd_ ;
      /**
       * <code>int64 votingEnd = 3;</code>
       * @return The votingEnd.
       */
      @java.lang.Override
      public long getVotingEnd() {
        return votingEnd_;
      }
      /**
       * <code>int64 votingEnd = 3;</code>
       * @param value The votingEnd to set.
       * @return This builder for chaining.
       */
      public Builder setVotingEnd(long value) {

        votingEnd_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 votingEnd = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVotingEnd() {
        bitField0_ = (bitField0_ & ~0x00000004);
        votingEnd_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<com.Tlock.io.entity.post.PostProto.Vote> vote_ =
        java.util.Collections.emptyList();
      private void ensureVoteIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          vote_ = new java.util.ArrayList<com.Tlock.io.entity.post.PostProto.Vote>(vote_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.post.PostProto.Vote, com.Tlock.io.entity.post.PostProto.Vote.Builder, com.Tlock.io.entity.post.PostProto.VoteOrBuilder> voteBuilder_;

      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public java.util.List<com.Tlock.io.entity.post.PostProto.Vote> getVoteList() {
        if (voteBuilder_ == null) {
          return java.util.Collections.unmodifiableList(vote_);
        } else {
          return voteBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public int getVoteCount() {
        if (voteBuilder_ == null) {
          return vote_.size();
        } else {
          return voteBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public com.Tlock.io.entity.post.PostProto.Vote getVote(int index) {
        if (voteBuilder_ == null) {
          return vote_.get(index);
        } else {
          return voteBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder setVote(
          int index, com.Tlock.io.entity.post.PostProto.Vote value) {
        if (voteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureVoteIsMutable();
          vote_.set(index, value);
          onChanged();
        } else {
          voteBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder setVote(
          int index, com.Tlock.io.entity.post.PostProto.Vote.Builder builderForValue) {
        if (voteBuilder_ == null) {
          ensureVoteIsMutable();
          vote_.set(index, builderForValue.build());
          onChanged();
        } else {
          voteBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder addVote(com.Tlock.io.entity.post.PostProto.Vote value) {
        if (voteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureVoteIsMutable();
          vote_.add(value);
          onChanged();
        } else {
          voteBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder addVote(
          int index, com.Tlock.io.entity.post.PostProto.Vote value) {
        if (voteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureVoteIsMutable();
          vote_.add(index, value);
          onChanged();
        } else {
          voteBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder addVote(
          com.Tlock.io.entity.post.PostProto.Vote.Builder builderForValue) {
        if (voteBuilder_ == null) {
          ensureVoteIsMutable();
          vote_.add(builderForValue.build());
          onChanged();
        } else {
          voteBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder addVote(
          int index, com.Tlock.io.entity.post.PostProto.Vote.Builder builderForValue) {
        if (voteBuilder_ == null) {
          ensureVoteIsMutable();
          vote_.add(index, builderForValue.build());
          onChanged();
        } else {
          voteBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder addAllVote(
          java.lang.Iterable<? extends com.Tlock.io.entity.post.PostProto.Vote> values) {
        if (voteBuilder_ == null) {
          ensureVoteIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, vote_);
          onChanged();
        } else {
          voteBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder clearVote() {
        if (voteBuilder_ == null) {
          vote_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          voteBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public Builder removeVote(int index) {
        if (voteBuilder_ == null) {
          ensureVoteIsMutable();
          vote_.remove(index);
          onChanged();
        } else {
          voteBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public com.Tlock.io.entity.post.PostProto.Vote.Builder getVoteBuilder(
          int index) {
        return getVoteFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public com.Tlock.io.entity.post.PostProto.VoteOrBuilder getVoteOrBuilder(
          int index) {
        if (voteBuilder_ == null) {
          return vote_.get(index);  } else {
          return voteBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public java.util.List<? extends com.Tlock.io.entity.post.PostProto.VoteOrBuilder> 
           getVoteOrBuilderList() {
        if (voteBuilder_ != null) {
          return voteBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(vote_);
        }
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public com.Tlock.io.entity.post.PostProto.Vote.Builder addVoteBuilder() {
        return getVoteFieldBuilder().addBuilder(
            com.Tlock.io.entity.post.PostProto.Vote.getDefaultInstance());
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public com.Tlock.io.entity.post.PostProto.Vote.Builder addVoteBuilder(
          int index) {
        return getVoteFieldBuilder().addBuilder(
            index, com.Tlock.io.entity.post.PostProto.Vote.getDefaultInstance());
      }
      /**
       * <code>repeated .Vote vote = 4;</code>
       */
      public java.util.List<com.Tlock.io.entity.post.PostProto.Vote.Builder> 
           getVoteBuilderList() {
        return getVoteFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.Tlock.io.entity.post.PostProto.Vote, com.Tlock.io.entity.post.PostProto.Vote.Builder, com.Tlock.io.entity.post.PostProto.VoteOrBuilder> 
          getVoteFieldBuilder() {
        if (voteBuilder_ == null) {
          voteBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.Tlock.io.entity.post.PostProto.Vote, com.Tlock.io.entity.post.PostProto.Vote.Builder, com.Tlock.io.entity.post.PostProto.VoteOrBuilder>(
                  vote_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          vote_ = null;
        }
        return voteBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Poll)
    }

    // @@protoc_insertion_point(class_scope:Poll)
    private static final com.Tlock.io.entity.post.PostProto.Poll DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.entity.post.PostProto.Poll();
    }

    public static com.Tlock.io.entity.post.PostProto.Poll getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Poll>
        PARSER = new com.google.protobuf.AbstractParser<Poll>() {
      @java.lang.Override
      public Poll parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Poll> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Poll> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.Poll getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface VoteOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Vote)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string option = 1;</code>
     * @return The option.
     */
    java.lang.String getOption();
    /**
     * <code>string option = 1;</code>
     * @return The bytes for option.
     */
    com.google.protobuf.ByteString
        getOptionBytes();

    /**
     * <code>int64 count = 2;</code>
     * @return The count.
     */
    long getCount();

    /**
     * <code>int64 id = 3;</code>
     * @return The id.
     */
    long getId();
  }
  /**
   * Protobuf type {@code Vote}
   */
  public static final class Vote extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Vote)
      VoteOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Vote.class.getName());
    }
    // Use Vote.newBuilder() to construct.
    private Vote(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Vote() {
      option_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.entity.post.PostProto.internal_static_Vote_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.entity.post.PostProto.internal_static_Vote_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.entity.post.PostProto.Vote.class, com.Tlock.io.entity.post.PostProto.Vote.Builder.class);
    }

    public static final int OPTION_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object option_ = "";
    /**
     * <code>string option = 1;</code>
     * @return The option.
     */
    @java.lang.Override
    public java.lang.String getOption() {
      java.lang.Object ref = option_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        option_ = s;
        return s;
      }
    }
    /**
     * <code>string option = 1;</code>
     * @return The bytes for option.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOptionBytes() {
      java.lang.Object ref = option_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        option_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private long count_ = 0L;
    /**
     * <code>int64 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public long getCount() {
      return count_;
    }

    public static final int ID_FIELD_NUMBER = 3;
    private long id_ = 0L;
    /**
     * <code>int64 id = 3;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(option_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, option_);
      }
      if (count_ != 0L) {
        output.writeInt64(2, count_);
      }
      if (id_ != 0L) {
        output.writeInt64(3, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(option_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, option_);
      }
      if (count_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, count_);
      }
      if (id_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.entity.post.PostProto.Vote)) {
        return super.equals(obj);
      }
      com.Tlock.io.entity.post.PostProto.Vote other = (com.Tlock.io.entity.post.PostProto.Vote) obj;

      if (!getOption()
          .equals(other.getOption())) return false;
      if (getCount()
          != other.getCount()) return false;
      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + OPTION_FIELD_NUMBER;
      hash = (53 * hash) + getOption().hashCode();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCount());
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.entity.post.PostProto.Vote parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.entity.post.PostProto.Vote parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.post.PostProto.Vote parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.entity.post.PostProto.Vote prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Vote}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Vote)
        com.Tlock.io.entity.post.PostProto.VoteOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Vote_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Vote_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.entity.post.PostProto.Vote.class, com.Tlock.io.entity.post.PostProto.Vote.Builder.class);
      }

      // Construct using com.Tlock.io.entity.post.PostProto.Vote.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        option_ = "";
        count_ = 0L;
        id_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.entity.post.PostProto.internal_static_Vote_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Vote getDefaultInstanceForType() {
        return com.Tlock.io.entity.post.PostProto.Vote.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Vote build() {
        com.Tlock.io.entity.post.PostProto.Vote result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.entity.post.PostProto.Vote buildPartial() {
        com.Tlock.io.entity.post.PostProto.Vote result = new com.Tlock.io.entity.post.PostProto.Vote(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.entity.post.PostProto.Vote result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.option_ = option_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.count_ = count_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.entity.post.PostProto.Vote) {
          return mergeFrom((com.Tlock.io.entity.post.PostProto.Vote)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.entity.post.PostProto.Vote other) {
        if (other == com.Tlock.io.entity.post.PostProto.Vote.getDefaultInstance()) return this;
        if (!other.getOption().isEmpty()) {
          option_ = other.option_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getCount() != 0L) {
          setCount(other.getCount());
        }
        if (other.getId() != 0L) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                option_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                count_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                id_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object option_ = "";
      /**
       * <code>string option = 1;</code>
       * @return The option.
       */
      public java.lang.String getOption() {
        java.lang.Object ref = option_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          option_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string option = 1;</code>
       * @return The bytes for option.
       */
      public com.google.protobuf.ByteString
          getOptionBytes() {
        java.lang.Object ref = option_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          option_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string option = 1;</code>
       * @param value The option to set.
       * @return This builder for chaining.
       */
      public Builder setOption(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        option_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string option = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOption() {
        option_ = getDefaultInstance().getOption();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string option = 1;</code>
       * @param value The bytes for option to set.
       * @return This builder for chaining.
       */
      public Builder setOptionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        option_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private long count_ ;
      /**
       * <code>int64 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public long getCount() {
        return count_;
      }
      /**
       * <code>int64 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(long value) {

        count_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        bitField0_ = (bitField0_ & ~0x00000002);
        count_ = 0L;
        onChanged();
        return this;
      }

      private long id_ ;
      /**
       * <code>int64 id = 3;</code>
       * @return The id.
       */
      @java.lang.Override
      public long getId() {
        return id_;
      }
      /**
       * <code>int64 id = 3;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(long value) {

        id_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 id = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        id_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Vote)
    }

    // @@protoc_insertion_point(class_scope:Vote)
    private static final com.Tlock.io.entity.post.PostProto.Vote DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.entity.post.PostProto.Vote();
    }

    public static com.Tlock.io.entity.post.PostProto.Vote getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Vote>
        PARSER = new com.google.protobuf.AbstractParser<Vote>() {
      @java.lang.Override
      public Vote parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Vote> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Vote> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.entity.post.PostProto.Vote getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Post_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Post_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Poll_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Poll_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Vote_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Vote_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021entity/post.proto\"\343\002\n\004Post\022\n\n\002id\030\001 \001(\t" +
      "\022\034\n\tpost_type\030\002 \001(\0162\t.PostType\022\021\n\tparent" +
      "_id\030\003 \001(\t\022\r\n\005title\030\004 \001(\t\022\017\n\007content\030\005 \001(" +
      "\t\022\017\n\007creator\030\006 \001(\t\022\021\n\ttimestamp\030\007 \001(\003\022\021\n" +
      "\timage_ids\030\010 \003(\t\022\022\n\nimages_url\030\t \003(\t\022\022\n\n" +
      "videos_url\030\n \003(\t\022\r\n\005quote\030\013 \001(\t\022\022\n\nlike_" +
      "count\030\014 \001(\004\022\025\n\rcomment_count\030\r \001(\004\022\024\n\014re" +
      "post_count\030\016 \001(\004\022\022\n\nsave_count\030\017 \001(\004\022\r\n\005" +
      "score\030\020 \001(\004\022\027\n\017homePostsUpdate\030\021 \001(\003\022\023\n\004" +
      "poll\030\022 \001(\0132\005.Poll\"W\n\004Poll\022\022\n\ntotalVotes\030" +
      "\001 \001(\003\022\023\n\013votingStart\030\002 \001(\003\022\021\n\tvotingEnd\030" +
      "\003 \001(\003\022\023\n\004vote\030\004 \003(\0132\005.Vote\"1\n\004Vote\022\016\n\006op" +
      "tion\030\001 \001(\t\022\r\n\005count\030\002 \001(\003\022\n\n\002id\030\003 \001(\003*f\n" +
      "\010PostType\022\014\n\010ORIGINAL\020\000\022\013\n\007ARTICLE\020\001\022\021\n\r" +
      "ADVERTISEMENT\020\002\022\t\n\005QUOTE\020\003\022\n\n\006REPOST\020\004\022\010" +
      "\n\004POLL\020\005\022\013\n\007COMMENT\020\006B%\n\030com.Tlock.io.en" +
      "tity.postB\tPostProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Post_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Post_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Post_descriptor,
        new java.lang.String[] { "Id", "PostType", "ParentId", "Title", "Content", "Creator", "Timestamp", "ImageIds", "ImagesUrl", "VideosUrl", "Quote", "LikeCount", "CommentCount", "RepostCount", "SaveCount", "Score", "HomePostsUpdate", "Poll", });
    internal_static_Poll_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Poll_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Poll_descriptor,
        new java.lang.String[] { "TotalVotes", "VotingStart", "VotingEnd", "Vote", });
    internal_static_Vote_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Vote_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Vote_descriptor,
        new java.lang.String[] { "Option", "Count", "Id", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
