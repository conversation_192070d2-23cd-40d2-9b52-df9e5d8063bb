<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <EditText
        android:id="@+id/ed_search"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_16"
        android:background="@drawable/btn_gray_60"
        android:hint="Search for topics"
        android:maxLines="1"
        android:paddingLeft="@dimen/dp_18"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="@dimen/dp_18"
        android:paddingBottom="@dimen/dp_10"
        android:singleLine="true"
        android:textColorHint="@color/hint_color"
        android:textSize="@dimen/sp_14"
        android:theme="@style/edit_text" />

    <View
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:layout_below="@id/ed_search"
        android:background="@color/gray_color" />

    <LinearLayout
        android:id="@+id/ll_tab"
        android:layout_width="@dimen/dp_100"
        android:layout_height="match_parent"
        android:layout_below="@id/ed_search"
        android:background="@color/gray_color"
        android:orientation="vertical">

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab1"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab2"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab3"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab4"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab5"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab6"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab7"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.Tlock.io.itemBean.cosmos.TopicTabView
            android:id="@+id/tab8"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_sort"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/ed_search"
        android:layout_toRightOf="@id/ll_tab" />

</RelativeLayout>