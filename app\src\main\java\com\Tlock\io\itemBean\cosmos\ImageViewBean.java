package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.UserUtil.dip2px;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;

import java.io.File;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * @ClassName LPHistoryBean
 * <AUTHOR>
 * @Data 2022/3/24 15:41
 * @Desc
 */

public class ImageViewBean extends BaseView {

    @BindView(R.id.iv_image)
    ImageView mIvImage;
    @BindView(R.id.iv_delete)
    ImageView mIvDelete;

    private File file;
    private int from = 0;

    /**
     * @param context
     * @param form    0添加 1展示
     */
    public ImageViewBean(Context context, int form) {
        super(context);
        this.from = form;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_image;
    }

    public ImageViewBean(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ImageViewBean(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public void setData(File file) {
        this.file = file;
        if (file == null) {
            Glide.with(mIvImage).load(R.mipmap.icon_add_image)
                    .centerCrop()
                    .into(mIvImage);
            mIvDelete.setVisibility(GONE);
        } else {
            int radiusPx = dip2px(getContext(), 6);
            RequestOptions options = new RequestOptions()
                    .transform(new MultiTransformation<>(
                            new CenterCrop(),
                            new RoundedCorners(radiusPx)
                    ));

            Glide.with(mIvImage).load(file)
                    .apply(options)
                    .into(mIvImage);
            mIvDelete.setVisibility(VISIBLE);

        }

    }


    @OnClick({R.id.iv_image, R.id.iv_delete})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_image:
                if (file == null && from == 0) {
                    if (callback != null) {
                        callback.onAdd();
                    }
                    return;
                }
                if (callback != null) {
                    callback.onSelect(mIvImage);
                }
                break;

            case R.id.iv_delete:
                if (callback != null) {
                    callback.onDelete(file);
                }
                break;
        }
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;

    }

    public interface Callback {
        void onDelete(File file);

        void onAdd();

        void onSelect(ImageView mIvImage);
    }
}
