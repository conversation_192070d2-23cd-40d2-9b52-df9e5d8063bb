package com.Tlock.io.fragment;


import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.ContentInfoActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.Event;
import com.Tlock.io.itemBean.cosmos.MessageItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.NewCosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.pop.PopEdit;
import com.lxj.xpopup.XPopup;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;


/**
 * 交易fragment
 */
public class MessageFragment extends LazyLoadBaseFragment {

    @BindView(R.id.tv_title)
    TextView mTvTitle;

    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    @BindView(R.id.message_rv)
    RecyclerView mMessageRv;
    private BaseRecyclerViewAdapter<PostQueryProto.ActivitiesReceivedResponse> adapter;
    private Pattern mentionPattern = Pattern.compile("@\\w+");

    @Override
    protected int getContentViewId() {
        return R.layout.activity_message;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRecycleView();
    }

    @Override
    protected void loadData() {
        getMessageData();
    }

    @Override
    protected void getData() {
        super.getData();
        getMessageData();
    }

    private void getMessageData() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.ActivitiesReceivedResponse> msgList = CosmosUtils.getMsgList(page);
                getActivity().runOnUiThread(new Runnable() {

                    @Override
                    public void run() {
                        if (msgList != null && msgList.size() > 0) {
                            adapter.addListNoChange(msgList, page);
                            hideError();
                            mRefreshLayout.finishRefresh();
                            mRefreshLayout.finishLoadMore();
                        }
                        if (adapter.getItemCount() == 0) {
                            showError("No new notifications. Start posting!");
                        }

                    }
                });
            }
        });
    }

    private void initRecycleView() {

        mMessageRv.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getContext(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.ActivitiesReceivedResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                MessageItemView itemView = new MessageItemView(getContext());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.ActivitiesReceivedResponse data, View view) {
                ((MessageItemView) view).setGlideTag(data.getParentPost().getId());
                ((MessageItemView) view).setData(data);
                ((MessageItemView) view).setCallback(new MessageItemView.Callback() {
                    @Override
                    public void like(String id) {
                        showToast("Liked");
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                NewCosmosUtils.postLike(WalletDaoUtils.getCurrent().getAddress(), id);
                            }
                        });
                    }

                    @Override
                    public void reply(String id) {
                        PopEdit popEdit = new PopEdit(getActivity(),
                                "",
                                "Comment",
                                "cancel",
                                "Reply",
                                false);
                        popEdit.setCallback(new PopEdit.Callback() {
                            @Override
                            public void notShow() {

                            }

                            @Override
                            public void confirm(String title) {
                                showToast("Commented");
                                ArrayList<String> currentMentions = new ArrayList<>();
//                                SpannableStringBuilder spannable = new SpannableStringBuilder(title);
                                Matcher mentionMatcher = mentionPattern.matcher(title);
                                // 处理提及
                                while (mentionMatcher.find()) {
                                    String mention = mentionMatcher.group(); // 获取匹配的 @标签
                                    currentMentions.add(mention.replace("@", "")); // 添加到当前提及列表
                                }
                                //上传设置数据
                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        CosmosUtils.postComment(title, WalletDaoUtils.getCurrent().getAddress(), id, currentMentions);
                                    }
                                });
                            }

                            @Override
                            public void finger() {

                            }
                        });
                        new XPopup.Builder(getActivity())
                                .hasShadowBg(true)
                                .isDestroyOnDismiss(true)
                                .asCustom(popEdit).show();
                    }

                    @Override
                    public void click(String id) {
                        ContentInfoActivity.start(getActivity(), id, false);

                    }

                    @Override
                    public void resetProfile(PostQueryProto.ActivitiesReceivedResponse data) {
                        adapter.getList().set(position, data);
                    }
                });
            }
        });
        mMessageRv.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.ActivitiesReceivedResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.ActivitiesReceivedResponse data, View view) {
                EventBus.getDefault().postSticky(new Event("info.Post", data));
            }
        });
    }
}