package com.Tlock.io.activity.wallet;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.wallet.WalletViewBean;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.MyItemTouchHelper;
import com.Tlock.io.widget.pop.PopTip;
import com.lxj.xpopup.XPopup;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

import butterknife.BindView;

public class WalletOrderActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;

    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    private ItemTouchHelper mItemTouchHelper;
    private ArrayList<ETHWallet> ethWallets;
    private BaseRecyclerViewAdapter<ETHWallet> adapter;


    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, WalletOrderActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_wallet_order;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRecyclerView();
    }

    private void initRecyclerView() {
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PopTip popTip = new PopTip(getActivity(),
                        getResources().getString(R.string.save_rank),
                        getResources().getString(R.string.rank),
                        "取消",
                        "确认",
                        true, true);
                popTip.setCallback(new PopTip.Callback() {
                    @Override
                    public void notShow() {
                    }

                    @Override
                    public void confirm() {
                        ArrayList<ETHWallet> list = adapter.getList();
                        for (int i = 0; i < list.size(); i++) {
                            WalletDaoUtils.updateWalletOrder(list.get(i).getId(), i + 1);
                        }
                        EventBus.getDefault().postSticky(new Event(EventConstant.REFRESH_WALLET_LIST));

                        finish();

                    }
                });
                new XPopup.Builder(getActivity())
                        .hasShadowBg(true)
                        .isDestroyOnDismiss(true)
                        .asCustom(popTip).show();
            }
        });

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        ethWallets = (ArrayList<ETHWallet>) WalletDaoUtils.loadAll();
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), ethWallets, new BaseRecyclerViewAdapter.Delegate<ETHWallet>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                WalletViewBean itemView = new WalletViewBean(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, ETHWallet data, View view) {
                ((WalletViewBean) view).setData(data, 2);
            }
        });
        mRecyclerView.setAdapter(adapter);
        mItemTouchHelper = new ItemTouchHelper(new MyItemTouchHelper(getActivity(), ethWallets, adapter));
        mItemTouchHelper.attachToRecyclerView(mRecyclerView);

    }

    @Override
    protected void loadData() {

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {

            PopTip popTip = new PopTip(getActivity(),
                    getResources().getString(R.string.save_rank),
                    getResources().getString(R.string.rank),
                    "取消",
                    "确认",
                    true, true);
            popTip.setCallback(new PopTip.Callback() {
                @Override
                public void notShow() {
                }

                @Override
                public void confirm() {
                    ArrayList<ETHWallet> list = adapter.getList();
                    for (int i = 0; i < list.size(); i++) {
                        WalletDaoUtils.updateWalletOrder(list.get(i).getId(), i + 1);
                    }
                    EventBus.getDefault().postSticky(new Event(EventConstant.REFRESH_WALLET_LIST));

                    finish();

                }
            });
            new XPopup.Builder(getActivity())
                    .hasShadowBg(true)
                    .isDestroyOnDismiss(true)
                    .asCustom(popTip).show();
            return true;
        } else {
            return super.onKeyDown(keyCode, event);

        }
    }
}