// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class BackupActivity_ViewBinding implements Unbinder {
  private BackupActivity target;

  private View view7f09024b;

  private View view7f090248;

  @UiThread
  public BackupActivity_ViewBinding(BackupActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public BackupActivity_ViewBinding(final BackupActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.rl_mnemonic, "field 'mRlMnemonic' and method 'onViewClicked'");
    target.mRlMnemonic = Utils.castView(view, R.id.rl_mnemonic, "field 'mRlMnemonic'", RelativeLayout.class);
    view7f09024b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_key, "field 'mRlKey' and method 'onViewClicked'");
    target.mRlKey = Utils.castView(view, R.id.rl_key, "field 'mRlKey'", RelativeLayout.class);
    view7f090248 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mIvBack = Utils.findRequiredViewAsType(source, R.id.iv_back, "field 'mIvBack'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    BackupActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRlMnemonic = null;
    target.mRlKey = null;
    target.mIvBack = null;

    view7f09024b.setOnClickListener(null);
    view7f09024b = null;
    view7f090248.setOnClickListener(null);
    view7f090248 = null;
  }
}
