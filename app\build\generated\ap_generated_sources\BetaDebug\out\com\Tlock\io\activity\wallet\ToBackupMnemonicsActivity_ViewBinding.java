// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ToBackupMnemonicsActivity_ViewBinding implements Unbinder {
  private ToBackupMnemonicsActivity target;

  private View view7f090304;

  @UiThread
  public ToBackupMnemonicsActivity_ViewBinding(ToBackupMnemonicsActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public ToBackupMnemonicsActivity_ViewBinding(final ToBackupMnemonicsActivity target,
      View source) {
    this.target = target;

    View view;
    target.mIvBack = Utils.findRequiredViewAsType(source, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    target.mRlToolbar = Utils.findRequiredViewAsType(source, R.id.rl_toolbar, "field 'mRlToolbar'", RelativeLayout.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_backup, "field 'mTvBackup' and method 'onViewClicked'");
    target.mTvBackup = Utils.castView(view, R.id.tv_backup, "field 'mTvBackup'", TextView.class);
    view7f090304 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked();
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    ToBackupMnemonicsActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mRlToolbar = null;
    target.mTvTitle = null;
    target.mTv1 = null;
    target.mTvBackup = null;

    view7f090304.setOnClickListener(null);
    view7f090304 = null;
  }
}
