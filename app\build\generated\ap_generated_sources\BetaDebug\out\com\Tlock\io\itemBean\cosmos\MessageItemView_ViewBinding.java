// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.RoundedImageView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MessageItemView_ViewBinding implements Unbinder {
  private MessageItemView target;

  private View view7f09014b;

  private View view7f09035a;

  private View view7f090151;

  private View view7f090323;

  private View view7f090239;

  @UiThread
  public MessageItemView_ViewBinding(MessageItemView target) {
    this(target, target);
  }

  @UiThread
  public MessageItemView_ViewBinding(final MessageItemView target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_heard, "field 'mIvHeard' and method 'onBindClick'");
    target.mIvHeard = Utils.castView(view, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    view7f09014b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvStatus = Utils.findRequiredViewAsType(source, R.id.iv_status, "field 'mIvStatus'", ImageView.class);
    target.mTvAccountName = Utils.findRequiredViewAsType(source, R.id.tv_account_name, "field 'mTvAccountName'", FontTextView.class);
    target.mTvContent = Utils.findRequiredViewAsType(source, R.id.tv_content, "field 'mTvContent'", FontTextView.class);
    target.mRlName = Utils.findRequiredViewAsType(source, R.id.rl_name, "field 'mRlName'", RelativeLayout.class);
    target.mTvQuoteTitle = Utils.findRequiredViewAsType(source, R.id.tv_quote_title, "field 'mTvQuoteTitle'", TextView.class);
    target.mTvQuoteContent = Utils.findRequiredViewAsType(source, R.id.tv_quote_content, "field 'mTvQuoteContent'", TextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_reply, "field 'mTvReply' and method 'onBindClick'");
    target.mTvReply = Utils.castView(view, R.id.tv_reply, "field 'mTvReply'", TextView.class);
    view7f09035a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_like, "field 'mIvLike' and method 'onBindClick'");
    target.mIvLike = Utils.castView(view, R.id.iv_like, "field 'mIvLike'", ImageView.class);
    view7f090151 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_follow, "field 'mTvFollow' and method 'onBindClick'");
    target.mTvFollow = Utils.castView(view, R.id.tv_follow, "field 'mTvFollow'", TextView.class);
    view7f090323 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mIvQuoteHeard = Utils.findRequiredViewAsType(source, R.id.iv_quote_heard, "field 'mIvQuoteHeard'", RoundedImageView.class);
    view = Utils.findRequiredView(source, R.id.rl_content_quote, "field 'mRlContentQuote' and method 'onBindClick'");
    target.mRlContentQuote = Utils.castView(view, R.id.rl_content_quote, "field 'mRlContentQuote'", RelativeLayout.class);
    view7f090239 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    MessageItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvHeard = null;
    target.mIvStatus = null;
    target.mTvAccountName = null;
    target.mTvContent = null;
    target.mRlName = null;
    target.mTvQuoteTitle = null;
    target.mTvQuoteContent = null;
    target.mTvTime = null;
    target.mTvReply = null;
    target.mIvLike = null;
    target.mTvFollow = null;
    target.mLine1 = null;
    target.mIvQuoteHeard = null;
    target.mRlContentQuote = null;

    view7f09014b.setOnClickListener(null);
    view7f09014b = null;
    view7f09035a.setOnClickListener(null);
    view7f09035a = null;
    view7f090151.setOnClickListener(null);
    view7f090151 = null;
    view7f090323.setOnClickListener(null);
    view7f090323 = null;
    view7f090239.setOnClickListener(null);
    view7f090239 = null;
  }
}
