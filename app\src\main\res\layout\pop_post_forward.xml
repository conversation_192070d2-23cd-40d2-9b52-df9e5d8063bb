<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_16"
    android:paddingTop="@dimen/dp_10"
    android:paddingBottom="@dimen/dp_10">

    <View
        android:id="@+id/line1"
        android:layout_width="46dp"
        android:layout_height="5dp"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/rounded_corner_60" />

<!--    <TextView-->
<!--        android:id="@+id/tv_list_title"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_below="@id/line1"-->
<!--        android:layout_centerInParent="true"-->
<!--        android:layout_marginTop="@dimen/dp_15"-->
<!--        android:text="Forward"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="@dimen/sp_16" />-->

<!--    <View-->
<!--        android:id="@+id/line2"-->
<!--        style="@style/deep_gray_horizontal_line_view"-->
<!--        android:layout_below="@id/tv_list_title"-->
<!--        android:layout_marginTop="28dp" />-->

    <TextView
        android:id="@+id/tv_repost"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_70"
        android:layout_below="@id/line1"
        android:layout_marginTop="@dimen/dp_15"
        android:drawableLeft="@mipmap/icon_repost"
        android:drawablePadding="@dimen/dp_11"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dp_36"
        android:text="Repost"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_16" />

    <View
        android:id="@+id/line3"
        style="@style/deep_gray_horizontal_line_view"
        android:layout_below="@id/tv_repost" />

    <TextView
        android:id="@+id/tv_Quote"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_70"
        android:layout_below="@id/line3"
        android:drawableLeft="@mipmap/icon_quote"
        android:drawablePadding="@dimen/dp_11"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dp_36"
        android:text="Quote"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_16" />
</RelativeLayout>
