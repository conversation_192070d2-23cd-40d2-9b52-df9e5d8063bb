<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_16"
    android:minHeight="@dimen/dp_150"
    android:padding="@dimen/dp_10">

    <View
        android:id="@+id/line1"
        android:layout_width="46dp"
        android:layout_height="5dp"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/rounded_corner_60" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/line1"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_15"
        android:textColor="@color/cosmos_black"
        android:text="Category"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_wallet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginRight="@dimen/dp_15"
        android:layout_marginBottom="@dimen/dp_40"
        android:background="@color/white" />

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:layout_below="@+id/rv_wallet"
        android:layout_marginLeft="@dimen/dp_22"
        android:layout_marginRight="@dimen/dp_22"
        android:layout_marginBottom="@dimen/dp_22"
        android:background="@drawable/btn_black_6"
        android:gravity="center"
        android:padding="@dimen/dp_10"
        android:text="Done"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_15" />

</RelativeLayout>
