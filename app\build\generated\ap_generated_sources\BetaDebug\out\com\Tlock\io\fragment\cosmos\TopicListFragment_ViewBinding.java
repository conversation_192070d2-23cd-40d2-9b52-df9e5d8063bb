// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.fragment.cosmos;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.custom.LoadErrorView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TopicListFragment_ViewBinding implements Unbinder {
  private TopicListFragment target;

  @UiThread
  public TopicListFragment_ViewBinding(TopicListFragment target, View source) {
    this.target = target;

    target.mRecyclerView = Utils.findRequiredViewAsType(source, R.id.recyclerView, "field 'mRecyclerView'", RecyclerView.class);
    target.mLoadError = Utils.findRequiredViewAsType(source, R.id.load_error, "field 'mLoadError'", LoadErrorView.class);
    target.mRefreshLayout = Utils.findRequiredViewAsType(source, R.id.refresh_layout, "field 'mRefreshLayout'", SmartRefreshLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TopicListFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRecyclerView = null;
    target.mLoadError = null;
    target.mRefreshLayout = null;
  }
}
