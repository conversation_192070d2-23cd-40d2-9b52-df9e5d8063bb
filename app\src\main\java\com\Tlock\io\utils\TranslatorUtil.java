//package com.Tlock.io.utils;
//
//import android.content.Context;
//import android.os.Handler;
//import android.os.Looper;
//import android.util.Log;
//import android.widget.Toast;
//
//import androidx.annotation.NonNull;
//
//import com.google.mlkit.common.model.DownloadConditions;
//import com.google.mlkit.common.model.RemoteModelManager;
//import com.google.mlkit.nl.translate.TranslateLanguage;
//import com.google.mlkit.nl.translate.TranslateRemoteModel;
//import com.google.mlkit.nl.translate.Translation;
//import com.google.mlkit.nl.translate.Translator;
//import com.google.mlkit.nl.translate.TranslatorOptions;
//
//import java.util.Locale;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.function.Consumer;
//
//public class TranslatorUtil {
//    private static volatile TranslatorUtil instance;
//    private final Context context;
//    private final ExecutorService executor;
//    private final Handler mainHandler;
//    private Translator translator;
//    private final RemoteModelManager modelManager;
//
//    // 单例模式
//    private TranslatorUtil(Context context) {
//        this.context = context.getApplicationContext();
//        this.executor = Executors.newSingleThreadExecutor();
//        this.mainHandler = new Handler(Looper.getMainLooper());
//        this.modelManager = RemoteModelManager.getInstance();
//    }
//
//    public static TranslatorUtil getInstance(Context context) {
//        if (instance == null) {
//            synchronized (TranslatorUtil.class) {
//                if (instance == null) {
//                    instance = new TranslatorUtil(context);
//                }
//            }
//        }
//        return instance;
//    }
//
//    // 翻译方法：输入文本，异步返回翻译结果
//    public void translate(@NonNull String inputText, @NonNull Consumer<String> onResult) {
//        if (inputText.trim().isEmpty()) {
//            postResult(onResult, "");
//            return;
//        }
//
//        // 检测输入语种
//        LanguageIdentifier identifier = LanguageIdentification.getClient();
//        identifier.identifyLanguage(inputText)
//                .addOnSuccessListener(languageCode -> {
//                    if (languageCode.equals("und")) {
//                        showToast("无法识别语言");
//                        postResult(onResult, inputText); // 返回原文本
//                        return;
//                    }
//
//                    // 获取手机默认语言
//                    String targetLanguage = getDeviceLanguage();
//                    if (targetLanguage == null || languageCode.equals(targetLanguage)) {
//                        postResult(onResult, inputText); // 无需翻译
//                        return;
//                    }
//
//                    // 配置翻译器
//                    TranslatorOptions options = new TranslatorOptions.Builder()
//                            .setSourceLanguage(languageCode)
//                            .setTargetLanguage(targetLanguage)
//                            .build();
//                    translator = Translation.getClient(options);
//
//                    // 下载离线模型
//                    TranslateRemoteModel model = new TranslateRemoteModel.Builder(targetLanguage).build();
//                    DownloadConditions conditions = new DownloadConditions.Builder().build();
//                    modelManager.download(model, conditions)
//                            .addOnSuccessListener(aVoid -> {
//                                // 执行翻译
//                                translator.translate(inputText)
//                                        .addOnSuccessListener(translatedText -> postResult(onResult, translatedText))
//                                        .addOnFailureListener(e -> {
//                                            showToast("翻译失败: " + e.getMessage());
//                                            Log.e("TAG", "translate: 翻译失败");
//                                            postResult(onResult, inputText);
//                                        });
//                            })
//                            .addOnFailureListener(e -> {
//                                        showToast("模型下载失败: " + e.getMessage());
//                                        Log.e("TAG", "translate: 模型下载失败");
//                                        postResult(onResult, inputText);
//                                    }
//                            );
//                })
//                .addOnFailureListener(e -> {
//                    showToast("语言检测失败: " + e.getMessage());
//                    Log.e("TAG", "translate: 语言检测失败");
//
//                    postResult(onResult, inputText);
//                });
//    }
//
//    // 获取手机默认语言（转换为 ML Kit 支持的语言代码）
//    private String getDeviceLanguage() {
//        String language = Locale.getDefault().getLanguage();
//        // ML Kit 支持的语言代码映射（部分示例）
//        switch (language) {
//            case "en":
//                return TranslateLanguage.ENGLISH;
//            case "zh":
//                return TranslateLanguage.CHINESE;
//            case "es":
//                return TranslateLanguage.SPANISH;
//            case "fr":
//                return TranslateLanguage.FRENCH;
//            case "de":
//                return TranslateLanguage.GERMAN;
//            case "ja":
//                return TranslateLanguage.JAPANESE;
//            case "ko":
//                return TranslateLanguage.KOREAN;
//            case "ru":
//                return TranslateLanguage.RUSSIAN;
//            // 添加更多语言映射
//            default:
//                return null; // 不支持的语言
//        }
//    }
//
//    // 在主线程返回结果
//    private void postResult(Consumer<String> onResult, String result) {
//        mainHandler.post(() -> onResult.accept(result));
//    }
//
//    // 显示 Toast 提示
//    private void showToast(String message) {
//        mainHandler.post(() -> Toast.makeText(context, message, Toast.LENGTH_SHORT).show());
//    }
//
//    // 清理资源
//    public void release() {
//        if (translator != null) {
//            translator.close();
//        }
//        executor.shutdown();
//    }
//}