package com.Tlock.io.widget.pop;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.utils.ToastUtil;
import com.lxj.xpopup.core.BottomPopupView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * @ClassName PopWhaleList
 * <AUTHOR>
 * @Data 2023/11/6 19:06
 * @Desc
 */

public class PopPostMore extends BottomPopupView {




    @BindView(R.id.tv_follow)
    TextView mTvFollow;

    @BindView(R.id.tv_Report)
    TextView mTvReport;
    private String handle;
    private String id;

    public PopPostMore(@NonNull Context context) {
        super(context);
    }

    public PopPostMore(@NonNull Context context, String id, String handle) {
        super(context);
        this.id = id;
        this.handle = handle;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.pop_post_more;
    }

    @Override
    protected void onCreate() {
        ButterKnife.bind(this);
        super.onCreate();
        mTvFollow.setText("Follow   @ " + handle);
    }



    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    private CallBack callBack;

    @OnClick({ R.id.tv_follow, R.id.tv_Report})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_follow:
                if (callBack != null)
                    callBack.follow(handle);
                break;
            case R.id.tv_Report:
                ToastUtil.toastShortCenter(AppApplication.getInstance(), "Coming soon");

//                if (callBack != null)
//                    callBack.report(id);
                break;
        }
    }

    public interface CallBack {

        void follow(String handle);

        void report(String id);

    }
}
