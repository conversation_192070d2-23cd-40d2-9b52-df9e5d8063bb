// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TopicClassView_ViewBinding implements Unbinder {
  private TopicClassView target;

  @UiThread
  public TopicClassView_ViewBinding(TopicClassView target) {
    this(target, target);
  }

  @UiThread
  public TopicClassView_ViewBinding(TopicClassView target, View source) {
    this.target = target;

    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv_1, "field 'mIv1'", ImageView.class);
    target.mIvSelect = Utils.findRequiredViewAsType(source, R.id.iv_select, "field 'mIvSelect'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TopicClassView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mIv1 = null;
    target.mIvSelect = null;
  }
}
