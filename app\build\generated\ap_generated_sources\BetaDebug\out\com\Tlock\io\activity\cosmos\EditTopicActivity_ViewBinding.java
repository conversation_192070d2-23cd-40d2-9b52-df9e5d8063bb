// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomEditBox;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class EditTopicActivity_ViewBinding implements Unbinder {
  private EditTopicActivity target;

  private View view7f090137;

  private View view7f09023a;

  private View view7f090365;

  @UiThread
  public EditTopicActivity_ViewBinding(EditTopicActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public EditTopicActivity_ViewBinding(final EditTopicActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    view = Utils.findRequiredView(source, R.id.iv_avatar, "field 'mIvAvatar' and method 'onBindClick'");
    target.mIvAvatar = Utils.castView(view, R.id.iv_avatar, "field 'mIvAvatar'", ImageView.class);
    view7f090137 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvAvatar1 = Utils.findRequiredViewAsType(source, R.id.iv_avatar1, "field 'mIvAvatar1'", ImageView.class);
    target.mEdNikeName = Utils.findRequiredViewAsType(source, R.id.ed_nike_name, "field 'mEdNikeName'", CustomEditBox.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mEditText = Utils.findRequiredViewAsType(source, R.id.editText, "field 'mEditText'", EditText.class);
    view = Utils.findRequiredView(source, R.id.rl_category, "field 'mRlCategory' and method 'onBindClick'");
    target.mRlCategory = Utils.castView(view, R.id.rl_category, "field 'mRlCategory'", RelativeLayout.class);
    view7f09023a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mEdBio = Utils.findRequiredViewAsType(source, R.id.ed_bio, "field 'mEdBio'", CustomEditBox.class);
    view = Utils.findRequiredView(source, R.id.tv_save, "field 'mTvSave' and method 'onBindClick'");
    target.mTvSave = Utils.castView(view, R.id.tv_save, "field 'mTvSave'", TextView.class);
    view7f090365 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv_2, "field 'mTv2'", TextView.class);
    target.mTv3 = Utils.findRequiredViewAsType(source, R.id.tv_3, "field 'mTv3'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    EditTopicActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvAvatar = null;
    target.mIvAvatar1 = null;
    target.mEdNikeName = null;
    target.mTvTitle = null;
    target.mEditText = null;
    target.mRlCategory = null;
    target.mEdBio = null;
    target.mTvSave = null;
    target.mTv2 = null;
    target.mTv3 = null;

    view7f090137.setOnClickListener(null);
    view7f090137 = null;
    view7f09023a.setOnClickListener(null);
    view7f09023a = null;
    view7f090365.setOnClickListener(null);
    view7f090365 = null;
  }
}
