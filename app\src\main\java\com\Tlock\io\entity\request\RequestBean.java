package com.Tlock.io.entity.request;

/**
 * 本类的主要功能是 :  接口分页固定格式
 *
 * <AUTHOR>  2020-04-06 11:55
 */
public class RequestBean {

    private CondBean cond;
    private PageableBean pageable;
    private EntityBean entity;

    public CondBean getCond() {
        return cond;
    }

    public void setCond(CondBean cond) {
        this.cond = cond;
    }

    public PageableBean getPageable() {
        return pageable;
    }

    public void setPageable(PageableBean pageable) {
        this.pageable = pageable;
    }

    public EntityBean getEntity() {
        return entity;
    }

    public void setEntity(EntityBean entity) {
        this.entity = entity;
    }
}
