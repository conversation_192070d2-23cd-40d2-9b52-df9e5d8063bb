package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.TimeUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.cosmos.tx.v1beta1.ServiceProto;
import com.google.protobuf.Any;
import com.google.protobuf.Parser;

import java.util.List;

import butterknife.BindView;

public class TransactionInfoActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.tv_method)
    TextView mTvMethod;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.tv_limit)
    TextView mTvLimit;
    @BindView(R.id.tv_used)
    TextView mTvUsed;
    @BindView(R.id.tv_height)
    TextView mTvHeight;
    @BindView(R.id.tv_wallet_address)
    TextView mTvWalletAddress;
    @BindView(R.id.tv_payer)
    TextView mTvPayer;
    @BindView(R.id.tv_hash)
    TextView mTvHash;
    @BindView(R.id.tv_data)
    TextView mTvData;
    private String hash;

    /**
     * @param context
     */
    public static void start(Context context, String hash) {
        Intent intent = new Intent(context, TransactionInfoActivity.class);
        intent.putExtra("hash", hash);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_transaction_info;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mCustomNavBar.setMidTitle("Transactions");
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }


    @Override
    protected void loadData() {
        params.clear();
        hash = getIntent().getStringExtra("hash");
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ServiceProto.GetTxResponse tx = CosmosUtils.queryHash(hash);
                mTvData.post(new Runnable() {
                    @Override
                    public void run() {
                        mTvMethod.setText( tx.getTx().getBody().getMessages(0).getTypeUrl());
                        mTvTime.setText(TimeUtils.convertToDeviceTime(tx.getTxResponse().getTimestamp()));
                        mTvLimit.setText(BigDecimalUtils.saveDecimals(tx.getTxResponse().getGasWanted()+"",0));
                        mTvUsed.setText(BigDecimalUtils.save4Valid(BigDecimalUtils.uTok2Tok(tx.getTxResponse().getGasUsed()+""),4)+" TOK");
                        mTvHeight.setText(tx.getTxResponse().getHeight()+"");
                        mTvWalletAddress.setText(WalletDaoUtils.getCurrent().getAddress());
                        mTvHash.setText(tx.getTxResponse().getTxhash());
                        mTvData.setText(tx.getTxResponse().getData());

                        Parser<Any> parserForType = tx.getTx().getBody().getMessages(0).getParserForType();


                    }
                });

            }
        });


    }
}