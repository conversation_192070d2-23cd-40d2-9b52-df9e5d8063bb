package com.Tlock.io.base;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.config.AppConstants;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.request.CondBean;
import com.Tlock.io.entity.request.PageableBean;
import com.Tlock.io.entity.request.RequestBean;
import com.Tlock.io.network.NetWorkConfig;
import com.Tlock.io.network.OKHttpManager;
import com.Tlock.io.utils.LoadingDialogUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.widget.CustomHeader;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;

import butterknife.ButterKnife;

/**
 * 本类的主要功能是 :  基类Frgment
 *
 */
public abstract class BaseFragment extends Fragment implements OnLoadMoreListener, OnRefreshListener {

    protected View rootView;
    protected HashMap<String, Object> params = new HashMap<>();//装载网络请求参数
    private Dialog loadingDialog;
    protected LoadErrorView mErrorView; //加载错误页面
    protected SmartRefreshLayout mRefreshLayout; //下拉刷新上拉加载

    protected InputMethodManager inputMethodManager;
    protected int page = 1;
    protected RecyclerView mRecylerView;
    protected String TAG = getClass().getSimpleName();


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(getContentViewId(), container, false);
        ButterKnife.bind(this, rootView);
        initBase();
        initView(savedInstanceState);
        return rootView;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        loadData();
    }


    // 初始化base
    private void initBase() {
        //设置 RecylerView的布局管理器
        mRecylerView = rootView.findViewById(R.id.recyler_view);
        if (mRecylerView != null) {
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL, false);
            mRecylerView.setLayoutManager(linearLayoutManager);
        }
        //加载错误页面
        mErrorView = rootView.findViewById(R.id.load_error);
        // RefreshLayout
        mRefreshLayout = rootView.findViewById(R.id.refresh_layout);
        if (mRefreshLayout != null) {
            mRefreshLayout.setRefreshHeader(new CustomHeader(getActivity()));

            mRefreshLayout.setOnLoadMoreListener(this);
            mRefreshLayout.setOnRefreshListener(this);
        }
        //加载失败的点击
        if (mErrorView != null) {
            mErrorView.setOnClickListener(this::onErrorViewClick);
        }

        //请求参数
        initRequestParams();
    }

    /**************************************************************************************/
    /**
     * 显示加载错误页面
     *
     * @param message {@link NetWorkConfig#}
     */
    protected void showError(String message) {
        if (mErrorView != null) {
            mErrorView.setErrorMessage(message);
            mErrorView.setVisibility(View.VISIBLE);
        }
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    /**
     * 显示加载错误页面
     *
     * @param message
     * @param page
     */
    protected void showError(String message, int page) {
        if (page == 1) {
            showError(message);
        } else {
            showToast(message);
            finishRefresh();
        }
    }

    /**
     * 改变加载更多或者刷新状态
     */
    protected void finishRefresh() {
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    //隐藏加载错误页面
    protected void hideError() {
        if (mErrorView != null) {
            mErrorView.setVisibility(View.GONE);
        }
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    /**************************************************************************************/

    // 吐司
    protected void showToast(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        ToastUtil.toastShortCenter(getActivity(), msg);
    }


    //加载布局文件
    protected abstract int getContentViewId();

    //初始化Views
    protected abstract void initView(Bundle savedInstanceState);

    //加载数据
    protected abstract void loadData();


    /**
     * 初始化请求参数
     */
    protected RequestBean initRequestParams() {
        RequestBean mRequestBean = new RequestBean();
        CondBean condBean = new CondBean();
        condBean.setGroups(new ArrayList());
        condBean.setRules(new ArrayList());
        mRequestBean.setCond(condBean);
        PageableBean pageableBean = new PageableBean();
        mRequestBean.setPageable(pageableBean);
        return mRequestBean;
    }

    /**************************************************************************************/

    public void showLoading() {
        LoadingDialogUtils.getUtils().showProgressDialog(getActivity());
    }

    public void hideLoading() {
        LoadingDialogUtils.getUtils().dismissDialog();
    }

    /**************************************************************************************/
    /**
     * 失败点击重新弄加载
     */

    public void onErrorViewClick(View view) {
        refresh();
    }

    public void refresh() {
        if (mRefreshLayout != null) {
            mRefreshLayout.autoRefresh();
        }
    }

    /**
     * 上拉加载更多回调
     *
     * @param refreshLayout
     */
    @Override
    public void onLoadMore(RefreshLayout refreshLayout) {
        page++;
        getData(page);
    }

    /**
     * 下拉刷新回调
     *
     * @param refreshLayout
     */
    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        page = 1;
        getData(page);
    }
    /**************************************************************************************/
    /**
     * 重写此方法即可实现带页码刷新
     *
     * @param page 页码
     */
    protected void getData(int page) {
        getData();
    }

    /**
     * 刷新数据执行
     */
    protected void getData() {

    }

    /**************************************************************************************/

    @Override
    public void onDestroy() {
        super.onDestroy();

        if (loadingDialog != null) {
            if (loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }
            loadingDialog = null;
        }

        OKHttpManager.cancelTag(this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        OKHttpManager.cancelTag(this);
    }


    /**
     * 清理临时压缩的图片和语音
     */
    public void deleteCacheDir() {
        try {
            File file = new File(Environment.getExternalStorageDirectory(), AppConstants.SD_PATH);
            deleteDirWihtFile(file);
        } catch (Exception e) {
//            e.printStackTrace();
        }

    }

    private void deleteDirWihtFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
            else if (file.isDirectory())
                deleteDirWihtFile(file); // 递规的方式删除文件夹
        }
        dir.delete();// 删除目录本身
    }

    protected void hideSoftKeyboard() {
        if (getActivity().getWindow().getAttributes().softInputMode != WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN) {
            if (getActivity().getCurrentFocus() != null)
                inputMethodManager.hideSoftInputFromWindow(getActivity().getCurrentFocus().getWindowToken(),
                        InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    /**
     * 下拉刷新 上拉加载完成后的动作
     *
     * @param respSize  服务器返回的List长度
     * @param limitSize 设置的每次请求条数
     */
    public void loadOrRefreshComplete(int respSize, int limitSize) {
        if (mRefreshLayout != null) {
            //如果正在下拉刷新,停止刷新
            if (mRefreshLayout.getState() == RefreshState.Refreshing) {
                mRefreshLayout.finishRefresh();
            }
            //如果正在上拉加载,停止加载
            if (mRefreshLayout.getState() == RefreshState.Loading) {
                mRefreshLayout.finishLoadMore();
            }
            //如果服务器返回数据小于设置的请求数,说明此次请求已取出最后所有数据,不必再上拉加载
            if (respSize < limitSize) {
                mRefreshLayout.setEnableLoadMore(false);
            } else if (!mRefreshLayout.isEnableLoadMore()) {
                mRefreshLayout.setEnableLoadMore(true);
            }
        }
        if (respSize == 0 && page == 1 && mErrorView != null) {
            showError(getResources().getString(R.string.no_data));
        } else if (mErrorView != null && mErrorView.getVisibility() == View.VISIBLE) {
            mErrorView.setVisibility(View.GONE);
        }
    }

    /**
     * 取消加載
     */
    public void cancleLoad(int size){
        if (mRefreshLayout != null) {
            //如果正在下拉刷新,停止刷新
            if (mRefreshLayout.getState() == RefreshState.Refreshing) {
                mRefreshLayout.finishRefresh();
            }
            //如果正在上拉加载,停止加载
            if (mRefreshLayout.getState() == RefreshState.Loading) {
                mRefreshLayout.finishLoadMore();
            }
            if (size==0){
                mRefreshLayout.finishLoadMore();
                mRefreshLayout.finishRefresh();
            }
        }
    }

    /**
     * 根view获取焦点
     * 用于让其他的组件失去焦点
     */
    public void setRootViewFocus() {
        if (rootView == null) return;
        rootView.setClickable(true);
        rootView.setFocusable(true);
        rootView.setFocusableInTouchMode(true);
        rootView.requestFocus();
    }

    protected GridLayoutManager getGridlayoutManger(int clumns) {
        return new GridLayoutManager(getActivity(), clumns);
    }

//    /**
//     * 获取统一GridLayoutManger
//     *
//     * @return
//     */
//    protected GridLayoutManager getGridlayoutManger() {
//        boolean phone = LayoutUtil.isPhone();
//        boolean screenPortrait = LayoutUtil.isScreenPortrait();
//        int clumns = 1;
//        if (phone) {
//            return new GridLayoutManager(getActivity(), clumns);
//        }
//        return new GridLayoutManager(getActivity(), screenPortrait ? 2 : 3);
//
//    }


}
