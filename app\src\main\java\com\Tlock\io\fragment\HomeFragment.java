package com.Tlock.io.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.SearchActivity;
import com.Tlock.io.activity.cosmos.Test1Activity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.fragment.cosmos.RecommendedFragment;
import com.Tlock.io.post.PostQueryProto;
import com.cy.tablayoutniubility.FragPageAdapterVp2NoScroll;
import com.cy.tablayoutniubility.TabAdapterNoScroll;
import com.cy.tablayoutniubility.TabLayoutNoScroll;
import com.cy.tablayoutniubility.TabMediatorVp2NoScroll;
import com.cy.tablayoutniubility.TabNoScrollViewHolder;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class HomeFragment extends LazyLoadBaseFragment {
    @BindView(R.id.tablayout)
    TabLayoutNoScroll tabLayoutLine;
    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;

    private BaseRecyclerViewAdapter<PostQueryProto.PostResponse> adapter;
    private ETHWallet current;
    private FragPageAdapterVp2NoScroll<String> fragmentPageAdapter;

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_home;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {

        //初始化dex列表
        fragmentPageAdapter = new FragPageAdapterVp2NoScroll<String>(this) {

            @Override
            public Fragment createFragment(String bean, int position) {
                return new RecommendedFragment(position);
            }

            @Override
            public void bindDataToTab(TabNoScrollViewHolder holder, int position, String bean, boolean isSelected) {
                TextView textView = holder.getView(R.id.tv);
                if (isSelected) {
                    textView.setTextColor(getResources().getColor(R.color.cosmos_black));
                } else {
                    textView.setTextColor(getResources().getColor(R.color.cosmos_default));
                }
                textView.setText(bean);
            }

            @Override
            public int getTabLayoutID(int position, String bean) {
                return R.layout.item_home_tab;
            }
        };
        TabAdapterNoScroll<String> tabAdapter = new TabMediatorVp2NoScroll<String>(tabLayoutLine, viewPager2).setAdapter(fragmentPageAdapter);

        List<String> list = new ArrayList<>();
        list.add("For you");
        list.add("Following");
        list.add("New");
        fragmentPageAdapter.add(list);
        tabAdapter.add(list);

        viewPager2.setCurrentItem(0);
    }


    @Override
    protected void loadData() {
    }

    @Override
    protected void getData() {
        super.getData();
    }


    @OnClick({R.id.iv_search})

    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_search:
                SearchActivity.start(getActivity());

                break;
        }
    }

}