package com.Tlock.io.entity.wallet;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "ETHWALLET".
*/
public class ETHWalletDao extends AbstractDao<ETHWallet, Long> {

    public static final String TABLENAME = "ETHWALLET";

    /**
     * Properties of entity ETHWallet.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property ChainId = new Property(1, String.class, "chainId", false, "CHAIN_ID");
        public final static Property Address = new Property(2, String.class, "address", false, "ADDRESS");
        public final static Property Name = new Property(3, String.class, "name", false, "NAME");
        public final static Property Password = new Property(4, String.class, "password", false, "PASSWORD");
        public final static Property Mnemonic = new Property(5, String.class, "mnemonic", false, "MNEMONIC");
        public final static Property PrivateKey = new Property(6, String.class, "privateKey", false, "PRIVATE_KEY");
        public final static Property PublicKey = new Property(7, String.class, "publicKey", false, "PUBLIC_KEY");
        public final static Property ChainList = new Property(8, String.class, "chainList", false, "CHAIN_LIST");
        public final static Property IsCurrent = new Property(9, boolean.class, "isCurrent", false, "IS_CURRENT");
        public final static Property IsBackup = new Property(10, boolean.class, "isBackup", false, "IS_BACKUP");
        public final static Property OrderID = new Property(11, Integer.class, "orderID", false, "ORDER_ID");
        public final static Property Type = new Property(12, int.class, "type", false, "TYPE");
    }

    private DaoSession daoSession;


    public ETHWalletDao(DaoConfig config) {
        super(config);
    }
    
    public ETHWalletDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"ETHWALLET\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"CHAIN_ID\" TEXT," + // 1: chainId
                "\"ADDRESS\" TEXT," + // 2: address
                "\"NAME\" TEXT," + // 3: name
                "\"PASSWORD\" TEXT," + // 4: password
                "\"MNEMONIC\" TEXT," + // 5: mnemonic
                "\"PRIVATE_KEY\" TEXT," + // 6: privateKey
                "\"PUBLIC_KEY\" TEXT," + // 7: publicKey
                "\"CHAIN_LIST\" TEXT," + // 8: chainList
                "\"IS_CURRENT\" INTEGER NOT NULL ," + // 9: isCurrent
                "\"IS_BACKUP\" INTEGER NOT NULL ," + // 10: isBackup
                "\"ORDER_ID\" INTEGER," + // 11: orderID
                "\"TYPE\" INTEGER NOT NULL );"); // 12: type
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"ETHWALLET\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, ETHWallet entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String chainId = entity.getChainId();
        if (chainId != null) {
            stmt.bindString(2, chainId);
        }
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(3, address);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(4, name);
        }
 
        String password = entity.getPassword();
        if (password != null) {
            stmt.bindString(5, password);
        }
 
        String mnemonic = entity.getMnemonic();
        if (mnemonic != null) {
            stmt.bindString(6, mnemonic);
        }
 
        String privateKey = entity.getPrivateKey();
        if (privateKey != null) {
            stmt.bindString(7, privateKey);
        }
 
        String publicKey = entity.getPublicKey();
        if (publicKey != null) {
            stmt.bindString(8, publicKey);
        }
 
        String chainList = entity.getChainList();
        if (chainList != null) {
            stmt.bindString(9, chainList);
        }
        stmt.bindLong(10, entity.getIsCurrent() ? 1L: 0L);
        stmt.bindLong(11, entity.getIsBackup() ? 1L: 0L);
 
        Integer orderID = entity.getOrderID();
        if (orderID != null) {
            stmt.bindLong(12, orderID);
        }
        stmt.bindLong(13, entity.getType());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, ETHWallet entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String chainId = entity.getChainId();
        if (chainId != null) {
            stmt.bindString(2, chainId);
        }
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(3, address);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(4, name);
        }
 
        String password = entity.getPassword();
        if (password != null) {
            stmt.bindString(5, password);
        }
 
        String mnemonic = entity.getMnemonic();
        if (mnemonic != null) {
            stmt.bindString(6, mnemonic);
        }
 
        String privateKey = entity.getPrivateKey();
        if (privateKey != null) {
            stmt.bindString(7, privateKey);
        }
 
        String publicKey = entity.getPublicKey();
        if (publicKey != null) {
            stmt.bindString(8, publicKey);
        }
 
        String chainList = entity.getChainList();
        if (chainList != null) {
            stmt.bindString(9, chainList);
        }
        stmt.bindLong(10, entity.getIsCurrent() ? 1L: 0L);
        stmt.bindLong(11, entity.getIsBackup() ? 1L: 0L);
 
        Integer orderID = entity.getOrderID();
        if (orderID != null) {
            stmt.bindLong(12, orderID);
        }
        stmt.bindLong(13, entity.getType());
    }

    @Override
    protected final void attachEntity(ETHWallet entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public ETHWallet readEntity(Cursor cursor, int offset) {
        ETHWallet entity = new ETHWallet( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // chainId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // address
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // name
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // password
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // mnemonic
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // privateKey
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // publicKey
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // chainList
            cursor.getShort(offset + 9) != 0, // isCurrent
            cursor.getShort(offset + 10) != 0, // isBackup
            cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11), // orderID
            cursor.getInt(offset + 12) // type
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, ETHWallet entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setChainId(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAddress(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setName(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setPassword(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setMnemonic(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setPrivateKey(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setPublicKey(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setChainList(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setIsCurrent(cursor.getShort(offset + 9) != 0);
        entity.setIsBackup(cursor.getShort(offset + 10) != 0);
        entity.setOrderID(cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11));
        entity.setType(cursor.getInt(offset + 12));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(ETHWallet entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(ETHWallet entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(ETHWallet entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
