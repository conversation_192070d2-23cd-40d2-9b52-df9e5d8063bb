package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.post.PostQueryProto;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TaskTopicItemView extends BaseView {


    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.iv1)
    ImageView mIv1;

    public TaskTopicItemView(Context context) {
        super(context);
    }

    public TaskTopicItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TaskTopicItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.item_task_topic;
    }

    public void setData(PostQueryProto.TopicResponse data) {
        mTvTitle.setText("# " + data.getName());

    }

}
