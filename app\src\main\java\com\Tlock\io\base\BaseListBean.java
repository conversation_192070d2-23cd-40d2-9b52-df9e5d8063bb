package com.Tlock.io.base;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * 加载List  data实体
 */
public class BaseListBean<T> implements Serializable {
    private int code;
    private int count;
    private int total;
    private String msg;
    private ArrayList<T> body;
    private ArrayList<T> data;
    private ArrayList<T> list;
    private ArrayList<T> items;
    private ArrayList<String> deleteList;


    public ArrayList<T> getList() {
        return list;
    }

    public void setList(ArrayList<T> list) {
        this.list = list;
    }

    public ArrayList<T> getData() {
        return data;
    }

    public void setData(ArrayList<T> data) {
        this.data = data;
    }

    public ArrayList<String> getDeleteList() {
        return deleteList;
    }

    public void setDeleteList(ArrayList<String> deleteList) {
        this.deleteList = deleteList;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public ArrayList<T> getItems() {
        return items;
    }

    public void setItems(ArrayList<T> items) {
        this.items = items;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getMessage() {
        return msg;
    }

    public void setMessage(String message) {
        this.msg = message;
    }

    public ArrayList<T> getBody() {
        return body;
    }

    public void setBody(ArrayList<T> body) {
        this.body = body;
    }
}
