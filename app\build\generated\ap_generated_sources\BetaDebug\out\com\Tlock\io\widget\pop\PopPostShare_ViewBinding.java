// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopPostShare_ViewBinding implements Unbinder {
  private PopPostShare target;

  private View view7f090316;

  private View view7f09036a;

  private View view7f09036b;

  @UiThread
  public PopPostShare_ViewBinding(PopPostShare target) {
    this(target, target);
  }

  @UiThread
  public PopPostShare_ViewBinding(final PopPostShare target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.tv_copy_link, "method 'onBindClick'");
    view7f090316 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_share_tg, "method 'onBindClick'");
    view7f09036a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_share_x, "method 'onBindClick'");
    view7f09036b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    target = null;


    view7f090316.setOnClickListener(null);
    view7f090316 = null;
    view7f09036a.setOnClickListener(null);
    view7f09036a = null;
    view7f09036b.setOnClickListener(null);
    view7f09036b = null;
  }
}
