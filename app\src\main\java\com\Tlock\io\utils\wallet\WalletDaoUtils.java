package com.Tlock.io.utils.wallet;

import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.Tlock.io.app.AppApplication;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.ETHWalletDao;
import com.Tlock.io.entity.wallet.OperationBean;
import com.Tlock.io.entity.wallet.OperationBeanDao;
import com.Tlock.io.entity.wallet.TokenInfo;
import com.Tlock.io.entity.wallet.TokenInfoDao;
import com.Tlock.io.entity.wallet.Transaction;
import com.Tlock.io.entity.wallet.TransactionDao;
import com.Tlock.io.entity.wallet.Transfer;
import com.Tlock.io.entity.wallet.TransferDao;
import com.youth.banner.util.LogUtils;

import org.greenrobot.greendao.Property;

import java.util.ArrayList;
import java.util.List;


public class WalletDaoUtils {
    public static ETHWalletDao ethWalletDao = AppApplication.getInstance().getDaoSession().getETHWalletDao();
    public static TransferDao transferDao = AppApplication.getInstance().getDaoSession().getTransferDao();
    public static TokenInfoDao tokenInfoDao = AppApplication.getInstance().getDaoSession().getTokenInfoDao();
    public static TransactionDao transactionDao = AppApplication.getInstance().getDaoSession().getTransactionDao();
    public static OperationBeanDao operationBeanDao = AppApplication.getInstance().getDaoSession().getOperationBeanDao();
    private StringBuilder orderBuilder;

    /**
     * 插入新创建钱包
     *
     * @param ethWallet 新创建钱包
     */
    public static void insertNewWallet(ETHWallet ethWallet) {
        updateCurrent(-1);
        ethWallet.setIsCurrent(true);
        ethWalletDao.insert(ethWallet);
    }

    /**
     * 插入新创建钱包
     *
     * @param ethWallet 新创建钱包
     */
    public static void insertNewWallet(ETHWallet ethWallet, boolean isCurrent) {
        ethWallet.setIsCurrent(isCurrent);
        ethWalletDao.insert(ethWallet);
    }

    /**
     * 更新选中钱包
     *
     * @param id 钱包ID
     */
    public static ETHWallet updateCurrent(long id) {
        List<ETHWallet> ethWallets = ethWalletDao.loadAll();
        ETHWallet currentWallet = null;
        for (ETHWallet ethwallet : ethWallets) {
            if (id != -1 && ethwallet.getId() == id) {
                ethwallet.setIsCurrent(true);
                currentWallet = ethwallet;
            } else {
                ethwallet.setIsCurrent(false);
            }
            ethWalletDao.update(ethwallet);
        }
        return currentWallet;
    }

    /**
     * 获取当前钱包
     *
     * @return 钱包对象
     */
    public static ETHWallet getCurrent() {
        List<ETHWallet> ethWallets = ethWalletDao.loadAll();
        for (ETHWallet ethwallet : ethWallets) {
            if (ethwallet.getIsCurrent()) {
                ethwallet.setIsCurrent(true);
                return ethwallet;
            }
        }
        return null;
    }

    /**
     * 获取当前钱包是否是观察钱包
     *
     * @return 钱包对象
     */
    public static boolean getCurrentIsObserve() {
        List<ETHWallet> ethWallets = ethWalletDao.loadAll();
        for (ETHWallet ethwallet : ethWallets) {
            if (ethwallet.getIsCurrent()) {
                ethwallet.setIsCurrent(true);
                if (ethwallet.getType() == 3) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 根据地址获取钱包
     *
     * @return 钱包对象
     */
    public static ETHWallet getWalletWithAddress(String walletAddress) {
        List<ETHWallet> ethWallets = ethWalletDao.loadAll();
        for (ETHWallet ethwallet : ethWallets) {
            if (ethwallet.getAddress().equalsIgnoreCase(walletAddress)) {
                return ethwallet;
            }
        }
        return null;
    }
//
//    /**
//     * 查询所有钱包
//     */
//    public static List<ETHWallet> loadAll() {
//        return ethWalletDao.loadAll();
//    }

    /**
     * 查询所有钱包
     */
    public static List<ETHWallet> loadAll() {
        List<ETHWallet> list = ethWalletDao.queryBuilder().orderAsc(ETHWalletDao.Properties.OrderID).list();
        return list;
    }

    /**
     * 查询所有观察钱包
     */
    public static List<ETHWallet> loadAllWatch() {
        List<ETHWallet> list = ethWalletDao.queryBuilder().where(ETHWalletDao.Properties.Type.eq("3")).orderAsc(ETHWalletDao.Properties.OrderID).list();
        return list;
    }

    /**
     * 查询所有观察钱包
     */
    public static List<ETHWallet> loadAllUserWallet() {
        List<ETHWallet> list = ethWalletDao.queryBuilder().where(ETHWalletDao.Properties.Type.notEq("3")).orderAsc(ETHWalletDao.Properties.OrderID).list();
        return list;
    }


    /**
     * 检查钱包名称是否存在
     *
     * @param name
     * @return
     */
    public static boolean walletNameChecking(String name) {
        List<ETHWallet> ethWallets = loadAll();
        for (ETHWallet ethWallet : ethWallets
        ) {
            if (TextUtils.equals(ethWallet.getName(), name)) {
                return true;
            }
        }
        return false;
    }

    public static ETHWallet getWalletById(long walletId) {
        return ethWalletDao.load(walletId);

    }

    /**
     * 设置isBackup为已备份
     *
     * @param walletId 钱包Id
     */
    public static void setIsBackup(long walletId) {
        ETHWallet ethWallet = ethWalletDao.load(walletId);
        ethWallet.setIsBackup(true);
        ethWalletDao.update(ethWallet);
    }

    /**
     * 以助记词检查钱包是否存在
     *
     * @param mnemonic
     * @return true if repeat
     */
    public static boolean checkRepeatByMnemonic(String mnemonic) {
        List<ETHWallet> ethWallets = loadAll();
        for (ETHWallet ethWallet : ethWallets) {
            if (TextUtils.isEmpty(ethWallet.getMnemonic())) {
                LogUtils.d("wallet mnemonic empty");
                continue;
            }
            if (TextUtils.equals(ethWallet.getMnemonic().trim(), mnemonic.trim())) {
                LogUtils.d("aleady");
                return true;
            }
        }
        return false;
    }

    /**
     * 以私钥检查钱包是否存在
     *
     * @param privateKey
     * @return true if repeat
     */
    public static boolean checkRepeatByPrivateKey(String privateKey) {
        List<ETHWallet> ethWallets = loadAll();
        for (ETHWallet ethWallet : ethWallets) {
            if (TextUtils.isEmpty(ethWallet.getPrivateKey())) {
                LogUtils.d("wallet getPrivateKey empty");
                continue;
            }
            if (TextUtils.equals(ethWallet.getPrivateKey().trim(), privateKey.trim())) {
                LogUtils.d("aleady");
                return true;
            }
        }
        return false;
    }

    /**
     * 以钱包地址检查钱包是否存在
     *
     * @param address
     * @return true if repeat
     */
    public static boolean checkRepeatByAddress(String address) {
        List<ETHWallet> ethWallets = loadAll();
        for (ETHWallet ethWallet : ethWallets) {
            if (TextUtils.isEmpty(ethWallet.getAddress())) {
                LogUtils.d("wallet getPrivateKey empty");
                continue;
            }
            if (TextUtils.equals(ethWallet.getAddress().trim(), address.trim())) {
                LogUtils.d("aleady");
                return true;
            }
        }
        return false;
    }

    /**
     * 删除重复的观察钱包
     *
     * @param address
     * @return true if repeat
     */
    public static boolean removeObserve(String address) {
        List<ETHWallet> ethWallets = loadAll();
        for (ETHWallet ethWallet : ethWallets) {
            if (TextUtils.isEmpty(ethWallet.getAddress())) {
                LogUtils.d("wallet getPrivateKey empty");
                continue;
            }
            if (ethWallet.getAddress().trim().equalsIgnoreCase(address.trim()) && ethWallet.getType() == 3) {
                WalletDaoUtils.deleteWallet(ethWallet.getId());
                return true;
            }
        }
        return false;
    }


    public static boolean isValid(String mnemonic) {
        return mnemonic.split(" ").length >= 12;
    }

    public static boolean checkRepeatByKeystore(String keystore) {
        return false;
    }

    /**
     * 修改钱包名称
     *
     * @param walletId
     * @param name
     */
    public static void updateWalletName(long walletId, String name) {
        ETHWallet wallet = ethWalletDao.load(walletId);
        wallet.setName(name);
        ethWalletDao.update(wallet);
    }

    /**
     * 修改钱包状态   观察钱包--->实体钱包
     *
     * @param ethWallet
     */
    public static void updateWallet(ETHWallet ethWallet) {
        ethWalletDao.update(ethWallet);
    }

    /**
     * 修改钱包顺序
     *
     * @param walletId
     * @param order
     */
    public static void updateWalletOrder(long walletId, int order) {
        ETHWallet wallet = ethWalletDao.load(walletId);
        wallet.setOrderID(order);
        ethWalletDao.update(wallet);
    }

    //    /**
//     * 修改钱包Token列表
//     *
//     * @param walletId 钱包id
//     * @param list     代币List
//     */
//    public static void updateTokenList(long walletId, ArrayList<TokenInfo> list) {
//        ETHWallet wallet = ethWalletDao.load(walletId);
//        wallet.setTokenInfoList(list);
//        ethWalletDao.update(wallet);
//    }
//
    public static void setCurrentAfterDelete() {
        List<ETHWallet> ethWallets = ethWalletDao.loadAll();
        if (ethWallets != null && ethWallets.size() > 0) {
            ETHWallet ethWallet = ethWallets.get(0);
            ethWallet.setIsCurrent(true);
            ethWalletDao.update(ethWallet);
        }
    }


    /**
     * 删除钱包
     *
     * @param walletId
     * @return
     */
    public static boolean deleteWallet(long walletId) {
        ETHWallet load = ethWalletDao.load(walletId);
        List<TokenInfo> tokenInfos = tokenInfoDao.loadAll();
        for (TokenInfo tokenInfo : tokenInfos) {
            if (tokenInfo.getWalletId().equals(load.getId())) {
                tokenInfoDao.deleteByKey(tokenInfo.getId());
            }
        }
        ethWalletDao.deleteByKey(walletId);
        setCurrentAfterDelete();
        return true;
    }


    //--------------token 部分---start----------------

    /**
     * 插入新代币
     */
    public static void insertNewToken(TokenInfo tokenInfo) {
        tokenInfoDao.insert(tokenInfo);
    }


    /**
     * 查询当前链上母币
     *
     * @return
     */
    public static TokenInfo getToken() {
        List<TokenInfo> tokenInfos = tokenInfoDao.loadAll();
        Long id = getCurrent().getId();
        ArrayList<TokenInfo> objects = new ArrayList<>();
        for (TokenInfo tokenInfo : tokenInfos) {
            if (tokenInfo.getWalletId() == id) {
                objects.add(tokenInfo);
                return tokenInfo;
            }
        }
        return new TokenInfo();
    }

    /**
     * 查询当前链上母币
     *
     * @return
     */
    public static TokenInfo getTokenByWalletId(long walletId) {
        List<TokenInfo> tokenInfos = tokenInfoDao.loadAll();
        ArrayList<TokenInfo> objects = new ArrayList<>();
        for (TokenInfo tokenInfo : tokenInfos) {
            if (tokenInfo.getWalletId() == walletId) {
                objects.add(tokenInfo);
                return tokenInfo;
            }
        }
        return new TokenInfo();
    }

    /**
     * 修改代币
     *
     * @param tokenInfo
     */
    public static void updateToken(TokenInfo tokenInfo) {
        try {
            tokenInfoDao.update(tokenInfo);
        } catch (Exception e) {
            if (e.getMessage().contains("Cannot update entity without key")) {
                insertNewToken(tokenInfo);
            }
        }
    }

    /**
     * 根据id删除对应代币
     *
     * @return
     */
    public static void deleteToken(long id) {
        tokenInfoDao.deleteByKey(id);
    }


    //--------------token 部分---end----------------


    //--------------交易 部分---start----------------

    /**
     * 删除所有交易
     *
     * @param type 0展示条数  1删除所有数据
     */
    public static void removeAllTransfer(int type) {
        if (transferDao == null) return;
        long count = transferDao.count();
//        ToastUtil.toastView("当前转账数据条数: "+count);
        if (count > 10000 && type == 1) {
            transferDao.deleteAll();
        }
    }

    /**
     * 插入新交易
     */
    public static void insertNewTransfer(Transfer transfer) {
        transferDao.insert(transfer);
    }

    /**
     * 清空交易记录库
     */
    public static void deleteTransfer() {
        transferDao.deleteAll();
    }

    public static int getTransferCount() {
        List<Transfer> transfers = transferDao.loadAll();
        return transfers.size();
    }

    /**
     * 查询当前钱包下交易列表
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static ArrayList<Transfer> getCurrentTransferList(long id) {
        List<Transfer> transfers = transferDao.loadAll();
        transfers.sort((x, y) -> Long.compare(Long.parseLong(y.getTime()), Long.parseLong(x.getTime())));
//        Collections.reverse(transfers);
        ArrayList<Transfer> objects = new ArrayList<>();
        for (Transfer transfer : transfers) {
            if (transfer.getTokenId() == id) {
                objects.add(transfer);
            }
        }
        return objects;
    }

    /**
     * 查询当前钱包下交易列表
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static ArrayList<Transfer> getCurrentTransferList() {
        List<Transfer> transfers = transferDao.loadAll();
        String address = getCurrent().getAddress();
        transfers.sort((x, y) -> Long.compare(Long.parseLong(y.getTime()), Long.parseLong(x.getTime())));
        ArrayList<Transfer> objects = new ArrayList<>();
        for (Transfer transfer : transfers) {
            if (transfer.getPayAddress().equalsIgnoreCase(address)) {
                objects.add(transfer);
            }
        }
        return objects;
    }


    /**
     * 查询当前钱包下交易列表(只有pending 当前链)
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static ArrayList<Transfer> getCurrentTransferPendingList(long id) {
        List<Transfer> transfers = transferDao.loadAll();
        transfers.sort((x, y) -> Long.compare(Long.parseLong(y.getTime()), Long.parseLong(x.getTime())));
//        Collections.reverse(transfers);
        ArrayList<Transfer> objects = new ArrayList<>();
        for (Transfer transfer : transfers) {
            if (transfer.getTokenId() == id && transfer.getPayStatus() == 0) {
                objects.add(transfer);
            }
        }
        return objects;
    }

    /**
     * 获取转入交易记录
     *
     * @param list
     * @return
     */
    public static ArrayList<Transfer> getInTransfer(ArrayList<Transfer> list) {
        ArrayList<Transfer> transfers = new ArrayList<>();
        for (Transfer transfer : list) {
            if (transfer.getPayStatus() == 1) {
                transfers.add(transfer);
            }
        }
        return transfers;
    }

    /**
     * 更新当前交易
     *
     * @return
     */
    public static void updataInTransfer(Transfer transfer) {
        transferDao.update(transfer);
    }

    /**
     * 更新当前交易
     *
     * @return
     */
    public static void removeInTransfer(Transfer transfer) {
        transferDao.delete(transfer);
    }

    /**
     * 获取转出交易记录
     *
     * @param list
     * @return
     */
    public static ArrayList<Transfer> getOutTransfer(ArrayList<Transfer> list) {
        ArrayList<Transfer> transfers = new ArrayList<>();
        for (Transfer transfer : list) {
            if (transfer.getPayStatus() == 2) {
                transfers.add(transfer);
            }
        }
        return transfers;
    }

    /**
     * 获取失败交易记录
     *
     * @param list
     * @return
     */
    public static ArrayList<Transfer> getFailTransfer(ArrayList<Transfer> list) {
        ArrayList<Transfer> transfers = new ArrayList<>();
        for (Transfer transfer : list) {
            if (!transfer.getStatus()) {
                transfers.add(transfer);
            }
        }
        return transfers;
    }


    /**
     * 插入新交易
     */
    public static void insertNewTransaction(Transaction transaction) {
        if (getTransactionCount() >= 100) {
            deleteTransaction();
        }
        transactionDao.insert(transaction);
        Log.e("TAG", "insertNewTransaction: " + getTransactionCount());
    }

    /**
     * 清空交易记录库
     */
    public static void deleteTransaction() {
        Transaction firstEntity = transactionDao.queryBuilder()
                .where(TransactionDao.Properties.WalletAddress.eq(getCurrent().getAddress()))
                .orderAsc(TransactionDao.Properties.Id)
                .limit(1)
                .unique();
        if (firstEntity != null) {
            transactionDao.delete(firstEntity);
        }
    }


    /**
     * 清空交易记录库
     */
    public static void deleteAllTransaction() {
        transactionDao.deleteAll();
    }

    public static int getTransactionCount() {
        return transactionDao.queryBuilder()
                .where(TransactionDao.Properties.WalletAddress.eq(getCurrent().getAddress()))
                .list().size();
    }

    /**
     * 查询当前钱包下交易列表
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static List<Transaction> getCurrentTransactionList() {
        return transactionDao.queryBuilder()
                .where(TransactionDao.Properties.WalletAddress.eq(getCurrent().getAddress()))
                .orderDesc(TransactionDao.Properties.Id)
                .list();
    }


    /**
     * 清空操作记录库
     */
    public static void deleteAllOperation() {
        operationBeanDao.deleteAll();
    }

    /**
     * 清除操作记录库
     */
    public static void deleteOperation() {
        String address = getCurrent().getAddress();
        Property walletAddress = OperationBeanDao.Properties.Address;
        OperationBean firstEntity = operationBeanDao.queryBuilder()
                .where(walletAddress.eq(address))
                .orderAsc(OperationBeanDao.Properties.Id)
                .limit(1)
                .unique();
        if (firstEntity != null) {
            operationBeanDao.delete(firstEntity);
        }
    }
    public static int getOptionCount() {
        return operationBeanDao.queryBuilder()
                .where(OperationBeanDao.Properties.Address.eq(getCurrent().getAddress()))
                .list().size();
    }
    /**
     * 插入新操作
     */
    public static void insertNewOperation(OperationBean operationBean) {
        if (getOptionCount() >= 100) {
            deleteOperation();
        }
        operationBeanDao.insert(operationBean);
    }

    /**
     * 更新操作
     *
     * @return
     */
    public static void updataOperation(OperationBean operationBean) {
        try {
            operationBeanDao.update(operationBean);
        } catch (Exception e) {
            Log.e("TAG", "updataOperation: " + e.getMessage());
            insertNewOperation(operationBean);
        }
    }

    /**
     * 查询当前钱包下操作
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static OperationBean getCurrentOperation(String postId) {
        try {
            OperationBean unique = operationBeanDao.queryBuilder().where(OperationBeanDao.Properties.Address.eq(getCurrent().getAddress()))
                    .where(OperationBeanDao.Properties.PostId.eq(postId)).unique();
            return unique == null ? new OperationBean() : unique;
        } catch (Exception e) {
            return new OperationBean();
        }
    }
}
