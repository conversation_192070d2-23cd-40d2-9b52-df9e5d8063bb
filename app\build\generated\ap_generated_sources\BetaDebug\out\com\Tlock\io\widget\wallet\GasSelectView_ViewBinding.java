// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class GasSelectView_ViewBinding implements Unbinder {
  private GasSelectView target;

  @UiThread
  public GasSelectView_ViewBinding(GasSelectView target) {
    this(target, target);
  }

  @UiThread
  public GasSelectView_ViewBinding(GasSelectView target, View source) {
    this.target = target;

    target.mTvSpeed = Utils.findRequiredViewAsType(source, R.id.tv_speed, "field 'mTvSpeed'", TextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    target.mLlRoot = Utils.findRequiredViewAsType(source, R.id.ll_root, "field 'mLlRoot'", LinearLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    GasSelectView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvSpeed = null;
    target.mTvTime = null;
    target.mLlRoot = null;
  }
}
