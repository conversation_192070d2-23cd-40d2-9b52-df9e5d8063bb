package com.Tlock.io.base;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.callback.OnItemLongClickRecyclerListener;


/**
 * 本类的主要功能是 :  recyclerView 父类适配器
 *
 * <AUTHOR>  2024/2/2 10:22
 */
public class BaseRecyclerViewAdapter<T> extends RecyclerView.Adapter<BaseViewHolder<T>> {
    //list列表数据
    public ArrayList<T> list = new ArrayList<>();
    private Delegate<T> mDelegate;

    public Context mContext;
    //点击事件接口回调
    protected OnItemClickRecyclerListener itemClickRecyclerListener;
    protected OnItemLongClickRecyclerListener itemLongClickRecyclerListener;

    public BaseRecyclerViewAdapter(Context context, ArrayList<T> list, Delegate<T> mDelegate) {
        this.mContext = context;
        this.list = list;
        this.mDelegate = mDelegate;
    }

    public BaseRecyclerViewAdapter(Context context, ArrayList<T> list) {
        this.mContext = context;
        this.list = list;
    }

    public void setmDelegate(Delegate<T> mDelegate) {
        this.mDelegate = mDelegate;
    }

    /**
     * 绑定布局
     *
     * @param parent
     * @param viewType
     * @return 顶级ViewHolder
     */
    @NonNull
    @Override
    public BaseViewHolder<T> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = mDelegate.buildView(parent, viewType);
        return new BaseViewHolder<T>(view);
    }

    /**
     * 绑定ViewHolder数据,处理数据
     *
     * @param holder
     * @param position
     */
    @Override
    public void onBindViewHolder(@NonNull BaseViewHolder holder, int position) {
//        if (mDelegate != null) {
//            mDelegate.bindViewData(position, list.get(position), holder.itemView);
//        }
//        // 对条目设置点击事件回调
//        holder.itemView.setOnClickListener((v) -> {
//            if (itemClickRecyclerListener != null) {
//                itemClickRecyclerListener.onItemClick(position, list.get(position), holder.itemView);
//            }
//        });
//        holder.itemView.setOnLongClickListener((v) -> {
//            if (itemLongClickRecyclerListener != null) {
//                return itemLongClickRecyclerListener.onItemLongClick(position, list.get(position), holder.itemView);
//            }
//            return false;
//        });
    }

    /**
     * 绑定ViewHolder数据,处理数据
     *
     * @param holder
     * @param position
     */
    @Override
    public void onBindViewHolder(@NonNull BaseViewHolder<T> holder, int position, @NonNull List<Object> payloads) {
        if (mDelegate != null) {
            mDelegate.bindViewData(position, list.get(position), holder.itemView);
        }
        // 对条目设置点击事件回调
        holder.itemView.setOnClickListener((v) -> {
            if (itemClickRecyclerListener != null) {
                itemClickRecyclerListener.onItemClick(position, list.get(position), holder.itemView);
            }
        });
        holder.itemView.setOnLongClickListener((v) -> {
            if (itemLongClickRecyclerListener != null) {
                return itemLongClickRecyclerListener.onItemLongClick(position, list.get(position), holder.itemView);
            }
            return false;
        });

        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads);
        } else {



        }
    }


    @Override
    public int getItemCount() {
        return list == null ? 0 : list.size();
    }

    @Override
    public int getItemViewType(int position) {
//        if (list != null
//                && list.get(position) != null
//                && list.get(position) instanceof ZhzfCheckFillBillFlowsBean) {
//            ZhzfCheckFillBillFlowsBean bean = (ZhzfCheckFillBillFlowsBean) list.get(position);
//            return bean.getType();
//        }
        return super.getItemViewType(position);
    }

    /**
     * 刷新数据
     *
     * @param list
     */
    public void setList(ArrayList<T> list) {
        this.list = list;
        notifyDataSetChanged();
    }

    public void setData(ArrayList<T> userList) {
        // 获取DiffResut结果
        long l = System.currentTimeMillis();
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new UserItemDiffCallBack<>(list, userList));
        //使用DiffResult分发给apdate热更新
        diffResult.dispatchUpdatesTo(this);
        //按照DiffeResult定义好的逻辑，更新数据
        list.clear();
        list.addAll(userList);
//        Log.e("pairAdapter", "列表数量  "+list.size());

    }



    /**
     * 添加数据
     *
     * @param list
     */
    public void addHeardList(ArrayList<T> list) {
        if (this.list == null) this.list = new ArrayList<>();
        if (list == null) return;
        this.list.addAll(0, list);
        //notifyItemRangeChanged(this.list.size() > 1 ? this.list.size() - 2 : 0, list.size());
        notifyDataSetChanged();
    }
    /**
     * 添加数据
     *
     * @param list
     */
    public void addList(ArrayList<T> list, int page) {
        if (page == 1) {
            this.list = list;
        } else {
            if (this.list == null) this.list = new ArrayList<>();
            if (list == null) return;
            this.list.addAll(list);
        }
        //notifyItemRangeChanged(this.list.size() > 1 ? this.list.size() - 2 : 0, list.size());
        notifyDataSetChanged();
    }
    /**
     * 添加数据
     *
     * @param list
     */
    public void addListNoChange(ArrayList<T> list, int page) {
        if (page == 1) {
            this.list = list;
            notifyDataSetChanged();
        } else {
            if (this.list == null) this.list = new ArrayList<>();
            if (list == null) return;
            this.list.addAll(list);
            notifyItemRangeInserted(this.list.size() > 1 ? this.list.size() - 2 : 0, list.size());
        }

    }

    public ArrayList<T> getList() {
        return list;
    }

    /**
     * 添加点击事件的接口回调
     *
     * @param itemClickRecyclerListener
     */
    public void setOnItemClickRecyclerListener(OnItemClickRecyclerListener<T> itemClickRecyclerListener) {
        this.itemClickRecyclerListener = itemClickRecyclerListener;
    }

    public void setOnItemLongClickRecyclerListener(OnItemLongClickRecyclerListener<T> itemLongClickRecyclerListener) {
        this.itemLongClickRecyclerListener = itemLongClickRecyclerListener;
    }

    public interface Delegate<T> {
        View buildView(ViewGroup parent, int viewType);

        void bindViewData(int position, T data, View view);
    }
}
