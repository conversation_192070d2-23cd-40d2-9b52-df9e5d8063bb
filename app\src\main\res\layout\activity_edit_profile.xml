<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".activity.cosmos.EditProfileActivity">

    <com.Tlock.io.widget.CustomNavBar
        android:id="@+id/custom_nav_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:title_mid="Edit Profile" />

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        android:layout_below="@id/custom_nav_bar"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/dp_24" />
    <ImageView
        android:layout_width="@dimen/dp_90"
        android:id="@+id/iv_avatar1"
        android:layout_height="@dimen/dp_90"
        android:src="@mipmap/icon_heard_logo"
        android:layout_below="@id/custom_nav_bar"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/dp_24" />
    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_nike_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_avatar"
        app:text_color="@color/cosmos_black"
        app:tv_title="Name" />

    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_user_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ed_nike_name"
        app:text_color="@color/cosmos_black"
        app:tv_title="Username" />

    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_bio"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ed_user_name"
        app:text_color="@color/cosmos_black"
        app:tv_title="Bio" />

    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_Location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ed_bio"
        android:visibility="gone"
        app:text_color="@color/cosmos_black"
        app:tv_title="Location" />

    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_web_site"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_below="@id/ed_Location"
        app:text_color="@color/cosmos_black"
        app:tv_title="Website" />


    <TextView
        android:id="@+id/tv_save"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp_24"
        android:background="@drawable/btn_black_60"
        android:gravity="center"
        android:padding="@dimen/dp_15"
        android:text="Save"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_below="@id/ed_web_site"
        android:layout_marginLeft="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginRight="@dimen/dp_15"
        android:background="@drawable/shape_light_red_2"
        android:paddingLeft="@dimen/dp_10"
        android:paddingTop="@dimen/dp_9"
        android:paddingRight="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_9"
        android:textColor="@color/error_red"
 />
</RelativeLayout>




