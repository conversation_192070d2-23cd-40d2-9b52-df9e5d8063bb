// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WalletInfoActivity_ViewBinding implements Unbinder {
  private WalletInfoActivity target;

  private View view7f090304;

  private View view7f090256;

  private View view7f090254;

  private View view7f09024d;

  private View view7f090245;

  private View view7f090252;

  private View view7f09024c;

  private View view7f090240;

  @UiThread
  public WalletInfoActivity_ViewBinding(WalletInfoActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public WalletInfoActivity_ViewBinding(final WalletInfoActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvBalance = Utils.findRequiredViewAsType(source, R.id.tv_balance, "field 'mTvBalance'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_address, "field 'mTvAddress' and method 'onBindClick'");
    target.mTvAddress = Utils.castView(view, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    view7f090304 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_send, "method 'onBindClick'");
    view7f090256 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_received, "method 'onBindClick'");
    view7f090254 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_name, "method 'onBindClick'");
    view7f09024d = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_history, "method 'onBindClick'");
    view7f090245 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_private_key, "method 'onBindClick'");
    view7f090252 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_mnemonic, "method 'onBindClick'");
    view7f09024c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_delete, "method 'onBindClick'");
    view7f090240 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    WalletInfoActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvHeard = null;
    target.mTvBalance = null;
    target.mTvAddress = null;

    view7f090304.setOnClickListener(null);
    view7f090304 = null;
    view7f090256.setOnClickListener(null);
    view7f090256 = null;
    view7f090254.setOnClickListener(null);
    view7f090254 = null;
    view7f09024d.setOnClickListener(null);
    view7f09024d = null;
    view7f090245.setOnClickListener(null);
    view7f090245 = null;
    view7f090252.setOnClickListener(null);
    view7f090252 = null;
    view7f09024c.setOnClickListener(null);
    view7f09024c = null;
    view7f090240.setOnClickListener(null);
    view7f090240 = null;
  }
}
