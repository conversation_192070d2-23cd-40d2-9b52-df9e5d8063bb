// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WalletInfoActivity_ViewBinding implements Unbinder {
  private WalletInfoActivity target;

  private View view7f090303;

  private View view7f090255;

  private View view7f090253;

  private View view7f09024c;

  private View view7f090244;

  private View view7f090251;

  private View view7f09024b;

  private View view7f09023f;

  @UiThread
  public WalletInfoActivity_ViewBinding(WalletInfoActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public WalletInfoActivity_ViewBinding(final WalletInfoActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvBalance = Utils.findRequiredViewAsType(source, R.id.tv_balance, "field 'mTvBalance'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_address, "field 'mTvAddress' and method 'onBindClick'");
    target.mTvAddress = Utils.castView(view, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    view7f090303 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_send, "method 'onBindClick'");
    view7f090255 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_received, "method 'onBindClick'");
    view7f090253 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_name, "method 'onBindClick'");
    view7f09024c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_history, "method 'onBindClick'");
    view7f090244 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_private_key, "method 'onBindClick'");
    view7f090251 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_mnemonic, "method 'onBindClick'");
    view7f09024b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.rl_delete, "method 'onBindClick'");
    view7f09023f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    WalletInfoActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvHeard = null;
    target.mTvBalance = null;
    target.mTvAddress = null;

    view7f090303.setOnClickListener(null);
    view7f090303 = null;
    view7f090255.setOnClickListener(null);
    view7f090255 = null;
    view7f090253.setOnClickListener(null);
    view7f090253 = null;
    view7f09024c.setOnClickListener(null);
    view7f09024c = null;
    view7f090244.setOnClickListener(null);
    view7f090244 = null;
    view7f090251.setOnClickListener(null);
    view7f090251 = null;
    view7f09024b.setOnClickListener(null);
    view7f09024b = null;
    view7f09023f.setOnClickListener(null);
    view7f09023f = null;
  }
}
