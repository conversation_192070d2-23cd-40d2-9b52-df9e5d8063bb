package com.Tlock.io.entity;

/**
 * @ClassName VersionBean
 * <AUTHOR>
 * @Data 2021/8/24 9:43
 * @Desc
 */

public class VersionBean {


    /**
     * id : 2
     * createDate : 2021-09-05T16:17:02.000+00:00
     * lastUpdate : 2021-09-05T16:17:25.000+00:00
     * createBy : admin
     * lastUpdateBy : admin
     * deleteFlag : false
     * appVersion : 2
     * appUrl : http://18.217.215.233:8888/group1/M00/00/00/rB8kuWE07Y-AdFzZAAABWrLreyw972.xml
     * appDesc : test
     * appVersionName : test
     * disable : false
     * osType : 1
     * size : 346
     * requireUpdate : false
     */

    private int id;
    private String createDate;
    private String lastUpdate;
    private String createBy;
    private String lastUpdateBy;
    private boolean deleteFlag;
    private int appVersion;
    private String appUrl;
    private String appDesc;
    private String appVersionName;
    private boolean disable;
    private int osType;
    private int size;
    private boolean requireUpdate;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(String lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    public boolean isDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public int getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(int appVersion) {
        this.appVersion = appVersion;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public String getAppDesc() {
        return appDesc;
    }

    public void setAppDesc(String appDesc) {
        this.appDesc = appDesc;
    }

    public String getAppVersionName() {
        return appVersionName;
    }

    public void setAppVersionName(String appVersionName) {
        this.appVersionName = appVersionName;
    }

    public boolean isDisable() {
        return disable;
    }

    public void setDisable(boolean disable) {
        this.disable = disable;
    }

    public int getOsType() {
        return osType;
    }

    public void setOsType(int osType) {
        this.osType = osType;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public boolean isRequireUpdate() {
        return requireUpdate;
    }

    public void setRequireUpdate(boolean requireUpdate) {
        this.requireUpdate = requireUpdate;
    }
}
