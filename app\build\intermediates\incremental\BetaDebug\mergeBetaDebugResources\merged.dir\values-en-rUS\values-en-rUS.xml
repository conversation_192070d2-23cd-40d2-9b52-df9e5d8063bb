<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="After">After</string>
    <string name="Backup">Backup</string>
    <string name="Backup_mnemonics">Backup Mnemonics</string>
    <string name="Backup_mnemonics_or">Backup Mnemonics or Private Key </string>
    <string name="Cancel">Cancel</string>
    <string name="Chinese">简体中文</string>
    <string name="Contain_illegal_character">Contain Illegal Character</string>
    <string name="Copy_url">Copy url</string>
    <string name="Count">Vol</string>
    <string name="English">English</string>
    <string name="In_minute">In a minute</string>
    <string name="Miners_fee">Gas Fee</string>
    <string name="Mnemonic">Mnemonic</string>
    <string name="My_qrCode">My QR Code</string>
    <string name="Network_Unavailable">Network Unavailable</string>
    <string name="New_version_found">New version found</string>
    <string name="Please_later">Please later...</string>
    <string name="Price">Price</string>
    <string name="Private_Key">Private Key</string>
    <string name="Reenter_password">Reenter password</string>
    <string name="Refresh">Refresh</string>
    <string name="Renew">Renew</string>
    <string name="Search">Search</string>
    <string name="So_Fast">So Fast</string>
    <string name="Successful_operation">Successful Operation</string>
    <string name="Telegram">Telegram :</string>
    <string name="Updating">Updating</string>
    <string name="Very_fast">Very Fast</string>
    <string name="Wallet">Wallet</string>
    <string name="addLiquidity">Add Liquidity</string>
    <string name="address_error">Address Error</string>
    <string name="announcement">Announcement</string>
    <string name="app_name">TLK</string>
    <string name="approve">Appr.</string>
    <string name="backup_key">Backup Private Key</string>
    <string name="backup_now">Backup Now</string>
    <string name="browser_error">No web browser found</string>
    <string name="change_password">Please enter the password you want to change</string>
    <string name="check_finger">Verify FingerPrint</string>
    <string name="check_success">Check Success</string>
    <string name="check_success_tip">We will remove the mnemonic from the device please keep it safe</string>
    <string name="checkpwd_tip">Please enter the wallet password at creation time</string>
    <string name="comfirm">Determine</string>
    <string name="coming_soon">Coming Soon</string>
    <string name="common">Common</string>
    <string name="confirm">Confirm</string>
    <string name="confirm_del">Confirm the deletion</string>
    <string name="copy">Copy</string>
    <string name="copy_address">Copy Address</string>
    <string name="copy_to">Copied to clipboard</string>
    <string name="create_or_input_wallet">Create/Import Wallet</string>
    <string name="create_wallet">Create Wallet</string>
    <string name="custom">Custom</string>
    <string name="day_ago">day ago</string>
    <string name="delete_confirm">Are you sure you want to delete this wallet？</string>
    <string name="delete_success">Delete Success</string>
    <string name="delete_wallet">Delete Wallet</string>
    <string name="digits_needed">6 digits is needed</string>
    <string name="edit_Name">Edit Name</string>
    <string name="error_pwd">Password Error</string>
    <string name="export_Privatekey">Export Private Key</string>
    <string name="fail">Fail</string>
    <string name="fast">Fast</string>
    <string name="have_wallet_name">Name exists</string>
    <string name="hour_ago">Hour ago</string>
    <string name="input_check_pwd">Input Password</string>
    <string name="input_observe">Watch Wallet</string>
    <string name="input_wallet">Import Wallet</string>
    <string name="input_wallet_name">Input Wallet Name</string>
    <string name="know">I Know</string>
    <string name="less_1"> &lt;1 min</string>
    <string name="less_2"> &lt;2 min</string>
    <string name="less_3"> &lt;3 min</string>
    <string name="less_5"> &lt;5 min</string>
    <string name="min_ago"> Min ago</string>
    <string name="mnemonics">Mnemonics</string>
    <string name="mnemonics_error">Wrong Mnemonics</string>
    <string name="next">Next</string>
    <string name="no_data">no data</string>
    <string name="no_sdcard">No SD card</string>
    <string name="no_space">Input private key, no space.</string>
    <string name="no_space_address">Input address, no space.</string>
    <string name="not_wifi_tip">运营商网络下继续更新可能产生超额流量费，建议使用wifi网络进行更新操作</string>
    <string name="notnull">wallet nickname not be null</string>
    <string name="old_password">Please enter your old password</string>
    <string name="passworld_disaffinity">Wrong password</string>
    <string name="passworld_must">Password shoulb be at least 6 digits</string>
    <string name="private_key_error">Private Key Error</string>
    <string name="rank">Rank</string>
    <string name="reset_password">Reset Password</string>
    <string name="reset_wallet_name">Edit Wallet Name</string>
    <string name="save_rank">Save current rank</string>
    <string name="select_browser">Select web browser</string>
    <string name="share_qr">Share QR</string>
    <string name="success">Success</string>
    <string name="text_back_up">Your 12/24-word recovery phrase is the ONLY way to restore access to your wallet and funds.
• Write it down on paper and store it securely (never digitally) • Keep it private - anyone with this phrase can steal your assets • Make multiple copies and store them in separate safe locations • Never share it with anyone, including wallet support staff • This phrase cannot be recovered if lost - you will permanently lose access to your funds
We cannot restore your recovery phrase if you lose it. You are solely responsible for keeping it safe.</string>
    <string name="to_backup_first">Go to the backup</string>
    <string name="url_error">Wrong URL</string>
    <string name="wallet_address">Wallet Address</string>
    <string name="wallet_already_exists">Wallet already added—no repeats allowed.</string>
    <string name="wallet_info">Wallet Info</string>
    <string name="wallet_list">Wallet List</string>
    <string name="wallet_name">Wallet Name</string>
    <string name="wallet_nickname_exists">wallet nickname already exists</string>
    <string name="with_space">Input seed words with space</string>
    <string name="world_must_list">please check words sequence</string>
</resources>