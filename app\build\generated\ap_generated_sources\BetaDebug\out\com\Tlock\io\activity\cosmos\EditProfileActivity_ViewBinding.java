// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomEditBox;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class EditProfileActivity_ViewBinding implements Unbinder {
  private EditProfileActivity target;

  private View view7f090133;

  private View view7f090361;

  @UiThread
  public EditProfileActivity_ViewBinding(EditProfileActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public EditProfileActivity_ViewBinding(final EditProfileActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    view = Utils.findRequiredView(source, R.id.iv_avatar, "field 'mIvAvatar' and method 'onBindClick'");
    target.mIvAvatar = Utils.castView(view, R.id.iv_avatar, "field 'mIvAvatar'", ImageView.class);
    view7f090133 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mEdNikeName = Utils.findRequiredViewAsType(source, R.id.ed_nike_name, "field 'mEdNikeName'", CustomEditBox.class);
    target.mEdUserName = Utils.findRequiredViewAsType(source, R.id.ed_user_name, "field 'mEdUserName'", CustomEditBox.class);
    target.mEdBio = Utils.findRequiredViewAsType(source, R.id.ed_bio, "field 'mEdBio'", CustomEditBox.class);
    target.mEdLocation = Utils.findRequiredViewAsType(source, R.id.ed_Location, "field 'mEdLocation'", CustomEditBox.class);
    target.mEdWebSite = Utils.findRequiredViewAsType(source, R.id.ed_web_site, "field 'mEdWebSite'", CustomEditBox.class);
    view = Utils.findRequiredView(source, R.id.tv_save, "field 'mTvSave' and method 'onBindClick'");
    target.mTvSave = Utils.castView(view, R.id.tv_save, "field 'mTvSave'", TextView.class);
    view7f090361 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvAvatar1 = Utils.findRequiredViewAsType(source, R.id.iv_avatar1, "field 'mIvAvatar1'", ImageView.class);
    target.mTvError = Utils.findRequiredViewAsType(source, R.id.tv_error, "field 'mTvError'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    EditProfileActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvAvatar = null;
    target.mEdNikeName = null;
    target.mEdUserName = null;
    target.mEdBio = null;
    target.mEdLocation = null;
    target.mEdWebSite = null;
    target.mTvSave = null;
    target.mIvAvatar1 = null;
    target.mTvError = null;

    view7f090133.setOnClickListener(null);
    view7f090133 = null;
    view7f090361.setOnClickListener(null);
    view7f090361 = null;
  }
}
