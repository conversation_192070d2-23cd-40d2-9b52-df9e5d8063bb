package com.Tlock.io;

import static com.Tlock.io.utils.cosmos.CosmosUtils.BASE_DENOM;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.activity.cosmos.PostActivity;
import com.Tlock.io.activity.wallet.NewWalletActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.ContentPagerAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.JsopLvDong;
import com.Tlock.io.entity.VersionBean;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.fragment.HomeFragment;
import com.Tlock.io.fragment.HotFragment;
import com.Tlock.io.fragment.IndexFragment;
import com.Tlock.io.fragment.MessageFragment;
import com.Tlock.io.fragment.MineFragment;
import com.Tlock.io.itemBean.wallet.WalletViewBean;
import com.Tlock.io.manger.ActivitiesManager;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.profile.ProfileTXProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.PermissionHelper;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.cosmos.CosmosWalletUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.bumptech.glide.Glide;
import com.cosmos.base.v1beta1.CoinProto;
import com.google.android.material.badge.BadgeDrawable;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.Any;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnSelectListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.greenrobot.greendao.annotation.NotNull;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;

import butterknife.BindView;
import butterknife.OnClick;
import wallet.core.jni.HDWallet;

@RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
public class MainActivity extends BaseActivity {

    private static final int REQUEST_CODE_SCAN = 102;

    @BindView(R.id.vp_main)
    ViewPager2 mVpMain;
    @BindView(R.id.rl_index)
    RelativeLayout mRlIndex;
    @BindView(R.id.bottomNavigationView)
    BottomNavigationView mBottomNavigationView;
    @BindView(R.id.iv_more)
    ImageView mIvMore;
    @BindView(R.id.menu_frame)
    FrameLayout mMenuFrame;
    @BindView(R.id.drawer_layout)
    DrawerLayout mDrawerLayout;
    @BindView(R.id.content_frame)
    FrameLayout mContentFrame;
    @BindView(R.id.tv_list_title)
    TextView mTvListTitle;
    @BindView(R.id.rv_wallet)
    RecyclerView mRvWallet;
    @BindView(R.id.tv_create)
    TextView mTvCreate;
    private IndexFragment indexFragment;
    private long exitTime = 0;
    public int code = 101;
    private VersionBean data;
    private int index = 1;
    private ArrayList<Fragment> fragments = new ArrayList<>();
    private List<String> titles = new ArrayList<>();
    private PowerManager.WakeLock wakeLock;
    private PermissionHelper permissionHelper;
    //    private FuturesFragment futuresFragment;
    private String[] mustPer = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED,
            Manifest.permission.CAMERA,
    };
    private HomeFragment homeFragment;
    //    private HomeFragment homeFragment;
    String[] classes = new String[]{"Crypto", "Finance", "World", "Entertainment", "Sports", "Tech", "Style"};
    private MineFragment mineFragment;
    private MessageFragment messageFragment;
    private HotFragment hotFragment;


    private Context context;
    private int type = 1;//1不带排序 2.带有排序  3.只搜素观察钱包 不带排序
    private String title;
    private ArrayList<ETHWallet> ethWallets = new ArrayList<>();
    private BaseRecyclerViewAdapter<ETHWallet> adapter;

    // 记录当前选中的导航项ID，用于检测重复点击
    private int currentSelectedItemId = R.id.item_home;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(@NotNull Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        context.startActivity(intent);
    }

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, int toIndex) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.putExtra("toIndex", toIndex);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.layout_main;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
        setStatusColor(getColor(R.color.white));
        permissionHelper = new PermissionHelper(this);
//WalletDaoUtils.deleteAllOperation();
        initFragment();
        initRecycler();
        //创建两个Fragment出来 用于点击时切换
        requestPermission();
        BottomNavigationView bottomNavigationView = findViewById(R.id.bottomNavigationView);
        bottomNavigationView.setItemIconTintList(null);
        bottomNavigationView.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            public boolean onNavigationItemSelected(MenuItem item) {
                int itemId = item.getItemId();

                // 检测是否重复点击Home按钮
                if (itemId == R.id.item_home && currentSelectedItemId == R.id.item_home) {
                    // 重复点击Home，刷新当前Tab数据
                    if (homeFragment != null) {
                        homeFragment.refreshCurrentTab();
                    }
                    return true;
                }

                // 更新当前选中的导航项
                currentSelectedItemId = itemId;

                // 选择item
                switch (itemId) {
                    case R.id.item_home:
                        mVpMain.setCurrentItem(0, false);
                        SpUtil.setStartFlag(0);
                        mIvMore.setVisibility(View.VISIBLE);
                        setStatusColor(getColor(R.color.white));
                        break;
                    case R.id.item_topic:
                        mVpMain.setCurrentItem(1, false);
                        SpUtil.setStartFlag(1);
                        mIvMore.setVisibility(View.GONE);

                        setStatusColor(getColor(R.color.white));
                        break;
                    case R.id.item_hot:
                        mVpMain.setCurrentItem(2, false);
                        SpUtil.setStartFlag(2);
                        mIvMore.setVisibility(View.GONE);

                        setStatusColor(getColor(R.color.white));
                        break;
                    case R.id.item_msg:
                        mVpMain.setCurrentItem(3, false);
                        SpUtil.setStartFlag(3);
                        mIvMore.setVisibility(View.GONE);

                        setStatusColor(getColor(R.color.white));
                        getMessageCount();
                        break;
                    case R.id.item_my:
                        mVpMain.setCurrentItem(4, false);
                        mIvMore.setVisibility(View.GONE);
                        SpUtil.setStartFlag(4);
//                        setBarColor();
                        break;
                }

                return true;
            }
        });
    }

    private void initRecycler() {
        mRvWallet.setLayoutManager(new LinearLayoutManager(context));
        ethWallets = (ArrayList<ETHWallet>) WalletDaoUtils.loadAll();
        if (ethWallets == null || ethWallets.size() == 0)
            ethWallets = new ArrayList<>();
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), ethWallets, new BaseRecyclerViewAdapter.Delegate<ETHWallet>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                WalletViewBean itemView = new WalletViewBean(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, ETHWallet data, View view) {
                ((WalletViewBean) view).setData(data, type);
            }
        });
        mRvWallet.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<ETHWallet>() {
            @Override
            public void onItemClick(int position, ETHWallet data, View view) {
                WalletDaoUtils.updateCurrent(data.getId());
                ArrayList<ETHWallet> ethWallets = (ArrayList<ETHWallet>) WalletDaoUtils.loadAll();
                if (ethWallets != null)
                    adapter.setList(ethWallets);
                mineFragment.resetAndChange();
                mDrawerLayout.closeDrawers();

            }
        });

        if (!TextUtils.isEmpty(title)) {
            mTvListTitle.setText(title);
        }
    }

    private void initFragment() {
//        Log.e(TAG, "initFragment: 执行了");
        //首页
        homeFragment = new HomeFragment();
        //话题
        indexFragment = new IndexFragment();
        //我的
        mineFragment = new MineFragment();
        //消息
        messageFragment = new MessageFragment();
        //热搜
        hotFragment = new HotFragment();

        fragments.add(homeFragment);
        fragments.add(indexFragment);
        fragments.add(hotFragment);
        fragments.add(messageFragment);
        fragments.add(mineFragment);

        titles.add("");
        titles.add("");
        titles.add("");
        titles.add("");
        titles.add("");

        ContentPagerAdapter contentPagerAdapter = new ContentPagerAdapter(getSupportFragmentManager(), getLifecycle(), fragments);
        mVpMain.setAdapter(contentPagerAdapter);
        mVpMain.setUserInputEnabled(false);
        mVpMain.setOffscreenPageLimit(2);


//        Log.e(TAG, "打开页面 " + SpUtil.getStartFlag());
        switch (SpUtil.getStartFlag()) {
            case 0:
                setStatusColor(getResources().getColor(R.color.white));
                mVpMain.setCurrentItem(0, false);
                mIvMore.setVisibility(View.VISIBLE);
                mBottomNavigationView.setSelectedItemId(R.id.item_home);
                currentSelectedItemId = R.id.item_home;
                break;
            case 1:
                setStatusColor(getResources().getColor(R.color.white));
                mVpMain.setCurrentItem(1, false);
                mBottomNavigationView.setSelectedItemId(R.id.item_topic);
                mIvMore.setVisibility(View.GONE);
                currentSelectedItemId = R.id.item_topic;
                break;
            case 2:
                setStatusColor(getResources().getColor(R.color.white));
                mVpMain.setCurrentItem(2, false);
                mBottomNavigationView.setSelectedItemId(R.id.item_hot);
                mIvMore.setVisibility(View.GONE);
                currentSelectedItemId = R.id.item_hot;
                break;
            case 3:
                setStatusColor(getResources().getColor(R.color.white));
                mVpMain.setCurrentItem(3, false);
                mBottomNavigationView.setSelectedItemId(R.id.item_msg);
                mIvMore.setVisibility(View.GONE);
                currentSelectedItemId = R.id.item_msg;
                break;
            case 4:
                mVpMain.setCurrentItem(4, false);
                mBottomNavigationView.setSelectedItemId(R.id.item_my);
                mIvMore.setVisibility(View.GONE);
                currentSelectedItemId = R.id.item_my;
                break;
        }
        mVpMain.setSaveEnabled(false);
    }


    @Override
    protected void loadData() {
        if (WalletDaoUtils.loadAll().size() == 0) {
            HDWallet hdWallet = new HDWallet(128, "");
            ETHWallet wallet = CosmosWalletUtils.getWallet(hdWallet.mnemonic(), 2);
            wallet.setName(wallet.getAddress().substring(wallet.getAddress().length() - 6));
            WalletDaoUtils.insertNewWallet(wallet);
        }
        getMessageCount();
    }

    private void getMessageCount() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                int i = SpUtil.getMessageCount();
                long msgCount = 0;
                try {
                    msgCount = CosmosUtils.getMsgCount(WalletDaoUtils.getCurrent().getAddress());
                } catch (Exception e) {
                }
                if (msgCount > i) {
                    BadgeDrawable orCreateBadge = mBottomNavigationView.getOrCreateBadge(R.id.item_msg);
                    orCreateBadge.setBackgroundColor(getResources().getColor(R.color.message_count_bg_color));
                    int l = (int) (msgCount - i);
                    orCreateBadge.setNumber(l > 99 ? 99 : l);
                    orCreateBadge.setVisible(true);
                } else {
                    BadgeDrawable orCreateBadge = mBottomNavigationView.getOrCreateBadge(R.id.item_msg);
                    orCreateBadge.setVisible(false);
                }
                SpUtil.setMessageCount((int) msgCount);
            }
        });
    }


    @Override
    protected void onBackClick(View view) {
        if ((System.currentTimeMillis() - exitTime) > 2000) {
            showToast("Click Exit program again");
            exitTime = System.currentTimeMillis();
        } else {
            ActivitiesManager.getInstance().popAllActivity();
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        //关闭所有线程
        AppApplication.getInstance().getThreadPoolExecutor().shutdownNow();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTokenChane(Event event) {
        if (event.getMessgae().equals("SHOW_LIST")) {
            ethWallets = (ArrayList<ETHWallet>) WalletDaoUtils.loadAll();
            if (ethWallets == null || ethWallets.size() == 0)
                ethWallets = new ArrayList<>();
            adapter.setList(ethWallets);
            mDrawerLayout.openDrawer(GravityCompat.START);

        }
        if (event.getMessgae().equals(EventConstant.WALLET)) {
            ethWallets = (ArrayList<ETHWallet>) WalletDaoUtils.loadAll();
            if (ethWallets == null || ethWallets.size() == 0)
                ethWallets = new ArrayList<>();
            adapter.setList(ethWallets);
        }

    }


    //授权回调
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        ArrayList<String> strings = new ArrayList<>();
        for (int i = 0; i < grantResults.length; i++) {
            if (grantResults[i] != 0) {
                strings.add(permissions[i]);
            }
//            Log.e("权限管理:", "onRequestPermissionsResult: " + permissions[i] + "  状态: " + grantResults[i]);
        }

        if (permissionHelper != null)
            permissionHelper.handleRequestPermissionsResult(requestCode, strings.toArray(new String[strings.size()]), grantResults);

    }

    @OnClick({R.id.iv_more, R.id.tv_create})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_create:
                //导入或创建钱包
                NewWalletActivity.start(getActivity());
                break;
            case R.id.iv_more:
                new XPopup.Builder(getActivity())
                        .atView(mIvMore)  // 依附于所点击的View，内部会自动判断在上方或者下方显示
                        .asAttachList(new String[]{
//                                "获取第一页",
//                                        "随机获取",
                                        "发帖",
                                        "读取100用户",
                                        "100用户转账",
                                        "获取100用户头像",
                                        "修改头像名称",
                                        "测试弹窗"
                                },
                                new int[]{},
                                new OnSelectListener() {
                                    @Override
                                    public void onSelect(int position, String text) {
                                        switch (position) {
                                            case 0:
                                                PostActivity.start(getActivity());
                                                break;
                                            case 1:
                                                getWalletForFile();
                                                break;
                                            case 2:
                                                send100Tok();
                                                break;
//                                            case 3:
//                                                getImageData();
//                                                break;
                                            case 3:
                                                getUserInfoForFile();
                                                break;
                                            case 4:
                                                changeName(walletList, 0);
                                                break;
                                            case 5:
//                                                PopCheckMnemonics popCheckMnemonics = new PopCheckMnemonics(getActivity());
//                                                popCheckMnemonics.setCallback(new PopCheckMnemonics.Callback() {
//                                                    @Override
//                                                    public void confirm() {
//                                                        showToast(getResources().getString(R.string.Successful_operation));
//                                                    }
//                                                });
//                                                new XPopup.Builder(getActivity())
//                                                        .isDestroyOnDismiss(true)
//                                                        .borderRadius(100)
//                                                        .dismissOnTouchOutside(false)
//                                                        .asCustom(popCheckMnemonics)
//                                                        .show();

                                                try {
                                                    CosmosUtils.getGranteeBalance();

                                                } catch (Exception e) {
                                                    Log.e(TAG, "onSelect: " + e.getMessage());
                                                }
                                                break;
                                        }
                                    }
                                })
                        .show();
                break;
        }
    }

    /**
     * 删除管理员
     */
    private void removeManger(String address) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ProfileTXProto.ManageOptions build = ProfileTXProto.ManageOptions.newBuilder()
                        .setAdminAddress(address)
                        .build();
                ProfileTXProto.MsgManageAdminRequest appoint = ProfileTXProto.MsgManageAdminRequest.newBuilder()
                        .setCreator(WalletDaoUtils.getCurrent().getAddress())
                        .setAction("remove")
                        .setManageJson(build)
                        .build();
                CosmosUtils.sendMangerBody(appoint);
            }
        });
    }

    /**
     * 添加管理员
     */
    private void addManger(ProfileTXProto.MsgManageAdminRequest build) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.sendMangerBody(build);
            }
        });
    }


    public void getPYdata() {
//        params.clear();
//        OKHttpManager.getAsyn("https://api.jinse.cn/noah/v2/lives?limit=1&reading=false&source=web&flag=down&id=0&category=0", new OKHttpManager.ResultCallback<BaseListBean<JinseBean>>() {
//            //        OKHttpManager.postAsyn(security, params, new OKHttpManager.ResultCallback<BaseBean<ContractSecurityBean>>() {
//            @Override
//            public void onError(int code, String result, String message) {
//                hideLoading();
//                showToast("未查询到数据");
//            }
//
//            @Override
//            public void onResponse(BaseListBean<JinseBean> response) {
//                ArrayList<JinseBean> list = response.getList();
//                for (JinseBean jinseBean : list) {
//                    JinseBean.Lives lives = jinseBean.getLives().get(0);
//                    PostTXProto.MsgCreateFreePost.Builder builder = PostTXProto.MsgCreateFreePost.newBuilder();
//                    ETHWallet current = WalletDaoUtils.getCurrent();
//                    builder.setCreator(current.getAddress());
//                    String content = lives.getContent();
//                    builder.setContent(content);
//                    int i = content.indexOf("【");
//                    int i1 = content.indexOf("】");
//                    String substring = lives.getContent().substring(i + 1, i1);
//                    builder.addTopic(substring);
//                    PostTXProto.MsgCreateFreePost build = builder.build();
//                    CosmosUtils.sendPostBody(build);
//                }
//
//            }
//        }, this);

    }

    private int id = 448000;
    private ArrayList<PostTXProto.MsgCreatePost> dataList = new ArrayList<>();

    public void getPYdata100() {
//        params.clear();
//        OKHttpManager.getAsyn("https://api.jinse.cn/noah/v2/lives?limit=1&reading=false&source=web&flag=down&id=" + id + "&category=0", new OKHttpManager.ResultCallback<BaseListBean<JinseBean>>() {
//            //        OKHttpManager.postAsyn(security, params, new OKHttpManager.ResultCallback<BaseBean<ContractSecurityBean>>() {
//            @Override
//            public void onError(int code, String result, String message) {
//                hideLoading();
//                showToast("未查询到数据");
//            }
//
//            @Override
//            public void onResponse(BaseListBean<JinseBean> response) {
//                ArrayList<JinseBean> list = response.getList();
//                for (JinseBean jinseBean : list) {
//                    JinseBean.Lives lives = jinseBean.getLives().get(0);
//                    PostTXProto.MsgCreatePost.Builder builder = PostTXProto.MsgCreatePost.newBuilder();
//                    ETHWallet current = WalletDaoUtils.getCurrent();
//                    builder.setCreator(current.getAddress());
//                    String content = lives.getContent();
//                    int len = 0;
//                    if (content.length() >= 300) {
//                        len = 250;
//                    } else {
//                        len = content.length();
//                    }
//                    builder.setContent(content.substring(0, len - 1));
//                    int i = content.indexOf("【");
//                    int i1 = content.indexOf("】");
//                    String substring = lives.getContent().substring(i + 1, i1);
//                    builder.addTopic(substring);
//                    builder.setCategory(classes[new Random().nextInt(classes.length)]);
//                    PostTXProto.MsgCreateFreePost build = builder.build();
//                    dataList.add(build);
//                    id = lives.getId();
////                    Log.e(TAG, "onResponse:输出最后id    " + id);
//                }
//                if (dataList.size() >= 100) {
//                    CosmosUtils.sendPostListBody(dataList);
//                    dataList.clear();
//                } else {
//                    getPYdata100();
//                }
//            }
//        }, this);

    }


    private void requestPermission() {
        permissionHelper.requestPermissions(this,
                new PermissionHelper.PermissionListener() {
                    @Override
                    public void doAfterGrand(String... permission) {

                    }

                    @Override
                    public void doAfterDenied(String... permission) {
                        permissionHelper.againWarnRequestPermission(getActivity(), permission);
                    }
                }, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        // 点击取消了
                        finish();
                    }
                }, mustPer);

    }


    private List<String> getRequiredPermissions() {
        List<String> permissions = new ArrayList<>();
        if (Build.VERSION.SDK_INT >= 33) { // Android 13+
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES);
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO);
            if (Build.VERSION.SDK_INT >= 34) { // Android 14+
                permissions.add(Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED);
            }
        } else {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        return permissions;
    }

    private void getWalletForFile() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {

            @Override
            public void run() {
                String string = readRawFile(getActivity(), R.raw.wallet_data);
                Type type = new TypeToken<List<ETHWallet>>() {
                }.getType();
                walletList = new Gson().fromJson(string, type);
//                Log.e(TAG, "run: 读取结束"+walletList.size());
            }
        });
    }

    private void send100Tok() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
//                Log.e(TAG, "getNameHeard: 读取数据结束-----------------------" + nameList.size());
                if (walletList.size() > 10) {
                    sendTOK2Wallet(walletList);
                }
            }
        });


    }

    private ArrayList<JsopLvDong> nameList = new ArrayList<>();
    private int indexPage = 1;

    public void getImageData() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                String string = readRawFile(getActivity(), R.raw.user_name_heard);
                Type type = new TypeToken<List<JsopLvDong>>() {
                }.getType();
                ArrayList<JsopLvDong> userList = new Gson().fromJson(string, type);


                for (int i = 0; i < userList.size(); i++) {
                    try {
                        Bitmap myBitmap = Glide.with(getActivity())
                                .asBitmap() //必须
                                .load(userList.get(i).getAvatar())
                                .centerCrop()
                                .into(150, 150)
                                .get();
                        int finalI = i;
                        mIvMore.post(new Runnable() {
                            @Override
                            public void run() {
                                String string = BitmapUtils.bitmapToBase64(myBitmap);
                                userList.get(finalI).setAvatar(string);
                                Log.e(TAG, "bitmap_Position: " + finalI + "  数据 :  " + string);
                            }
                        });

                    } catch (ExecutionException e) {
                        Log.e(TAG, "run: " + e.getMessage());
                    } catch (InterruptedException e) {
                        Log.e(TAG, "run: " + e.getMessage());

                    }
                }
                writeToInternalStorage(getActivity(), "user_info.txt", JsonUtils.listToJson(userList));

            }
        });


    }


    private ArrayList<ETHWallet> walletList = new ArrayList<>();

    public String readRawFile(Context context, int resourceId) {
        try (InputStream is = context.getResources().openRawResource(resourceId);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = is.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            return baos.toString("UTF-8"); // 指定编码
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private ArrayList<ETHWallet> get100Wallet() {
        ArrayList<ETHWallet> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            HDWallet hdWallet = new HDWallet(128, "");
            ETHWallet walletInfo = CosmosWalletUtils.getWallet(hdWallet.mnemonic(), 2);
            list.add(walletInfo);
        }
        return list;
    }


    private void sendTOK2Wallet(ArrayList<ETHWallet> walletList) {
        List<Any> anyList = new ArrayList<>();
        for (int i = 0; i < walletList.size(); i++) {
            com.cosmos.bank.v1beta1.TxProto.MsgSend.Builder builder = com.cosmos.bank.v1beta1.TxProto.MsgSend.newBuilder();
            builder.setFromAddress(WalletDaoUtils.getCurrent().getAddress());
            CoinProto.Coin coin = CoinProto.Coin.newBuilder().setAmount("*************").setDenom(BASE_DENOM).build();
            builder.addAmount(coin);
            builder.setToAddress(walletList.get(i).getAddress());
            anyList.add(Any.newBuilder().setTypeUrl("/cosmos.bank.v1beta1.MsgSend")
                    .setValue(builder.build().toByteString()).build());
        }
        CosmosUtils.sendTX(anyList, false);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {

        }

    }

    private void getUserInfoForFile() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                String string = readRawFile(getActivity(), R.raw.user_info);
                Type type = new TypeToken<List<JsopLvDong>>() {
                }.getType();
                nameList = new Gson().fromJson(string, type);
                Log.e(TAG, "run: 读取结束" + nameList.size());
            }
        });
    }


    private void changeName(ArrayList<ETHWallet> walletList, int i) {
        if (i == walletList.size()) {
            return;
        }
        Log.e(TAG, "getNameHeard: 当前执行下标-----------------------" + i);
        List<Any> anyList = new ArrayList<>();
        ProfileTXProto.ProfileOptions.Builder pro = ProfileTXProto.ProfileOptions.newBuilder();
        pro.setAvatar(nameList.get(i).getAvatar());
        pro.setNickname(nameList.get(i).getName());
        String address = walletList.get(i).getAddress();
        pro.setUserHandle(address.substring(address.length() - 10));

        ProfileTXProto.MsgAddProfileRequest build1 = ProfileTXProto.MsgAddProfileRequest.newBuilder()
                .setCreator(walletList.get(i).getAddress())
                .setProfileJson(pro.build())
                .build();
        Any build = Any.newBuilder().setTypeUrl("/profile.v1.MsgAddProfileRequest")
                .setValue(build1.toByteString()).build();
        anyList.add(build);
        CosmosUtils.sendTX(anyList, walletList.get(i).getAddress(), walletList.get(i).getPrivateKey());
        if (i < walletList.size()) {
            changeName(walletList, i + 1);
        }

    }

    public void writeToInternalStorage(Context context, String fileName, String content) {
        try (FileOutputStream fos = context.openFileOutput(fileName, Context.MODE_PRIVATE)) {
            fos.write(content.getBytes());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


//         case 3:
//                                                PopAddManger popAddManger = new PopAddManger(getActivity());
//                                                popAddManger.setCallBack(new PopAddManger.CallBack() {
//                                                    @Override
//                                                    public void back(ProfileTXProto.MsgManageAdminRequest build) {
//                                                        addManger(build);
//                                                    }
//                                                });
//
//                                                new XPopup.Builder(getActivity())
//                                                        .hasShadowBg(true)
//                                                        .isDestroyOnDismiss(true)
//                                                        .asCustom(popAddManger).show();
//                                                break;
//                                            case 4:
//                                                PopEdit pop = new PopEdit(getActivity(), "", "删除管理员", false);
//                                                pop.setCallback(new PopEdit.Callback() {
//                                                    @Override
//                                                    public void notShow() {
//
//                                                    }
//
//                                                    @Override
//                                                    public void confirm(String title) {
//                                                        removeManger(title);
//                                                    }
//
//                                                    @Override
//                                                    public void finger() {
//
//                                                    }
//                                                });
//                                                new XPopup.Builder(getActivity())
//                                                        .hasShadowBg(true)
//                                                        .isDestroyOnDismiss(true)
//                                                        .asCustom(pop).show();
//                                                break;
//                                            case 5:
////                                                getPYdata100();
//                                                break;
//                                            case 6:
//
//                                                break;
//
//
//                                            case 21:
//                                                AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        PostProto.Poll vote = PostProto.Poll.newBuilder()
//                                                                .addVote(PostProto.Vote.newBuilder()
//                                                                        .setId(0)
//                                                                        .setOption("春天")
//                                                                        .setCount(0)
//                                                                        .build()).setVotingStart(0)
//                                                                .addVote(PostProto.Vote.newBuilder()
//                                                                        .setId(1)
//                                                                        .setOption("夏天")
//                                                                        .setCount(0)
//                                                                        .build())
//                                                                .addVote(PostProto.Vote.newBuilder()
//                                                                        .setId(2)
//                                                                        .setOption("秋天")
//                                                                        .setCount(0)
//                                                                        .build())
//                                                                .addVote(PostProto.Vote.newBuilder()
//                                                                        .setId(3)
//                                                                        .setOption("冬天")
//                                                                        .setCount(0)
//                                                                        .build())
//                                                                .setTotalVotes(0)
//                                                                .setVotingEnd(9999999990000L).build();
//                                                        PostTXProto.MsgCreateFreePost build = PostTXProto.MsgCreateFreePost.newBuilder()
//                                                                .setContent("这是投票内容说明333")
//                                                                .setCreator(WalletDaoUtils.getCurrent().getAddress())
//                                                                .setPoll(vote)
//                                                                .build();
//                                                        CosmosUtils.sendPostBody(build);
//                                                    }
//                                                });
//                                                break;
}