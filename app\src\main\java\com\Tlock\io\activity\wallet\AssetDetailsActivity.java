package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;

public class AssetDetailsActivity extends BaseActivity {
    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String data) {
        Intent intent = new Intent(context, AssetDetailsActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_asset_details;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {

    }

    @Override
    protected void loadData() {

    }
}