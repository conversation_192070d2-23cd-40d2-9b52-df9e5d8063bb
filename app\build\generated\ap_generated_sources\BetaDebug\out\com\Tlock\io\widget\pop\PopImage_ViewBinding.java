// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.lxj.xpopup.widget.PhotoViewContainer;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopImage_ViewBinding implements Unbinder {
  private PopImage target;

  @UiThread
  public PopImage_ViewBinding(PopImage target) {
    this(target, target);
  }

  @UiThread
  public PopImage_ViewBinding(PopImage target, View source) {
    this.target = target;

    target.mImage = Utils.findRequiredViewAsType(source, R.id.image, "field 'mImage'", ImageView.class);
    target.mTvSave = Utils.findRequiredViewAsType(source, R.id.tv_save, "field 'mTvSave'", TextView.class);
    target.mPhotoViewContainer = Utils.findRequiredViewAsType(source, R.id.photoViewContainer, "field 'mPhotoViewContainer'", PhotoViewContainer.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopImage target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mImage = null;
    target.mTvSave = null;
    target.mPhotoViewContainer = null;
  }
}
