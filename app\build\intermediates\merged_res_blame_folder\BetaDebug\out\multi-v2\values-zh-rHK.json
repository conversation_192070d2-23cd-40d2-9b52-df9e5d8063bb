{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,440,540,622,719,827,904,979,1071,1165,1256,1352,1447,1541,1637,1729,1821,1913,1991,2087,2182,2277,2374,2470,2568,2719,7244", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "342,435,535,617,714,822,899,974,1066,1160,1251,1347,1442,1536,1632,1724,1816,1908,1986,2082,2177,2272,2369,2465,2563,2714,2808,7318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,359,466,539,601,679,738,796,874,931,987,1046,1104,1158,1244,1300,1358,1412,1477,1570,1644,1722,1812,1875,1938,2015,2082,2148,2212,2281,2356,2417,2488,2555,2615,2695,2758,2841,2926,3000,3065,3141,3189,3253,3329,3407,3469,3533,3596,3676,3751,3827,3903", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "197,264,354,461,534,596,674,733,791,869,926,982,1041,1099,1153,1239,1295,1353,1407,1472,1565,1639,1717,1807,1870,1933,2010,2077,2143,2207,2276,2351,2412,2483,2550,2610,2690,2753,2836,2921,2995,3060,3136,3184,3248,3324,3402,3464,3528,3591,3671,3746,3822,3898,3967"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2813,3541,3631,3738,3811,3873,3951,4010,4068,4146,4203,4259,4318,4376,4430,4516,4572,4630,4684,4749,4842,4916,4994,5084,5147,5210,5287,5354,5420,5484,5553,5628,5689,5760,5827,5887,5967,6030,6113,6198,6272,6337,6413,6461,6525,6601,6679,6741,6805,6868,6948,7023,7099,7175", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "247,2875,3626,3733,3806,3868,3946,4005,4063,4141,4198,4254,4313,4371,4425,4511,4567,4625,4679,4744,4837,4911,4989,5079,5142,5205,5282,5349,5415,5479,5548,5623,5684,5755,5822,5882,5962,6025,6108,6193,6267,6332,6408,6456,6520,6596,6674,6736,6800,6863,6943,7018,7094,7170,7239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2880,2972,3071,3165,3259,3352,3445,7323", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2967,3066,3160,3254,3347,3440,3536,7419"}}]}]}