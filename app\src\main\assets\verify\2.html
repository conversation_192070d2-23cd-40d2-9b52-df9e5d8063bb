<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=0">
    <!-- 国内使用 -->
    <!-- 原版自动生成的代码里对js的引用地址开头是//,我觉得这有点怪，就尝试在本地打开，果然不行，加了https:就可以了，不知道它为啥生成的地址是这样，难道说当被放入项目中之后就能恢复正常加载么？ -->
    <script type="text/javascript" charset="utf-8" src="//g.alicdn.com/sd/nch5/index.js?t=2015052012"></script>
    <!-- 若您的主要用户来源于海外，请替换使用下面的js资源 -->
    <!-- <script type="text/javascript" charset="utf-8" src="https://aeis.alicdn.com/sd/nch5/index.js?t=2015052012"></script> -->
    <style type="text/css">
      ._nc .stage1 .slider {
        height: 52px !important;
        border-radius: 6px !important;
        box-shadow: 0 0 1px #1F2630 !important;
        background-color: #1F2630 !important;
      }
      ._nc .stage1 .track div {
        border-radius: 6px !important;
        color: #000 !important;
      }
      ._nc .stage1 .bg-green {
        background-color: #FFC510 !important;
      }
      ._nc .stage1 .bg-red {
        background-color: #78c430 !important;
      }
    </style>
</head>
<body>
<div id="__nc" style="margin-left:auto;margin-right:auto;width:80%;height:100px;padding-top:100px;">
    <div id="nc"></div>
</div>

<script>
    var nc_token = ["FFFF0N0000000000B38C", (new Date()).getTime(), Math.random()].join(':');
    var nc=NoCaptcha.init({
        renderTo: '#nc',
        appkey: "FFFF0N0000000000B38C",
        scene: "nc_register_h5",
        token: nc_token,
        trans: {"key1": "code200"},
        elementID: ["usernameID"],
        is_Opt: 0,
        language: "cn",
        timeout: 10000,
        retryTimes: 5,
        errorTimes: 5,
        inline:false,
        apimap: {
            // 'analyze': '//a.com/nocaptcha/analyze.jsonp',
            // 'uab_Url': '//aeu.alicdn.com/js/uac/909.js',
        },
        bannerHidden:false,
        initHidden:false,
        callback: function (data) {
            window.console && console.log(nc_token)
            window.console && console.log(data.csessionid)
            window.console && console.log(data.sig)
        },
        error: function (s) {
        }
    });
    NoCaptcha.setEnabled(true);
    nc.reset();//请务必确保这里调用一次reset()方法

    NoCaptcha.upLang('cn', {
        'LOADING':"加载中...",//加载
        'SLIDER_LABEL': "请向右滑动验证",//等待滑动
        'CHECK_Y':"验证通过",//通过
        'ERROR_TITLE':"非常抱歉，这出错了...",//拦截
        'CHECK_N':"验证未通过", //准备唤醒二次验证
        'OVERLAY_INFORM':"经检测你当前操作环境存在风险，请输入验证码",//二次验证
        'TIPS_TITLE':"验证码错误，请重新输入"//验证码输错时的提示
    });
    </script>
</body>
</html>