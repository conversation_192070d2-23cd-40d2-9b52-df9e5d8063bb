package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.Transfer;
import com.Tlock.io.itemBean.cosmos.TransferItemView;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;

import java.util.ArrayList;

import butterknife.BindView;

public class TransferHistoryActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.recyler_view)
    RecyclerView mRecyclerView;
    private ArrayList<Transfer> dappBeans = new ArrayList<>();
    private BaseRecyclerViewAdapter<Transfer> adapter;
    private ETHWallet current;

    /**
     * @param context
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, TransferHistoryActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_transfer_history_list;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        current = WalletDaoUtils.getCurrent();
        initRecycleView();
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<Transfer>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                TransferItemView itemView = new TransferItemView(getActivity());
                return itemView;
            }

            @Override
            public void bindViewData(int position, Transfer data, View view) {
                ((TransferItemView) view).setData(data);
            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<Transfer>() {
            @Override
            public void onItemClick(int position, Transfer data, View view) {
                TransferInfoActivity.start(getActivity(), 2, JsonUtils.objectToJson(data));
            }
        });
    }

    @Override
    protected void loadData() {
        ArrayList<Transfer> currentTransferList = WalletDaoUtils.getCurrentTransferList();
        adapter.setList(currentTransferList);
    }
}