package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.widget.FontTextView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TaskReportItemView extends BaseView {


    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_danger)
    TextView mTvDanger;
    @BindView(R.id.tv_count)
    TextView mTvCount;
    @BindView(R.id.tv_account)
    TextView mTvAccount;
    @BindView(R.id.rl_name)
    RelativeLayout mRlName;
    @BindView(R.id.tv_content)
    FontTextView mTvContent;
    @BindView(R.id.iv1)
    ImageView mIv1;

    public TaskReportItemView(Context context) {
        super(context);
    }

    public TaskReportItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TaskReportItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.item_task_roport;
    }

    public void setData(ProfileProto.Profile data) {
        mTvAccountName.setText(data.getNickname());
        mTvAccount.setText(data.getUserHandle());
        if (TextUtils.isEmpty(data.getAvatar())) {
            mIvHeard.setImageDrawable(getResources().getDrawable(R.mipmap.tlk_round));
        } else {
            Glide.with(getContext()).load(data.getAvatar())
                    .apply(new RequestOptions()
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .centerCrop()
                            .signature(new ObjectKey(data.getAvatar()))
                            .dontTransform())
                    .apply(RequestOptions.circleCropTransform().circleCrop())
                    .placeholder(getResources().getDrawable(R.mipmap.tlk_round)).into(mIvHeard);
        }
    }

    public void setData(PostQueryProto.TopicResponse data) {

    }

}
