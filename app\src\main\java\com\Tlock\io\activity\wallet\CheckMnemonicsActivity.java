package com.Tlock.io.activity.wallet;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.MainActivity;
import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.config.EventConstant;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.wallet.MnemonicViewBean;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.pop.PopCheckMnemonics;
import com.lxj.xpopup.XPopup;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;

import butterknife.BindView;
import butterknife.OnClick;

public class CheckMnemonicsActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.rl_toolbar)
    RelativeLayout mRlToolbar;

    @BindView(R.id.tv_next)
    TextView mTvNext;
    @BindView(R.id.tv1_title)
    TextView mTv1Title;
    @BindView(R.id.tv1)
    TextView mTv1;
    @BindView(R.id.tv2_title)
    TextView mTv2Title;
    @BindView(R.id.tv2)
    TextView mTv2;
    @BindView(R.id.tv3_title)
    TextView mTv3Title;
    @BindView(R.id.tv3)
    TextView mTv3;
    @BindView(R.id.tv4_title)
    TextView mTv4Title;
    @BindView(R.id.tv4)
    TextView mTv4;
    @BindView(R.id.ll_check)
    LinearLayout mLlCheck;

    @BindView(R.id.recyler_view)
    RecyclerView mRecylerView;
    @BindView(R.id.tv_error)
    TextView mTvError;
    private ArrayList<String> valueBeans = new ArrayList<>();
    private ArrayList<Integer> words = new ArrayList<>();
    private ArrayList<String> correctWords = new ArrayList<>();
    private ArrayList<String> selectWords = new ArrayList<>();
    private ETHWallet ethHDWallet;
    private TagAdapter<String> tagAdapter;
    private BaseRecyclerViewAdapter<String> wordAdapter;
    private String data;
    private int type;
    private BaseRecyclerViewAdapter<String> adapter;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String data) {
        Intent intent = new Intent(context, CheckMnemonicsActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_check_mnemonics;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        String data = getIntent().getStringExtra("data");
        ethHDWallet = JsonUtils.jsonToObject(data, ETHWallet.class);
        String[] s = ethHDWallet.getMnemonic().split(" ");
        List<String> mnemonic = Arrays.asList(s);
        new Random().ints(1, 12)
                .distinct()
                .limit(4)
                .forEach(i -> {
                    words.add(i - 1);
                });
        setWordTitle();


        correctWords.addAll(mnemonic);
        valueBeans.addAll(mnemonic);
        Collections.shuffle(valueBeans);
        initRecyclerView();
//        initMnemonicAdapter();
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @SuppressLint("SetTextI18n")
    private void setWordTitle() {
        mTv1Title.setText("#" + (words.get(0) + 1));
        mTv2Title.setText("#" + (words.get(1) + 1));
        mTv3Title.setText("#" + (words.get(2) + 1));
        mTv4Title.setText("#" + (words.get(3) + 1));
    }

    private void setWord(int position) {
        switch (position) {
            case 0:
                mTv1.setText(selectWords.get(position));
                break;
            case 1:
                mTv2.setText(selectWords.get(position));
                break;
            case 2:
                mTv3.setText(selectWords.get(position));
                break;
            case 3:
                mTv4.setText(selectWords.get(position));
                break;
        }
    }

    @Override
    protected void loadData() {

    }

    /**
     * 初始化Mnemonic
     */
    private void initMnemonicAdapter() {

        tagAdapter = new TagAdapter<String>((List<String>) valueBeans) {
            @Override
            public View getView(FlowLayout parent, int position, String s) {
                MnemonicViewBean mnemonicViewBean = new MnemonicViewBean(getActivity());
                ViewGroup.LayoutParams layoutParams = mnemonicViewBean.getLayoutParams();
                layoutParams.width = LinearLayout.LayoutParams.WRAP_CONTENT;
                mnemonicViewBean.setLayoutParams(layoutParams);
                mnemonicViewBean.setData(valueBeans.get(position), position + 1, 0);
                return mnemonicViewBean;
            }

            @Override
            public void onSelected(int position, View view) {
                super.onSelected(position, view);
                int i = emptyIndex();
                if (i == -1) {
                    selectWords.add(valueBeans.get(position));
                    setWord(selectWords.size() - 1);

                } else {
                    selectWords.set(i, valueBeans.get(position));
                    setWord(i);

                }
                ((MnemonicViewBean) view).setSelected(true);
            }

            @Override
            public void unSelected(int position, View view) {
                super.unSelected(position, view);
                int i = selectWords.indexOf(valueBeans.get(position));
                selectWords.set(i, "");
                setWord(i);
                ((MnemonicViewBean) view).setSelected(false);

            }
        };

    }

    private void initRecyclerView() {
        mRecylerView.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), valueBeans, new BaseRecyclerViewAdapter.Delegate<String>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new MnemonicViewBean(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, String data, View view) {
                MnemonicViewBean viewBean = (MnemonicViewBean) view;
                viewBean.setData(data, position + 1, 0);
            }
        });
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<String>() {
            @Override
            public void onItemClick(int position, String data, View view) {
                MnemonicViewBean viewBean = (MnemonicViewBean) view;
                boolean select = viewBean.getSelect();
                if (!select && (!checkWord() || getTrueCount() == 4)) {
                    // 没有选中
                    return;
                }
                boolean click = viewBean.click();
                if (click) {
                    int i = emptyIndex();
                    if (i == -1) {
                        selectWords.add(valueBeans.get(position));
                        setWord(selectWords.size() - 1);

                    } else {
                        selectWords.set(i, valueBeans.get(position));
                        setWord(i);
                    }
                } else {
                    int i = selectWords.indexOf(valueBeans.get(position));
                    selectWords.set(i, "");
                    setWord(i);
                }
            }
        });
        mRecylerView.setAdapter(adapter);
    }

    private int getTrueCount() {
        int trueCount = 0;
        for (String selectWord : selectWords) {
            if (!selectWord.isEmpty()) {
                trueCount++;
            }
        }
        return trueCount;
    }

    @SuppressLint("NewApi")
    @OnClick({R.id.tv_next})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_next:
                // 检验正确性
                if (selectWords.size() != 4) {
                    showToast("Please verify all words");
                    return;
                }
                if (checkWord()) {
                    ETHWallet ethWallet = new ETHWallet();
                    ethWallet.setType(2);
                    ethWallet.setAddress(ethHDWallet.getAddress());
                    ethWallet.setMnemonic(ethHDWallet.getMnemonic());
                    ethWallet.setPassword(ethHDWallet.getPassword());
                    ethWallet.setPrivateKey(ethHDWallet.getPrivateKey());
                    ethWallet.setName(ethHDWallet.getName());
                    ethWallet.setChainId("10889");
                    WalletDaoUtils.insertNewWallet(ethWallet);
                    showToast(getResources().getString(R.string.check_success));
                    PopCheckMnemonics popCheckMnemonics = new PopCheckMnemonics(getActivity());
                    popCheckMnemonics.setCallback(new PopCheckMnemonics.Callback() {
                        @Override
                        public void confirm() {
                            EventBus.getDefault().postSticky(new Event(EventConstant.WALLET));
                            MainActivity.start(getActivity());
                            showToast(getResources().getString(R.string.Successful_operation));
                        }
                    });
                    new XPopup.Builder(getActivity())
                            .isDestroyOnDismiss(true)
                            .borderRadius(100)
                            .dismissOnTouchOutside(false)
                            .asCustom(popCheckMnemonics)
                            .show();

                } else {
                    showToast(getResources().getString(R.string.world_must_list));
                }
                break;

        }
    }

    private boolean checkWord() {
        for (int i = 0; i < selectWords.size(); i++) {
            if (selectWords.get(i).isEmpty()) continue;
            if (!correctWords.get(words.get(i)).equalsIgnoreCase(selectWords.get(i))) {
                mTvError.setVisibility(View.VISIBLE);
                showToast("World error");
                return false;
            }
        }
        mTvError.setVisibility(View.GONE);
        return true;

    }

    private int emptyIndex() {
        if (selectWords.size() == 0) {
            return -1;
        }
        for (int i = 0; i < selectWords.size(); i++) {
            if (selectWords.get(i).isEmpty()) {
                return i;
            }
        }
        return -1;
    }

}