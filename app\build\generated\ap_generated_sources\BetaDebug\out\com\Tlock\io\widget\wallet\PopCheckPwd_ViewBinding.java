// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopCheckPwd_ViewBinding implements Unbinder {
  private PopCheckPwd target;

  private View view7f09014b;

  private View view7f090140;

  private View view7f090312;

  @UiThread
  public PopCheckPwd_ViewBinding(PopCheckPwd target) {
    this(target, target);
  }

  @UiThread
  public PopCheckPwd_ViewBinding(final PopCheckPwd target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_fingerprint, "field 'mIvFingerprint' and method 'onViewClicked'");
    target.mIvFingerprint = Utils.castView(view, R.id.iv_fingerprint, "field 'mIvFingerprint'", ImageView.class);
    view7f09014b = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_close, "field 'mIvClose' and method 'onViewClicked'");
    target.mIvClose = Utils.castView(view, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    view7f090140 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mEdPwd = Utils.findRequiredViewAsType(source, R.id.ed_pwd, "field 'mEdPwd'", EditText.class);
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onViewClicked'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f090312 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopCheckPwd target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvFingerprint = null;
    target.mIvClose = null;
    target.mEdPwd = null;
    target.mTvConfirm = null;

    view7f09014b.setOnClickListener(null);
    view7f09014b = null;
    view7f090140.setOnClickListener(null);
    view7f090140 = null;
    view7f090312.setOnClickListener(null);
    view7f090312 = null;
  }
}
