package com.Tlock.io.custom;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.network.NetWorkConfig;

import butterknife.BindView;


/**
 * 本类的主要功能是 :  网络请求数据后的异常情况展示
 *
 * <AUTHOR>  2020-04-05 22:48
 */
public class LoadErrorView extends BaseView {


    @BindView(R.id.iv_error_img)
    ImageView mIvErrorImg;
    @BindView(R.id.tv_error_msg)
    TextView mErrorMsg;

    public LoadErrorView(Context context) {
        this(context, null);
    }

    public LoadErrorView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LoadErrorView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }

    @Override
    protected int getLayoutId() {
        return R.layout.load_error_page;
    }

    /**
     * 加载异常信息
     *
     * @param message {@link NetWorkConfig#}
     */
    public void setErrorMessage(String message) {
        if (TextUtils.isEmpty(message)) {
            message = NetWorkConfig.HTTP_NO_DATA;
        }
        mErrorMsg.setText(message);
        //错误信息提示
//        switch (message) {
//            case NetWorkConfig.HTTP_NO_DATA:
//                mIvErrorImg.setImageResource(R.mipmap.icon_network);
//                mErrorMsg.setText(getResources().getString(R.string.no_data));
//                break;
//            case NetWorkConfig.HTTP_SERVER_EXCEPTION:
//                mIvErrorImg.setImageResource(R.mipmap.icon_network);
//                mErrorMsg.setText(getResources().getString(R.string.Network_Unavailable));
//
//                break;
//            case NetWorkConfig.COMMING:
//                mIvErrorImg.setImageResource(R.mipmap.icon_network);
//                mErrorMsg.setText(getResources().getString(R.string.coming_soon));
//                break;
//        }
    }
}
