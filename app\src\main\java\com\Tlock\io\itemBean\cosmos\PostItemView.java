package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.DateUtil.formatDate;
import static com.Tlock.io.utils.DateUtil.friendly_time_3;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.vectordrawable.graphics.drawable.Animatable2Compat;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.ContentInfoActivity;
import com.Tlock.io.activity.cosmos.UserInfoActivity;
import com.Tlock.io.adapter.SpacesItemDecoration1;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.utils.Base64BitmapUtils;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.cosmos.ProposalOptionItemView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.ImageViewerPopupView;
import com.lxj.xpopup.interfaces.OnSrcViewUpdateListener;
import com.lxj.xpopup.util.SmartGlideImageLoader;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class PostItemView extends BaseView {


    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.iv_more)
    ImageView mIvMore;
    @BindView(R.id.tv_title)
    FontTextView mTvTitle;
    @BindView(R.id.tv_content)
    FontTextView mTvContent;
    @BindView(R.id.rv_image)
    RecyclerView mRvImages;
    @BindView(R.id.tv_quote_title)
    TextView mTvQuoteTitle;
    @BindView(R.id.tv_quote_content)
    TextView mTvQuoteContent;
    @BindView(R.id.ll_content_quote)
    LinearLayout mLlContentQuote;
    @BindView(R.id.ll_vote)
    LinearLayout mLlVote;
    @BindView(R.id.tv_voteTime)
    TextView mTvVoteTime;
    @BindView(R.id.tv_time1)
    TextView mTvTime1;
    @BindView(R.id.iv_praise)
    ImageView mIvPraise;
    @BindView(R.id.tv_praise)
    TextView mTvPraise;
    @BindView(R.id.ll_praise)
    View mLlPraise;
    @BindView(R.id.iv_quote)
    ImageView mIvQuote;
    @BindView(R.id.tv_quote)
    TextView mTvQuote;
    @BindView(R.id.ll_quote)
    View mLlQuote;
    @BindView(R.id.iv_review)
    ImageView mIvReview;
    @BindView(R.id.tv_review)
    TextView mTvReview;
    @BindView(R.id.ll_review)
    View mLlReview;
    @BindView(R.id.iv_Collect)
    ImageView mIvCollect;
    @BindView(R.id.tv_Collect)
    TextView mTvCollect;
    @BindView(R.id.ll_Collect)
    View mLlCollect;
    @BindView(R.id.iv_share)
    ImageView mIvShare;
    @BindView(R.id.tv_share)
    TextView mTvShare;
    @BindView(R.id.ll_share)
    View mLlShare;
    @BindView(R.id.constraint)
    LinearLayout mConstraint;
    @BindView(R.id.line1)
    View mLine1;

    @BindView(R.id.root)
    ConstraintLayout mRoot;
    private BaseRecyclerViewAdapter<String> imageAdapter;
    private GifDrawable gifDrawable;
    private String id = "";

    public PostItemView(Context context) {
        super(context);
    }

    public PostItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public PostItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public PostProto.Post post;
    public ProfileProto.Profile profile;
    public PostQueryProto.PostResponse data;
    public PostProto.Post data1;

    @Override
    protected int getLayoutId() {
        return R.layout.item_post1;
    }

    public void setData(PostQueryProto.PostResponse data) {
        this.data = data;
        this.post = data.getPost();

        mTvAccountName.setText(data.getProfile().getNickname().isEmpty() ? data.getProfile().getUserHandle() : data.getProfile().getNickname());

        mRvImages.setVisibility(GONE);

        if (post.getImageIdsCount() != 0) {
            ArrayList<String> files1 = new ArrayList<>();
            String imageIds = post.getImageIds(0);
            mRvImages.setVisibility(VISIBLE);
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {

                    String postImages = CosmosUtils.getPostImages(imageIds);

                    if (!postImages.isEmpty()) {
                        files1.add(postImages);
                    }

                    mRvImages.post(new Runnable() {
                        @Override
                        public void run() {
                            if (post.getImageIdsCount() == 0 || !post.getImageIds(0).equalsIgnoreCase(imageIds)) {
                                Log.e("TAG", "地址不同-----------: ");
                                return;
                            }
                            Log.e("TAG", "地址相同: " + profile.getNickname());
                            if (imageAdapter == null) {
                                int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
                                mRvImages.addItemDecoration(new SpacesItemDecoration1(spacingInPixels, 3));
                            }
                            initAdapter(files1);
                        }
                    });
                }
            });
        }
        if (post.getImagesUrlCount() != 0) {
            mRvImages.setVisibility(VISIBLE);
            ArrayList<String> files1 = new ArrayList<>();
            if (post.getImagesUrlList() != null && post.getImagesUrlList().size() > 0) {
                files1.addAll(post.getImagesUrlList());
            }
            if (imageAdapter == null) {
                int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
                mRvImages.addItemDecoration(new SpacesItemDecoration1(spacingInPixels, 3));
            }
            initAdapter(files1);
        }


        PostProto.Poll poll = data.getPost().getPoll();
        if (TextUtils.isEmpty(post.getTitle())) {
            mTvTitle.setVisibility(GONE);
        } else {
            mTvTitle.setVisibility(VISIBLE);
            mTvTitle.setText(post.getTitle());
        }

        if (!TextUtils.isEmpty(post.getQuote())) {
            mLlContentQuote.setVisibility(VISIBLE);
            mTvQuoteContent.setText(data.getQuotePost().getContent());
            if (!TextUtils.isEmpty(data.getQuotePost().getTitle())) {
                mTvQuoteTitle.setVisibility(VISIBLE);
            } else {
                mTvQuoteTitle.setVisibility(GONE);
            }
            mTvQuoteTitle.setText(data.getQuotePost().getTitle());
        } else {
            mLlContentQuote.setVisibility(GONE);
        }

        if (post.getPostType() == PostProto.PostType.POLL) {
            getOption();
            mTvVoteTime.setVisibility(VISIBLE);
            mLlVote.setVisibility(VISIBLE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mTvVoteTime.setText(poll.getTotalVotes() + " votes · " + formatDate(poll.getVotingEnd(), "yyyy/MM/dd HH:mm:ss"));
            }
            long totalVotes = poll.getTotalVotes();
            mLlVote.removeAllViews();
            for (int i = 0; i < poll.getVoteList().size(); i++) {
                PostProto.Vote vote = poll.getVoteList().get(i);
                ProposalOptionItemView proposalOptionItemView = new ProposalOptionItemView(mLlVote.getContext(), vote.getOption(), BigDecimalUtils.multipliy(BigDecimalUtils.division(vote.getCount() + "", totalVotes + ""), "100"));
                int finalI = i;
                proposalOptionItemView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        for (int i1 = 0; i1 < mLlVote.getChildCount(); i1++) {
                            ProposalOptionItemView proposalOptionItemView1 = (ProposalOptionItemView) mLlVote.getChildAt(i1);
                            if (proposalOptionItemView1.isSelected()) {
                                return;
                            }
                            proposalOptionItemView1.setSelected(false);
                        }

                        proposalOptionItemView.setSelected(true);
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                PostTXProto.CastVoteOnPollRequest build = PostTXProto.CastVoteOnPollRequest.newBuilder().setCreator(WalletDaoUtils.getCurrent().getAddress()).setId(data.getPost().getId()).setOptionId(finalI).build();
                                CosmosUtils.sendPoll(build);
                            }
                        });

                    }
                });
                mLlVote.addView(proposalOptionItemView);
            }
        }

        mTvContent.setMaxLines(4);
        mTvContent.setEllipsize(TextUtils.TruncateAt.END);
        mTvContent.setText(post.getContent());

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_3(post.getTimestamp() + "000"));
        }
        mTvReview.setText(post.getCommentCount() != 0 ? post.getCommentCount() + "" : "");
        mTvPraise.setText(post.getLikeCount() != 0 ? post.getLikeCount() + "" : "");
        mTvCollect.setText(post.getSaveCount() != 0 ? post.getSaveCount() + "" : "");
        setImage();
    }

    private void setImage() {
        ProfileProto.Profile profile1 = data.getProfile();
        if (profile1.getAvatar().isEmpty()) {
            this.profile = data.getProfile();
            Glide.with(getContext()).load(getResources().getDrawable(R.drawable.shape_light_gray_60)).into(mIvHeard);
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    ProfileProto.Profile authInfo = CosmosUtils.getAuthInfo(post.getCreator());
                    if (id.equalsIgnoreCase(data.getProfile().getWalletAddress()) && !id.isEmpty()) {
                        return;
                    }
                    mTvTime.post(new Runnable() {
                        @Override
                        public void run() {
                            PostItemView.this.profile = authInfo;
                            setProfile();
                        }
                    });
                }
            });
        } else {
            this.profile = data.getProfile();
            setProfile();
        }
    }

    /**
     * @param type //1喜欢,2收藏,3喜欢收藏
     */
    public void setDefaultImg(int type) {
        switch (type) {
            case 1:
                Glide.with(mIvPraise).load(R.mipmap.icon_post_like_select).into(mIvPraise);
                mTvPraise.setTextColor(getResources().getColor(R.color.red_text));
                break;
            case 2:
                Glide.with(mIvCollect).load(R.mipmap.icon_post_save_select).diskCacheStrategy(DiskCacheStrategy.ALL).into(mIvCollect);
                mTvCollect.setTextColor(getResources().getColor(R.color.text_collect_blue));
                break;
            case 3:
                Glide.with(mIvPraise).load(R.mipmap.icon_post_like_select).into(mIvPraise);
                mTvPraise.setTextColor(getResources().getColor(R.color.red_text));
                Glide.with(mIvCollect).load(R.mipmap.icon_post_save_select).diskCacheStrategy(DiskCacheStrategy.ALL).into(mIvCollect);
                mTvCollect.setTextColor(getResources().getColor(R.color.text_collect_blue));
                break;
            default:
                Glide.with(mIvPraise).load(R.mipmap.icon_post_like).into(mIvPraise);
                mTvPraise.setTextColor(getResources().getColor(R.color.cosmos_black));
                Glide.with(mIvCollect).load(R.mipmap.icon_post_save).diskCacheStrategy(DiskCacheStrategy.ALL).into(mIvCollect);
                mTvCollect.setTextColor(getResources().getColor(R.color.cosmos_black));
                break;
        }
    }

    private void getOption() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                if (post.getPoll().getTotalVotes() != 0) {
                    String string = CosmosUtils.queryVoteOption(post.getId());
                    int i = Integer.parseInt(string);
                    mTvTime.post(() -> {
                        if (i != -1) {
                            for (int i1 = 0; i1 < mLlVote.getChildCount(); i1++) {
                                ProposalOptionItemView proposalOptionItemView1 = (ProposalOptionItemView) mLlVote.getChildAt(i1);
                                proposalOptionItemView1.setSelected(false);
                            }
                            ProposalOptionItemView proposalOptionItemView1 = (ProposalOptionItemView) mLlVote.getChildAt(i);
                            proposalOptionItemView1.setSelected(true);
                        }
                    });
                }
            }
        });
    }

    private void setProfile() {
        if (TextUtils.isEmpty(profile.getAvatar())) {
            TextAvatarDrawable a = new TextAvatarDrawable(data.getProfile().getUserHandle().substring(0, 1));
            // 应用到 ImageView
            mIvHeard.setImageDrawable(a);
        } else {
            mIvHeard.setTag(post.getCreator());
            if (profile.getAvatar().startsWith("http")) {
                id = post.getId();
                Glide.with(getContext()).load(profile.getAvatar()).apply(new RequestOptions().placeholder(getResources().getDrawable(R.drawable.shape_light_gray_60)).diskCacheStrategy(DiskCacheStrategy.ALL).signature(new ObjectKey(profile.getAvatar())).centerCrop().format(DecodeFormat.PREFER_RGB_565).dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop().skipMemoryCache(true) // 跳过内存缓存
                        .diskCacheStrategy(DiskCacheStrategy.NONE)).into(mIvHeard);
            } else {
                Base64BitmapUtils.base64ToBitmap(profile.getAvatar(), new Base64BitmapUtils.Base64ToBitmapCallback() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        // 这里已经在主线程中，可以直接操作UI
                        Glide.with(getContext()).load(bitmap).apply(new RequestOptions().format(DecodeFormat.PREFER_RGB_565)
                                .placeholder(getResources().getDrawable(R.drawable.shape_light_gray_60)).diskCacheStrategy(DiskCacheStrategy.ALL).centerCrop().dontTransform()).transition(DrawableTransitionOptions.withCrossFade(500)).circleCrop().diskCacheStrategy(DiskCacheStrategy.NONE).into(mIvHeard);
                    }

                    @Override
                    public void onError(String error) {
                        // 这里已经在主线程中，可以直接操作UI
                        mIvHeard.setImageResource(android.R.drawable.ic_menu_gallery);
                    }
                });


                id = post.getId();

            }

        }
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @OnClick({R.id.ll_share, R.id.ll_review, R.id.ll_praise, R.id.ll_Collect, R.id.tv_content, R.id.iv_more, R.id.ll_quote, R.id.iv_heard, R.id.ll_content_quote})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.ll_share:
                if (callback != null) callback.share();
                break;
            case R.id.ll_content_quote:
                ContentInfoActivity.start(getContext(), data.getQuotePost().getId(), true);
                break;
            case R.id.iv_heard:
                UserInfoActivity.start(getContext(), profile.getWalletAddress());
                break;
            case R.id.tv_content:
                if (callback != null) callback.info(data);
                break;
            case R.id.ll_review:
                if (callback != null) callback.review(post.getId(), profile.getUserHandle());
                break;
            case R.id.ll_praise:
                Glide.with(mIvPraise).clear(mIvPraise);

                Glide.with(getContext()).asGif().load(R.drawable.heart_1) // 替换为你的GIF资源ID
                        .listener(new RequestListener<GifDrawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
                                resource.setLoopCount(1);
                                // 加载静态图片
                                resource.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {
                                    @Override
                                    public void onAnimationEnd(Drawable drawable) {
                                        // 加载静态图片
                                        Glide.with(mIvPraise).load(R.mipmap.icon_post_like_select).into(mIvPraise);
                                    }
                                });
                                return false;
                            }
                        }).into(mIvPraise);
                String string = mTvPraise.getText().toString();
                if (!TextUtils.isEmpty(string) && TextUtils.isDigitsOnly(string)) {
                    int i = Integer.parseInt(string);
                    mTvPraise.setText((i + 1) + "");
                } else {
                    mTvPraise.setText("1");
                }
                mTvPraise.setTextColor(getResources().getColor(R.color.red_text));
                if (callback != null) callback.praise(post.getId());
                break;
            case R.id.ll_Collect:
                Glide.with(getContext()).asGif().load(R.drawable.gif_collect) // 替换为你的GIF资源ID
                        .listener(new RequestListener<GifDrawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
                                resource.setLoopCount(1);
                                resource.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {
                                    @Override
                                    public void onAnimationEnd(Drawable drawable) {
                                        // 加载静态图片
                                        Glide.with(mIvCollect).load(R.mipmap.icon_post_save_select).diskCacheStrategy(DiskCacheStrategy.ALL).into(mIvCollect);
                                    }
                                });
                                return false;
                            }
                        }).into(mIvCollect);
                String string1 = mTvCollect.getText().toString();
                if (!TextUtils.isEmpty(string1) && TextUtils.isDigitsOnly(string1)) {
                    int i = Integer.parseInt(string1);
                    mTvCollect.setText((i + 1) + "");
                } else {
                    mTvCollect.setText("1");
                }
                mTvCollect.setTextColor(getResources().getColor(R.color.text_collect_blue));
                if (callback != null) callback.collect(post.getId());
                break;
            case R.id.iv_more:
                if (callback != null)
                    callback.more(post.getId(), profile.getUserHandle(), profile.getWalletAddress());
                break;
            case R.id.ll_quote:
                if (callback != null) callback.quote();
                break;
        }
    }

    private void initAdapter(ArrayList<String> files1) {
        if (files1.size() == 0) {
            mRvImages.setAdapter(null); // 清空适配器
            return;
        }
        mRvImages.setLayoutManager(new GridLayoutManager(getContext(), files1.size()));
        imageAdapter = new BaseRecyclerViewAdapter<>(getContext(), files1, new BaseRecyclerViewAdapter.Delegate<String>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new ImageShowViewBean(getContext(), 0);
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, String url, View view) {
                ImageShowViewBean viewBean = (ImageShowViewBean) view;
                viewBean.setGlideTag(data.getPost().getId());
                mRvImages.setVisibility(VISIBLE);
                viewBean.setData(url, files1.size(), false, data.getPost().getId());
                viewBean.setCallback(new ImageShowViewBean.Callback() {
                    @Override
                    public void onDelete(File file) {

                    }

                    @Override
                    public void onAdd() {

                    }

                    @Override
                    public void onSelect(ImageView mIvImage) {
                        ArrayList<Object> objects = new ArrayList<>();
                        if (post.getImageIdsCount() != 0) {
                            File tempImage = createTempBitmapFile(getContext(), BitmapUtils.base64ToBitmap(url), "temp_image");
                            new XPopup.Builder(getContext()).asImageViewer(mIvImage, tempImage.getAbsolutePath(), new SmartGlideImageLoader()).show();

                        } else if (post.getImagesUrlCount() != 0) {
                            for (int i = 0; i < post.getImagesUrlList().size(); i++) {
                                objects.add(post.getImagesUrlList().get(i));
                            }
                            new XPopup.Builder(getContext()).asImageViewer(mIvImage, position, objects, new OnSrcViewUpdateListener() {
                                @Override
                                public void onSrcViewUpdate(final ImageViewerPopupView popupView, final int position) {
                                }
                            }, new SmartGlideImageLoader()).show();
                        }
                    }

                    @Override
                    public void outSide() {
                        ContentInfoActivity.start(getContext(), data.getPost().getId(), false);
                    }
                });

            }
        });

        mRvImages.setAdapter(imageAdapter);

    }

    public File createTempBitmapFile(Context context, Bitmap bitmap, String fileName) {
        // 创建缓存目录（若不存在）
        File cacheDir = new File(context.getCacheDir(), "temp_images");
        File tempFile = null;
        try {
            if (!cacheDir.exists()) cacheDir.mkdirs();

            // 创建临时文件（前缀+时间戳保证唯一性）
            tempFile = File.createTempFile("TEMP_", ".jpg", cacheDir);

            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos); // 压缩质量80%
                fos.flush();
            }

        } catch (Exception e) {

        }

        return tempFile; // 返回临时文件引用
    }


    public interface Callback {
        void quote();

        void share();

        void review(String id, String handle);

        void praise(String id);

        void collect(String id);

        void more(String data, String handle, String address);

        void info(PostQueryProto.PostResponse data);

        void resetProfile(PostQueryProto.PostResponse postResponse);

    }
}
