// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: entity/profile.proto
// Protobuf Java Version: 4.28.3

package com.Tlock.io.entity.profile;

public final class ProfileProto {
  private ProfileProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 28,
      /* patch= */ 3,
      /* suffix= */ "",
      ProfileProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code IdVerificationStatus}
   */
  public enum IdVerificationStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ID_VERIFICATION_UNSPECIFIED = 0;</code>
     */
    ID_VERIFICATION_UNSPECIFIED(0),
    /**
     * <code>ID_VERIFICATION_PERSONAL = 1;</code>
     */
    ID_VERIFICATION_PERSONAL(1),
    /**
     * <code>ID_VERIFICATION_ENTERPRISE = 2;</code>
     */
    ID_VERIFICATION_ENTERPRISE(2),
    UNRECOGNIZED(-1),
    ;

    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        IdVerificationStatus.class.getName());
    }
    /**
     * <code>ID_VERIFICATION_UNSPECIFIED = 0;</code>
     */
    public static final int ID_VERIFICATION_UNSPECIFIED_VALUE = 0;
    /**
     * <code>ID_VERIFICATION_PERSONAL = 1;</code>
     */
    public static final int ID_VERIFICATION_PERSONAL_VALUE = 1;
    /**
     * <code>ID_VERIFICATION_ENTERPRISE = 2;</code>
     */
    public static final int ID_VERIFICATION_ENTERPRISE_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static IdVerificationStatus valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static IdVerificationStatus forNumber(int value) {
      switch (value) {
        case 0: return ID_VERIFICATION_UNSPECIFIED;
        case 1: return ID_VERIFICATION_PERSONAL;
        case 2: return ID_VERIFICATION_ENTERPRISE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<IdVerificationStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        IdVerificationStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<IdVerificationStatus>() {
            public IdVerificationStatus findValueByNumber(int number) {
              return IdVerificationStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.Tlock.io.entity.profile.ProfileProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final IdVerificationStatus[] VALUES = values();

    public static IdVerificationStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private IdVerificationStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:IdVerificationStatus)
  }

  public interface ProfileOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Profile)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string wallet_address = 1;</code>
     * @return The walletAddress.
     */
    java.lang.String getWalletAddress();
    /**
     * <code>string wallet_address = 1;</code>
     * @return The bytes for walletAddress.
     */
    com.google.protobuf.ByteString
        getWalletAddressBytes();

    /**
     * <code>string user_handle = 2;</code>
     * @return The userHandle.
     */
    java.lang.String getUserHandle();
    /**
     * <code>string user_handle = 2;</code>
     * @return The bytes for userHandle.
     */
    com.google.protobuf.ByteString
        getUserHandleBytes();

    /**
     * <code>string nickname = 3;</code>
     * @return The nickname.
     */
    java.lang.String getNickname();
    /**
     * <code>string nickname = 3;</code>
     * @return The bytes for nickname.
     */
    com.google.protobuf.ByteString
        getNicknameBytes();

    /**
     * <code>string avatar = 4;</code>
     * @return The avatar.
     */
    java.lang.String getAvatar();
    /**
     * <code>string avatar = 4;</code>
     * @return The bytes for avatar.
     */
    com.google.protobuf.ByteString
        getAvatarBytes();

    /**
     * <code>string bio = 5;</code>
     * @return The bio.
     */
    java.lang.String getBio();
    /**
     * <code>string bio = 5;</code>
     * @return The bytes for bio.
     */
    com.google.protobuf.ByteString
        getBioBytes();

    /**
     * <code>uint64 level = 6;</code>
     * @return The level.
     */
    long getLevel();

    /**
     * <code>uint64 admin_level = 7;</code>
     * @return The adminLevel.
     */
    long getAdminLevel();

    /**
     * <code>uint64 following = 8;</code>
     * @return The following.
     */
    long getFollowing();

    /**
     * <code>uint64 followers = 9;</code>
     * @return The followers.
     */
    long getFollowers();

    /**
     * <code>int64 creation_time = 10;</code>
     * @return The creationTime.
     */
    long getCreationTime();

    /**
     * <code>string location = 11;</code>
     * @return The location.
     */
    java.lang.String getLocation();
    /**
     * <code>string location = 11;</code>
     * @return The bytes for location.
     */
    com.google.protobuf.ByteString
        getLocationBytes();

    /**
     * <code>string website = 12;</code>
     * @return The website.
     */
    java.lang.String getWebsite();
    /**
     * <code>string website = 12;</code>
     * @return The bytes for website.
     */
    com.google.protobuf.ByteString
        getWebsiteBytes();

    /**
     * <code>.IdVerificationStatus idVerification_status = 13;</code>
     * @return The enum numeric value on the wire for idVerificationStatus.
     */
    int getIdVerificationStatusValue();
    /**
     * <code>.IdVerificationStatus idVerification_status = 13;</code>
     * @return The idVerificationStatus.
     */
    com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus getIdVerificationStatus();

    /**
     * <code>uint64 score = 14;</code>
     * @return The score.
     */
    long getScore();

    /**
     * <code>string line_manager = 15;</code>
     * @return The lineManager.
     */
    java.lang.String getLineManager();
    /**
     * <code>string line_manager = 15;</code>
     * @return The bytes for lineManager.
     */
    com.google.protobuf.ByteString
        getLineManagerBytes();
  }
  /**
   * <pre>
   * Profile defines the structure of a profile
   * </pre>
   *
   * Protobuf type {@code Profile}
   */
  public static final class Profile extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Profile)
      ProfileOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 28,
        /* patch= */ 3,
        /* suffix= */ "",
        Profile.class.getName());
    }
    // Use Profile.newBuilder() to construct.
    private Profile(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Profile() {
      walletAddress_ = "";
      userHandle_ = "";
      nickname_ = "";
      avatar_ = "";
      bio_ = "";
      location_ = "";
      website_ = "";
      idVerificationStatus_ = 0;
      lineManager_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.Tlock.io.entity.profile.ProfileProto.internal_static_Profile_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.Tlock.io.entity.profile.ProfileProto.internal_static_Profile_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.Tlock.io.entity.profile.ProfileProto.Profile.class, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder.class);
    }

    public static final int WALLET_ADDRESS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object walletAddress_ = "";
    /**
     * <code>string wallet_address = 1;</code>
     * @return The walletAddress.
     */
    @java.lang.Override
    public java.lang.String getWalletAddress() {
      java.lang.Object ref = walletAddress_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        walletAddress_ = s;
        return s;
      }
    }
    /**
     * <code>string wallet_address = 1;</code>
     * @return The bytes for walletAddress.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getWalletAddressBytes() {
      java.lang.Object ref = walletAddress_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        walletAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USER_HANDLE_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object userHandle_ = "";
    /**
     * <code>string user_handle = 2;</code>
     * @return The userHandle.
     */
    @java.lang.Override
    public java.lang.String getUserHandle() {
      java.lang.Object ref = userHandle_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userHandle_ = s;
        return s;
      }
    }
    /**
     * <code>string user_handle = 2;</code>
     * @return The bytes for userHandle.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserHandleBytes() {
      java.lang.Object ref = userHandle_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userHandle_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NICKNAME_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object nickname_ = "";
    /**
     * <code>string nickname = 3;</code>
     * @return The nickname.
     */
    @java.lang.Override
    public java.lang.String getNickname() {
      java.lang.Object ref = nickname_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickname_ = s;
        return s;
      }
    }
    /**
     * <code>string nickname = 3;</code>
     * @return The bytes for nickname.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNicknameBytes() {
      java.lang.Object ref = nickname_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AVATAR_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object avatar_ = "";
    /**
     * <code>string avatar = 4;</code>
     * @return The avatar.
     */
    @java.lang.Override
    public java.lang.String getAvatar() {
      java.lang.Object ref = avatar_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        avatar_ = s;
        return s;
      }
    }
    /**
     * <code>string avatar = 4;</code>
     * @return The bytes for avatar.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAvatarBytes() {
      java.lang.Object ref = avatar_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        avatar_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BIO_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object bio_ = "";
    /**
     * <code>string bio = 5;</code>
     * @return The bio.
     */
    @java.lang.Override
    public java.lang.String getBio() {
      java.lang.Object ref = bio_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bio_ = s;
        return s;
      }
    }
    /**
     * <code>string bio = 5;</code>
     * @return The bytes for bio.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBioBytes() {
      java.lang.Object ref = bio_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bio_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LEVEL_FIELD_NUMBER = 6;
    private long level_ = 0L;
    /**
     * <code>uint64 level = 6;</code>
     * @return The level.
     */
    @java.lang.Override
    public long getLevel() {
      return level_;
    }

    public static final int ADMIN_LEVEL_FIELD_NUMBER = 7;
    private long adminLevel_ = 0L;
    /**
     * <code>uint64 admin_level = 7;</code>
     * @return The adminLevel.
     */
    @java.lang.Override
    public long getAdminLevel() {
      return adminLevel_;
    }

    public static final int FOLLOWING_FIELD_NUMBER = 8;
    private long following_ = 0L;
    /**
     * <code>uint64 following = 8;</code>
     * @return The following.
     */
    @java.lang.Override
    public long getFollowing() {
      return following_;
    }

    public static final int FOLLOWERS_FIELD_NUMBER = 9;
    private long followers_ = 0L;
    /**
     * <code>uint64 followers = 9;</code>
     * @return The followers.
     */
    @java.lang.Override
    public long getFollowers() {
      return followers_;
    }

    public static final int CREATION_TIME_FIELD_NUMBER = 10;
    private long creationTime_ = 0L;
    /**
     * <code>int64 creation_time = 10;</code>
     * @return The creationTime.
     */
    @java.lang.Override
    public long getCreationTime() {
      return creationTime_;
    }

    public static final int LOCATION_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object location_ = "";
    /**
     * <code>string location = 11;</code>
     * @return The location.
     */
    @java.lang.Override
    public java.lang.String getLocation() {
      java.lang.Object ref = location_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        location_ = s;
        return s;
      }
    }
    /**
     * <code>string location = 11;</code>
     * @return The bytes for location.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLocationBytes() {
      java.lang.Object ref = location_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        location_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int WEBSITE_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object website_ = "";
    /**
     * <code>string website = 12;</code>
     * @return The website.
     */
    @java.lang.Override
    public java.lang.String getWebsite() {
      java.lang.Object ref = website_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        website_ = s;
        return s;
      }
    }
    /**
     * <code>string website = 12;</code>
     * @return The bytes for website.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getWebsiteBytes() {
      java.lang.Object ref = website_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        website_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IDVERIFICATION_STATUS_FIELD_NUMBER = 13;
    private int idVerificationStatus_ = 0;
    /**
     * <code>.IdVerificationStatus idVerification_status = 13;</code>
     * @return The enum numeric value on the wire for idVerificationStatus.
     */
    @java.lang.Override public int getIdVerificationStatusValue() {
      return idVerificationStatus_;
    }
    /**
     * <code>.IdVerificationStatus idVerification_status = 13;</code>
     * @return The idVerificationStatus.
     */
    @java.lang.Override public com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus getIdVerificationStatus() {
      com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus result = com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus.forNumber(idVerificationStatus_);
      return result == null ? com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus.UNRECOGNIZED : result;
    }

    public static final int SCORE_FIELD_NUMBER = 14;
    private long score_ = 0L;
    /**
     * <code>uint64 score = 14;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }

    public static final int LINE_MANAGER_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile java.lang.Object lineManager_ = "";
    /**
     * <code>string line_manager = 15;</code>
     * @return The lineManager.
     */
    @java.lang.Override
    public java.lang.String getLineManager() {
      java.lang.Object ref = lineManager_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lineManager_ = s;
        return s;
      }
    }
    /**
     * <code>string line_manager = 15;</code>
     * @return The bytes for lineManager.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLineManagerBytes() {
      java.lang.Object ref = lineManager_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lineManager_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(walletAddress_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, walletAddress_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(userHandle_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, userHandle_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickname_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, nickname_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(avatar_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, avatar_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bio_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, bio_);
      }
      if (level_ != 0L) {
        output.writeUInt64(6, level_);
      }
      if (adminLevel_ != 0L) {
        output.writeUInt64(7, adminLevel_);
      }
      if (following_ != 0L) {
        output.writeUInt64(8, following_);
      }
      if (followers_ != 0L) {
        output.writeUInt64(9, followers_);
      }
      if (creationTime_ != 0L) {
        output.writeInt64(10, creationTime_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(location_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, location_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(website_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, website_);
      }
      if (idVerificationStatus_ != com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus.ID_VERIFICATION_UNSPECIFIED.getNumber()) {
        output.writeEnum(13, idVerificationStatus_);
      }
      if (score_ != 0L) {
        output.writeUInt64(14, score_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lineManager_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 15, lineManager_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(walletAddress_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, walletAddress_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(userHandle_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, userHandle_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickname_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, nickname_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(avatar_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, avatar_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(bio_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, bio_);
      }
      if (level_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, level_);
      }
      if (adminLevel_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, adminLevel_);
      }
      if (following_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(8, following_);
      }
      if (followers_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(9, followers_);
      }
      if (creationTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, creationTime_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(location_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, location_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(website_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, website_);
      }
      if (idVerificationStatus_ != com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus.ID_VERIFICATION_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(13, idVerificationStatus_);
      }
      if (score_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, score_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(lineManager_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(15, lineManager_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.Tlock.io.entity.profile.ProfileProto.Profile)) {
        return super.equals(obj);
      }
      com.Tlock.io.entity.profile.ProfileProto.Profile other = (com.Tlock.io.entity.profile.ProfileProto.Profile) obj;

      if (!getWalletAddress()
          .equals(other.getWalletAddress())) return false;
      if (!getUserHandle()
          .equals(other.getUserHandle())) return false;
      if (!getNickname()
          .equals(other.getNickname())) return false;
      if (!getAvatar()
          .equals(other.getAvatar())) return false;
      if (!getBio()
          .equals(other.getBio())) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (getAdminLevel()
          != other.getAdminLevel()) return false;
      if (getFollowing()
          != other.getFollowing()) return false;
      if (getFollowers()
          != other.getFollowers()) return false;
      if (getCreationTime()
          != other.getCreationTime()) return false;
      if (!getLocation()
          .equals(other.getLocation())) return false;
      if (!getWebsite()
          .equals(other.getWebsite())) return false;
      if (idVerificationStatus_ != other.idVerificationStatus_) return false;
      if (getScore()
          != other.getScore()) return false;
      if (!getLineManager()
          .equals(other.getLineManager())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + WALLET_ADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getWalletAddress().hashCode();
      hash = (37 * hash) + USER_HANDLE_FIELD_NUMBER;
      hash = (53 * hash) + getUserHandle().hashCode();
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickname().hashCode();
      hash = (37 * hash) + AVATAR_FIELD_NUMBER;
      hash = (53 * hash) + getAvatar().hashCode();
      hash = (37 * hash) + BIO_FIELD_NUMBER;
      hash = (53 * hash) + getBio().hashCode();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLevel());
      hash = (37 * hash) + ADMIN_LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAdminLevel());
      hash = (37 * hash) + FOLLOWING_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getFollowing());
      hash = (37 * hash) + FOLLOWERS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getFollowers());
      hash = (37 * hash) + CREATION_TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCreationTime());
      hash = (37 * hash) + LOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getLocation().hashCode();
      hash = (37 * hash) + WEBSITE_FIELD_NUMBER;
      hash = (53 * hash) + getWebsite().hashCode();
      hash = (37 * hash) + IDVERIFICATION_STATUS_FIELD_NUMBER;
      hash = (53 * hash) + idVerificationStatus_;
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
      hash = (37 * hash) + LINE_MANAGER_FIELD_NUMBER;
      hash = (53 * hash) + getLineManager().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.Tlock.io.entity.profile.ProfileProto.Profile parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.Tlock.io.entity.profile.ProfileProto.Profile prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * Profile defines the structure of a profile
     * </pre>
     *
     * Protobuf type {@code Profile}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Profile)
        com.Tlock.io.entity.profile.ProfileProto.ProfileOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.Tlock.io.entity.profile.ProfileProto.internal_static_Profile_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.Tlock.io.entity.profile.ProfileProto.internal_static_Profile_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.Tlock.io.entity.profile.ProfileProto.Profile.class, com.Tlock.io.entity.profile.ProfileProto.Profile.Builder.class);
      }

      // Construct using com.Tlock.io.entity.profile.ProfileProto.Profile.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        walletAddress_ = "";
        userHandle_ = "";
        nickname_ = "";
        avatar_ = "";
        bio_ = "";
        level_ = 0L;
        adminLevel_ = 0L;
        following_ = 0L;
        followers_ = 0L;
        creationTime_ = 0L;
        location_ = "";
        website_ = "";
        idVerificationStatus_ = 0;
        score_ = 0L;
        lineManager_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.Tlock.io.entity.profile.ProfileProto.internal_static_Profile_descriptor;
      }

      @java.lang.Override
      public com.Tlock.io.entity.profile.ProfileProto.Profile getDefaultInstanceForType() {
        return com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance();
      }

      @java.lang.Override
      public com.Tlock.io.entity.profile.ProfileProto.Profile build() {
        com.Tlock.io.entity.profile.ProfileProto.Profile result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.Tlock.io.entity.profile.ProfileProto.Profile buildPartial() {
        com.Tlock.io.entity.profile.ProfileProto.Profile result = new com.Tlock.io.entity.profile.ProfileProto.Profile(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.Tlock.io.entity.profile.ProfileProto.Profile result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.walletAddress_ = walletAddress_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userHandle_ = userHandle_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.nickname_ = nickname_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.avatar_ = avatar_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.bio_ = bio_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.level_ = level_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.adminLevel_ = adminLevel_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.following_ = following_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.followers_ = followers_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.creationTime_ = creationTime_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.location_ = location_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.website_ = website_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.idVerificationStatus_ = idVerificationStatus_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.score_ = score_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.lineManager_ = lineManager_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.Tlock.io.entity.profile.ProfileProto.Profile) {
          return mergeFrom((com.Tlock.io.entity.profile.ProfileProto.Profile)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.Tlock.io.entity.profile.ProfileProto.Profile other) {
        if (other == com.Tlock.io.entity.profile.ProfileProto.Profile.getDefaultInstance()) return this;
        if (!other.getWalletAddress().isEmpty()) {
          walletAddress_ = other.walletAddress_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getUserHandle().isEmpty()) {
          userHandle_ = other.userHandle_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.getNickname().isEmpty()) {
          nickname_ = other.nickname_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getAvatar().isEmpty()) {
          avatar_ = other.avatar_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getBio().isEmpty()) {
          bio_ = other.bio_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.getLevel() != 0L) {
          setLevel(other.getLevel());
        }
        if (other.getAdminLevel() != 0L) {
          setAdminLevel(other.getAdminLevel());
        }
        if (other.getFollowing() != 0L) {
          setFollowing(other.getFollowing());
        }
        if (other.getFollowers() != 0L) {
          setFollowers(other.getFollowers());
        }
        if (other.getCreationTime() != 0L) {
          setCreationTime(other.getCreationTime());
        }
        if (!other.getLocation().isEmpty()) {
          location_ = other.location_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (!other.getWebsite().isEmpty()) {
          website_ = other.website_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (other.idVerificationStatus_ != 0) {
          setIdVerificationStatusValue(other.getIdVerificationStatusValue());
        }
        if (other.getScore() != 0L) {
          setScore(other.getScore());
        }
        if (!other.getLineManager().isEmpty()) {
          lineManager_ = other.lineManager_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                walletAddress_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                userHandle_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                nickname_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                avatar_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                bio_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                level_ = input.readUInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                adminLevel_ = input.readUInt64();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                following_ = input.readUInt64();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                followers_ = input.readUInt64();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                creationTime_ = input.readInt64();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 90: {
                location_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                website_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 104: {
                idVerificationStatus_ = input.readEnum();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                score_ = input.readUInt64();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 122: {
                lineManager_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object walletAddress_ = "";
      /**
       * <code>string wallet_address = 1;</code>
       * @return The walletAddress.
       */
      public java.lang.String getWalletAddress() {
        java.lang.Object ref = walletAddress_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          walletAddress_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string wallet_address = 1;</code>
       * @return The bytes for walletAddress.
       */
      public com.google.protobuf.ByteString
          getWalletAddressBytes() {
        java.lang.Object ref = walletAddress_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          walletAddress_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string wallet_address = 1;</code>
       * @param value The walletAddress to set.
       * @return This builder for chaining.
       */
      public Builder setWalletAddress(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        walletAddress_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string wallet_address = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearWalletAddress() {
        walletAddress_ = getDefaultInstance().getWalletAddress();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string wallet_address = 1;</code>
       * @param value The bytes for walletAddress to set.
       * @return This builder for chaining.
       */
      public Builder setWalletAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        walletAddress_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object userHandle_ = "";
      /**
       * <code>string user_handle = 2;</code>
       * @return The userHandle.
       */
      public java.lang.String getUserHandle() {
        java.lang.Object ref = userHandle_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          userHandle_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string user_handle = 2;</code>
       * @return The bytes for userHandle.
       */
      public com.google.protobuf.ByteString
          getUserHandleBytes() {
        java.lang.Object ref = userHandle_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userHandle_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string user_handle = 2;</code>
       * @param value The userHandle to set.
       * @return This builder for chaining.
       */
      public Builder setUserHandle(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        userHandle_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string user_handle = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserHandle() {
        userHandle_ = getDefaultInstance().getUserHandle();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string user_handle = 2;</code>
       * @param value The bytes for userHandle to set.
       * @return This builder for chaining.
       */
      public Builder setUserHandleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        userHandle_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object nickname_ = "";
      /**
       * <code>string nickname = 3;</code>
       * @return The nickname.
       */
      public java.lang.String getNickname() {
        java.lang.Object ref = nickname_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          nickname_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string nickname = 3;</code>
       * @return The bytes for nickname.
       */
      public com.google.protobuf.ByteString
          getNicknameBytes() {
        java.lang.Object ref = nickname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          nickname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string nickname = 3;</code>
       * @param value The nickname to set.
       * @return This builder for chaining.
       */
      public Builder setNickname(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        nickname_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string nickname = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNickname() {
        nickname_ = getDefaultInstance().getNickname();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string nickname = 3;</code>
       * @param value The bytes for nickname to set.
       * @return This builder for chaining.
       */
      public Builder setNicknameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        nickname_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object avatar_ = "";
      /**
       * <code>string avatar = 4;</code>
       * @return The avatar.
       */
      public java.lang.String getAvatar() {
        java.lang.Object ref = avatar_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          avatar_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string avatar = 4;</code>
       * @return The bytes for avatar.
       */
      public com.google.protobuf.ByteString
          getAvatarBytes() {
        java.lang.Object ref = avatar_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          avatar_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string avatar = 4;</code>
       * @param value The avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatar(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        avatar_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>string avatar = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatar() {
        avatar_ = getDefaultInstance().getAvatar();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>string avatar = 4;</code>
       * @param value The bytes for avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatarBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        avatar_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object bio_ = "";
      /**
       * <code>string bio = 5;</code>
       * @return The bio.
       */
      public java.lang.String getBio() {
        java.lang.Object ref = bio_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bio_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string bio = 5;</code>
       * @return The bytes for bio.
       */
      public com.google.protobuf.ByteString
          getBioBytes() {
        java.lang.Object ref = bio_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bio_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string bio = 5;</code>
       * @param value The bio to set.
       * @return This builder for chaining.
       */
      public Builder setBio(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        bio_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>string bio = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBio() {
        bio_ = getDefaultInstance().getBio();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <code>string bio = 5;</code>
       * @param value The bytes for bio to set.
       * @return This builder for chaining.
       */
      public Builder setBioBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        bio_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private long level_ ;
      /**
       * <code>uint64 level = 6;</code>
       * @return The level.
       */
      @java.lang.Override
      public long getLevel() {
        return level_;
      }
      /**
       * <code>uint64 level = 6;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(long value) {

        level_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 level = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000020);
        level_ = 0L;
        onChanged();
        return this;
      }

      private long adminLevel_ ;
      /**
       * <code>uint64 admin_level = 7;</code>
       * @return The adminLevel.
       */
      @java.lang.Override
      public long getAdminLevel() {
        return adminLevel_;
      }
      /**
       * <code>uint64 admin_level = 7;</code>
       * @param value The adminLevel to set.
       * @return This builder for chaining.
       */
      public Builder setAdminLevel(long value) {

        adminLevel_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 admin_level = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearAdminLevel() {
        bitField0_ = (bitField0_ & ~0x00000040);
        adminLevel_ = 0L;
        onChanged();
        return this;
      }

      private long following_ ;
      /**
       * <code>uint64 following = 8;</code>
       * @return The following.
       */
      @java.lang.Override
      public long getFollowing() {
        return following_;
      }
      /**
       * <code>uint64 following = 8;</code>
       * @param value The following to set.
       * @return This builder for chaining.
       */
      public Builder setFollowing(long value) {

        following_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 following = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearFollowing() {
        bitField0_ = (bitField0_ & ~0x00000080);
        following_ = 0L;
        onChanged();
        return this;
      }

      private long followers_ ;
      /**
       * <code>uint64 followers = 9;</code>
       * @return The followers.
       */
      @java.lang.Override
      public long getFollowers() {
        return followers_;
      }
      /**
       * <code>uint64 followers = 9;</code>
       * @param value The followers to set.
       * @return This builder for chaining.
       */
      public Builder setFollowers(long value) {

        followers_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 followers = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearFollowers() {
        bitField0_ = (bitField0_ & ~0x00000100);
        followers_ = 0L;
        onChanged();
        return this;
      }

      private long creationTime_ ;
      /**
       * <code>int64 creation_time = 10;</code>
       * @return The creationTime.
       */
      @java.lang.Override
      public long getCreationTime() {
        return creationTime_;
      }
      /**
       * <code>int64 creation_time = 10;</code>
       * @param value The creationTime to set.
       * @return This builder for chaining.
       */
      public Builder setCreationTime(long value) {

        creationTime_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>int64 creation_time = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreationTime() {
        bitField0_ = (bitField0_ & ~0x00000200);
        creationTime_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object location_ = "";
      /**
       * <code>string location = 11;</code>
       * @return The location.
       */
      public java.lang.String getLocation() {
        java.lang.Object ref = location_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          location_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string location = 11;</code>
       * @return The bytes for location.
       */
      public com.google.protobuf.ByteString
          getLocationBytes() {
        java.lang.Object ref = location_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          location_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string location = 11;</code>
       * @param value The location to set.
       * @return This builder for chaining.
       */
      public Builder setLocation(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        location_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>string location = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearLocation() {
        location_ = getDefaultInstance().getLocation();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <code>string location = 11;</code>
       * @param value The bytes for location to set.
       * @return This builder for chaining.
       */
      public Builder setLocationBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        location_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object website_ = "";
      /**
       * <code>string website = 12;</code>
       * @return The website.
       */
      public java.lang.String getWebsite() {
        java.lang.Object ref = website_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          website_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string website = 12;</code>
       * @return The bytes for website.
       */
      public com.google.protobuf.ByteString
          getWebsiteBytes() {
        java.lang.Object ref = website_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          website_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string website = 12;</code>
       * @param value The website to set.
       * @return This builder for chaining.
       */
      public Builder setWebsite(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        website_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>string website = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearWebsite() {
        website_ = getDefaultInstance().getWebsite();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <code>string website = 12;</code>
       * @param value The bytes for website to set.
       * @return This builder for chaining.
       */
      public Builder setWebsiteBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        website_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private int idVerificationStatus_ = 0;
      /**
       * <code>.IdVerificationStatus idVerification_status = 13;</code>
       * @return The enum numeric value on the wire for idVerificationStatus.
       */
      @java.lang.Override public int getIdVerificationStatusValue() {
        return idVerificationStatus_;
      }
      /**
       * <code>.IdVerificationStatus idVerification_status = 13;</code>
       * @param value The enum numeric value on the wire for idVerificationStatus to set.
       * @return This builder for chaining.
       */
      public Builder setIdVerificationStatusValue(int value) {
        idVerificationStatus_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>.IdVerificationStatus idVerification_status = 13;</code>
       * @return The idVerificationStatus.
       */
      @java.lang.Override
      public com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus getIdVerificationStatus() {
        com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus result = com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus.forNumber(idVerificationStatus_);
        return result == null ? com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus.UNRECOGNIZED : result;
      }
      /**
       * <code>.IdVerificationStatus idVerification_status = 13;</code>
       * @param value The idVerificationStatus to set.
       * @return This builder for chaining.
       */
      public Builder setIdVerificationStatus(com.Tlock.io.entity.profile.ProfileProto.IdVerificationStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00001000;
        idVerificationStatus_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.IdVerificationStatus idVerification_status = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdVerificationStatus() {
        bitField0_ = (bitField0_ & ~0x00001000);
        idVerificationStatus_ = 0;
        onChanged();
        return this;
      }

      private long score_ ;
      /**
       * <code>uint64 score = 14;</code>
       * @return The score.
       */
      @java.lang.Override
      public long getScore() {
        return score_;
      }
      /**
       * <code>uint64 score = 14;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(long value) {

        score_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 score = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00002000);
        score_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object lineManager_ = "";
      /**
       * <code>string line_manager = 15;</code>
       * @return The lineManager.
       */
      public java.lang.String getLineManager() {
        java.lang.Object ref = lineManager_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          lineManager_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string line_manager = 15;</code>
       * @return The bytes for lineManager.
       */
      public com.google.protobuf.ByteString
          getLineManagerBytes() {
        java.lang.Object ref = lineManager_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lineManager_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string line_manager = 15;</code>
       * @param value The lineManager to set.
       * @return This builder for chaining.
       */
      public Builder setLineManager(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        lineManager_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>string line_manager = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineManager() {
        lineManager_ = getDefaultInstance().getLineManager();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <code>string line_manager = 15;</code>
       * @param value The bytes for lineManager to set.
       * @return This builder for chaining.
       */
      public Builder setLineManagerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        lineManager_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Profile)
    }

    // @@protoc_insertion_point(class_scope:Profile)
    private static final com.Tlock.io.entity.profile.ProfileProto.Profile DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.Tlock.io.entity.profile.ProfileProto.Profile();
    }

    public static com.Tlock.io.entity.profile.ProfileProto.Profile getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Profile>
        PARSER = new com.google.protobuf.AbstractParser<Profile>() {
      @java.lang.Override
      public Profile parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Profile> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Profile> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.Tlock.io.entity.profile.ProfileProto.Profile getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Profile_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Profile_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024entity/profile.proto\"\304\002\n\007Profile\022\026\n\016wa" +
      "llet_address\030\001 \001(\t\022\023\n\013user_handle\030\002 \001(\t\022" +
      "\020\n\010nickname\030\003 \001(\t\022\016\n\006avatar\030\004 \001(\t\022\013\n\003bio" +
      "\030\005 \001(\t\022\r\n\005level\030\006 \001(\004\022\023\n\013admin_level\030\007 \001" +
      "(\004\022\021\n\tfollowing\030\010 \001(\004\022\021\n\tfollowers\030\t \001(\004" +
      "\022\025\n\rcreation_time\030\n \001(\003\022\020\n\010location\030\013 \001(" +
      "\t\022\017\n\007website\030\014 \001(\t\0224\n\025idVerification_sta" +
      "tus\030\r \001(\0162\025.IdVerificationStatus\022\r\n\005scor" +
      "e\030\016 \001(\004\022\024\n\014line_manager\030\017 \001(\t*u\n\024IdVerif" +
      "icationStatus\022\037\n\033ID_VERIFICATION_UNSPECI" +
      "FIED\020\000\022\034\n\030ID_VERIFICATION_PERSONAL\020\001\022\036\n\032" +
      "ID_VERIFICATION_ENTERPRISE\020\002B+\n\033com.Tloc" +
      "k.io.entity.profileB\014ProfileProtob\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Profile_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Profile_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Profile_descriptor,
        new java.lang.String[] { "WalletAddress", "UserHandle", "Nickname", "Avatar", "Bio", "Level", "AdminLevel", "Following", "Followers", "CreationTime", "Location", "Website", "IdVerificationStatus", "Score", "LineManager", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
