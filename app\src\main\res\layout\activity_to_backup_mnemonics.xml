<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".activity.wallet.BackupMnemonicsActivity">


    <RelativeLayout
        android:id="@+id/rl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_45"
        android:layout_marginBottom="@dimen/dp_10">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="Create wallet"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dp_4"
            android:adjustViewBounds="true"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/icon_back_black" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_toolbar"
        android:layout_marginLeft="@dimen/dp_22"
        android:layout_marginRight="@dimen/dp_22"
        android:layout_marginTop="@dimen/dp_10"
        android:text="IMPORTANT: SAFEGUARD YOUR RECOVERY PHRASE"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_20"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_marginTop="@dimen/dp_19"
        android:layout_marginLeft="@dimen/dp_19"
        android:layout_marginRight="@dimen/dp_19"
        android:background="@drawable/btn_gray_6"
        android:padding="@dimen/dp_16"
        android:lineHeight="@dimen/dp_24"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/dp_15" />

    <TextView
        android:id="@+id/tv_backup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:layout_margin="@dimen/dp_24"
        android:layout_marginLeft="@dimen/dp_34"
        android:layout_marginRight="@dimen/dp_34"
        android:background="@drawable/btn_blue_6"
        android:gravity="center"
        android:padding="@dimen/dp_15"
        android:text="Back up now"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold" />

</RelativeLayout>