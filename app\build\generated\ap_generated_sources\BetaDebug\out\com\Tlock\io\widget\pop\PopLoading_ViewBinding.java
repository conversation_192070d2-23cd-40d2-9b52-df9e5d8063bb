// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopLoading_ViewBinding implements Unbinder {
  private PopLoading target;

  @UiThread
  public PopLoading_ViewBinding(PopLoading target) {
    this(target, target);
  }

  @UiThread
  public PopLoading_ViewBinding(PopLoading target, View source) {
    this.target = target;

    target.mIvAnimation = Utils.findRequiredViewAsType(source, R.id.iv_animation, "field 'mIvAnimation'", ImageView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mRlRoot = Utils.findRequiredViewAsType(source, R.id.rl_root, "field 'mRlRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopLoading target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvAnimation = null;
    target.mTvTitle = null;
    target.mRlRoot = null;
  }
}
