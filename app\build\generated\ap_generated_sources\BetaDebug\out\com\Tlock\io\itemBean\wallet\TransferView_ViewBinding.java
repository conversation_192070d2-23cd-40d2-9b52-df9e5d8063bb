// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TransferView_ViewBinding implements Unbinder {
  private TransferView target;

  @UiThread
  public TransferView_ViewBinding(TransferView target) {
    this(target, target);
  }

  @UiThread
  public TransferView_ViewBinding(TransferView target, View source) {
    this.target = target;

    target.mIvStatus = Utils.findRequiredViewAsType(source, R.id.iv_status, "field 'mIvStatus'", ImageView.class);
    target.mTvAddress = Utils.findRequiredViewAsType(source, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    target.mTvCount = Utils.findRequiredViewAsType(source, R.id.tv_count, "field 'mTvCount'", TextView.class);
    target.mTvCancel = Utils.findRequiredViewAsType(source, R.id.tv_cancel, "field 'mTvCancel'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TransferView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvStatus = null;
    target.mTvAddress = null;
    target.mTvTime = null;
    target.mTvCount = null;
    target.mTvCancel = null;
  }
}
