int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim lib_dialog_enter 0x7f01001d
int anim lib_dialog_exit 0x7f01001e
int anim mtrl_bottom_sheet_slide_in 0x7f01001f
int anim mtrl_bottom_sheet_slide_out 0x7f010020
int anim mtrl_card_lowers_interpolator 0x7f010021
int anim pickerview_dialog_scale_in 0x7f010022
int anim pickerview_dialog_scale_out 0x7f010023
int anim pickerview_slide_in_bottom 0x7f010024
int anim pickerview_slide_out_bottom 0x7f010025
int anim pop_scale_in 0x7f010026
int anim pop_scale_out 0x7f010027
int anim pop_slide_in_bottom 0x7f010028
int anim pop_slide_in_top 0x7f010029
int anim pop_slide_out_bottom 0x7f01002a
int anim pop_slide_out_top 0x7f01002b
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator linear_indeterminate_line1_head_interpolator 0x7f020009
int animator linear_indeterminate_line1_tail_interpolator 0x7f02000a
int animator linear_indeterminate_line2_head_interpolator 0x7f02000b
int animator linear_indeterminate_line2_tail_interpolator 0x7f02000c
int animator mtrl_btn_state_list_anim 0x7f02000d
int animator mtrl_btn_unelevated_state_list_anim 0x7f02000e
int animator mtrl_card_state_list_anim 0x7f02000f
int animator mtrl_chip_state_list_anim 0x7f020010
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020011
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f020012
int animator mtrl_extended_fab_hide_motion_spec 0x7f020013
int animator mtrl_extended_fab_show_motion_spec 0x7f020014
int animator mtrl_extended_fab_state_list_animator 0x7f020015
int animator mtrl_fab_hide_motion_spec 0x7f020016
int animator mtrl_fab_show_motion_spec 0x7f020017
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020018
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020019
int attr actionBarDivider 0x7f030000
int attr actionBarItemBackground 0x7f030001
int attr actionBarPopupTheme 0x7f030002
int attr actionBarSize 0x7f030003
int attr actionBarSplitStyle 0x7f030004
int attr actionBarStyle 0x7f030005
int attr actionBarTabBarStyle 0x7f030006
int attr actionBarTabStyle 0x7f030007
int attr actionBarTabTextStyle 0x7f030008
int attr actionBarTheme 0x7f030009
int attr actionBarWidgetTheme 0x7f03000a
int attr actionButtonStyle 0x7f03000b
int attr actionDropDownStyle 0x7f03000c
int attr actionLayout 0x7f03000d
int attr actionMenuTextAppearance 0x7f03000e
int attr actionMenuTextColor 0x7f03000f
int attr actionModeBackground 0x7f030010
int attr actionModeCloseButtonStyle 0x7f030011
int attr actionModeCloseContentDescription 0x7f030012
int attr actionModeCloseDrawable 0x7f030013
int attr actionModeCopyDrawable 0x7f030014
int attr actionModeCutDrawable 0x7f030015
int attr actionModeFindDrawable 0x7f030016
int attr actionModePasteDrawable 0x7f030017
int attr actionModePopupWindowStyle 0x7f030018
int attr actionModeSelectAllDrawable 0x7f030019
int attr actionModeShareDrawable 0x7f03001a
int attr actionModeSplitBackground 0x7f03001b
int attr actionModeStyle 0x7f03001c
int attr actionModeTheme 0x7f03001d
int attr actionModeWebSearchDrawable 0x7f03001e
int attr actionOverflowButtonStyle 0x7f03001f
int attr actionOverflowMenuStyle 0x7f030020
int attr actionProviderClass 0x7f030021
int attr actionTextColorAlpha 0x7f030022
int attr actionViewClass 0x7f030023
int attr activityChooserViewStyle 0x7f030024
int attr adjustHeightOffset 0x7f030025
int attr alertDialogButtonGroupStyle 0x7f030026
int attr alertDialogCenterButtons 0x7f030027
int attr alertDialogStyle 0x7f030028
int attr alertDialogTheme 0x7f030029
int attr allowStacking 0x7f03002a
int attr alpha 0x7f03002b
int attr alphabeticModifiers 0x7f03002c
int attr altSrc 0x7f03002d
int attr animate_relativeTo 0x7f03002e
int attr animationMode 0x7f03002f
int attr appBarLayoutStyle 0x7f030030
int attr applyMotionScene 0x7f030031
int attr arcMode 0x7f030032
int attr arrowHeadLength 0x7f030033
int attr arrowShaftLength 0x7f030034
int attr assetName 0x7f030035
int attr attributeName 0x7f030036
int attr autoAdjustHeightAtBottomView 0x7f030037
int attr autoCompleteTextViewStyle 0x7f030038
int attr autoSizeMaxTextSize 0x7f030039
int attr autoSizeMinTextSize 0x7f03003a
int attr autoSizePresetSizes 0x7f03003b
int attr autoSizeStepGranularity 0x7f03003c
int attr autoSizeTextType 0x7f03003d
int attr autoTransition 0x7f03003e
int attr background 0x7f03003f
int attr backgroundColor 0x7f030040
int attr backgroundInsetBottom 0x7f030041
int attr backgroundInsetEnd 0x7f030042
int attr backgroundInsetStart 0x7f030043
int attr backgroundInsetTop 0x7f030044
int attr backgroundOverlayColorAlpha 0x7f030045
int attr backgroundSplit 0x7f030046
int attr backgroundStacked 0x7f030047
int attr backgroundTint 0x7f030048
int attr backgroundTintMode 0x7f030049
int attr badgeGravity 0x7f03004a
int attr badgeStyle 0x7f03004b
int attr badgeTextColor 0x7f03004c
int attr banner_auto_loop 0x7f03004d
int attr banner_indicator_gravity 0x7f03004e
int attr banner_indicator_height 0x7f03004f
int attr banner_indicator_margin 0x7f030050
int attr banner_indicator_marginBottom 0x7f030051
int attr banner_indicator_marginLeft 0x7f030052
int attr banner_indicator_marginRight 0x7f030053
int attr banner_indicator_marginTop 0x7f030054
int attr banner_indicator_normal_color 0x7f030055
int attr banner_indicator_normal_width 0x7f030056
int attr banner_indicator_radius 0x7f030057
int attr banner_indicator_selected_color 0x7f030058
int attr banner_indicator_selected_width 0x7f030059
int attr banner_indicator_space 0x7f03005a
int attr banner_infinite_loop 0x7f03005b
int attr banner_loop_time 0x7f03005c
int attr banner_orientation 0x7f03005d
int attr banner_radius 0x7f03005e
int attr barColor 0x7f03005f
int attr barLength 0x7f030060
int attr barRadius 0x7f030061
int attr barrierAllowsGoneWidgets 0x7f030062
int attr barrierDirection 0x7f030063
int attr barrierMargin 0x7f030064
int attr behavior_autoHide 0x7f030065
int attr behavior_autoShrink 0x7f030066
int attr behavior_draggable 0x7f030067
int attr behavior_expandedOffset 0x7f030068
int attr behavior_fitToContents 0x7f030069
int attr behavior_halfExpandedRatio 0x7f03006a
int attr behavior_hideable 0x7f03006b
int attr behavior_overlapTop 0x7f03006c
int attr behavior_peekHeight 0x7f03006d
int attr behavior_saveFlags 0x7f03006e
int attr behavior_skipCollapsed 0x7f03006f
int attr borderWidth 0x7f030070
int attr borderlessButtonStyle 0x7f030071
int attr bottomAppBarStyle 0x7f030072
int attr bottomDrawable 0x7f030073
int attr bottomDrawableHeight 0x7f030074
int attr bottomDrawableWidth 0x7f030075
int attr bottomNavigationStyle 0x7f030076
int attr bottomSheetDialogTheme 0x7f030077
int attr bottomSheetStyle 0x7f030078
int attr boxBackgroundColor 0x7f030079
int attr boxBackgroundMode 0x7f03007a
int attr boxCollapsedPaddingTop 0x7f03007b
int attr boxCornerRadiusBottomEnd 0x7f03007c
int attr boxCornerRadiusBottomStart 0x7f03007d
int attr boxCornerRadiusTopEnd 0x7f03007e
int attr boxCornerRadiusTopStart 0x7f03007f
int attr boxStrokeColor 0x7f030080
int attr boxStrokeErrorColor 0x7f030081
int attr boxStrokeWidth 0x7f030082
int attr boxStrokeWidthFocused 0x7f030083
int attr brightness 0x7f030084
int attr bsb_always_show_bubble 0x7f030085
int attr bsb_always_show_bubble_delay 0x7f030086
int attr bsb_anim_duration 0x7f030087
int attr bsb_auto_adjust_section_mark 0x7f030088
int attr bsb_bubble_color 0x7f030089
int attr bsb_bubble_text_color 0x7f03008a
int attr bsb_bubble_text_size 0x7f03008b
int attr bsb_hide_bubble 0x7f03008c
int attr bsb_is_float_type 0x7f03008d
int attr bsb_is_touch_auto 0x7f03008e
int attr bsb_max 0x7f03008f
int attr bsb_min 0x7f030090
int attr bsb_progress 0x7f030091
int attr bsb_rtl 0x7f030092
int attr bsb_second_track_color 0x7f030093
int attr bsb_second_track_size 0x7f030094
int attr bsb_section_count 0x7f030095
int attr bsb_section_text_color 0x7f030096
int attr bsb_section_text_interval 0x7f030097
int attr bsb_section_text_position 0x7f030098
int attr bsb_section_text_size 0x7f030099
int attr bsb_seek_by_section 0x7f03009a
int attr bsb_seek_step_section 0x7f03009b
int attr bsb_show_progress_in_float 0x7f03009c
int attr bsb_show_section_mark 0x7f03009d
int attr bsb_show_section_text 0x7f03009e
int attr bsb_show_thumb_text 0x7f03009f
int attr bsb_thumb_color 0x7f0300a0
int attr bsb_thumb_radius 0x7f0300a1
int attr bsb_thumb_radius_on_dragging 0x7f0300a2
int attr bsb_thumb_text_color 0x7f0300a3
int attr bsb_thumb_text_size 0x7f0300a4
int attr bsb_touch_to_seek 0x7f0300a5
int attr bsb_track_color 0x7f0300a6
int attr bsb_track_size 0x7f0300a7
int attr buttonBarButtonStyle 0x7f0300a8
int attr buttonBarNegativeButtonStyle 0x7f0300a9
int attr buttonBarNeutralButtonStyle 0x7f0300aa
int attr buttonBarPositiveButtonStyle 0x7f0300ab
int attr buttonBarStyle 0x7f0300ac
int attr buttonCompat 0x7f0300ad
int attr buttonGravity 0x7f0300ae
int attr buttonIconDimen 0x7f0300af
int attr buttonPanelSideLayout 0x7f0300b0
int attr buttonStyle 0x7f0300b1
int attr buttonStyleSmall 0x7f0300b2
int attr buttonTint 0x7f0300b3
int attr buttonTintMode 0x7f0300b4
int attr cardBackgroundColor 0x7f0300b5
int attr cardCornerRadius 0x7f0300b6
int attr cardElevation 0x7f0300b7
int attr cardForegroundColor 0x7f0300b8
int attr cardMaxElevation 0x7f0300b9
int attr cardPreventCornerOverlap 0x7f0300ba
int attr cardUseCompatPadding 0x7f0300bb
int attr cardViewStyle 0x7f0300bc
int attr chainUseRtl 0x7f0300bd
int attr checkboxStyle 0x7f0300be
int attr checkedButton 0x7f0300bf
int attr checkedChip 0x7f0300c0
int attr checkedIcon 0x7f0300c1
int attr checkedIconEnabled 0x7f0300c2
int attr checkedIconMargin 0x7f0300c3
int attr checkedIconSize 0x7f0300c4
int attr checkedIconTint 0x7f0300c5
int attr checkedIconVisible 0x7f0300c6
int attr checkedTextViewStyle 0x7f0300c7
int attr chipBackgroundColor 0x7f0300c8
int attr chipCornerRadius 0x7f0300c9
int attr chipEndPadding 0x7f0300ca
int attr chipGroupStyle 0x7f0300cb
int attr chipIcon 0x7f0300cc
int attr chipIconEnabled 0x7f0300cd
int attr chipIconSize 0x7f0300ce
int attr chipIconTint 0x7f0300cf
int attr chipIconVisible 0x7f0300d0
int attr chipMinHeight 0x7f0300d1
int attr chipMinTouchTargetSize 0x7f0300d2
int attr chipSpacing 0x7f0300d3
int attr chipSpacingHorizontal 0x7f0300d4
int attr chipSpacingVertical 0x7f0300d5
int attr chipStandaloneStyle 0x7f0300d6
int attr chipStartPadding 0x7f0300d7
int attr chipStrokeColor 0x7f0300d8
int attr chipStrokeWidth 0x7f0300d9
int attr chipStyle 0x7f0300da
int attr chipSurfaceColor 0x7f0300db
int attr circleRadius 0x7f0300dc
int attr circularProgressIndicatorStyle 0x7f0300dd
int attr clickAction 0x7f0300de
int attr clockFaceBackgroundColor 0x7f0300df
int attr clockHandColor 0x7f0300e0
int attr clockIcon 0x7f0300e1
int attr clockNumberTextColor 0x7f0300e2
int attr closeIcon 0x7f0300e3
int attr closeIconEnabled 0x7f0300e4
int attr closeIconEndPadding 0x7f0300e5
int attr closeIconSize 0x7f0300e6
int attr closeIconStartPadding 0x7f0300e7
int attr closeIconTint 0x7f0300e8
int attr closeIconVisible 0x7f0300e9
int attr closeItemLayout 0x7f0300ea
int attr collapseContentDescription 0x7f0300eb
int attr collapseIcon 0x7f0300ec
int attr collapsedSize 0x7f0300ed
int attr collapsedTitleGravity 0x7f0300ee
int attr collapsedTitleTextAppearance 0x7f0300ef
int attr collapsingToolbarLayoutStyle 0x7f0300f0
int attr color 0x7f0300f1
int attr colorAccent 0x7f0300f2
int attr colorBackgroundFloating 0x7f0300f3
int attr colorButtonNormal 0x7f0300f4
int attr colorControlActivated 0x7f0300f5
int attr colorControlHighlight 0x7f0300f6
int attr colorControlNormal 0x7f0300f7
int attr colorError 0x7f0300f8
int attr colorOnBackground 0x7f0300f9
int attr colorOnError 0x7f0300fa
int attr colorOnPrimary 0x7f0300fb
int attr colorOnPrimarySurface 0x7f0300fc
int attr colorOnSecondary 0x7f0300fd
int attr colorOnSurface 0x7f0300fe
int attr colorPrimary 0x7f0300ff
int attr colorPrimaryDark 0x7f030100
int attr colorPrimarySurface 0x7f030101
int attr colorPrimaryVariant 0x7f030102
int attr colorSecondary 0x7f030103
int attr colorSecondaryVariant 0x7f030104
int attr colorSurface 0x7f030105
int attr colorSwitchThumbNormal 0x7f030106
int attr commitIcon 0x7f030107
int attr constraintSet 0x7f030108
int attr constraintSetEnd 0x7f030109
int attr constraintSetStart 0x7f03010a
int attr constraint_referenced_ids 0x7f03010b
int attr constraints 0x7f03010c
int attr content 0x7f03010d
int attr contentDescription 0x7f03010e
int attr contentInsetEnd 0x7f03010f
int attr contentInsetEndWithActions 0x7f030110
int attr contentInsetLeft 0x7f030111
int attr contentInsetRight 0x7f030112
int attr contentInsetStart 0x7f030113
int attr contentInsetStartWithNavigation 0x7f030114
int attr contentPadding 0x7f030115
int attr contentPaddingBottom 0x7f030116
int attr contentPaddingEnd 0x7f030117
int attr contentPaddingLeft 0x7f030118
int attr contentPaddingRight 0x7f030119
int attr contentPaddingStart 0x7f03011a
int attr contentPaddingTop 0x7f03011b
int attr contentScrim 0x7f03011c
int attr contrast 0x7f03011d
int attr controlBackground 0x7f03011e
int attr coordinatorLayoutStyle 0x7f03011f
int attr cornerFamily 0x7f030120
int attr cornerFamilyBottomLeft 0x7f030121
int attr cornerFamilyBottomRight 0x7f030122
int attr cornerFamilyTopLeft 0x7f030123
int attr cornerFamilyTopRight 0x7f030124
int attr cornerRadius 0x7f030125
int attr cornerSize 0x7f030126
int attr cornerSizeBottomLeft 0x7f030127
int attr cornerSizeBottomRight 0x7f030128
int attr cornerSizeTopLeft 0x7f030129
int attr cornerSizeTopRight 0x7f03012a
int attr counterEnabled 0x7f03012b
int attr counterMaxLength 0x7f03012c
int attr counterOverflowTextAppearance 0x7f03012d
int attr counterOverflowTextColor 0x7f03012e
int attr counterTextAppearance 0x7f03012f
int attr counterTextColor 0x7f030130
int attr crossfade 0x7f030131
int attr currentState 0x7f030132
int attr curveFit 0x7f030133
int attr customBoolean 0x7f030134
int attr customColorDrawableValue 0x7f030135
int attr customColorValue 0x7f030136
int attr customDimension 0x7f030137
int attr customFloatValue 0x7f030138
int attr customIntegerValue 0x7f030139
int attr customNavigationLayout 0x7f03013a
int attr customPixelDimension 0x7f03013b
int attr customStringValue 0x7f03013c
int attr cy_color_indicator 0x7f03013d
int attr cy_height_indicator 0x7f03013e
int attr cy_radius_indicator 0x7f03013f
int attr cy_scrollable 0x7f030140
int attr cy_space_horizontal 0x7f030141
int attr cy_space_vertical 0x7f030142
int attr cy_textColorNormal 0x7f030143
int attr cy_textColorSelected 0x7f030144
int attr cy_width_indicator_max 0x7f030145
int attr cy_width_indicator_selected 0x7f030146
int attr dayInvalidStyle 0x7f030147
int attr daySelectedStyle 0x7f030148
int attr dayStyle 0x7f030149
int attr dayTodayStyle 0x7f03014a
int attr defaultDuration 0x7f03014b
int attr defaultQueryHint 0x7f03014c
int attr defaultState 0x7f03014d
int attr default_watch 0x7f03014e
int attr del_show 0x7f03014f
int attr deltaPolarAngle 0x7f030150
int attr deltaPolarRadius 0x7f030151
int attr deriveConstraintsFrom 0x7f030152
int attr dhDrawable1 0x7f030153
int attr dhDrawable2 0x7f030154
int attr dhDrawable3 0x7f030155
int attr dialogCornerRadius 0x7f030156
int attr dialogPreferredPadding 0x7f030157
int attr dialogTheme 0x7f030158
int attr disableChildHorizontalScroll 0x7f030159
int attr displayOptions 0x7f03015a
int attr divider 0x7f03015b
int attr dividerHorizontal 0x7f03015c
int attr dividerPadding 0x7f03015d
int attr dividerVertical 0x7f03015e
int attr dragDirection 0x7f03015f
int attr dragEdge 0x7f030160
int attr dragScale 0x7f030161
int attr dragThreshold 0x7f030162
int attr drawPath 0x7f030163
int attr drawableBottomCompat 0x7f030164
int attr drawableEndCompat 0x7f030165
int attr drawableLeftCompat 0x7f030166
int attr drawableRightCompat 0x7f030167
int attr drawableSize 0x7f030168
int attr drawableStartCompat 0x7f030169
int attr drawableTint 0x7f03016a
int attr drawableTintMode 0x7f03016b
int attr drawableTopCompat 0x7f03016c
int attr drawerArrowStyle 0x7f03016d
int attr dropDownListViewStyle 0x7f03016e
int attr dropdownListPreferredItemHeight 0x7f03016f
int attr duration 0x7f030170
int attr editTextBackground 0x7f030171
int attr editTextColor 0x7f030172
int attr editTextStyle 0x7f030173
int attr elevation 0x7f030174
int attr elevationOverlayColor 0x7f030175
int attr elevationOverlayEnabled 0x7f030176
int attr enableEdgeToEdge 0x7f030177
int attr endColor 0x7f030178
int attr endIconCheckable 0x7f030179
int attr endIconContentDescription 0x7f03017a
int attr endIconDrawable 0x7f03017b
int attr endIconMode 0x7f03017c
int attr endIconTint 0x7f03017d
int attr endIconTintMode 0x7f03017e
int attr enforceMaterialTheme 0x7f03017f
int attr enforceTextAppearance 0x7f030180
int attr ensureMinTouchTargetSize 0x7f030181
int attr errorContentDescription 0x7f030182
int attr errorEnabled 0x7f030183
int attr errorIconDrawable 0x7f030184
int attr errorIconTint 0x7f030185
int attr errorIconTintMode 0x7f030186
int attr errorTextAppearance 0x7f030187
int attr errorTextColor 0x7f030188
int attr expandActivityOverflowButtonDrawable 0x7f030189
int attr expanded 0x7f03018a
int attr expandedHintEnabled 0x7f03018b
int attr expandedTitleGravity 0x7f03018c
int attr expandedTitleMargin 0x7f03018d
int attr expandedTitleMarginBottom 0x7f03018e
int attr expandedTitleMarginEnd 0x7f03018f
int attr expandedTitleMarginStart 0x7f030190
int attr expandedTitleMarginTop 0x7f030191
int attr expandedTitleTextAppearance 0x7f030192
int attr extendMotionSpec 0x7f030193
int attr extendedFloatingActionButtonStyle 0x7f030194
int attr extraMultilineHeightEnabled 0x7f030195
int attr fabAlignmentMode 0x7f030196
int attr fabAnimationMode 0x7f030197
int attr fabCradleMargin 0x7f030198
int attr fabCradleRoundedCornerRadius 0x7f030199
int attr fabCradleVerticalOffset 0x7f03019a
int attr fabCustomSize 0x7f03019b
int attr fabSize 0x7f03019c
int attr fastScrollEnabled 0x7f03019d
int attr fastScrollHorizontalThumbDrawable 0x7f03019e
int attr fastScrollHorizontalTrackDrawable 0x7f03019f
int attr fastScrollVerticalThumbDrawable 0x7f0301a0
int attr fastScrollVerticalTrackDrawable 0x7f0301a1
int attr fghBackColor 0x7f0301a2
int attr fghBallSpeed 0x7f0301a3
int attr fghBlockHorizontalNum 0x7f0301a4
int attr fghLeftColor 0x7f0301a5
int attr fghMaskTextBottom 0x7f0301a6
int attr fghMaskTextSizeBottom 0x7f0301a7
int attr fghMaskTextSizeTop 0x7f0301a8
int attr fghMaskTextTop 0x7f0301a9
int attr fghMaskTextTopPull 0x7f0301aa
int attr fghMaskTextTopRelease 0x7f0301ab
int attr fghMiddleColor 0x7f0301ac
int attr fghRightColor 0x7f0301ad
int attr fghTextGameOver 0x7f0301ae
int attr fghTextLoading 0x7f0301af
int attr fghTextLoadingFailed 0x7f0301b0
int attr fghTextLoadingFinished 0x7f0301b1
int attr firstBaselineToTopHeight 0x7f0301b2
int attr flingVelocity 0x7f0301b3
int attr floatingActionButtonStyle 0x7f0301b4
int attr flow_firstHorizontalBias 0x7f0301b5
int attr flow_firstHorizontalStyle 0x7f0301b6
int attr flow_firstVerticalBias 0x7f0301b7
int attr flow_firstVerticalStyle 0x7f0301b8
int attr flow_horizontalAlign 0x7f0301b9
int attr flow_horizontalBias 0x7f0301ba
int attr flow_horizontalGap 0x7f0301bb
int attr flow_horizontalStyle 0x7f0301bc
int attr flow_lastHorizontalBias 0x7f0301bd
int attr flow_lastHorizontalStyle 0x7f0301be
int attr flow_lastVerticalBias 0x7f0301bf
int attr flow_lastVerticalStyle 0x7f0301c0
int attr flow_maxElementsWrap 0x7f0301c1
int attr flow_padding 0x7f0301c2
int attr flow_verticalAlign 0x7f0301c3
int attr flow_verticalBias 0x7f0301c4
int attr flow_verticalGap 0x7f0301c5
int attr flow_verticalStyle 0x7f0301c6
int attr flow_wrapMode 0x7f0301c7
int attr font 0x7f0301c8
int attr fontFamily 0x7f0301c9
int attr fontProviderAuthority 0x7f0301ca
int attr fontProviderCerts 0x7f0301cb
int attr fontProviderFetchStrategy 0x7f0301cc
int attr fontProviderFetchTimeout 0x7f0301cd
int attr fontProviderPackage 0x7f0301ce
int attr fontProviderQuery 0x7f0301cf
int attr fontProviderSystemFontFamily 0x7f0301d0
int attr fontStyle 0x7f0301d1
int attr fontType 0x7f0301d2
int attr fontVariationSettings 0x7f0301d3
int attr fontWeight 0x7f0301d4
int attr forceApplySystemWindowInsetTop 0x7f0301d5
int attr foregroundInsidePadding 0x7f0301d6
int attr framePosition 0x7f0301d7
int attr gapBetweenBars 0x7f0301d8
int attr gestureInsetBottomIgnored 0x7f0301d9
int attr goIcon 0x7f0301da
int attr haloColor 0x7f0301db
int attr haloRadius 0x7f0301dc
int attr has_del 0x7f0301dd
int attr headerLayout 0x7f0301de
int attr height 0x7f0301df
int attr helperText 0x7f0301e0
int attr helperTextEnabled 0x7f0301e1
int attr helperTextTextAppearance 0x7f0301e2
int attr helperTextTextColor 0x7f0301e3
int attr hideAnimationBehavior 0x7f0301e4
int attr hideMotionSpec 0x7f0301e5
int attr hideOnContentScroll 0x7f0301e6
int attr hideOnScroll 0x7f0301e7
int attr hint 0x7f0301e8
int attr hintAnimationEnabled 0x7f0301e9
int attr hintEnabled 0x7f0301ea
int attr hintTextAppearance 0x7f0301eb
int attr hintTextColor 0x7f0301ec
int attr hint_color 0x7f0301ed
int attr hint_text 0x7f0301ee
int attr homeAsUpIndicator 0x7f0301ef
int attr homeLayout 0x7f0301f0
int attr horizontalOffset 0x7f0301f1
int attr hoveredFocusedTranslationZ 0x7f0301f2
int attr icon 0x7f0301f3
int attr iconEndPadding 0x7f0301f4
int attr iconGravity 0x7f0301f5
int attr iconPadding 0x7f0301f6
int attr iconSize 0x7f0301f7
int attr iconStartPadding 0x7f0301f8
int attr iconTint 0x7f0301f9
int attr iconTintMode 0x7f0301fa
int attr icon_left 0x7f0301fb
int attr icon_right 0x7f0301fc
int attr iconifiedByDefault 0x7f0301fd
int attr imageButtonStyle 0x7f0301fe
int attr indeterminateAnimationType 0x7f0301ff
int attr indeterminateProgressStyle 0x7f030200
int attr indicatorColor 0x7f030201
int attr indicatorDirectionCircular 0x7f030202
int attr indicatorDirectionLinear 0x7f030203
int attr indicatorInset 0x7f030204
int attr indicatorSize 0x7f030205
int attr indicator_color 0x7f030206
int attr indicator_corners 0x7f030207
int attr indicator_gravity 0x7f030208
int attr indicator_width 0x7f030209
int attr initialActivityCount 0x7f03020a
int attr insetForeground 0x7f03020b
int attr isLightTheme 0x7f03020c
int attr isMaterialTheme 0x7f03020d
int attr isPermanent 0x7f03020e
int attr isb_clear_default_padding 0x7f03020f
int attr isb_indicator_color 0x7f030210
int attr isb_indicator_content_layout 0x7f030211
int attr isb_indicator_text_color 0x7f030212
int attr isb_indicator_text_size 0x7f030213
int attr isb_indicator_top_content_layout 0x7f030214
int attr isb_max 0x7f030215
int attr isb_min 0x7f030216
int attr isb_only_thumb_draggable 0x7f030217
int attr isb_progress 0x7f030218
int attr isb_progress_value_float 0x7f030219
int attr isb_r2l 0x7f03021a
int attr isb_seek_smoothly 0x7f03021b
int attr isb_show_indicator 0x7f03021c
int attr isb_show_thumb_text 0x7f03021d
int attr isb_show_tick_marks_type 0x7f03021e
int attr isb_show_tick_texts 0x7f03021f
int attr isb_thumb_adjust_auto 0x7f030220
int attr isb_thumb_color 0x7f030221
int attr isb_thumb_drawable 0x7f030222
int attr isb_thumb_size 0x7f030223
int attr isb_thumb_text_color 0x7f030224
int attr isb_tick_marks_color 0x7f030225
int attr isb_tick_marks_drawable 0x7f030226
int attr isb_tick_marks_ends_hide 0x7f030227
int attr isb_tick_marks_size 0x7f030228
int attr isb_tick_marks_swept_hide 0x7f030229
int attr isb_tick_texts_array 0x7f03022a
int attr isb_tick_texts_color 0x7f03022b
int attr isb_tick_texts_size 0x7f03022c
int attr isb_tick_texts_typeface 0x7f03022d
int attr isb_ticks_count 0x7f03022e
int attr isb_track_background_color 0x7f03022f
int attr isb_track_background_size 0x7f030230
int attr isb_track_progress_color 0x7f030231
int attr isb_track_progress_size 0x7f030232
int attr isb_track_rounded_corners 0x7f030233
int attr isb_user_seekable 0x7f030234
int attr itemBackground 0x7f030235
int attr itemFillColor 0x7f030236
int attr itemHorizontalPadding 0x7f030237
int attr itemHorizontalTranslationEnabled 0x7f030238
int attr itemIconPadding 0x7f030239
int attr itemIconSize 0x7f03023a
int attr itemIconTint 0x7f03023b
int attr itemMaxLines 0x7f03023c
int attr itemPadding 0x7f03023d
int attr itemRippleColor 0x7f03023e
int attr itemShapeAppearance 0x7f03023f
int attr itemShapeAppearanceOverlay 0x7f030240
int attr itemShapeFillColor 0x7f030241
int attr itemShapeInsetBottom 0x7f030242
int attr itemShapeInsetEnd 0x7f030243
int attr itemShapeInsetStart 0x7f030244
int attr itemShapeInsetTop 0x7f030245
int attr itemSpacing 0x7f030246
int attr itemStrokeColor 0x7f030247
int attr itemStrokeWidth 0x7f030248
int attr itemTextAppearance 0x7f030249
int attr itemTextAppearanceActive 0x7f03024a
int attr itemTextAppearanceInactive 0x7f03024b
int attr itemTextColor 0x7f03024c
int attr keyPositionType 0x7f03024d
int attr keyboardIcon 0x7f03024e
int attr keylines 0x7f03024f
int attr lStar 0x7f030250
int attr labelBehavior 0x7f030251
int attr labelStyle 0x7f030252
int attr labelVisibilityMode 0x7f030253
int attr lastBaselineToBottomHeight 0x7f030254
int attr layout 0x7f030255
int attr layoutDescription 0x7f030256
int attr layoutDuringTransition 0x7f030257
int attr layoutManager 0x7f030258
int attr layout_align 0x7f030259
int attr layout_anchor 0x7f03025a
int attr layout_anchorGravity 0x7f03025b
int attr layout_behavior 0x7f03025c
int attr layout_collapseMode 0x7f03025d
int attr layout_collapseParallaxMultiplier 0x7f03025e
int attr layout_constrainedHeight 0x7f03025f
int attr layout_constrainedWidth 0x7f030260
int attr layout_constraintBaseline_creator 0x7f030261
int attr layout_constraintBaseline_toBaselineOf 0x7f030262
int attr layout_constraintBottom_creator 0x7f030263
int attr layout_constraintBottom_toBottomOf 0x7f030264
int attr layout_constraintBottom_toTopOf 0x7f030265
int attr layout_constraintCircle 0x7f030266
int attr layout_constraintCircleAngle 0x7f030267
int attr layout_constraintCircleRadius 0x7f030268
int attr layout_constraintDimensionRatio 0x7f030269
int attr layout_constraintEnd_toEndOf 0x7f03026a
int attr layout_constraintEnd_toStartOf 0x7f03026b
int attr layout_constraintGuide_begin 0x7f03026c
int attr layout_constraintGuide_end 0x7f03026d
int attr layout_constraintGuide_percent 0x7f03026e
int attr layout_constraintHeight_default 0x7f03026f
int attr layout_constraintHeight_max 0x7f030270
int attr layout_constraintHeight_min 0x7f030271
int attr layout_constraintHeight_percent 0x7f030272
int attr layout_constraintHorizontal_bias 0x7f030273
int attr layout_constraintHorizontal_chainStyle 0x7f030274
int attr layout_constraintHorizontal_weight 0x7f030275
int attr layout_constraintLeft_creator 0x7f030276
int attr layout_constraintLeft_toLeftOf 0x7f030277
int attr layout_constraintLeft_toRightOf 0x7f030278
int attr layout_constraintRight_creator 0x7f030279
int attr layout_constraintRight_toLeftOf 0x7f03027a
int attr layout_constraintRight_toRightOf 0x7f03027b
int attr layout_constraintStart_toEndOf 0x7f03027c
int attr layout_constraintStart_toStartOf 0x7f03027d
int attr layout_constraintTag 0x7f03027e
int attr layout_constraintTop_creator 0x7f03027f
int attr layout_constraintTop_toBottomOf 0x7f030280
int attr layout_constraintTop_toTopOf 0x7f030281
int attr layout_constraintVertical_bias 0x7f030282
int attr layout_constraintVertical_chainStyle 0x7f030283
int attr layout_constraintVertical_weight 0x7f030284
int attr layout_constraintWidth_default 0x7f030285
int attr layout_constraintWidth_max 0x7f030286
int attr layout_constraintWidth_min 0x7f030287
int attr layout_constraintWidth_percent 0x7f030288
int attr layout_dodgeInsetEdges 0x7f030289
int attr layout_editor_absoluteX 0x7f03028a
int attr layout_editor_absoluteY 0x7f03028b
int attr layout_goneMarginBottom 0x7f03028c
int attr layout_goneMarginEnd 0x7f03028d
int attr layout_goneMarginLeft 0x7f03028e
int attr layout_goneMarginRight 0x7f03028f
int attr layout_goneMarginStart 0x7f030290
int attr layout_goneMarginTop 0x7f030291
int attr layout_insetEdge 0x7f030292
int attr layout_isConsecutive 0x7f030293
int attr layout_isNestedScroll 0x7f030294
int attr layout_isSink 0x7f030295
int attr layout_isSticky 0x7f030296
int attr layout_isTriggerScroll 0x7f030297
int attr layout_keyline 0x7f030298
int attr layout_optimizationLevel 0x7f030299
int attr layout_scrollChild 0x7f03029a
int attr layout_scrollFlags 0x7f03029b
int attr layout_scrollInterpolator 0x7f03029c
int attr layout_srlBackgroundColor 0x7f03029d
int attr layout_srlSpinnerStyle 0x7f03029e
int attr leftDrawable 0x7f03029f
int attr leftDrawableHeight 0x7f0302a0
int attr leftDrawableWidth 0x7f0302a1
int attr leftProgress 0x7f0302a2
int attr liftOnScroll 0x7f0302a3
int attr liftOnScrollTargetViewId 0x7f0302a4
int attr limitBoundsTo 0x7f0302a5
int attr lineHeight 0x7f0302a6
int attr lineSpacing 0x7f0302a7
int attr lineWidth 0x7f0302a8
int attr linearProgressIndicatorStyle 0x7f0302a9
int attr listChoiceBackgroundIndicator 0x7f0302aa
int attr listChoiceIndicatorMultipleAnimated 0x7f0302ab
int attr listChoiceIndicatorSingleAnimated 0x7f0302ac
int attr listDividerAlertDialog 0x7f0302ad
int attr listItemLayout 0x7f0302ae
int attr listLayout 0x7f0302af
int attr listMenuViewStyle 0x7f0302b0
int attr listPopupWindowStyle 0x7f0302b1
int attr listPreferredItemHeight 0x7f0302b2
int attr listPreferredItemHeightLarge 0x7f0302b3
int attr listPreferredItemHeightSmall 0x7f0302b4
int attr listPreferredItemPaddingEnd 0x7f0302b5
int attr listPreferredItemPaddingLeft 0x7f0302b6
int attr listPreferredItemPaddingRight 0x7f0302b7
int attr listPreferredItemPaddingStart 0x7f0302b8
int attr logo 0x7f0302b9
int attr logoDescription 0x7f0302ba
int attr marginHorizontal 0x7f0302bb
int attr marginVertical 0x7f0302bc
int attr materialAlertDialogBodyTextStyle 0x7f0302bd
int attr materialAlertDialogTheme 0x7f0302be
int attr materialAlertDialogTitleIconStyle 0x7f0302bf
int attr materialAlertDialogTitlePanelStyle 0x7f0302c0
int attr materialAlertDialogTitleTextStyle 0x7f0302c1
int attr materialButtonOutlinedStyle 0x7f0302c2
int attr materialButtonStyle 0x7f0302c3
int attr materialButtonToggleGroupStyle 0x7f0302c4
int attr materialCalendarDay 0x7f0302c5
int attr materialCalendarFullscreenTheme 0x7f0302c6
int attr materialCalendarHeaderCancelButton 0x7f0302c7
int attr materialCalendarHeaderConfirmButton 0x7f0302c8
int attr materialCalendarHeaderDivider 0x7f0302c9
int attr materialCalendarHeaderLayout 0x7f0302ca
int attr materialCalendarHeaderSelection 0x7f0302cb
int attr materialCalendarHeaderTitle 0x7f0302cc
int attr materialCalendarHeaderToggleButton 0x7f0302cd
int attr materialCalendarMonth 0x7f0302ce
int attr materialCalendarMonthNavigationButton 0x7f0302cf
int attr materialCalendarStyle 0x7f0302d0
int attr materialCalendarTheme 0x7f0302d1
int attr materialCalendarYearNavigationButton 0x7f0302d2
int attr materialCardViewStyle 0x7f0302d3
int attr materialCircleRadius 0x7f0302d4
int attr materialClockStyle 0x7f0302d5
int attr materialThemeOverlay 0x7f0302d6
int attr materialTimePickerStyle 0x7f0302d7
int attr materialTimePickerTheme 0x7f0302d8
int attr maxAcceleration 0x7f0302d9
int attr maxActionInlineWidth 0x7f0302da
int attr maxButtonHeight 0x7f0302db
int attr maxCharacterCount 0x7f0302dc
int attr maxHeight 0x7f0302dd
int attr maxImageSize 0x7f0302de
int attr maxLines 0x7f0302df
int attr maxVelocity 0x7f0302e0
int attr maxWidth 0x7f0302e1
int attr max_select 0x7f0302e2
int attr max_text 0x7f0302e3
int attr measureWithLargestChild 0x7f0302e4
int attr menu 0x7f0302e5
int attr menuGravity 0x7f0302e6
int attr mhPrimaryColor 0x7f0302e7
int attr mhScrollableWhenRefreshing 0x7f0302e8
int attr mhShadowColor 0x7f0302e9
int attr mhShadowRadius 0x7f0302ea
int attr mhShowBezierWave 0x7f0302eb
int attr minDistRequestDisallowParent 0x7f0302ec
int attr minHeight 0x7f0302ed
int attr minHideDelay 0x7f0302ee
int attr minSeparation 0x7f0302ef
int attr minTouchTargetSize 0x7f0302f0
int attr minWidth 0x7f0302f1
int attr mock_diagonalsColor 0x7f0302f2
int attr mock_label 0x7f0302f3
int attr mock_labelBackgroundColor 0x7f0302f4
int attr mock_labelColor 0x7f0302f5
int attr mock_showDiagonals 0x7f0302f6
int attr mock_showLabel 0x7f0302f7
int attr mode 0x7f0302f8
int attr motionDebug 0x7f0302f9
int attr motionDurationLong1 0x7f0302fa
int attr motionDurationLong2 0x7f0302fb
int attr motionDurationMedium1 0x7f0302fc
int attr motionDurationMedium2 0x7f0302fd
int attr motionDurationShort1 0x7f0302fe
int attr motionDurationShort2 0x7f0302ff
int attr motionEasingAccelerated 0x7f030300
int attr motionEasingDecelerated 0x7f030301
int attr motionEasingEmphasized 0x7f030302
int attr motionEasingLinear 0x7f030303
int attr motionEasingStandard 0x7f030304
int attr motionInterpolator 0x7f030305
int attr motionPath 0x7f030306
int attr motionPathRotate 0x7f030307
int attr motionProgress 0x7f030308
int attr motionStagger 0x7f030309
int attr motionTarget 0x7f03030a
int attr motion_postLayoutCollision 0x7f03030b
int attr motion_triggerOnCollision 0x7f03030c
int attr moveWhenScrollAtTop 0x7f03030d
int attr msvPrimaryColor 0x7f03030e
int attr msvViewportHeight 0x7f03030f
int attr multiChoiceItemLayout 0x7f030310
int attr nav_bar_color_bg 0x7f030311
int attr navigationContentDescription 0x7f030312
int attr navigationIcon 0x7f030313
int attr navigationIconTint 0x7f030314
int attr navigationMode 0x7f030315
int attr navigationRailStyle 0x7f030316
int attr navigationViewStyle 0x7f030317
int attr nestedScrollFlags 0x7f030318
int attr nestedScrollViewStyle 0x7f030319
int attr nestedScrollable 0x7f03031a
int attr number 0x7f03031b
int attr numericModifiers 0x7f03031c
int attr onCross 0x7f03031d
int attr onHide 0x7f03031e
int attr onNegativeCross 0x7f03031f
int attr onPositiveCross 0x7f030320
int attr onShow 0x7f030321
int attr onTouchUp 0x7f030322
int attr overDragMaxDistanceOfBottom 0x7f030323
int attr overDragMaxDistanceOfTop 0x7f030324
int attr overDragMode 0x7f030325
int attr overDragRate 0x7f030326
int attr overlapAnchor 0x7f030327
int attr overlay 0x7f030328
int attr paddingBottomNoButtons 0x7f030329
int attr paddingBottomSystemWindowInsets 0x7f03032a
int attr paddingEnd 0x7f03032b
int attr paddingLeftSystemWindowInsets 0x7f03032c
int attr paddingRightSystemWindowInsets 0x7f03032d
int attr paddingStart 0x7f03032e
int attr paddingTopNoTitle 0x7f03032f
int attr paddingTopSystemWindowInsets 0x7f030330
int attr panEnabled 0x7f030331
int attr panelBackground 0x7f030332
int attr panelMenuListTheme 0x7f030333
int attr panelMenuListWidth 0x7f030334
int attr passwordToggleContentDescription 0x7f030335
int attr passwordToggleDrawable 0x7f030336
int attr passwordToggleEnabled 0x7f030337
int attr passwordToggleTint 0x7f030338
int attr passwordToggleTintMode 0x7f030339
int attr pathMotionArc 0x7f03033a
int attr path_percent 0x7f03033b
int attr percentHeight 0x7f03033c
int attr percentWidth 0x7f03033d
int attr percentX 0x7f03033e
int attr percentY 0x7f03033f
int attr perpendicularPath_percent 0x7f030340
int attr phAccentColor 0x7f030341
int attr phPrimaryColor 0x7f030342
int attr pivotAnchor 0x7f030343
int attr placeholderText 0x7f030344
int attr placeholderTextAppearance 0x7f030345
int attr placeholderTextColor 0x7f030346
int attr placeholder_emptyVisibility 0x7f030347
int attr popupMenuBackground 0x7f030348
int attr popupMenuStyle 0x7f030349
int attr popupTheme 0x7f03034a
int attr popupWindowStyle 0x7f03034b
int attr prefixText 0x7f03034c
int attr prefixTextAppearance 0x7f03034d
int attr prefixTextColor 0x7f03034e
int attr preserveIconSpacing 0x7f03034f
int attr pressedTranslationZ 0x7f030350
int attr progressBarPadding 0x7f030351
int attr progressBarStyle 0x7f030352
int attr queryBackground 0x7f030353
int attr queryHint 0x7f030354
int attr queryPatterns 0x7f030355
int attr quickScaleEnabled 0x7f030356
int attr radioButtonStyle 0x7f030357
int attr rangeFillColor 0x7f030358
int attr ratingBarStyle 0x7f030359
int attr ratingBarStyleIndicator 0x7f03035a
int attr ratingBarStyleSmall 0x7f03035b
int attr recyclerViewStyle 0x7f03035c
int attr region_heightLessThan 0x7f03035d
int attr region_heightMoreThan 0x7f03035e
int attr region_widthLessThan 0x7f03035f
int attr region_widthMoreThan 0x7f030360
int attr reverseLayout 0x7f030361
int attr rightDrawable 0x7f030362
int attr rightDrawableHeight 0x7f030363
int attr rightDrawableWidth 0x7f030364
int attr rightProgress 0x7f030365
int attr rippleColor 0x7f030366
int attr round 0x7f030367
int attr roundPercent 0x7f030368
int attr saturation 0x7f030369
int attr scrimAnimationDuration 0x7f03036a
int attr scrimBackground 0x7f03036b
int attr scrimVisibleHeightTrigger 0x7f03036c
int attr searchHintIcon 0x7f03036d
int attr searchIcon 0x7f03036e
int attr searchViewStyle 0x7f03036f
int attr seekBackgroundColor 0x7f030370
int attr seekBarStyle 0x7f030371
int attr seekProgressColor 0x7f030372
int attr selectableItemBackground 0x7f030373
int attr selectableItemBackgroundBorderless 0x7f030374
int attr selectionRequired 0x7f030375
int attr selectorSize 0x7f030376
int attr shapeAppearance 0x7f030377
int attr shapeAppearanceLargeComponent 0x7f030378
int attr shapeAppearanceMediumComponent 0x7f030379
int attr shapeAppearanceOverlay 0x7f03037a
int attr shapeAppearanceSmallComponent 0x7f03037b
int attr shhDropHeight 0x7f03037c
int attr shhEnableFadeAnimation 0x7f03037d
int attr shhLineWidth 0x7f03037e
int attr shhText 0x7f03037f
int attr shortcutMatchRequired 0x7f030380
int attr showAnimationBehavior 0x7f030381
int attr showAsAction 0x7f030382
int attr showDelay 0x7f030383
int attr showDividers 0x7f030384
int attr showMotionSpec 0x7f030385
int attr showPaths 0x7f030386
int attr showText 0x7f030387
int attr showTitle 0x7f030388
int attr shrinkMotionSpec 0x7f030389
int attr singleChoiceItemLayout 0x7f03038a
int attr singleLine 0x7f03038b
int attr singleSelection 0x7f03038c
int attr sizePercent 0x7f03038d
int attr sliderStyle 0x7f03038e
int attr snackbarButtonStyle 0x7f03038f
int attr snackbarStyle 0x7f030390
int attr snackbarTextViewStyle 0x7f030391
int attr spanCount 0x7f030392
int attr spinBars 0x7f030393
int attr spinnerDropDownItemStyle 0x7f030394
int attr spinnerStyle 0x7f030395
int attr splitTrack 0x7f030396
int attr src 0x7f030397
int attr srcCompat 0x7f030398
int attr srlAccentColor 0x7f030399
int attr srlAnimatingColor 0x7f03039a
int attr srlClassicsSpinnerStyle 0x7f03039b
int attr srlDisableContentWhenLoading 0x7f03039c
int attr srlDisableContentWhenRefresh 0x7f03039d
int attr srlDragRate 0x7f03039e
int attr srlDrawableArrow 0x7f03039f
int attr srlDrawableArrowSize 0x7f0303a0
int attr srlDrawableMarginRight 0x7f0303a1
int attr srlDrawableProgress 0x7f0303a2
int attr srlDrawableProgressSize 0x7f0303a3
int attr srlDrawableSize 0x7f0303a4
int attr srlEnableAutoLoadMore 0x7f0303a5
int attr srlEnableClipFooterWhenFixedBehind 0x7f0303a6
int attr srlEnableClipHeaderWhenFixedBehind 0x7f0303a7
int attr srlEnableFooterFollowWhenLoadFinished 0x7f0303a8
int attr srlEnableFooterTranslationContent 0x7f0303a9
int attr srlEnableHeaderTranslationContent 0x7f0303aa
int attr srlEnableHorizontalDrag 0x7f0303ab
int attr srlEnableLastTime 0x7f0303ac
int attr srlEnableLoadMore 0x7f0303ad
int attr srlEnableLoadMoreWhenContentNotFull 0x7f0303ae
int attr srlEnableNestedScrolling 0x7f0303af
int attr srlEnableOverScrollBounce 0x7f0303b0
int attr srlEnableOverScrollDrag 0x7f0303b1
int attr srlEnablePreviewInEditMode 0x7f0303b2
int attr srlEnablePullToCloseTwoLevel 0x7f0303b3
int attr srlEnablePureScrollMode 0x7f0303b4
int attr srlEnableRefresh 0x7f0303b5
int attr srlEnableScrollContentWhenLoaded 0x7f0303b6
int attr srlEnableScrollContentWhenRefreshed 0x7f0303b7
int attr srlEnableTwoLevel 0x7f0303b8
int attr srlFinishDuration 0x7f0303b9
int attr srlFixedFooterViewId 0x7f0303ba
int attr srlFixedHeaderViewId 0x7f0303bb
int attr srlFloorDuration 0x7f0303bc
int attr srlFloorRage 0x7f0303bd
int attr srlFooterHeight 0x7f0303be
int attr srlFooterInsetStart 0x7f0303bf
int attr srlFooterMaxDragRate 0x7f0303c0
int attr srlFooterTranslationViewId 0x7f0303c1
int attr srlFooterTriggerRate 0x7f0303c2
int attr srlHeaderHeight 0x7f0303c3
int attr srlHeaderInsetStart 0x7f0303c4
int attr srlHeaderMaxDragRate 0x7f0303c5
int attr srlHeaderTranslationViewId 0x7f0303c6
int attr srlHeaderTriggerRate 0x7f0303c7
int attr srlMaxRage 0x7f0303c8
int attr srlNormalColor 0x7f0303c9
int attr srlPrimaryColor 0x7f0303ca
int attr srlReboundDuration 0x7f0303cb
int attr srlRefreshRage 0x7f0303cc
int attr srlScrollableWhenRefreshing 0x7f0303cd
int attr srlShadowColor 0x7f0303ce
int attr srlShadowRadius 0x7f0303cf
int attr srlShowBezierWave 0x7f0303d0
int attr srlTextSizeTime 0x7f0303d1
int attr srlTextSizeTitle 0x7f0303d2
int attr srlTextTimeMarginTop 0x7f0303d3
int attr stackFromEnd 0x7f0303d4
int attr staggered 0x7f0303d5
int attr startColor 0x7f0303d6
int attr startIconCheckable 0x7f0303d7
int attr startIconContentDescription 0x7f0303d8
int attr startIconDrawable 0x7f0303d9
int attr startIconTint 0x7f0303da
int attr startIconTintMode 0x7f0303db
int attr state_above_anchor 0x7f0303dc
int attr state_collapsed 0x7f0303dd
int attr state_collapsible 0x7f0303de
int attr state_dragged 0x7f0303df
int attr state_liftable 0x7f0303e0
int attr state_lifted 0x7f0303e1
int attr statusBarBackground 0x7f0303e2
int attr statusBarForeground 0x7f0303e3
int attr statusBarScrim 0x7f0303e4
int attr stickyOffset 0x7f0303e5
int attr strokeColor 0x7f0303e6
int attr strokeWidth 0x7f0303e7
int attr subMenuArrow 0x7f0303e8
int attr submitBackground 0x7f0303e9
int attr subtitle 0x7f0303ea
int attr subtitleCentered 0x7f0303eb
int attr subtitleTextAppearance 0x7f0303ec
int attr subtitleTextColor 0x7f0303ed
int attr subtitleTextStyle 0x7f0303ee
int attr suffixText 0x7f0303ef
int attr suffixTextAppearance 0x7f0303f0
int attr suffixTextColor 0x7f0303f1
int attr suggestionRowLayout 0x7f0303f2
int attr switchMinWidth 0x7f0303f3
int attr switchPadding 0x7f0303f4
int attr switchStyle 0x7f0303f5
int attr switchTextAppearance 0x7f0303f6
int attr tabBackground 0x7f0303f7
int attr tabContentStart 0x7f0303f8
int attr tabGravity 0x7f0303f9
int attr tabIconTint 0x7f0303fa
int attr tabIconTintMode 0x7f0303fb
int attr tabIndicator 0x7f0303fc
int attr tabIndicatorAnimationDuration 0x7f0303fd
int attr tabIndicatorAnimationMode 0x7f0303fe
int attr tabIndicatorColor 0x7f0303ff
int attr tabIndicatorFullWidth 0x7f030400
int attr tabIndicatorGravity 0x7f030401
int attr tabIndicatorHeight 0x7f030402
int attr tabInlineLabel 0x7f030403
int attr tabMaxWidth 0x7f030404
int attr tabMinWidth 0x7f030405
int attr tabMode 0x7f030406
int attr tabPadding 0x7f030407
int attr tabPaddingBottom 0x7f030408
int attr tabPaddingEnd 0x7f030409
int attr tabPaddingStart 0x7f03040a
int attr tabPaddingTop 0x7f03040b
int attr tabRippleColor 0x7f03040c
int attr tabSelectedTextColor 0x7f03040d
int attr tabStyle 0x7f03040e
int attr tabTextAppearance 0x7f03040f
int attr tabTextColor 0x7f030410
int attr tabUnboundedRipple 0x7f030411
int attr tab_height 0x7f030412
int attr tab_margin 0x7f030413
int attr tab_mode 0x7f030414
int attr tag_gravity 0x7f030415
int attr targetId 0x7f030416
int attr telltales_tailColor 0x7f030417
int attr telltales_tailScale 0x7f030418
int attr telltales_velocityMode 0x7f030419
int attr textAllCaps 0x7f03041a
int attr textAppearanceBody1 0x7f03041b
int attr textAppearanceBody2 0x7f03041c
int attr textAppearanceButton 0x7f03041d
int attr textAppearanceCaption 0x7f03041e
int attr textAppearanceHeadline1 0x7f03041f
int attr textAppearanceHeadline2 0x7f030420
int attr textAppearanceHeadline3 0x7f030421
int attr textAppearanceHeadline4 0x7f030422
int attr textAppearanceHeadline5 0x7f030423
int attr textAppearanceHeadline6 0x7f030424
int attr textAppearanceLargePopupMenu 0x7f030425
int attr textAppearanceLineHeightEnabled 0x7f030426
int attr textAppearanceListItem 0x7f030427
int attr textAppearanceListItemSecondary 0x7f030428
int attr textAppearanceListItemSmall 0x7f030429
int attr textAppearanceOverline 0x7f03042a
int attr textAppearancePopupMenuHeader 0x7f03042b
int attr textAppearanceSearchResultSubtitle 0x7f03042c
int attr textAppearanceSearchResultTitle 0x7f03042d
int attr textAppearanceSmallPopupMenu 0x7f03042e
int attr textAppearanceSubtitle1 0x7f03042f
int attr textAppearanceSubtitle2 0x7f030430
int attr textColorAlertDialogListItem 0x7f030431
int attr textColorSearchUrl 0x7f030432
int attr textEndPadding 0x7f030433
int attr textInputLayoutFocusedRectEnabled 0x7f030434
int attr textInputStyle 0x7f030435
int attr textLocale 0x7f030436
int attr textStartPadding 0x7f030437
int attr text_color 0x7f030438
int attr text_size 0x7f030439
int attr thPrimaryColor 0x7f03043a
int attr theme 0x7f03043b
int attr themeLineHeight 0x7f03043c
int attr thickness 0x7f03043d
int attr thumbColor 0x7f03043e
int attr thumbElevation 0x7f03043f
int attr thumbRadius 0x7f030440
int attr thumbStrokeColor 0x7f030441
int attr thumbStrokeWidth 0x7f030442
int attr thumbTextPadding 0x7f030443
int attr thumbTint 0x7f030444
int attr thumbTintMode 0x7f030445
int attr tickColor 0x7f030446
int attr tickColorActive 0x7f030447
int attr tickColorInactive 0x7f030448
int attr tickMark 0x7f030449
int attr tickMarkTint 0x7f03044a
int attr tickMarkTintMode 0x7f03044b
int attr tickVisible 0x7f03044c
int attr tileBackgroundColor 0x7f03044d
int attr tint 0x7f03044e
int attr tintMode 0x7f03044f
int attr title 0x7f030450
int attr titleCentered 0x7f030451
int attr titleCollapseMode 0x7f030452
int attr titleEnabled 0x7f030453
int attr titleMargin 0x7f030454
int attr titleMarginBottom 0x7f030455
int attr titleMarginEnd 0x7f030456
int attr titleMarginStart 0x7f030457
int attr titleMarginTop 0x7f030458
int attr titleMargins 0x7f030459
int attr titleTextAppearance 0x7f03045a
int attr titleTextColor 0x7f03045b
int attr titleTextStyle 0x7f03045c
int attr title_color 0x7f03045d
int attr title_icon 0x7f03045e
int attr title_left 0x7f03045f
int attr title_mid 0x7f030460
int attr title_right 0x7f030461
int attr title_right_color 0x7f030462
int attr toolbarId 0x7f030463
int attr toolbarNavigationButtonStyle 0x7f030464
int attr toolbarStyle 0x7f030465
int attr tooltipForegroundColor 0x7f030466
int attr tooltipFrameBackground 0x7f030467
int attr tooltipStyle 0x7f030468
int attr tooltipText 0x7f030469
int attr topDelta 0x7f03046a
int attr topDrawable 0x7f03046b
int attr topDrawableHeight 0x7f03046c
int attr topDrawableWidth 0x7f03046d
int attr touchAnchorId 0x7f03046e
int attr touchAnchorSide 0x7f03046f
int attr touchRegionId 0x7f030470
int attr track 0x7f030471
int attr trackColor 0x7f030472
int attr trackColorActive 0x7f030473
int attr trackColorInactive 0x7f030474
int attr trackCornerRadius 0x7f030475
int attr trackHeight 0x7f030476
int attr trackThickness 0x7f030477
int attr trackTint 0x7f030478
int attr trackTintMode 0x7f030479
int attr transitionDisable 0x7f03047a
int attr transitionEasing 0x7f03047b
int attr transitionFlags 0x7f03047c
int attr transitionPathRotate 0x7f03047d
int attr transitionShapeAppearance 0x7f03047e
int attr triggerId 0x7f03047f
int attr triggerReceiver 0x7f030480
int attr triggerSlack 0x7f030481
int attr ttcIndex 0x7f030482
int attr tv_size 0x7f030483
int attr tv_tile 0x7f030484
int attr tv_time 0x7f030485
int attr tv_title 0x7f030486
int attr useCompatPadding 0x7f030487
int attr useMaterialThemeColors 0x7f030488
int attr value 0x7f030489
int attr values 0x7f03048a
int attr verticalOffset 0x7f03048b
int attr viewInflaterClass 0x7f03048c
int attr visibilityMode 0x7f03048d
int attr voiceIcon 0x7f03048e
int attr warmth 0x7f03048f
int attr watch_show 0x7f030490
int attr waveDecay 0x7f030491
int attr waveOffset 0x7f030492
int attr wavePeriod 0x7f030493
int attr waveShape 0x7f030494
int attr waveVariesBy 0x7f030495
int attr wheelview_dividerColor 0x7f030496
int attr wheelview_dividerWidth 0x7f030497
int attr wheelview_gravity 0x7f030498
int attr wheelview_lineSpacingMultiplier 0x7f030499
int attr wheelview_textColorCenter 0x7f03049a
int attr wheelview_textColorOut 0x7f03049b
int attr wheelview_textSize 0x7f03049c
int attr windowActionBar 0x7f03049d
int attr windowActionBarOverlay 0x7f03049e
int attr windowActionModeOverlay 0x7f03049f
int attr windowFixedHeightMajor 0x7f0304a0
int attr windowFixedHeightMinor 0x7f0304a1
int attr windowFixedWidthMajor 0x7f0304a2
int attr windowFixedWidthMinor 0x7f0304a3
int attr windowMinWidthMajor 0x7f0304a4
int attr windowMinWidthMinor 0x7f0304a5
int attr windowNoTitle 0x7f0304a6
int attr wshAccentColor 0x7f0304a7
int attr wshPrimaryColor 0x7f0304a8
int attr wshShadowColor 0x7f0304a9
int attr wshShadowRadius 0x7f0304aa
int attr yearSelectedStyle 0x7f0304ab
int attr yearStyle 0x7f0304ac
int attr yearTodayStyle 0x7f0304ad
int attr zoomEnabled 0x7f0304ae
int attr zpb_backgroundColor 0x7f0304af
int attr zpb_duration 0x7f0304b0
int attr zpb_progressColor 0x7f0304b1
int attr zpb_radius 0x7f0304b2
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_config_actionMenuItemAllCaps 0x7f040001
int bool mtrl_btn_textappearance_all_caps 0x7f040002
int color ColorF3ECE4 0x7f050000
int color ColorFCF5FA 0x7f050001
int color _xpopup_content_color 0x7f050002
int color _xpopup_dark_color 0x7f050003
int color _xpopup_light_color 0x7f050004
int color _xpopup_list_dark_divider 0x7f050005
int color _xpopup_list_divider 0x7f050006
int color _xpopup_title_color 0x7f050007
int color _xpopup_white_color 0x7f050008
int color abc_background_cache_hint_selector_material_dark 0x7f050009
int color abc_background_cache_hint_selector_material_light 0x7f05000a
int color abc_btn_colored_borderless_text_material 0x7f05000b
int color abc_btn_colored_text_material 0x7f05000c
int color abc_color_highlight_material 0x7f05000d
int color abc_decor_view_status_guard 0x7f05000e
int color abc_decor_view_status_guard_light 0x7f05000f
int color abc_hint_foreground_material_dark 0x7f050010
int color abc_hint_foreground_material_light 0x7f050011
int color abc_primary_text_disable_only_material_dark 0x7f050012
int color abc_primary_text_disable_only_material_light 0x7f050013
int color abc_primary_text_material_dark 0x7f050014
int color abc_primary_text_material_light 0x7f050015
int color abc_search_url_text 0x7f050016
int color abc_search_url_text_normal 0x7f050017
int color abc_search_url_text_pressed 0x7f050018
int color abc_search_url_text_selected 0x7f050019
int color abc_secondary_text_material_dark 0x7f05001a
int color abc_secondary_text_material_light 0x7f05001b
int color abc_tint_btn_checkable 0x7f05001c
int color abc_tint_default 0x7f05001d
int color abc_tint_edittext 0x7f05001e
int color abc_tint_seek_thumb 0x7f05001f
int color abc_tint_spinner 0x7f050020
int color abc_tint_switch_track 0x7f050021
int color accent_material_dark 0x7f050022
int color accent_material_light 0x7f050023
int color androidx_core_ripple_material_light 0x7f050024
int color androidx_core_secondary_text_default_material_light 0x7f050025
int color background_floating_material_dark 0x7f050026
int color background_floating_material_light 0x7f050027
int color background_material_dark 0x7f050028
int color background_material_light 0x7f050029
int color bg_btn_blue 0x7f05002a
int color bg_gray_color 0x7f05002b
int color bg_yellow_color 0x7f05002c
int color black 0x7f05002d
int color blue_text 0x7f05002e
int color bright_foreground_disabled_material_dark 0x7f05002f
int color bright_foreground_disabled_material_light 0x7f050030
int color bright_foreground_inverse_material_dark 0x7f050031
int color bright_foreground_inverse_material_light 0x7f050032
int color bright_foreground_material_dark 0x7f050033
int color bright_foreground_material_light 0x7f050034
int color button_material_dark 0x7f050035
int color button_material_light 0x7f050036
int color call_notification_answer_color 0x7f050037
int color call_notification_decline_color 0x7f050038
int color cardview_dark_background 0x7f050039
int color cardview_light_background 0x7f05003a
int color cardview_shadow_end_color 0x7f05003b
int color cardview_shadow_start_color 0x7f05003c
int color checkbox_themeable_attribute_color 0x7f05003d
int color color008FEB 0x7f05003e
int color color0E0E0E 0x7f05003f
int color color0F0F0F 0x7f050040
int color color0f0f0f 0x7f050041
int color color10FF8535 0x7f050042
int color color111111 0x7f050043
int color color14FFFFFF 0x7f050044
int color color17AA1C 0x7f050045
int color color191919 0x7f050046
int color color1A1A1A 0x7f050047
int color color1C0B3A 0x7f050048
int color color1F1F1F 0x7f050049
int color color281744 0x7f05004a
int color color2B2B2B 0x7f05004b
int color color333333 0x7f05004c
int color color38197A 0x7f05004d
int color color3C3C3C 0x7f05004e
int color color3D3D3D 0x7f05004f
int color color3F3F3F 0x7f050050
int color color40B4B4B4 0x7f050051
int color color444444 0x7f050052
int color color4990E2 0x7f050053
int color color4A4A4A 0x7f050054
int color color4F4F4F 0x7f050055
int color color50000000 0x7f050056
int color color515151 0x7f050057
int color color5378AB 0x7f050058
int color color55000000 0x7f050059
int color color576F6F6F 0x7f05005a
int color color577699 0x7f05005b
int color color5A5A5A 0x7f05005c
int color color5B5B5B 0x7f05005d
int color color66000000 0x7f05005e
int color color666666 0x7f05005f
int color color6d6d6d 0x7f050060
int color color727272 0x7f050061
int color color73FF6602 0x7f050062
int color color787878 0x7f050063
int color color7C7B7B 0x7f050064
int color color7F4A90E2 0x7f050065
int color color7F7F7F 0x7f050066
int color color80000000 0x7f050067
int color color808080 0x7f050068
int color color818181 0x7f050069
int color color838383 0x7f05006a
int color color848484 0x7f05006b
int color color89A4C9 0x7f05006c
int color color8C8C8C 0x7f05006d
int color color8DBEC4 0x7f05006e
int color color8F8F8F 0x7f05006f
int color color959595 0x7f050070
int color color959FA6 0x7f050071
int color color969696 0x7f050072
int color color979797 0x7f050073
int color color99000000 0x7f050074
int color color999999 0x7f050075
int color color9EA8EA 0x7f050076
int color colorA4A4A4 0x7f050077
int color colorABABAB 0x7f050078
int color colorAccent 0x7f050079
int color colorB0B0B0 0x7f05007a
int color colorB9B9B9 0x7f05007b
int color colorC3C3C3 0x7f05007c
int color colorCC9900 0x7f05007d
int color colorCCCCCC 0x7f05007e
int color colorCDCDCD 0x7f05007f
int color colorCECECE 0x7f050080
int color colorD0D0D0 0x7f050081
int color colorD4D4D4 0x7f050082
int color colorD8D8D8 0x7f050083
int color colorD9D9D9 0x7f050084
int color colorDCDCDC 0x7f050085
int color colorDDA66F 0x7f050086
int color colorDDDDDD 0x7f050087
int color colorE1E1E1 0x7f050088
int color colorE3E3EC 0x7f050089
int color colorE5E5E5 0x7f05008a
int color colorE9E9E9 0x7f05008b
int color colorECB408 0x7f05008c
int color colorEDEDEE 0x7f05008d
int color colorEECC76 0x7f05008e
int color colorEF0000 0x7f05008f
int color colorEF5F00 0x7f050090
int color colorF0F0F0 0x7f050091
int color colorF3F3F3 0x7f050092
int color colorF3F3F5 0x7f050093
int color colorF4F4F4 0x7f050094
int color colorF66100 0x7f050095
int color colorF6F5F4 0x7f050096
int color colorF6F6F6 0x7f050097
int color colorF7F4EA 0x7f050098
int color colorF7F7F7 0x7f050099
int color colorF8F8F8 0x7f05009a
int color colorFAFAFA 0x7f05009b
int color colorFEDAE4 0x7f05009c
int color colorFEFBFB 0x7f05009d
int color colorFF1010 0x7f05009e
int color colorFF1313 0x7f05009f
int color colorFF3B30 0x7f0500a0
int color colorFF444444 0x7f0500a1
int color colorFF4F4F 0x7f0500a2
int color colorFF5252 0x7f0500a3
int color colorFF552E 0x7f0500a4
int color colorFF6602 0x7f0500a5
int color colorFF8535 0x7f0500a6
int color colorFF8E44 0x7f0500a7
int color colorFFA467 0x7f0500a8
int color colorFFAD44 0x7f0500a9
int color colorFFB63F 0x7f0500aa
int color colorFFD571 0x7f0500ab
int color colorFFE89C 0x7f0500ac
int color colorPrimary 0x7f0500ad
int color colorPrimaryDark 0x7f0500ae
int color contents_text 0x7f0500af
int color cosmos_black 0x7f0500b0
int color cosmos_default 0x7f0500b1
int color cosmos_line_default 0x7f0500b2
int color cosmos_topic 0x7f0500b3
int color cosmos_view 0x7f0500b4
int color cr_bg_color 0x7f0500b5
int color cropBackground 0x7f0500b6
int color crop_shadow_color 0x7f0500b7
int color crop_shadow_wp_color 0x7f0500b8
int color crop_wp_markers 0x7f0500b9
int color defaultColor 0x7f0500ba
int color design_bottom_navigation_shadow_color 0x7f0500bb
int color design_box_stroke_color 0x7f0500bc
int color design_dark_default_color_background 0x7f0500bd
int color design_dark_default_color_error 0x7f0500be
int color design_dark_default_color_on_background 0x7f0500bf
int color design_dark_default_color_on_error 0x7f0500c0
int color design_dark_default_color_on_primary 0x7f0500c1
int color design_dark_default_color_on_secondary 0x7f0500c2
int color design_dark_default_color_on_surface 0x7f0500c3
int color design_dark_default_color_primary 0x7f0500c4
int color design_dark_default_color_primary_dark 0x7f0500c5
int color design_dark_default_color_primary_variant 0x7f0500c6
int color design_dark_default_color_secondary 0x7f0500c7
int color design_dark_default_color_secondary_variant 0x7f0500c8
int color design_dark_default_color_surface 0x7f0500c9
int color design_default_color_background 0x7f0500ca
int color design_default_color_error 0x7f0500cb
int color design_default_color_on_background 0x7f0500cc
int color design_default_color_on_error 0x7f0500cd
int color design_default_color_on_primary 0x7f0500ce
int color design_default_color_on_secondary 0x7f0500cf
int color design_default_color_on_surface 0x7f0500d0
int color design_default_color_primary 0x7f0500d1
int color design_default_color_primary_dark 0x7f0500d2
int color design_default_color_primary_variant 0x7f0500d3
int color design_default_color_secondary 0x7f0500d4
int color design_default_color_secondary_variant 0x7f0500d5
int color design_default_color_surface 0x7f0500d6
int color design_error 0x7f0500d7
int color design_fab_shadow_end_color 0x7f0500d8
int color design_fab_shadow_mid_color 0x7f0500d9
int color design_fab_shadow_start_color 0x7f0500da
int color design_fab_stroke_end_inner_color 0x7f0500db
int color design_fab_stroke_end_outer_color 0x7f0500dc
int color design_fab_stroke_top_inner_color 0x7f0500dd
int color design_fab_stroke_top_outer_color 0x7f0500de
int color design_icon_tint 0x7f0500df
int color design_snackbar_background_color 0x7f0500e0
int color dim_foreground_disabled_material_dark 0x7f0500e1
int color dim_foreground_disabled_material_light 0x7f0500e2
int color dim_foreground_material_dark 0x7f0500e3
int color dim_foreground_material_light 0x7f0500e4
int color edit_bg_color 0x7f0500e5
int color edit_line 0x7f0500e6
int color encode_view 0x7f0500e7
int color error_color_material_dark 0x7f0500e8
int color error_color_material_light 0x7f0500e9
int color error_red 0x7f0500ea
int color foreground_material_dark 0x7f0500eb
int color foreground_material_light 0x7f0500ec
int color gray_color 0x7f0500ed
int color gray_text 0x7f0500ee
int color green_text 0x7f0500ef
int color highlighted_text_material_dark 0x7f0500f0
int color highlighted_text_material_light 0x7f0500f1
int color hint_color 0x7f0500f2
int color index_text_defult 0x7f0500f3
int color isb_selector_tick_marks_color 0x7f0500f4
int color isb_selector_tick_texts_color 0x7f0500f5
int color light_gray_f2 0x7f0500f6
int color light_gray_f9 0x7f0500f7
int color light_gray_text 0x7f0500f8
int color light_red 0x7f0500f9
int color ma10_color 0x7f0500fa
int color ma20_color 0x7f0500fb
int color ma30_color 0x7f0500fc
int color ma5_color 0x7f0500fd
int color material_blue_grey_800 0x7f0500fe
int color material_blue_grey_900 0x7f0500ff
int color material_blue_grey_950 0x7f050100
int color material_cursor_color 0x7f050101
int color material_deep_teal_200 0x7f050102
int color material_deep_teal_500 0x7f050103
int color material_grey_100 0x7f050104
int color material_grey_300 0x7f050105
int color material_grey_50 0x7f050106
int color material_grey_600 0x7f050107
int color material_grey_800 0x7f050108
int color material_grey_850 0x7f050109
int color material_grey_900 0x7f05010a
int color material_on_background_disabled 0x7f05010b
int color material_on_background_emphasis_high_type 0x7f05010c
int color material_on_background_emphasis_medium 0x7f05010d
int color material_on_primary_disabled 0x7f05010e
int color material_on_primary_emphasis_high_type 0x7f05010f
int color material_on_primary_emphasis_medium 0x7f050110
int color material_on_surface_disabled 0x7f050111
int color material_on_surface_emphasis_high_type 0x7f050112
int color material_on_surface_emphasis_medium 0x7f050113
int color material_on_surface_stroke 0x7f050114
int color material_slider_active_tick_marks_color 0x7f050115
int color material_slider_active_track_color 0x7f050116
int color material_slider_halo_color 0x7f050117
int color material_slider_inactive_tick_marks_color 0x7f050118
int color material_slider_inactive_track_color 0x7f050119
int color material_slider_thumb_color 0x7f05011a
int color material_timepicker_button_background 0x7f05011b
int color material_timepicker_button_stroke 0x7f05011c
int color material_timepicker_clock_text_color 0x7f05011d
int color material_timepicker_clockface 0x7f05011e
int color material_timepicker_modebutton_tint 0x7f05011f
int color message_count_bg_color 0x7f050120
int color mtrl_btn_bg_color_selector 0x7f050121
int color mtrl_btn_ripple_color 0x7f050122
int color mtrl_btn_stroke_color_selector 0x7f050123
int color mtrl_btn_text_btn_bg_color_selector 0x7f050124
int color mtrl_btn_text_btn_ripple_color 0x7f050125
int color mtrl_btn_text_color_disabled 0x7f050126
int color mtrl_btn_text_color_selector 0x7f050127
int color mtrl_btn_transparent_bg_color 0x7f050128
int color mtrl_calendar_item_stroke_color 0x7f050129
int color mtrl_calendar_selected_range 0x7f05012a
int color mtrl_card_view_foreground 0x7f05012b
int color mtrl_card_view_ripple 0x7f05012c
int color mtrl_chip_background_color 0x7f05012d
int color mtrl_chip_close_icon_tint 0x7f05012e
int color mtrl_chip_surface_color 0x7f05012f
int color mtrl_chip_text_color 0x7f050130
int color mtrl_choice_chip_background_color 0x7f050131
int color mtrl_choice_chip_ripple_color 0x7f050132
int color mtrl_choice_chip_text_color 0x7f050133
int color mtrl_error 0x7f050134
int color mtrl_fab_bg_color_selector 0x7f050135
int color mtrl_fab_icon_text_color_selector 0x7f050136
int color mtrl_fab_ripple_color 0x7f050137
int color mtrl_filled_background_color 0x7f050138
int color mtrl_filled_icon_tint 0x7f050139
int color mtrl_filled_stroke_color 0x7f05013a
int color mtrl_indicator_text_color 0x7f05013b
int color mtrl_navigation_bar_colored_item_tint 0x7f05013c
int color mtrl_navigation_bar_colored_ripple_color 0x7f05013d
int color mtrl_navigation_bar_item_tint 0x7f05013e
int color mtrl_navigation_bar_ripple_color 0x7f05013f
int color mtrl_navigation_item_background_color 0x7f050140
int color mtrl_navigation_item_icon_tint 0x7f050141
int color mtrl_navigation_item_text_color 0x7f050142
int color mtrl_on_primary_text_btn_text_color_selector 0x7f050143
int color mtrl_on_surface_ripple_color 0x7f050144
int color mtrl_outlined_icon_tint 0x7f050145
int color mtrl_outlined_stroke_color 0x7f050146
int color mtrl_popupmenu_overlay_color 0x7f050147
int color mtrl_scrim_color 0x7f050148
int color mtrl_tabs_colored_ripple_color 0x7f050149
int color mtrl_tabs_icon_color_selector 0x7f05014a
int color mtrl_tabs_icon_color_selector_colored 0x7f05014b
int color mtrl_tabs_legacy_text_color_selector 0x7f05014c
int color mtrl_tabs_ripple_color 0x7f05014d
int color mtrl_text_btn_text_color_selector 0x7f05014e
int color mtrl_textinput_default_box_stroke_color 0x7f05014f
int color mtrl_textinput_disabled_color 0x7f050150
int color mtrl_textinput_filled_box_default_background_color 0x7f050151
int color mtrl_textinput_focused_box_stroke_color 0x7f050152
int color mtrl_textinput_hovered_box_stroke_color 0x7f050153
int color notification_action_color_filter 0x7f050154
int color notification_icon_bg_color 0x7f050155
int color notification_material_background_media_default_color 0x7f050156
int color pickerview_bgColor_default 0x7f050157
int color pickerview_bgColor_overlay 0x7f050158
int color pickerview_bg_topbar 0x7f050159
int color pickerview_timebtn_nor 0x7f05015a
int color pickerview_timebtn_pre 0x7f05015b
int color pickerview_topbar_title 0x7f05015c
int color pickerview_wheelview_textcolor_center 0x7f05015d
int color pickerview_wheelview_textcolor_divider 0x7f05015e
int color pickerview_wheelview_textcolor_out 0x7f05015f
int color possible_result_points 0x7f050160
int color primary_dark_material_dark 0x7f050161
int color primary_dark_material_light 0x7f050162
int color primary_material_dark 0x7f050163
int color primary_material_light 0x7f050164
int color primary_text_default_material_dark 0x7f050165
int color primary_text_default_material_light 0x7f050166
int color primary_text_disabled_material_dark 0x7f050167
int color primary_text_disabled_material_light 0x7f050168
int color radiobutton_themeable_attribute_color 0x7f050169
int color react 0x7f05016a
int color red_text 0x7f05016b
int color result_minor_text 0x7f05016c
int color result_points 0x7f05016d
int color result_text 0x7f05016e
int color result_view 0x7f05016f
int color ripple_material_dark 0x7f050170
int color ripple_material_light 0x7f050171
int color scanLineColor 0x7f050172
int color secondary_text_default_material_dark 0x7f050173
int color secondary_text_default_material_light 0x7f050174
int color secondary_text_disabled_material_dark 0x7f050175
int color secondary_text_disabled_material_light 0x7f050176
int color status_text 0x7f050177
int color switch_thumb_disabled_material_dark 0x7f050178
int color switch_thumb_disabled_material_light 0x7f050179
int color switch_thumb_material_dark 0x7f05017a
int color switch_thumb_material_light 0x7f05017b
int color switch_thumb_normal_material_dark 0x7f05017c
int color switch_thumb_normal_material_light 0x7f05017d
int color test_mtrl_calendar_day 0x7f05017e
int color test_mtrl_calendar_day_selected 0x7f05017f
int color text_collect_blue 0x7f050180
int color tooltip_background_dark 0x7f050181
int color tooltip_background_light 0x7f050182
int color transparent 0x7f050183
int color viewfinder_mask 0x7f050184
int color white 0x7f050185
int color yellow 0x7f050186
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_star_big 0x7f06003b
int dimen abc_star_medium 0x7f06003c
int dimen abc_star_small 0x7f06003d
int dimen abc_switch_padding 0x7f06003e
int dimen abc_text_size_body_1_material 0x7f06003f
int dimen abc_text_size_body_2_material 0x7f060040
int dimen abc_text_size_button_material 0x7f060041
int dimen abc_text_size_caption_material 0x7f060042
int dimen abc_text_size_display_1_material 0x7f060043
int dimen abc_text_size_display_2_material 0x7f060044
int dimen abc_text_size_display_3_material 0x7f060045
int dimen abc_text_size_display_4_material 0x7f060046
int dimen abc_text_size_headline_material 0x7f060047
int dimen abc_text_size_large_material 0x7f060048
int dimen abc_text_size_medium_material 0x7f060049
int dimen abc_text_size_menu_header_material 0x7f06004a
int dimen abc_text_size_menu_material 0x7f06004b
int dimen abc_text_size_small_material 0x7f06004c
int dimen abc_text_size_subhead_material 0x7f06004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004e
int dimen abc_text_size_title_material 0x7f06004f
int dimen abc_text_size_title_material_toolbar 0x7f060050
int dimen action_bar_size 0x7f060051
int dimen activity_horizontal_margin 0x7f060052
int dimen activity_vertical_margin 0x7f060053
int dimen appcompat_dialog_background_inset 0x7f060054
int dimen cardview_compat_inset_shadow 0x7f060055
int dimen cardview_default_elevation 0x7f060056
int dimen cardview_default_radius 0x7f060057
int dimen clock_face_margin_start 0x7f060058
int dimen compat_button_inset_horizontal_material 0x7f060059
int dimen compat_button_inset_vertical_material 0x7f06005a
int dimen compat_button_padding_horizontal_material 0x7f06005b
int dimen compat_button_padding_vertical_material 0x7f06005c
int dimen compat_control_corner_material 0x7f06005d
int dimen compat_notification_large_icon_max_height 0x7f06005e
int dimen compat_notification_large_icon_max_width 0x7f06005f
int dimen crop_indicator_size 0x7f060060
int dimen crop_min_side 0x7f060061
int dimen crop_touch_tolerance 0x7f060062
int dimen default_dimension 0x7f060063
int dimen design_appbar_elevation 0x7f060064
int dimen design_bottom_navigation_active_item_max_width 0x7f060065
int dimen design_bottom_navigation_active_item_min_width 0x7f060066
int dimen design_bottom_navigation_active_text_size 0x7f060067
int dimen design_bottom_navigation_elevation 0x7f060068
int dimen design_bottom_navigation_height 0x7f060069
int dimen design_bottom_navigation_icon_size 0x7f06006a
int dimen design_bottom_navigation_item_max_width 0x7f06006b
int dimen design_bottom_navigation_item_min_width 0x7f06006c
int dimen design_bottom_navigation_label_padding 0x7f06006d
int dimen design_bottom_navigation_margin 0x7f06006e
int dimen design_bottom_navigation_shadow_height 0x7f06006f
int dimen design_bottom_navigation_text_size 0x7f060070
int dimen design_bottom_sheet_elevation 0x7f060071
int dimen design_bottom_sheet_modal_elevation 0x7f060072
int dimen design_bottom_sheet_peek_height_min 0x7f060073
int dimen design_fab_border_width 0x7f060074
int dimen design_fab_elevation 0x7f060075
int dimen design_fab_image_size 0x7f060076
int dimen design_fab_size_mini 0x7f060077
int dimen design_fab_size_normal 0x7f060078
int dimen design_fab_translation_z_hovered_focused 0x7f060079
int dimen design_fab_translation_z_pressed 0x7f06007a
int dimen design_navigation_elevation 0x7f06007b
int dimen design_navigation_icon_padding 0x7f06007c
int dimen design_navigation_icon_size 0x7f06007d
int dimen design_navigation_item_horizontal_padding 0x7f06007e
int dimen design_navigation_item_icon_padding 0x7f06007f
int dimen design_navigation_max_width 0x7f060080
int dimen design_navigation_padding_bottom 0x7f060081
int dimen design_navigation_separator_vertical_padding 0x7f060082
int dimen design_snackbar_action_inline_max_width 0x7f060083
int dimen design_snackbar_action_text_color_alpha 0x7f060084
int dimen design_snackbar_background_corner_radius 0x7f060085
int dimen design_snackbar_elevation 0x7f060086
int dimen design_snackbar_extra_spacing_horizontal 0x7f060087
int dimen design_snackbar_max_width 0x7f060088
int dimen design_snackbar_min_width 0x7f060089
int dimen design_snackbar_padding_horizontal 0x7f06008a
int dimen design_snackbar_padding_vertical 0x7f06008b
int dimen design_snackbar_padding_vertical_2lines 0x7f06008c
int dimen design_snackbar_text_size 0x7f06008d
int dimen design_tab_max_width 0x7f06008e
int dimen design_tab_scrollable_min_width 0x7f06008f
int dimen design_tab_text_size 0x7f060090
int dimen design_tab_text_size_2line 0x7f060091
int dimen design_textinput_caption_translate_y 0x7f060092
int dimen disabled_alpha_material_dark 0x7f060093
int dimen disabled_alpha_material_light 0x7f060094
int dimen dp_1 0x7f060095
int dimen dp_10 0x7f060096
int dimen dp_100 0x7f060097
int dimen dp_11 0x7f060098
int dimen dp_12 0x7f060099
int dimen dp_120 0x7f06009a
int dimen dp_13 0x7f06009b
int dimen dp_14 0x7f06009c
int dimen dp_15 0x7f06009d
int dimen dp_150 0x7f06009e
int dimen dp_16 0x7f06009f
int dimen dp_17 0x7f0600a0
int dimen dp_18 0x7f0600a1
int dimen dp_19 0x7f0600a2
int dimen dp_2 0x7f0600a3
int dimen dp_20 0x7f0600a4
int dimen dp_200 0x7f0600a5
int dimen dp_21 0x7f0600a6
int dimen dp_22 0x7f0600a7
int dimen dp_23 0x7f0600a8
int dimen dp_24 0x7f0600a9
int dimen dp_25 0x7f0600aa
int dimen dp_26 0x7f0600ab
int dimen dp_27 0x7f0600ac
int dimen dp_270 0x7f0600ad
int dimen dp_3 0x7f0600ae
int dimen dp_30 0x7f0600af
int dimen dp_300 0x7f0600b0
int dimen dp_32 0x7f0600b1
int dimen dp_320 0x7f0600b2
int dimen dp_33 0x7f0600b3
int dimen dp_34 0x7f0600b4
int dimen dp_35 0x7f0600b5
int dimen dp_350 0x7f0600b6
int dimen dp_36 0x7f0600b7
int dimen dp_38 0x7f0600b8
int dimen dp_4 0x7f0600b9
int dimen dp_40 0x7f0600ba
int dimen dp_42 0x7f0600bb
int dimen dp_44 0x7f0600bc
int dimen dp_45 0x7f0600bd
int dimen dp_5 0x7f0600be
int dimen dp_50 0x7f0600bf
int dimen dp_52 0x7f0600c0
int dimen dp_54 0x7f0600c1
int dimen dp_6 0x7f0600c2
int dimen dp_60 0x7f0600c3
int dimen dp_65 0x7f0600c4
int dimen dp_66 0x7f0600c5
int dimen dp_67 0x7f0600c6
int dimen dp_7 0x7f0600c7
int dimen dp_70 0x7f0600c8
int dimen dp_8 0x7f0600c9
int dimen dp_80 0x7f0600ca
int dimen dp_82 0x7f0600cb
int dimen dp_83 0x7f0600cc
int dimen dp_85 0x7f0600cd
int dimen dp_88 0x7f0600ce
int dimen dp_9 0x7f0600cf
int dimen dp_90 0x7f0600d0
int dimen fastscroll_default_thickness 0x7f0600d1
int dimen fastscroll_margin 0x7f0600d2
int dimen fastscroll_minimum_range 0x7f0600d3
int dimen filter_show_height 0x7f0600d4
int dimen height_30dp 0x7f0600d5
int dimen height_40dp 0x7f0600d6
int dimen highlight_alpha_material_colored 0x7f0600d7
int dimen highlight_alpha_material_dark 0x7f0600d8
int dimen highlight_alpha_material_light 0x7f0600d9
int dimen hint_alpha_material_dark 0x7f0600da
int dimen hint_alpha_material_light 0x7f0600db
int dimen hint_pressed_alpha_material_dark 0x7f0600dc
int dimen hint_pressed_alpha_material_light 0x7f0600dd
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f0600de
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f0600df
int dimen item_touch_helper_swipe_escape_velocity 0x7f0600e0
int dimen material_bottom_sheet_max_width 0x7f0600e1
int dimen material_clock_display_padding 0x7f0600e2
int dimen material_clock_face_margin_top 0x7f0600e3
int dimen material_clock_hand_center_dot_radius 0x7f0600e4
int dimen material_clock_hand_padding 0x7f0600e5
int dimen material_clock_hand_stroke_width 0x7f0600e6
int dimen material_clock_number_text_size 0x7f0600e7
int dimen material_clock_period_toggle_height 0x7f0600e8
int dimen material_clock_period_toggle_margin_left 0x7f0600e9
int dimen material_clock_period_toggle_width 0x7f0600ea
int dimen material_clock_size 0x7f0600eb
int dimen material_cursor_inset_bottom 0x7f0600ec
int dimen material_cursor_inset_top 0x7f0600ed
int dimen material_cursor_width 0x7f0600ee
int dimen material_emphasis_disabled 0x7f0600ef
int dimen material_emphasis_high_type 0x7f0600f0
int dimen material_emphasis_medium 0x7f0600f1
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f0600f2
int dimen material_filled_edittext_font_1_3_padding_top 0x7f0600f3
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f0600f4
int dimen material_filled_edittext_font_2_0_padding_top 0x7f0600f5
int dimen material_font_1_3_box_collapsed_padding_top 0x7f0600f6
int dimen material_font_2_0_box_collapsed_padding_top 0x7f0600f7
int dimen material_helper_text_default_padding_top 0x7f0600f8
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f0600f9
int dimen material_helper_text_font_1_3_padding_top 0x7f0600fa
int dimen material_input_text_to_prefix_suffix_padding 0x7f0600fb
int dimen material_text_view_test_line_height 0x7f0600fc
int dimen material_text_view_test_line_height_override 0x7f0600fd
int dimen material_textinput_default_width 0x7f0600fe
int dimen material_textinput_max_width 0x7f0600ff
int dimen material_textinput_min_width 0x7f060100
int dimen material_time_picker_minimum_screen_height 0x7f060101
int dimen material_time_picker_minimum_screen_width 0x7f060102
int dimen material_timepicker_dialog_buttons_margin_top 0x7f060103
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f060104
int dimen mtrl_alert_dialog_background_inset_end 0x7f060105
int dimen mtrl_alert_dialog_background_inset_start 0x7f060106
int dimen mtrl_alert_dialog_background_inset_top 0x7f060107
int dimen mtrl_alert_dialog_picker_background_inset 0x7f060108
int dimen mtrl_badge_horizontal_edge_offset 0x7f060109
int dimen mtrl_badge_long_text_horizontal_padding 0x7f06010a
int dimen mtrl_badge_radius 0x7f06010b
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f06010c
int dimen mtrl_badge_text_size 0x7f06010d
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f06010e
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f06010f
int dimen mtrl_badge_with_text_radius 0x7f060110
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f060111
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f060112
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f060113
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f060114
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f060115
int dimen mtrl_bottomappbar_height 0x7f060116
int dimen mtrl_btn_corner_radius 0x7f060117
int dimen mtrl_btn_dialog_btn_min_width 0x7f060118
int dimen mtrl_btn_disabled_elevation 0x7f060119
int dimen mtrl_btn_disabled_z 0x7f06011a
int dimen mtrl_btn_elevation 0x7f06011b
int dimen mtrl_btn_focused_z 0x7f06011c
int dimen mtrl_btn_hovered_z 0x7f06011d
int dimen mtrl_btn_icon_btn_padding_left 0x7f06011e
int dimen mtrl_btn_icon_padding 0x7f06011f
int dimen mtrl_btn_inset 0x7f060120
int dimen mtrl_btn_letter_spacing 0x7f060121
int dimen mtrl_btn_max_width 0x7f060122
int dimen mtrl_btn_padding_bottom 0x7f060123
int dimen mtrl_btn_padding_left 0x7f060124
int dimen mtrl_btn_padding_right 0x7f060125
int dimen mtrl_btn_padding_top 0x7f060126
int dimen mtrl_btn_pressed_z 0x7f060127
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f060128
int dimen mtrl_btn_stroke_size 0x7f060129
int dimen mtrl_btn_text_btn_icon_padding 0x7f06012a
int dimen mtrl_btn_text_btn_padding_left 0x7f06012b
int dimen mtrl_btn_text_btn_padding_right 0x7f06012c
int dimen mtrl_btn_text_size 0x7f06012d
int dimen mtrl_btn_z 0x7f06012e
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f06012f
int dimen mtrl_calendar_action_height 0x7f060130
int dimen mtrl_calendar_action_padding 0x7f060131
int dimen mtrl_calendar_bottom_padding 0x7f060132
int dimen mtrl_calendar_content_padding 0x7f060133
int dimen mtrl_calendar_day_corner 0x7f060134
int dimen mtrl_calendar_day_height 0x7f060135
int dimen mtrl_calendar_day_horizontal_padding 0x7f060136
int dimen mtrl_calendar_day_today_stroke 0x7f060137
int dimen mtrl_calendar_day_vertical_padding 0x7f060138
int dimen mtrl_calendar_day_width 0x7f060139
int dimen mtrl_calendar_days_of_week_height 0x7f06013a
int dimen mtrl_calendar_dialog_background_inset 0x7f06013b
int dimen mtrl_calendar_header_content_padding 0x7f06013c
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f06013d
int dimen mtrl_calendar_header_divider_thickness 0x7f06013e
int dimen mtrl_calendar_header_height 0x7f06013f
int dimen mtrl_calendar_header_height_fullscreen 0x7f060140
int dimen mtrl_calendar_header_selection_line_height 0x7f060141
int dimen mtrl_calendar_header_text_padding 0x7f060142
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f060143
int dimen mtrl_calendar_header_toggle_margin_top 0x7f060144
int dimen mtrl_calendar_landscape_header_width 0x7f060145
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f060146
int dimen mtrl_calendar_month_horizontal_padding 0x7f060147
int dimen mtrl_calendar_month_vertical_padding 0x7f060148
int dimen mtrl_calendar_navigation_bottom_padding 0x7f060149
int dimen mtrl_calendar_navigation_height 0x7f06014a
int dimen mtrl_calendar_navigation_top_padding 0x7f06014b
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f06014c
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f06014d
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f06014e
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f06014f
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f060150
int dimen mtrl_calendar_text_input_padding_top 0x7f060151
int dimen mtrl_calendar_title_baseline_to_top 0x7f060152
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f060153
int dimen mtrl_calendar_year_corner 0x7f060154
int dimen mtrl_calendar_year_height 0x7f060155
int dimen mtrl_calendar_year_horizontal_padding 0x7f060156
int dimen mtrl_calendar_year_vertical_padding 0x7f060157
int dimen mtrl_calendar_year_width 0x7f060158
int dimen mtrl_card_checked_icon_margin 0x7f060159
int dimen mtrl_card_checked_icon_size 0x7f06015a
int dimen mtrl_card_corner_radius 0x7f06015b
int dimen mtrl_card_dragged_z 0x7f06015c
int dimen mtrl_card_elevation 0x7f06015d
int dimen mtrl_card_spacing 0x7f06015e
int dimen mtrl_chip_pressed_translation_z 0x7f06015f
int dimen mtrl_chip_text_size 0x7f060160
int dimen mtrl_edittext_rectangle_top_offset 0x7f060161
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f060162
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f060163
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f060164
int dimen mtrl_extended_fab_bottom_padding 0x7f060165
int dimen mtrl_extended_fab_corner_radius 0x7f060166
int dimen mtrl_extended_fab_disabled_elevation 0x7f060167
int dimen mtrl_extended_fab_disabled_translation_z 0x7f060168
int dimen mtrl_extended_fab_elevation 0x7f060169
int dimen mtrl_extended_fab_end_padding 0x7f06016a
int dimen mtrl_extended_fab_end_padding_icon 0x7f06016b
int dimen mtrl_extended_fab_icon_size 0x7f06016c
int dimen mtrl_extended_fab_icon_text_spacing 0x7f06016d
int dimen mtrl_extended_fab_min_height 0x7f06016e
int dimen mtrl_extended_fab_min_width 0x7f06016f
int dimen mtrl_extended_fab_start_padding 0x7f060170
int dimen mtrl_extended_fab_start_padding_icon 0x7f060171
int dimen mtrl_extended_fab_top_padding 0x7f060172
int dimen mtrl_extended_fab_translation_z_base 0x7f060173
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f060174
int dimen mtrl_extended_fab_translation_z_pressed 0x7f060175
int dimen mtrl_fab_elevation 0x7f060176
int dimen mtrl_fab_min_touch_target 0x7f060177
int dimen mtrl_fab_translation_z_hovered_focused 0x7f060178
int dimen mtrl_fab_translation_z_pressed 0x7f060179
int dimen mtrl_high_ripple_default_alpha 0x7f06017a
int dimen mtrl_high_ripple_focused_alpha 0x7f06017b
int dimen mtrl_high_ripple_hovered_alpha 0x7f06017c
int dimen mtrl_high_ripple_pressed_alpha 0x7f06017d
int dimen mtrl_large_touch_target 0x7f06017e
int dimen mtrl_low_ripple_default_alpha 0x7f06017f
int dimen mtrl_low_ripple_focused_alpha 0x7f060180
int dimen mtrl_low_ripple_hovered_alpha 0x7f060181
int dimen mtrl_low_ripple_pressed_alpha 0x7f060182
int dimen mtrl_min_touch_target_size 0x7f060183
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f060184
int dimen mtrl_navigation_bar_item_default_margin 0x7f060185
int dimen mtrl_navigation_elevation 0x7f060186
int dimen mtrl_navigation_item_horizontal_padding 0x7f060187
int dimen mtrl_navigation_item_icon_padding 0x7f060188
int dimen mtrl_navigation_item_icon_size 0x7f060189
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f06018a
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f06018b
int dimen mtrl_navigation_rail_active_text_size 0x7f06018c
int dimen mtrl_navigation_rail_compact_width 0x7f06018d
int dimen mtrl_navigation_rail_default_width 0x7f06018e
int dimen mtrl_navigation_rail_elevation 0x7f06018f
int dimen mtrl_navigation_rail_icon_margin 0x7f060190
int dimen mtrl_navigation_rail_icon_size 0x7f060191
int dimen mtrl_navigation_rail_margin 0x7f060192
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f060193
int dimen mtrl_navigation_rail_text_size 0x7f060194
int dimen mtrl_progress_circular_inset 0x7f060195
int dimen mtrl_progress_circular_inset_extra_small 0x7f060196
int dimen mtrl_progress_circular_inset_medium 0x7f060197
int dimen mtrl_progress_circular_inset_small 0x7f060198
int dimen mtrl_progress_circular_radius 0x7f060199
int dimen mtrl_progress_circular_size 0x7f06019a
int dimen mtrl_progress_circular_size_extra_small 0x7f06019b
int dimen mtrl_progress_circular_size_medium 0x7f06019c
int dimen mtrl_progress_circular_size_small 0x7f06019d
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f06019e
int dimen mtrl_progress_circular_track_thickness_medium 0x7f06019f
int dimen mtrl_progress_circular_track_thickness_small 0x7f0601a0
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f0601a1
int dimen mtrl_progress_track_thickness 0x7f0601a2
int dimen mtrl_shape_corner_size_large_component 0x7f0601a3
int dimen mtrl_shape_corner_size_medium_component 0x7f0601a4
int dimen mtrl_shape_corner_size_small_component 0x7f0601a5
int dimen mtrl_slider_halo_radius 0x7f0601a6
int dimen mtrl_slider_label_padding 0x7f0601a7
int dimen mtrl_slider_label_radius 0x7f0601a8
int dimen mtrl_slider_label_square_side 0x7f0601a9
int dimen mtrl_slider_thumb_elevation 0x7f0601aa
int dimen mtrl_slider_thumb_radius 0x7f0601ab
int dimen mtrl_slider_track_height 0x7f0601ac
int dimen mtrl_slider_track_side_padding 0x7f0601ad
int dimen mtrl_slider_track_top 0x7f0601ae
int dimen mtrl_slider_widget_height 0x7f0601af
int dimen mtrl_snackbar_action_text_color_alpha 0x7f0601b0
int dimen mtrl_snackbar_background_corner_radius 0x7f0601b1
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f0601b2
int dimen mtrl_snackbar_margin 0x7f0601b3
int dimen mtrl_snackbar_message_margin_horizontal 0x7f0601b4
int dimen mtrl_snackbar_padding_horizontal 0x7f0601b5
int dimen mtrl_switch_thumb_elevation 0x7f0601b6
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0601b7
int dimen mtrl_textinput_box_corner_radius_small 0x7f0601b8
int dimen mtrl_textinput_box_label_cutout_padding 0x7f0601b9
int dimen mtrl_textinput_box_stroke_width_default 0x7f0601ba
int dimen mtrl_textinput_box_stroke_width_focused 0x7f0601bb
int dimen mtrl_textinput_counter_margin_start 0x7f0601bc
int dimen mtrl_textinput_end_icon_margin_start 0x7f0601bd
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f0601be
int dimen mtrl_textinput_start_icon_margin_end 0x7f0601bf
int dimen mtrl_toolbar_default_height 0x7f0601c0
int dimen mtrl_tooltip_arrowSize 0x7f0601c1
int dimen mtrl_tooltip_cornerSize 0x7f0601c2
int dimen mtrl_tooltip_minHeight 0x7f0601c3
int dimen mtrl_tooltip_minWidth 0x7f0601c4
int dimen mtrl_tooltip_padding 0x7f0601c5
int dimen mtrl_transition_shared_axis_slide_distance 0x7f0601c6
int dimen notification_action_icon_size 0x7f0601c7
int dimen notification_action_text_size 0x7f0601c8
int dimen notification_big_circle_margin 0x7f0601c9
int dimen notification_content_margin_start 0x7f0601ca
int dimen notification_large_icon_height 0x7f0601cb
int dimen notification_large_icon_width 0x7f0601cc
int dimen notification_main_column_padding_top 0x7f0601cd
int dimen notification_media_narrow_margin 0x7f0601ce
int dimen notification_right_icon_size 0x7f0601cf
int dimen notification_right_side_padding_top 0x7f0601d0
int dimen notification_small_icon_background_padding 0x7f0601d1
int dimen notification_small_icon_size_as_large 0x7f0601d2
int dimen notification_subtext_size 0x7f0601d3
int dimen notification_top_pad 0x7f0601d4
int dimen notification_top_pad_large_text 0x7f0601d5
int dimen pickerview_textsize 0x7f0601d6
int dimen pickerview_topbar_btn_textsize 0x7f0601d7
int dimen pickerview_topbar_height 0x7f0601d8
int dimen pickerview_topbar_padding 0x7f0601d9
int dimen pickerview_topbar_title_textsize 0x7f0601da
int dimen preview_margin 0x7f0601db
int dimen shadow_margin 0x7f0601dc
int dimen size_20sp 0x7f0601dd
int dimen sp_10 0x7f0601de
int dimen sp_11 0x7f0601df
int dimen sp_12 0x7f0601e0
int dimen sp_13 0x7f0601e1
int dimen sp_14 0x7f0601e2
int dimen sp_15 0x7f0601e3
int dimen sp_16 0x7f0601e4
int dimen sp_17 0x7f0601e5
int dimen sp_18 0x7f0601e6
int dimen sp_20 0x7f0601e7
int dimen sp_30 0x7f0601e8
int dimen sp_6 0x7f0601e9
int dimen subtitle_corner_radius 0x7f0601ea
int dimen subtitle_outline_width 0x7f0601eb
int dimen subtitle_shadow_offset 0x7f0601ec
int dimen subtitle_shadow_radius 0x7f0601ed
int dimen test_mtrl_calendar_day_cornerSize 0x7f0601ee
int dimen test_navigation_bar_active_item_max_width 0x7f0601ef
int dimen test_navigation_bar_active_item_min_width 0x7f0601f0
int dimen test_navigation_bar_active_text_size 0x7f0601f1
int dimen test_navigation_bar_elevation 0x7f0601f2
int dimen test_navigation_bar_height 0x7f0601f3
int dimen test_navigation_bar_icon_size 0x7f0601f4
int dimen test_navigation_bar_item_max_width 0x7f0601f5
int dimen test_navigation_bar_item_min_width 0x7f0601f6
int dimen test_navigation_bar_label_padding 0x7f0601f7
int dimen test_navigation_bar_shadow_height 0x7f0601f8
int dimen test_navigation_bar_text_size 0x7f0601f9
int dimen toolBarHeight 0x7f0601fa
int dimen tooltip_corner_radius 0x7f0601fb
int dimen tooltip_horizontal_padding 0x7f0601fc
int dimen tooltip_margin 0x7f0601fd
int dimen tooltip_precise_anchor_extra_offset 0x7f0601fe
int dimen tooltip_precise_anchor_threshold 0x7f0601ff
int dimen tooltip_vertical_padding 0x7f060200
int dimen tooltip_y_offset_non_touch 0x7f060201
int dimen tooltip_y_offset_touch 0x7f060202
int dimen wp_selector_dash_length 0x7f060203
int dimen wp_selector_off_length 0x7f060204
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070007
int drawable abc_action_bar_item_background_material 0x7f070008
int drawable abc_btn_borderless_material 0x7f070009
int drawable abc_btn_check_material 0x7f07000a
int drawable abc_btn_check_material_anim 0x7f07000b
int drawable abc_btn_check_to_on_mtrl_000 0x7f07000c
int drawable abc_btn_check_to_on_mtrl_015 0x7f07000d
int drawable abc_btn_colored_material 0x7f07000e
int drawable abc_btn_default_mtrl_shape 0x7f07000f
int drawable abc_btn_radio_material 0x7f070010
int drawable abc_btn_radio_material_anim 0x7f070011
int drawable abc_btn_radio_to_on_mtrl_000 0x7f070012
int drawable abc_btn_radio_to_on_mtrl_015 0x7f070013
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f070014
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f070015
int drawable abc_cab_background_internal_bg 0x7f070016
int drawable abc_cab_background_top_material 0x7f070017
int drawable abc_cab_background_top_mtrl_alpha 0x7f070018
int drawable abc_control_background_material 0x7f070019
int drawable abc_dialog_material_background 0x7f07001a
int drawable abc_edit_text_material 0x7f07001b
int drawable abc_ic_ab_back_material 0x7f07001c
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f07001d
int drawable abc_ic_clear_material 0x7f07001e
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f07001f
int drawable abc_ic_go_search_api_material 0x7f070020
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f070021
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f070022
int drawable abc_ic_menu_overflow_material 0x7f070023
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f070024
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f070025
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070026
int drawable abc_ic_search_api_material 0x7f070027
int drawable abc_ic_voice_search_api_material 0x7f070028
int drawable abc_item_background_holo_dark 0x7f070029
int drawable abc_item_background_holo_light 0x7f07002a
int drawable abc_list_divider_material 0x7f07002b
int drawable abc_list_divider_mtrl_alpha 0x7f07002c
int drawable abc_list_focused_holo 0x7f07002d
int drawable abc_list_longpressed_holo 0x7f07002e
int drawable abc_list_pressed_holo_dark 0x7f07002f
int drawable abc_list_pressed_holo_light 0x7f070030
int drawable abc_list_selector_background_transition_holo_dark 0x7f070031
int drawable abc_list_selector_background_transition_holo_light 0x7f070032
int drawable abc_list_selector_disabled_holo_dark 0x7f070033
int drawable abc_list_selector_disabled_holo_light 0x7f070034
int drawable abc_list_selector_holo_dark 0x7f070035
int drawable abc_list_selector_holo_light 0x7f070036
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070037
int drawable abc_popup_background_mtrl_mult 0x7f070038
int drawable abc_ratingbar_indicator_material 0x7f070039
int drawable abc_ratingbar_material 0x7f07003a
int drawable abc_ratingbar_small_material 0x7f07003b
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f07003c
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f07003d
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f07003e
int drawable abc_scrubber_primary_mtrl_alpha 0x7f07003f
int drawable abc_scrubber_track_mtrl_alpha 0x7f070040
int drawable abc_seekbar_thumb_material 0x7f070041
int drawable abc_seekbar_tick_mark_material 0x7f070042
int drawable abc_seekbar_track_material 0x7f070043
int drawable abc_spinner_mtrl_am_alpha 0x7f070044
int drawable abc_spinner_textfield_background_material 0x7f070045
int drawable abc_star_black_48dp 0x7f070046
int drawable abc_star_half_black_48dp 0x7f070047
int drawable abc_switch_thumb_material 0x7f070048
int drawable abc_switch_track_mtrl_alpha 0x7f070049
int drawable abc_tab_indicator_material 0x7f07004a
int drawable abc_tab_indicator_mtrl_alpha 0x7f07004b
int drawable abc_text_cursor_material 0x7f07004c
int drawable abc_text_select_handle_left_mtrl 0x7f07004d
int drawable abc_text_select_handle_middle_mtrl 0x7f07004e
int drawable abc_text_select_handle_right_mtrl 0x7f07004f
int drawable abc_textfield_activated_mtrl_alpha 0x7f070050
int drawable abc_textfield_default_mtrl_alpha 0x7f070051
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070052
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070053
int drawable abc_textfield_search_material 0x7f070054
int drawable abc_vector_test 0x7f070055
int drawable actionbar_translucent 0x7f070056
int drawable avd_hide_password 0x7f070057
int drawable avd_show_password 0x7f070058
int drawable bg_deep_gray_13 0x7f070059
int drawable bg_deep_gray_60 0x7f07005a
int drawable bg_gray_13 0x7f07005b
int drawable bg_gray_6 0x7f07005c
int drawable bg_gray_60 0x7f07005d
int drawable bg_light_gray_6 0x7f07005e
int drawable bg_random_heard 0x7f07005f
int drawable btn_black_6 0x7f070060
int drawable btn_black_60 0x7f070061
int drawable btn_blue_6 0x7f070062
int drawable btn_blue_60 0x7f070063
int drawable btn_checkbox_checked_mtrl 0x7f070064
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f070065
int drawable btn_checkbox_unchecked_mtrl 0x7f070066
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f070067
int drawable btn_gray_6 0x7f070068
int drawable btn_gray_60 0x7f070069
int drawable btn_radio_off_mtrl 0x7f07006a
int drawable btn_radio_off_to_on_mtrl_animation 0x7f07006b
int drawable btn_radio_on_mtrl 0x7f07006c
int drawable btn_radio_on_to_off_mtrl_animation 0x7f07006d
int drawable camera_crop 0x7f07006e
int drawable design_fab_background 0x7f07006f
int drawable design_ic_visibility 0x7f070070
int drawable design_ic_visibility_off 0x7f070071
int drawable design_password_eye 0x7f070072
int drawable design_snackbar_background 0x7f070073
int drawable filtershow_background_new 0x7f070074
int drawable filtershow_button_background 0x7f070075
int drawable filtershow_button_selected_background 0x7f070076
int drawable filtershow_tiled_background 0x7f070077
int drawable geometry_shadow 0x7f070078
int drawable gif_collect 0x7f070079
int drawable gif_loading 0x7f07007a
int drawable green_bg 0x7f07007b
int drawable heart_1 0x7f07007c
int drawable ic_back 0x7f07007d
int drawable ic_call_answer 0x7f07007e
int drawable ic_call_answer_low 0x7f07007f
int drawable ic_call_answer_video 0x7f070080
int drawable ic_call_answer_video_low 0x7f070081
int drawable ic_call_decline 0x7f070082
int drawable ic_call_decline_low 0x7f070083
int drawable ic_clock_black_24dp 0x7f070084
int drawable ic_close 0x7f070085
int drawable ic_keyboard_black_24dp 0x7f070086
int drawable ic_launcher_background 0x7f070087
int drawable ic_launcher_foreground 0x7f070088
int drawable ic_menu_savephoto 0x7f070089
int drawable ic_menu_savephoto_disabled 0x7f07008a
int drawable ic_mtrl_checked_circle 0x7f07008b
int drawable ic_mtrl_chip_checked_black 0x7f07008c
int drawable ic_mtrl_chip_checked_circle 0x7f07008d
int drawable ic_mtrl_chip_close_circle 0x7f07008e
int drawable ic_open 0x7f07008f
int drawable ic_photo 0x7f070090
int drawable img_border 0x7f070091
int drawable isb_indicator_rounded_corners 0x7f070092
int drawable isb_indicator_square_corners 0x7f070093
int drawable item_category_default 0x7f070094
int drawable item_category_select 0x7f070095
int drawable item_home 0x7f070096
int drawable item_hot 0x7f070097
int drawable item_msg 0x7f070098
int drawable item_my 0x7f070099
int drawable item_topic 0x7f07009a
int drawable layer_h5_progress 0x7f07009b
int drawable lib_tv_bg_normal 0x7f07009c
int drawable lib_tv_bg_selected 0x7f07009d
int drawable man_01_none 0x7f07009e
int drawable man_01_pressed 0x7f07009f
int drawable man_02_none 0x7f0700a0
int drawable man_02_pressed 0x7f0700a1
int drawable man_03_none 0x7f0700a2
int drawable man_03_pressed 0x7f0700a3
int drawable man_04_none 0x7f0700a4
int drawable man_04_pressed 0x7f0700a5
int drawable material_cursor_drawable 0x7f0700a6
int drawable material_ic_calendar_black_24dp 0x7f0700a7
int drawable material_ic_clear_black_24dp 0x7f0700a8
int drawable material_ic_edit_black_24dp 0x7f0700a9
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f0700aa
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f0700ab
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f0700ac
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f0700ad
int drawable material_ic_menu_arrow_down_black_24dp 0x7f0700ae
int drawable material_ic_menu_arrow_up_black_24dp 0x7f0700af
int drawable menu_save_photo 0x7f0700b0
int drawable mtrl_dialog_background 0x7f0700b1
int drawable mtrl_dropdown_arrow 0x7f0700b2
int drawable mtrl_ic_arrow_drop_down 0x7f0700b3
int drawable mtrl_ic_arrow_drop_up 0x7f0700b4
int drawable mtrl_ic_cancel 0x7f0700b5
int drawable mtrl_ic_error 0x7f0700b6
int drawable mtrl_navigation_bar_item_background 0x7f0700b7
int drawable mtrl_popupmenu_background 0x7f0700b8
int drawable mtrl_popupmenu_background_dark 0x7f0700b9
int drawable mtrl_tabs_default_indicator 0x7f0700ba
int drawable navigation_empty_icon 0x7f0700bb
int drawable notification_action_background 0x7f0700bc
int drawable notification_bg 0x7f0700bd
int drawable notification_bg_low 0x7f0700be
int drawable notification_bg_low_normal 0x7f0700bf
int drawable notification_bg_low_pressed 0x7f0700c0
int drawable notification_bg_normal 0x7f0700c1
int drawable notification_bg_normal_pressed 0x7f0700c2
int drawable notification_icon_background 0x7f0700c3
int drawable notification_oversize_large_icon_bg 0x7f0700c4
int drawable notification_template_icon_bg 0x7f0700c5
int drawable notification_template_icon_low_bg 0x7f0700c6
int drawable notification_tile_bg 0x7f0700c7
int drawable notify_panel_notification_icon_bg 0x7f0700c8
int drawable progress_indeterminate_horizontal_default 0x7f0700c9
int drawable progress_indeterminate_horizontal_selected 0x7f0700ca
int drawable progressbar_bg 0x7f0700cb
int drawable rotate_loading_progressbar 0x7f0700cc
int drawable rounded_corner 0x7f0700cd
int drawable rounded_corner_60 0x7f0700ce
int drawable scan_light 0x7f0700cf
int drawable selector_pickerview_btn 0x7f0700d0
int drawable shape_alpha_red_2 0x7f0700d1
int drawable shape_blue_line_60 0x7f0700d2
int drawable shape_dark_4 0x7f0700d3
int drawable shape_dark_6 0x7f0700d4
int drawable shape_deep_blue_6 0x7f0700d5
int drawable shape_deep_gray_6 0x7f0700d6
int drawable shape_deep_gray_60 0x7f0700d7
int drawable shape_deep_gray_line_6 0x7f0700d8
int drawable shape_default_16 0x7f0700d9
int drawable shape_fill_rectangle_trancelucence_4 0x7f0700da
int drawable shape_gray_6 0x7f0700db
int drawable shape_gray_60 0x7f0700dc
int drawable shape_gray_line_13 0x7f0700dd
int drawable shape_gray_line_6 0x7f0700de
int drawable shape_gray_shade_10 0x7f0700df
int drawable shape_h5_progress 0x7f0700e0
int drawable shape_light_gray_60 0x7f0700e1
int drawable shape_light_red_2 0x7f0700e2
int drawable shape_round_orange_bg 0x7f0700e3
int drawable shape_transparent_60 0x7f0700e4
int drawable shape_white_10 0x7f0700e5
int drawable shape_white_16 0x7f0700e6
int drawable shape_white_2 0x7f0700e7
int drawable shape_white_6 0x7f0700e8
int drawable shape_white_bottom_6 0x7f0700e9
int drawable shape_yellow_6 0x7f0700ea
int drawable shape_yellow_alpha_shade_10 0x7f0700eb
int drawable shape_yellow_border_60 0x7f0700ec
int drawable shape_yellow_shade_10 0x7f0700ed
int drawable test_custom_background 0x7f0700ee
int drawable titile_bg 0x7f0700ef
int drawable toast_blue_60 0x7f0700f0
int drawable tooltip_frame_dark 0x7f0700f1
int drawable tooltip_frame_light 0x7f0700f2
int font chirp_heavy_800 0x7f080000
int font chirp_medium_500 0x7f080001
int font chirp_regular_400 0x7f080002
int font font_family 0x7f080003
int font font_family_bold 0x7f080004
int font ooo 0x7f080005
int font roboto_medium_numbers 0x7f080006
int id ALT 0x7f090000
int id BOTTOM_END 0x7f090001
int id BOTTOM_START 0x7f090002
int id CENTER 0x7f090003
int id CTRL 0x7f090004
int id FUNCTION 0x7f090005
int id FixedBehind 0x7f090006
int id FixedFront 0x7f090007
int id LEFT 0x7f090008
int id META 0x7f090009
int id MatchLayout 0x7f09000a
int id NO_DEBUG 0x7f09000b
int id RIGHT 0x7f09000c
int id SHIFT 0x7f09000d
int id SHOW_ALL 0x7f09000e
int id SHOW_PATH 0x7f09000f
int id SHOW_PROGRESS 0x7f090010
int id SYM 0x7f090011
int id Scale 0x7f090012
int id TOP_END 0x7f090013
int id TOP_START 0x7f090014
int id Translate 0x7f090015
int id Tv_balance 0x7f090016
int id _ll_temp 0x7f090017
int id accelerate 0x7f090018
int id accessibility_action_clickable_span 0x7f090019
int id accessibility_custom_action_0 0x7f09001a
int id accessibility_custom_action_1 0x7f09001b
int id accessibility_custom_action_10 0x7f09001c
int id accessibility_custom_action_11 0x7f09001d
int id accessibility_custom_action_12 0x7f09001e
int id accessibility_custom_action_13 0x7f09001f
int id accessibility_custom_action_14 0x7f090020
int id accessibility_custom_action_15 0x7f090021
int id accessibility_custom_action_16 0x7f090022
int id accessibility_custom_action_17 0x7f090023
int id accessibility_custom_action_18 0x7f090024
int id accessibility_custom_action_19 0x7f090025
int id accessibility_custom_action_2 0x7f090026
int id accessibility_custom_action_20 0x7f090027
int id accessibility_custom_action_21 0x7f090028
int id accessibility_custom_action_22 0x7f090029
int id accessibility_custom_action_23 0x7f09002a
int id accessibility_custom_action_24 0x7f09002b
int id accessibility_custom_action_25 0x7f09002c
int id accessibility_custom_action_26 0x7f09002d
int id accessibility_custom_action_27 0x7f09002e
int id accessibility_custom_action_28 0x7f09002f
int id accessibility_custom_action_29 0x7f090030
int id accessibility_custom_action_3 0x7f090031
int id accessibility_custom_action_30 0x7f090032
int id accessibility_custom_action_31 0x7f090033
int id accessibility_custom_action_4 0x7f090034
int id accessibility_custom_action_5 0x7f090035
int id accessibility_custom_action_6 0x7f090036
int id accessibility_custom_action_7 0x7f090037
int id accessibility_custom_action_8 0x7f090038
int id accessibility_custom_action_9 0x7f090039
int id action0 0x7f09003a
int id action_bar 0x7f09003b
int id action_bar_activity_content 0x7f09003c
int id action_bar_container 0x7f09003d
int id action_bar_root 0x7f09003e
int id action_bar_spinner 0x7f09003f
int id action_bar_subtitle 0x7f090040
int id action_bar_title 0x7f090041
int id action_container 0x7f090042
int id action_context_bar 0x7f090043
int id action_divider 0x7f090044
int id action_image 0x7f090045
int id action_menu_divider 0x7f090046
int id action_menu_presenter 0x7f090047
int id action_mode_bar 0x7f090048
int id action_mode_bar_stub 0x7f090049
int id action_mode_close_button 0x7f09004a
int id action_text 0x7f09004b
int id actions 0x7f09004c
int id activity_chooser_view_content 0x7f09004d
int id add 0x7f09004e
int id albumIv 0x7f09004f
int id albumLayout 0x7f090050
int id alertTitle 0x7f090051
int id aligned 0x7f090052
int id all 0x7f090053
int id always 0x7f090054
int id animateToEnd 0x7f090055
int id animateToStart 0x7f090056
int id arc 0x7f090057
int id asConfigured 0x7f090058
int id async 0x7f090059
int id attachPopupContainer 0x7f09005a
int id auto 0x7f09005b
int id autoComplete 0x7f09005c
int id autoCompleteToEnd 0x7f09005d
int id autoCompleteToStart 0x7f09005e
int id backIv 0x7f09005f
int id barrier 0x7f090060
int id barrier_actions_bottom 0x7f090061
int id barrier_content_bottom 0x7f090062
int id baseline 0x7f090063
int id beginOnFirstDraw 0x7f090064
int id beginning 0x7f090065
int id below_section_mark 0x7f090066
int id bg_progress 0x7f090067
int id blocking 0x7f090068
int id bottom 0x7f090069
int id bottomLayout 0x7f09006a
int id bottomNavigationView 0x7f09006b
int id bottomPopupContainer 0x7f09006c
int id bottom_sides 0x7f09006d
int id bounce 0x7f09006e
int id btnCancel 0x7f09006f
int id btnSubmit 0x7f090070
int id btn_backup 0x7f090071
int id btn_confirm 0x7f090072
int id btn_send_Comment 0x7f090073
int id btn_updata 0x7f090074
int id bubbleContainer 0x7f090075
int id buttonPanel 0x7f090076
int id cancel_action 0x7f090077
int id cancel_button 0x7f090078
int id center 0x7f090079
int id centerPopupContainer 0x7f09007a
int id center_horizontal 0x7f09007b
int id center_vertical 0x7f09007c
int id chain 0x7f09007d
int id chains 0x7f09007e
int id check_view 0x7f09007f
int id checkbox 0x7f090080
int id checked 0x7f090081
int id chip 0x7f090082
int id chip1 0x7f090083
int id chip2 0x7f090084
int id chip3 0x7f090085
int id chip_group 0x7f090086
int id chronometer 0x7f090087
int id circle_center 0x7f090088
int id circular_bubble 0x7f090089
int id cl_nav_bar 0x7f09008a
int id clear_text 0x7f09008b
int id clip_horizontal 0x7f09008c
int id clip_vertical 0x7f09008d
int id clockwise 0x7f09008e
int id collapseActionView 0x7f09008f
int id confirm_button 0x7f090090
int id constraint 0x7f090091
int id container 0x7f090092
int id content 0x7f090093
int id contentPanel 0x7f090094
int id content_container 0x7f090095
int id content_frame 0x7f090096
int id contiguous 0x7f090097
int id coordinator 0x7f090098
int id cos 0x7f090099
int id counterclockwise 0x7f09009a
int id cropView 0x7f09009b
int id custom 0x7f09009c
int id customPanel 0x7f09009d
int id custom_nav_bar 0x7f09009e
int id cut 0x7f09009f
int id date_picker_actions 0x7f0900a0
int id day 0x7f0900a1
int id decelerate 0x7f0900a2
int id decelerateAndComplete 0x7f0900a3
int id decor_content_parent 0x7f0900a4
int id default_activity_button 0x7f0900a5
int id deltaRelative 0x7f0900a6
int id design_bottom_sheet 0x7f0900a7
int id design_menu_item_action_area 0x7f0900a8
int id design_menu_item_action_area_stub 0x7f0900a9
int id design_menu_item_text 0x7f0900aa
int id design_navigation_view 0x7f0900ab
int id dialog_button 0x7f0900ac
int id dialog_dgv_bottom 0x7f0900ad
int id dialog_dgv_top 0x7f0900ae
int id dialog_tv_division 0x7f0900af
int id dialog_tv_title 0x7f0900b0
int id dimensions 0x7f0900b1
int id direct 0x7f0900b2
int id disableHome 0x7f0900b3
int id disablePostScroll 0x7f0900b4
int id disableScroll 0x7f0900b5
int id disjoint 0x7f0900b6
int id divider 0x7f0900b7
int id dragDown 0x7f0900b8
int id dragEnd 0x7f0900b9
int id dragLeft 0x7f0900ba
int id dragRight 0x7f0900bb
int id dragStart 0x7f0900bc
int id dragUp 0x7f0900bd
int id drawerContentContainer 0x7f0900be
int id drawerLayout 0x7f0900bf
int id drawer_layout 0x7f0900c0
int id dropdown_menu 0x7f0900c1
int id easeIn 0x7f0900c2
int id easeInOut 0x7f0900c3
int id easeOut 0x7f0900c4
int id ed_Comment 0x7f0900c5
int id ed_Gwei 0x7f0900c6
int id ed_Location 0x7f0900c7
int id ed_address 0x7f0900c8
int id ed_bio 0x7f0900c9
int id ed_check_pwd 0x7f0900ca
int id ed_content 0x7f0900cb
int id ed_count 0x7f0900cc
int id ed_input 0x7f0900cd
int id ed_limit 0x7f0900ce
int id ed_local 0x7f0900cf
int id ed_name 0x7f0900d0
int id ed_new_pwd 0x7f0900d1
int id ed_nike_name 0x7f0900d2
int id ed_old_pwd 0x7f0900d3
int id ed_proposal1 0x7f0900d4
int id ed_pwd 0x7f0900d5
int id ed_search 0x7f0900d6
int id ed_search_text 0x7f0900d7
int id ed_title 0x7f0900d8
int id ed_user_name 0x7f0900d9
int id ed_web_site 0x7f0900da
int id editText 0x7f0900db
int id edit_query 0x7f0900dc
int id edit_text_id 0x7f0900dd
int id elastic 0x7f0900de
int id end 0x7f0900df
int id endToStart 0x7f0900e0
int id end_padder 0x7f0900e1
int id enterAlways 0x7f0900e2
int id enterAlwaysCollapsed 0x7f0900e3
int id et_input 0x7f0900e4
int id et_pwd 0x7f0900e5
int id et_pwd_confirm 0x7f0900e6
int id et_wallet_name 0x7f0900e7
int id exitUntilCollapsed 0x7f0900e8
int id expand_activities_button 0x7f0900e9
int id expanded_menu 0x7f0900ea
int id fade 0x7f0900eb
int id fans_count 0x7f0900ec
int id fill 0x7f0900ed
int id fill_horizontal 0x7f0900ee
int id fill_vertical 0x7f0900ef
int id filled 0x7f0900f0
int id filtershow_cancl 0x7f0900f1
int id filtershow_done 0x7f0900f2
int id fitToContents 0x7f0900f3
int id fixed 0x7f0900f4
int id flashLightIv 0x7f0900f5
int id flashLightLayout 0x7f0900f6
int id flashLightTv 0x7f0900f7
int id flip 0x7f0900f8
int id floating 0x7f0900f9
int id flowlayout_mnemonic 0x7f0900fa
int id forever 0x7f0900fb
int id fragment_container_view_tag 0x7f0900fc
int id fullPopupContainer 0x7f0900fd
int id full_web_webview 0x7f0900fe
int id ghost_view 0x7f0900ff
int id ghost_view_holder 0x7f090100
int id glide_custom_view_target_tag 0x7f090101
int id gone 0x7f090102
int id graph 0x7f090103
int id graph_wrap 0x7f090104
int id group_divider 0x7f090105
int id groups 0x7f090106
int id guideline 0x7f090107
int id guideline_20 0x7f090108
int id guideline_40 0x7f090109
int id guideline_60 0x7f09010a
int id guideline_80 0x7f09010b
int id headerLayout 0x7f09010c
int id header_title 0x7f09010d
int id hide_ime_id 0x7f09010e
int id hideable 0x7f09010f
int id home 0x7f090110
int id homeAsUp 0x7f090111
int id honorRequest 0x7f090112
int id horizontal 0x7f090113
int id hour 0x7f090114
int id icon 0x7f090115
int id icon_check 0x7f090116
int id icon_group 0x7f090117
int id ifRoom 0x7f090118
int id ignore 0x7f090119
int id ignoreRequest 0x7f09011a
int id image 0x7f09011b
int id img_finger 0x7f09011c
int id ind 0x7f09011d
int id indicator_arrow 0x7f09011e
int id indicator_container 0x7f09011f
int id info 0x7f090120
int id invisible 0x7f090121
int id inward 0x7f090122
int id isb_progress 0x7f090123
int id italic 0x7f090124
int id item_home 0x7f090125
int id item_hot 0x7f090126
int id item_msg 0x7f090127
int id item_my 0x7f090128
int id item_topic 0x7f090129
int id item_touch_helper_previous_elevation 0x7f09012a
int id iv1 0x7f09012b
int id iv2 0x7f09012c
int id iv3 0x7f09012d
int id iv_1 0x7f09012e
int id iv_2 0x7f09012f
int id iv_3 0x7f090130
int id iv_4 0x7f090131
int id iv_Collect 0x7f090132
int id iv_Moderator 0x7f090133
int id iv_add 0x7f090134
int id iv_address 0x7f090135
int id iv_animation 0x7f090136
int id iv_avatar 0x7f090137
int id iv_avatar1 0x7f090138
int id iv_back 0x7f090139
int id iv_balance 0x7f09013a
int id iv_check_set 0x7f09013b
int id iv_classify 0x7f09013c
int id iv_clear 0x7f09013d
int id iv_clear_history 0x7f09013e
int id iv_click 0x7f09013f
int id iv_close 0x7f090140
int id iv_close_Proposal 0x7f090141
int id iv_collect 0x7f090142
int id iv_copy_1 0x7f090143
int id iv_copy_2 0x7f090144
int id iv_copy_3 0x7f090145
int id iv_del 0x7f090146
int id iv_delete 0x7f090147
int id iv_dot 0x7f090148
int id iv_error_img 0x7f090149
int id iv_favorite 0x7f09014a
int id iv_fingerprint 0x7f09014b
int id iv_free 0x7f09014c
int id iv_free_1 0x7f09014d
int id iv_from 0x7f09014e
int id iv_governance 0x7f09014f
int id iv_hash 0x7f090150
int id iv_heard 0x7f090151
int id iv_heard_quote 0x7f090152
int id iv_history 0x7f090153
int id iv_hot_topic 0x7f090154
int id iv_image 0x7f090155
int id iv_left 0x7f090156
int id iv_like 0x7f090157
int id iv_line 0x7f090158
int id iv_logo 0x7f090159
int id iv_manger 0x7f09015a
int id iv_menu_at 0x7f09015b
int id iv_menu_list 0x7f09015c
int id iv_menu_topic 0x7f09015d
int id iv_mnemonic 0x7f09015e
int id iv_more 0x7f09015f
int id iv_name 0x7f090160
int id iv_new_set 0x7f090161
int id iv_notice1 0x7f090162
int id iv_notice2 0x7f090163
int id iv_post 0x7f090164
int id iv_praise 0x7f090165
int id iv_private_key 0x7f090166
int id iv_proposal 0x7f090167
int id iv_qr_code 0x7f090168
int id iv_quote 0x7f090169
int id iv_quote_heard 0x7f09016a
int id iv_received 0x7f09016b
int id iv_refresh 0x7f09016c
int id iv_review 0x7f09016d
int id iv_right 0x7f09016e
int id iv_scan 0x7f09016f
int id iv_search 0x7f090170
int id iv_select 0x7f090171
int id iv_send 0x7f090172
int id iv_share 0x7f090173
int id iv_show 0x7f090174
int id iv_show_more 0x7f090175
int id iv_status 0x7f090176
int id iv_title 0x7f090177
int id iv_to 0x7f090178
int id iv_topic_avatar 0x7f090179
int id iv_transaction 0x7f09017a
int id iv_user_heard 0x7f09017b
int id iv_vip 0x7f09017c
int id iv_wallet 0x7f09017d
int id iv_warn 0x7f09017e
int id jumpToEnd 0x7f09017f
int id jumpToStart 0x7f090180
int id labeled 0x7f090181
int id layout 0x7f090182
int id left 0x7f090183
int id leftToRight 0x7f090184
int id line1 0x7f090185
int id line11 0x7f090186
int id line2 0x7f090187
int id line3 0x7f090188
int id line4 0x7f090189
int id line5 0x7f09018a
int id linear 0x7f09018b
int id listMode 0x7f09018c
int id list_item 0x7f09018d
int id ll_Collect 0x7f09018e
int id ll_Gwei 0x7f09018f
int id ll_btn 0x7f090190
int id ll_check 0x7f090191
int id ll_clear 0x7f090192
int id ll_comment 0x7f090193
int id ll_comment_child 0x7f090194
int id ll_container 0x7f090195
int id ll_content 0x7f090196
int id ll_content_quote 0x7f090197
int id ll_count 0x7f090198
int id ll_custom 0x7f090199
int id ll_first 0x7f09019a
int id ll_history 0x7f09019b
int id ll_img 0x7f09019c
int id ll_left 0x7f09019d
int id ll_menu 0x7f09019e
int id ll_name 0x7f09019f
int id ll_praise 0x7f0901a0
int id ll_price 0x7f0901a1
int id ll_quote 0x7f0901a2
int id ll_review 0x7f0901a3
int id ll_right 0x7f0901a4
int id ll_root 0x7f0901a5
int id ll_root_pop 0x7f0901a6
int id ll_search 0x7f0901a7
int id ll_second 0x7f0901a8
int id ll_select 0x7f0901a9
int id ll_share 0x7f0901aa
int id ll_tab 0x7f0901ab
int id ll_third 0x7f0901ac
int id ll_title 0x7f0901ad
int id ll_type 0x7f0901ae
int id ll_vote 0x7f0901af
int id loadProgress 0x7f0901b0
int id load_error 0x7f0901b1
int id loading 0x7f0901b2
int id loadview 0x7f0901b3
int id main 0x7f0901b4
int id mainPanel 0x7f0901b5
int id mainView 0x7f0901b6
int id markwon_drawables_scheduler 0x7f0901b7
int id markwon_drawables_scheduler_last_text_hashcode 0x7f0901b8
int id masked 0x7f0901b9
int id material_clock_display 0x7f0901ba
int id material_clock_face 0x7f0901bb
int id material_clock_hand 0x7f0901bc
int id material_clock_period_am_button 0x7f0901bd
int id material_clock_period_pm_button 0x7f0901be
int id material_clock_period_toggle 0x7f0901bf
int id material_hour_text_input 0x7f0901c0
int id material_hour_tv 0x7f0901c1
int id material_label 0x7f0901c2
int id material_minute_text_input 0x7f0901c3
int id material_minute_tv 0x7f0901c4
int id material_textinput_timepicker 0x7f0901c5
int id material_timepicker_cancel_button 0x7f0901c6
int id material_timepicker_container 0x7f0901c7
int id material_timepicker_edit_text 0x7f0901c8
int id material_timepicker_mode_button 0x7f0901c9
int id material_timepicker_ok_button 0x7f0901ca
int id material_timepicker_view 0x7f0901cb
int id material_value_index 0x7f0901cc
int id media_actions 0x7f0901cd
int id menu_frame 0x7f0901ce
int id message 0x7f0901cf
int id message_rv 0x7f0901d0
int id middle 0x7f0901d1
int id min 0x7f0901d2
int id mini 0x7f0901d3
int id monospace 0x7f0901d4
int id month 0x7f0901d5
int id month_grid 0x7f0901d6
int id month_navigation_bar 0x7f0901d7
int id month_navigation_fragment_toggle 0x7f0901d8
int id month_navigation_next 0x7f0901d9
int id month_navigation_previous 0x7f0901da
int id month_title 0x7f0901db
int id motion_base 0x7f0901dc
int id mtrl_anchor_parent 0x7f0901dd
int id mtrl_calendar_day_selector_frame 0x7f0901de
int id mtrl_calendar_days_of_week 0x7f0901df
int id mtrl_calendar_frame 0x7f0901e0
int id mtrl_calendar_main_pane 0x7f0901e1
int id mtrl_calendar_months 0x7f0901e2
int id mtrl_calendar_selection_frame 0x7f0901e3
int id mtrl_calendar_text_input_frame 0x7f0901e4
int id mtrl_calendar_year_selector_frame 0x7f0901e5
int id mtrl_card_checked_layer_id 0x7f0901e6
int id mtrl_child_content_container 0x7f0901e7
int id mtrl_internal_children_alpha_tag 0x7f0901e8
int id mtrl_motion_snapshot_view 0x7f0901e9
int id mtrl_picker_fullscreen 0x7f0901ea
int id mtrl_picker_header 0x7f0901eb
int id mtrl_picker_header_selection_text 0x7f0901ec
int id mtrl_picker_header_title_and_selection 0x7f0901ed
int id mtrl_picker_header_toggle 0x7f0901ee
int id mtrl_picker_text_input_date 0x7f0901ef
int id mtrl_picker_text_input_range_end 0x7f0901f0
int id mtrl_picker_text_input_range_start 0x7f0901f1
int id mtrl_picker_title_text 0x7f0901f2
int id mtrl_view_tag_bottom_padding 0x7f0901f3
int id multiply 0x7f0901f4
int id name 0x7f0901f5
int id navigation_bar_item_icon_view 0x7f0901f6
int id navigation_bar_item_labels_group 0x7f0901f7
int id navigation_bar_item_large_label_view 0x7f0901f8
int id navigation_bar_item_small_label_view 0x7f0901f9
int id navigation_header_container 0x7f0901fa
int id never 0x7f0901fb
int id noScroll 0x7f0901fc
int id none 0x7f0901fd
int id normal 0x7f0901fe
int id notification_background 0x7f0901ff
int id notification_main_column 0x7f090200
int id notification_main_column_container 0x7f090201
int id number 0x7f090202
int id off 0x7f090203
int id on 0x7f090204
int id options1 0x7f090205
int id options2 0x7f090206
int id options3 0x7f090207
int id optionspicker 0x7f090208
int id outline 0x7f090209
int id outmost_container 0x7f09020a
int id outward 0x7f09020b
int id oval 0x7f09020c
int id packed 0x7f09020d
int id pager 0x7f09020e
int id pager2 0x7f09020f
int id parallax 0x7f090210
int id parent 0x7f090211
int id parentPanel 0x7f090212
int id parentRelative 0x7f090213
int id parent_matrix 0x7f090214
int id password_toggle 0x7f090215
int id path 0x7f090216
int id pathRelative 0x7f090217
int id pb_load_progress 0x7f090218
int id pb_update 0x7f090219
int id pb_waiting_progress 0x7f09021a
int id peekHeight 0x7f09021b
int id percent 0x7f09021c
int id photoViewContainer 0x7f09021d
int id pin 0x7f09021e
int id placeholderView 0x7f09021f
int id position 0x7f090220
int id positionPopupContainer 0x7f090221
int id postLayout 0x7f090222
int id preview_view 0x7f090223
int id progress_circular 0x7f090224
int id progress_horizontal 0x7f090225
int id radio 0x7f090226
int id ratio 0x7f090227
int id rectangle 0x7f090228
int id rectangles 0x7f090229
int id recyclerView 0x7f09022a
int id recyclerView_community 0x7f09022b
int id recyler_view 0x7f09022c
int id refresh_layout 0x7f09022d
int id report_drawn 0x7f09022e
int id reverseSawtooth 0x7f09022f
int id review_count 0x7f090230
int id right 0x7f090231
int id rightToLeft 0x7f090232
int id right_icon 0x7f090233
int id right_side 0x7f090234
int id rl1 0x7f090235
int id rl2 0x7f090236
int id rl_Bio 0x7f090237
int id rl_Moderator 0x7f090238
int id rl_balance 0x7f090239
int id rl_category 0x7f09023a
int id rl_change_pwd 0x7f09023b
int id rl_check 0x7f09023c
int id rl_collect 0x7f09023d
int id rl_comment 0x7f09023e
int id rl_content_quote 0x7f09023f
int id rl_delete 0x7f090240
int id rl_favorite 0x7f090241
int id rl_free 0x7f090242
int id rl_gas 0x7f090243
int id rl_governance 0x7f090244
int id rl_history 0x7f090245
int id rl_index 0x7f090246
int id rl_info 0x7f090247
int id rl_item 0x7f090248
int id rl_key 0x7f090249
int id rl_local 0x7f09024a
int id rl_member_list 0x7f09024b
int id rl_mnemonic 0x7f09024c
int id rl_name 0x7f09024d
int id rl_new 0x7f09024e
int id rl_old 0x7f09024f
int id rl_out_private 0x7f090250
int id rl_out_world 0x7f090251
int id rl_private_key 0x7f090252
int id rl_proposal 0x7f090253
int id rl_received 0x7f090254
int id rl_root 0x7f090255
int id rl_send 0x7f090256
int id rl_task 0x7f090257
int id rl_title 0x7f090258
int id rl_toolbar 0x7f090259
int id rl_topic 0x7f09025a
int id rl_transaction 0x7f09025b
int id rl_web_site 0x7f09025c
int id roboto 0x7f09025d
int id root 0x7f09025e
int id rounded 0x7f09025f
int id rounded_rectangle 0x7f090260
int id row_index_key 0x7f090261
int id rv_account 0x7f090262
int id rv_comment 0x7f090263
int id rv_history 0x7f090264
int id rv_image 0x7f090265
int id rv_images 0x7f090266
int id rv_topbar 0x7f090267
int id rv_topic 0x7f090268
int id rv_user 0x7f090269
int id rv_wallet 0x7f09026a
int id same_level 0x7f09026b
int id sans 0x7f09026c
int id save_non_transition_alpha 0x7f09026d
int id save_overlay_view 0x7f09026e
int id sawtooth 0x7f09026f
int id scale 0x7f090270
int id screen 0x7f090271
int id scroll 0x7f090272
int id scrollIndicatorDown 0x7f090273
int id scrollIndicatorUp 0x7f090274
int id scrollView 0x7f090275
int id scroll_view 0x7f090276
int id scrollable 0x7f090277
int id search_badge 0x7f090278
int id search_bar 0x7f090279
int id search_button 0x7f09027a
int id search_close_btn 0x7f09027b
int id search_edit_frame 0x7f09027c
int id search_go_btn 0x7f09027d
int id search_mag_icon 0x7f09027e
int id search_plate 0x7f09027f
int id search_src_text 0x7f090280
int id search_view 0x7f090281
int id search_voice_btn 0x7f090282
int id second 0x7f090283
int id select_dialog_listview 0x7f090284
int id selected 0x7f090285
int id selection_type 0x7f090286
int id semiBold 0x7f090287
int id send_post 0x7f090288
int id serif 0x7f090289
int id shortcut 0x7f09028a
int id showCustom 0x7f09028b
int id showHome 0x7f09028c
int id showTitle 0x7f09028d
int id sides 0x7f09028e
int id sin 0x7f09028f
int id skipCollapsed 0x7f090290
int id slide 0x7f090291
int id smallLabel1 0x7f090292
int id smallLabel2 0x7f090293
int id snackbar_action 0x7f090294
int id snackbar_text 0x7f090295
int id snap 0x7f090296
int id snapMargins 0x7f090297
int id spacer 0x7f090298
int id special_effects_controller_view_tag 0x7f090299
int id spline 0x7f09029a
int id split_action_bar 0x7f09029b
int id spread 0x7f09029c
int id spread_inside 0x7f09029d
int id square 0x7f09029e
int id src_atop 0x7f09029f
int id src_in 0x7f0902a0
int id src_over 0x7f0902a1
int id standard 0x7f0902a2
int id start 0x7f0902a3
int id startHorizontal 0x7f0902a4
int id startToEnd 0x7f0902a5
int id startVertical 0x7f0902a6
int id staticLayout 0x7f0902a7
int id staticPostLayout 0x7f0902a8
int id status_bar_latest_event_content 0x7f0902a9
int id stop 0x7f0902aa
int id stretch 0x7f0902ab
int id submenuarrow 0x7f0902ac
int id submit_area 0x7f0902ad
int id tab1 0x7f0902ae
int id tab2 0x7f0902af
int id tab3 0x7f0902b0
int id tab4 0x7f0902b1
int id tab5 0x7f0902b2
int id tab6 0x7f0902b3
int id tab7 0x7f0902b4
int id tab8 0x7f0902b5
int id tabMode 0x7f0902b6
int id tablayout 0x7f0902b7
int id tag_accessibility_actions 0x7f0902b8
int id tag_accessibility_clickable_spans 0x7f0902b9
int id tag_accessibility_heading 0x7f0902ba
int id tag_accessibility_pane_title 0x7f0902bb
int id tag_on_apply_window_listener 0x7f0902bc
int id tag_on_receive_content_listener 0x7f0902bd
int id tag_on_receive_content_mime_types 0x7f0902be
int id tag_screen_reader_focusable 0x7f0902bf
int id tag_state_description 0x7f0902c0
int id tag_transition_group 0x7f0902c1
int id tag_unhandled_key_event_manager 0x7f0902c2
int id tag_unhandled_key_listeners 0x7f0902c3
int id tag_window_insets_animation_callback 0x7f0902c4
int id test_checkbox_android_button_tint 0x7f0902c5
int id test_checkbox_app_button_tint 0x7f0902c6
int id test_radiobutton_android_button_tint 0x7f0902c7
int id test_radiobutton_app_button_tint 0x7f0902c8
int id text 0x7f0902c9
int id text2 0x7f0902ca
int id textEnd 0x7f0902cb
int id textSpacerNoButtons 0x7f0902cc
int id textSpacerNoTitle 0x7f0902cd
int id textStart 0x7f0902ce
int id textTop 0x7f0902cf
int id text_input_end_icon 0x7f0902d0
int id text_input_error_icon 0x7f0902d1
int id text_input_start_icon 0x7f0902d2
int id textinput_counter 0x7f0902d3
int id textinput_error 0x7f0902d4
int id textinput_helper_text 0x7f0902d5
int id textinput_placeholder 0x7f0902d6
int id textinput_prefix_text 0x7f0902d7
int id textinput_suffix_text 0x7f0902d8
int id time 0x7f0902d9
int id timepicker 0x7f0902da
int id title 0x7f0902db
int id titleDividerNoCustom 0x7f0902dc
int id title_template 0x7f0902dd
int id toast_msg 0x7f0902de
int id toggle 0x7f0902df
int id top 0x7f0902e0
int id topPanel 0x7f0902e1
int id touch_outside 0x7f0902e2
int id transitionToEnd 0x7f0902e3
int id transitionToStart 0x7f0902e4
int id transition_current_scene 0x7f0902e5
int id transition_layout_save 0x7f0902e6
int id transition_position 0x7f0902e7
int id transition_scene_layoutid_cache 0x7f0902e8
int id transition_transform 0x7f0902e9
int id triangle 0x7f0902ea
int id tv 0x7f0902eb
int id tv1 0x7f0902ec
int id tv1_title 0x7f0902ed
int id tv2 0x7f0902ee
int id tv2_title 0x7f0902ef
int id tv3 0x7f0902f0
int id tv3_title 0x7f0902f1
int id tv4 0x7f0902f2
int id tv4_title 0x7f0902f3
int id tv5 0x7f0902f4
int id tv6 0x7f0902f5
int id tvTitle 0x7f0902f6
int id tv_1 0x7f0902f7
int id tv_2 0x7f0902f8
int id tv_3 0x7f0902f9
int id tv_4 0x7f0902fa
int id tv_Bio 0x7f0902fb
int id tv_Cancel 0x7f0902fc
int id tv_Collect 0x7f0902fd
int id tv_From_title 0x7f0902fe
int id tv_Quote 0x7f0902ff
int id tv_Report 0x7f090300
int id tv_account 0x7f090301
int id tv_account_name 0x7f090302
int id tv_add_account 0x7f090303
int id tv_address 0x7f090304
int id tv_all 0x7f090305
int id tv_already 0x7f090306
int id tv_amount 0x7f090307
int id tv_backup 0x7f090308
int id tv_balance 0x7f090309
int id tv_bnb 0x7f09030a
int id tv_buyCount 0x7f09030b
int id tv_cancel 0x7f09030c
int id tv_cancle 0x7f09030d
int id tv_capture_tip 0x7f09030e
int id tv_chain 0x7f09030f
int id tv_change_pwd 0x7f090310
int id tv_clear 0x7f090311
int id tv_confirm 0x7f090312
int id tv_confirm1 0x7f090313
int id tv_content 0x7f090314
int id tv_copy_address 0x7f090315
int id tv_copy_link 0x7f090316
int id tv_copy_url 0x7f090317
int id tv_count 0x7f090318
int id tv_create 0x7f090319
int id tv_current 0x7f09031a
int id tv_custom 0x7f09031b
int id tv_danger 0x7f09031c
int id tv_data 0x7f09031d
int id tv_data_title 0x7f09031e
int id tv_date 0x7f09031f
int id tv_delete 0x7f090320
int id tv_delete_tip 0x7f090321
int id tv_edit 0x7f090322
int id tv_end_time 0x7f090323
int id tv_error 0x7f090324
int id tv_error_msg 0x7f090325
int id tv_fenge 0x7f090326
int id tv_fingererprint 0x7f090327
int id tv_first 0x7f090328
int id tv_follow 0x7f090329
int id tv_free 0x7f09032a
int id tv_free_data 0x7f09032b
int id tv_from 0x7f09032c
int id tv_gas 0x7f09032d
int id tv_gasPrice 0x7f09032e
int id tv_gas_title 0x7f09032f
int id tv_gold 0x7f090330
int id tv_handle 0x7f090331
int id tv_hash 0x7f090332
int id tv_hash_title 0x7f090333
int id tv_height 0x7f090334
int id tv_height_title 0x7f090335
int id tv_index 0x7f090336
int id tv_info 0x7f090337
int id tv_input 0x7f090338
int id tv_isShow 0x7f090339
int id tv_key 0x7f09033a
int id tv_key_1 0x7f09033b
int id tv_key_2 0x7f09033c
int id tv_key_3 0x7f09033d
int id tv_know 0x7f09033e
int id tv_left 0x7f09033f
int id tv_level 0x7f090340
int id tv_limit 0x7f090341
int id tv_limit_title 0x7f090342
int id tv_list_title 0x7f090343
int id tv_method 0x7f090344
int id tv_method_title 0x7f090345
int id tv_mid 0x7f090346
int id tv_mnemonics 0x7f090347
int id tv_msg 0x7f090348
int id tv_name 0x7f090349
int id tv_next 0x7f09034a
int id tv_nike_name 0x7f09034b
int id tv_order 0x7f09034c
int id tv_pager_indicator 0x7f09034d
int id tv_payer 0x7f09034e
int id tv_payer_title 0x7f09034f
int id tv_percent 0x7f090350
int id tv_pop_content 0x7f090351
int id tv_pop_normal_content 0x7f090352
int id tv_pop_title 0x7f090353
int id tv_post 0x7f090354
int id tv_praise 0x7f090355
int id tv_price 0x7f090356
int id tv_price_now 0x7f090357
int id tv_proposal_option 0x7f090358
int id tv_pwd_error 0x7f090359
int id tv_pwd_tip 0x7f09035a
int id tv_queding 0x7f09035b
int id tv_quote 0x7f09035c
int id tv_quote_content 0x7f09035d
int id tv_quote_title 0x7f09035e
int id tv_quxiao 0x7f09035f
int id tv_reply 0x7f090360
int id tv_repost 0x7f090361
int id tv_reset_time 0x7f090362
int id tv_review 0x7f090363
int id tv_right 0x7f090364
int id tv_save 0x7f090365
int id tv_second 0x7f090366
int id tv_service 0x7f090367
int id tv_share 0x7f090368
int id tv_share_address 0x7f090369
int id tv_share_tg 0x7f09036a
int id tv_share_x 0x7f09036b
int id tv_speed 0x7f09036c
int id tv_status 0x7f09036d
int id tv_sure 0x7f09036e
int id tv_text 0x7f09036f
int id tv_third 0x7f090370
int id tv_time 0x7f090371
int id tv_time1 0x7f090372
int id tv_time_1 0x7f090373
int id tv_time_2 0x7f090374
int id tv_time_3 0x7f090375
int id tv_time_4 0x7f090376
int id tv_time_title 0x7f090377
int id tv_tip 0x7f090378
int id tv_title 0x7f090379
int id tv_to 0x7f09037a
int id tv_to_title 0x7f09037b
int id tv_token 0x7f09037c
int id tv_topic 0x7f09037d
int id tv_topic_follow 0x7f09037e
int id tv_topic_hotness 0x7f09037f
int id tv_topic_name 0x7f090380
int id tv_total 0x7f090381
int id tv_used 0x7f090382
int id tv_used_title 0x7f090383
int id tv_voteTime 0x7f090384
int id tv_wallet 0x7f090385
int id tv_wallet_address 0x7f090386
int id tv_wallet_address_title 0x7f090387
int id tv_warn_cancle 0x7f090388
int id tv_warn_continue 0x7f090389
int id unchecked 0x7f09038a
int id uniform 0x7f09038b
int id unlabeled 0x7f09038c
int id up 0x7f09038d
int id useLogo 0x7f09038e
int id vertical 0x7f09038f
int id view1 0x7f090390
int id view2 0x7f090391
int id view3 0x7f090392
int id view_line 0x7f090393
int id view_offset_helper 0x7f090394
int id view_pager 0x7f090395
int id view_tree_lifecycle_owner 0x7f090396
int id view_tree_on_back_pressed_dispatcher_owner 0x7f090397
int id view_tree_saved_state_registry_owner 0x7f090398
int id view_tree_view_model_store_owner 0x7f090399
int id viewfinder_view 0x7f09039a
int id visible 0x7f09039b
int id visible_removing_fragment_view_tag 0x7f09039c
int id vp_main 0x7f09039d
int id vp_sort 0x7f09039e
int id vv_divider 0x7f09039f
int id walletname 0x7f0903a0
int id web_1 0x7f0903a1
int id withText 0x7f0903a2
int id withinBounds 0x7f0903a3
int id wrap 0x7f0903a4
int id wrap_content 0x7f0903a5
int id xpopup_divider 0x7f0903a6
int id xpopup_divider1 0x7f0903a7
int id xpopup_divider2 0x7f0903a8
int id year 0x7f0903a9
int id zero_corner_chip 0x7f0903aa
int integer abc_config_activityDefaultDur 0x7f0a0000
int integer abc_config_activityShortDur 0x7f0a0001
int integer animation_default_duration 0x7f0a0002
int integer app_bar_elevation_anim_duration 0x7f0a0003
int integer bottom_sheet_slide_duration 0x7f0a0004
int integer cancel_button_image_alpha 0x7f0a0005
int integer config_tooltipAnimTime 0x7f0a0006
int integer design_snackbar_text_max_lines 0x7f0a0007
int integer design_tab_indicator_anim_duration_ms 0x7f0a0008
int integer dialog_enter_duration 0x7f0a0009
int integer hide_password_duration 0x7f0a000a
int integer material_motion_duration_long_1 0x7f0a000b
int integer material_motion_duration_long_2 0x7f0a000c
int integer material_motion_duration_medium_1 0x7f0a000d
int integer material_motion_duration_medium_2 0x7f0a000e
int integer material_motion_duration_short_1 0x7f0a000f
int integer material_motion_duration_short_2 0x7f0a0010
int integer material_motion_path 0x7f0a0011
int integer mtrl_badge_max_character_count 0x7f0a0012
int integer mtrl_btn_anim_delay_ms 0x7f0a0013
int integer mtrl_btn_anim_duration_ms 0x7f0a0014
int integer mtrl_calendar_header_orientation 0x7f0a0015
int integer mtrl_calendar_selection_text_lines 0x7f0a0016
int integer mtrl_calendar_year_selector_span 0x7f0a0017
int integer mtrl_card_anim_delay_ms 0x7f0a0018
int integer mtrl_card_anim_duration_ms 0x7f0a0019
int integer mtrl_chip_anim_duration 0x7f0a001a
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0a001b
int integer show_password_duration 0x7f0a001c
int integer status_bar_notification_info_maxnum 0x7f0a001d
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0b0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0b0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0b0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0b0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0b0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0b0005
int interpolator fast_out_slow_in 0x7f0b0006
int interpolator mtrl_fast_out_linear_in 0x7f0b0007
int interpolator mtrl_fast_out_slow_in 0x7f0b0008
int interpolator mtrl_linear 0x7f0b0009
int interpolator mtrl_linear_out_slow_in 0x7f0b000a
int layout _xpopup_adapter_text 0x7f0c0000
int layout _xpopup_adapter_text_match 0x7f0c0001
int layout _xpopup_attach_impl_list 0x7f0c0002
int layout _xpopup_attach_popup_view 0x7f0c0003
int layout _xpopup_bottom_impl_list 0x7f0c0004
int layout _xpopup_bottom_popup_view 0x7f0c0005
int layout _xpopup_bubble_attach_popup_view 0x7f0c0006
int layout _xpopup_center_impl_confirm 0x7f0c0007
int layout _xpopup_center_impl_list 0x7f0c0008
int layout _xpopup_center_impl_loading 0x7f0c0009
int layout _xpopup_center_popup_view 0x7f0c000a
int layout _xpopup_divider 0x7f0c000b
int layout _xpopup_drawer_popup_view 0x7f0c000c
int layout _xpopup_fullscreen_popup_view 0x7f0c000d
int layout _xpopup_image_viewer_popup_view 0x7f0c000e
int layout _xpopup_partshadow_popup_view 0x7f0c000f
int layout _xpopup_position_popup_view 0x7f0c0010
int layout abc_action_bar_title_item 0x7f0c0011
int layout abc_action_bar_up_container 0x7f0c0012
int layout abc_action_menu_item_layout 0x7f0c0013
int layout abc_action_menu_layout 0x7f0c0014
int layout abc_action_mode_bar 0x7f0c0015
int layout abc_action_mode_close_item_material 0x7f0c0016
int layout abc_activity_chooser_view 0x7f0c0017
int layout abc_activity_chooser_view_list_item 0x7f0c0018
int layout abc_alert_dialog_button_bar_material 0x7f0c0019
int layout abc_alert_dialog_material 0x7f0c001a
int layout abc_alert_dialog_title_material 0x7f0c001b
int layout abc_cascading_menu_item_layout 0x7f0c001c
int layout abc_dialog_title_material 0x7f0c001d
int layout abc_expanded_menu_layout 0x7f0c001e
int layout abc_list_menu_item_checkbox 0x7f0c001f
int layout abc_list_menu_item_icon 0x7f0c0020
int layout abc_list_menu_item_layout 0x7f0c0021
int layout abc_list_menu_item_radio 0x7f0c0022
int layout abc_popup_menu_header_item_layout 0x7f0c0023
int layout abc_popup_menu_item_layout 0x7f0c0024
int layout abc_screen_content_include 0x7f0c0025
int layout abc_screen_simple 0x7f0c0026
int layout abc_screen_simple_overlay_action_mode 0x7f0c0027
int layout abc_screen_toolbar 0x7f0c0028
int layout abc_search_dropdown_item_icons_2line 0x7f0c0029
int layout abc_search_view 0x7f0c002a
int layout abc_select_dialog_material 0x7f0c002b
int layout abc_tooltip 0x7f0c002c
int layout activity_allowance 0x7f0c002d
int layout activity_asset_details 0x7f0c002e
int layout activity_backup 0x7f0c002f
int layout activity_backup_mnemonics 0x7f0c0030
int layout activity_backup_private_key 0x7f0c0031
int layout activity_capture 0x7f0c0032
int layout activity_check_mnemonics 0x7f0c0033
int layout activity_community 0x7f0c0034
int layout activity_content_info 0x7f0c0035
int layout activity_create_wallet 0x7f0c0036
int layout activity_dapp_brower 0x7f0c0037
int layout activity_edit_profile 0x7f0c0038
int layout activity_edit_topic 0x7f0c0039
int layout activity_h5 0x7f0c003a
int layout activity_input_wallet 0x7f0c003b
int layout activity_m_task_list 0x7f0c003c
int layout activity_m_topic 0x7f0c003d
int layout activity_main 0x7f0c003e
int layout activity_manger 0x7f0c003f
int layout activity_member_list 0x7f0c0040
int layout activity_message 0x7f0c0041
int layout activity_new_wallet 0x7f0c0042
int layout activity_post 0x7f0c0043
int layout activity_post_quote 0x7f0c0044
int layout activity_receive 0x7f0c0045
int layout activity_search 0x7f0c0046
int layout activity_share_address 0x7f0c0047
int layout activity_test1 0x7f0c0048
int layout activity_tip_list 0x7f0c0049
int layout activity_to_backup_mnemonics 0x7f0c004a
int layout activity_topic 0x7f0c004b
int layout activity_topic_detail 0x7f0c004c
int layout activity_topic_search 0x7f0c004d
int layout activity_transaction_info 0x7f0c004e
int layout activity_transfer 0x7f0c004f
int layout activity_transfer_history_list 0x7f0c0050
int layout activity_transfer_info 0x7f0c0051
int layout activity_user_info 0x7f0c0052
int layout activity_wallet_info 0x7f0c0053
int layout activity_wallet_order 0x7f0c0054
int layout activity_wallet_setting 0x7f0c0055
int layout common_waiting_dialog 0x7f0c0056
int layout crop_activity 0x7f0c0057
int layout custom_dialog 0x7f0c0058
int layout custom_edit_view 0x7f0c0059
int layout custom_input_view 0x7f0c005a
int layout custom_navi_bar 0x7f0c005b
int layout custom_search_view 0x7f0c005c
int layout design_bottom_navigation_item 0x7f0c005d
int layout design_bottom_sheet_dialog 0x7f0c005e
int layout design_layout_snackbar 0x7f0c005f
int layout design_layout_snackbar_include 0x7f0c0060
int layout design_layout_tab_icon 0x7f0c0061
int layout design_layout_tab_text 0x7f0c0062
int layout design_menu_item_action_area 0x7f0c0063
int layout design_navigation_item 0x7f0c0064
int layout design_navigation_item_header 0x7f0c0065
int layout design_navigation_item_separator 0x7f0c0066
int layout design_navigation_item_subheader 0x7f0c0067
int layout design_navigation_menu 0x7f0c0068
int layout design_navigation_menu_item 0x7f0c0069
int layout design_text_input_end_icon 0x7f0c006a
int layout design_text_input_start_icon 0x7f0c006b
int layout filtershow_actionbar 0x7f0c006c
int layout fragment_home 0x7f0c006d
int layout fragment_hot 0x7f0c006e
int layout fragment_hot_topic 0x7f0c006f
int layout fragment_index 0x7f0c0070
int layout fragment_mine 0x7f0c0071
int layout fragment_post_like 0x7f0c0072
int layout fragment_post_report 0x7f0c0073
int layout fragment_recommend 0x7f0c0074
int layout ime_base_split_test_activity 0x7f0c0075
int layout ime_secondary_split_test_activity 0x7f0c0076
int layout include_pickerview_topbar 0x7f0c0077
int layout isb_indicator 0x7f0c0078
int layout item_comment 0x7f0c0079
int layout item_home_tab 0x7f0c007a
int layout item_hot_topic 0x7f0c007b
int layout item_image 0x7f0c007c
int layout item_message 0x7f0c007d
int layout item_mnemonic 0x7f0c007e
int layout item_post 0x7f0c007f
int layout item_post1 0x7f0c0080
int layout item_post_info 0x7f0c0081
int layout item_post_topic 0x7f0c0082
int layout item_proposal_edit 0x7f0c0083
int layout item_proposal_option 0x7f0c0084
int layout item_qtabview 0x7f0c0085
int layout item_search_topic 0x7f0c0086
int layout item_show_image 0x7f0c0087
int layout item_task_roport 0x7f0c0088
int layout item_task_topic 0x7f0c0089
int layout item_topic_class_item 0x7f0c008a
int layout item_topic_tab 0x7f0c008b
int layout item_transaction 0x7f0c008c
int layout item_transfer 0x7f0c008d
int layout item_wallet 0x7f0c008e
int layout layout_basepickerview 0x7f0c008f
int layout layout_gas_select 0x7f0c0090
int layout layout_main 0x7f0c0091
int layout layout_menu 0x7f0c0092
int layout layout_pop_loading 0x7f0c0093
int layout layout_toast 0x7f0c0094
int layout lib_dialog_drag_sort 0x7f0c0095
int layout load_error_page 0x7f0c0096
int layout material_chip_input_combo 0x7f0c0097
int layout material_clock_display 0x7f0c0098
int layout material_clock_display_divider 0x7f0c0099
int layout material_clock_period_toggle 0x7f0c009a
int layout material_clock_period_toggle_land 0x7f0c009b
int layout material_clockface_textview 0x7f0c009c
int layout material_clockface_view 0x7f0c009d
int layout material_radial_view_group 0x7f0c009e
int layout material_textinput_timepicker 0x7f0c009f
int layout material_time_chip 0x7f0c00a0
int layout material_time_input 0x7f0c00a1
int layout material_timepicker 0x7f0c00a2
int layout material_timepicker_dialog 0x7f0c00a3
int layout material_timepicker_textinput_display 0x7f0c00a4
int layout mtrl_alert_dialog 0x7f0c00a5
int layout mtrl_alert_dialog_actions 0x7f0c00a6
int layout mtrl_alert_dialog_title 0x7f0c00a7
int layout mtrl_alert_select_dialog_item 0x7f0c00a8
int layout mtrl_alert_select_dialog_multichoice 0x7f0c00a9
int layout mtrl_alert_select_dialog_singlechoice 0x7f0c00aa
int layout mtrl_calendar_day 0x7f0c00ab
int layout mtrl_calendar_day_of_week 0x7f0c00ac
int layout mtrl_calendar_days_of_week 0x7f0c00ad
int layout mtrl_calendar_horizontal 0x7f0c00ae
int layout mtrl_calendar_month 0x7f0c00af
int layout mtrl_calendar_month_labeled 0x7f0c00b0
int layout mtrl_calendar_month_navigation 0x7f0c00b1
int layout mtrl_calendar_months 0x7f0c00b2
int layout mtrl_calendar_vertical 0x7f0c00b3
int layout mtrl_calendar_year 0x7f0c00b4
int layout mtrl_layout_snackbar 0x7f0c00b5
int layout mtrl_layout_snackbar_include 0x7f0c00b6
int layout mtrl_navigation_rail_item 0x7f0c00b7
int layout mtrl_picker_actions 0x7f0c00b8
int layout mtrl_picker_dialog 0x7f0c00b9
int layout mtrl_picker_fullscreen 0x7f0c00ba
int layout mtrl_picker_header_dialog 0x7f0c00bb
int layout mtrl_picker_header_fullscreen 0x7f0c00bc
int layout mtrl_picker_header_selection_text 0x7f0c00bd
int layout mtrl_picker_header_title_text 0x7f0c00be
int layout mtrl_picker_header_toggle 0x7f0c00bf
int layout mtrl_picker_text_input_date 0x7f0c00c0
int layout mtrl_picker_text_input_date_range 0x7f0c00c1
int layout netwarmingdlg 0x7f0c00c2
int layout notification_action 0x7f0c00c3
int layout notification_action_tombstone 0x7f0c00c4
int layout notification_media_action 0x7f0c00c5
int layout notification_media_cancel_action 0x7f0c00c6
int layout notification_template_big_media 0x7f0c00c7
int layout notification_template_big_media_custom 0x7f0c00c8
int layout notification_template_big_media_narrow 0x7f0c00c9
int layout notification_template_big_media_narrow_custom 0x7f0c00ca
int layout notification_template_custom_big 0x7f0c00cb
int layout notification_template_icon_group 0x7f0c00cc
int layout notification_template_lines_media 0x7f0c00cd
int layout notification_template_media 0x7f0c00ce
int layout notification_template_media_custom 0x7f0c00cf
int layout notification_template_part_chronometer 0x7f0c00d0
int layout notification_template_part_time 0x7f0c00d1
int layout pickerview_options 0x7f0c00d2
int layout pickerview_time 0x7f0c00d3
int layout pop_change_pwd 0x7f0c00d4
int layout pop_check_finger 0x7f0c00d5
int layout pop_check_mnemonics 0x7f0c00d6
int layout pop_check_pwd 0x7f0c00d7
int layout pop_edit 0x7f0c00d8
int layout pop_h5_more 0x7f0c00d9
int layout pop_image 0x7f0c00da
int layout pop_payer 0x7f0c00db
int layout pop_post_forward 0x7f0c00dc
int layout pop_post_image_select 0x7f0c00dd
int layout pop_post_more 0x7f0c00de
int layout pop_post_share 0x7f0c00df
int layout pop_private_key 0x7f0c00e0
int layout pop_select_list 0x7f0c00e1
int layout pop_tip 0x7f0c00e2
int layout pop_transfer_confirm 0x7f0c00e3
int layout pop_two_button_tip 0x7f0c00e4
int layout pop_wallet 0x7f0c00e5
int layout pop_wallet_edit 0x7f0c00e6
int layout pop_wallet_edit_name 0x7f0c00e7
int layout pop_whale_list 0x7f0c00e8
int layout progressdlg 0x7f0c00e9
int layout select_dialog_item_material 0x7f0c00ea
int layout select_dialog_multichoice_material 0x7f0c00eb
int layout select_dialog_singlechoice_material 0x7f0c00ec
int layout support_simple_spinner_dropdown_item 0x7f0c00ed
int layout test_action_chip 0x7f0c00ee
int layout test_chip_zero_corner_radius 0x7f0c00ef
int layout test_design_checkbox 0x7f0c00f0
int layout test_design_radiobutton 0x7f0c00f1
int layout test_navigation_bar_item_layout 0x7f0c00f2
int layout test_reflow_chipgroup 0x7f0c00f3
int layout test_toolbar 0x7f0c00f4
int layout test_toolbar_custom_background 0x7f0c00f5
int layout test_toolbar_elevation 0x7f0c00f6
int layout test_toolbar_surface 0x7f0c00f7
int layout text_view_with_line_height_from_appearance 0x7f0c00f8
int layout text_view_with_line_height_from_layout 0x7f0c00f9
int layout text_view_with_line_height_from_style 0x7f0c00fa
int layout text_view_with_theme_line_height 0x7f0c00fb
int layout text_view_without_line_height 0x7f0c00fc
int layout tip_wallet 0x7f0c00fd
int layout toast_center 0x7f0c00fe
int layout updatedialog 0x7f0c00ff
int layout warn_tip 0x7f0c0100
int menu navigation_home 0x7f0d0000
int mipmap app_icon 0x7f0e0000
int mipmap copy_url 0x7f0e0001
int mipmap ic_launcher 0x7f0e0002
int mipmap ic_launcher_round 0x7f0e0003
int mipmap icon_add_image 0x7f0e0004
int mipmap icon_allowance 0x7f0e0005
int mipmap icon_at 0x7f0e0006
int mipmap icon_back_black 0x7f0e0007
int mipmap icon_backup_1 0x7f0e0008
int mipmap icon_black_down 0x7f0e0009
int mipmap icon_blue_add 0x7f0e000a
int mipmap icon_blue_add1 0x7f0e000b
int mipmap icon_check 0x7f0e000c
int mipmap icon_classify 0x7f0e000d
int mipmap icon_clear 0x7f0e000e
int mipmap icon_close_black 0x7f0e000f
int mipmap icon_close_post 0x7f0e0010
int mipmap icon_collect 0x7f0e0011
int mipmap icon_copy_black 0x7f0e0012
int mipmap icon_copy_gray 0x7f0e0013
int mipmap icon_copy_url0 0x7f0e0014
int mipmap icon_crypto 0x7f0e0015
int mipmap icon_current 0x7f0e0016
int mipmap icon_delete 0x7f0e0017
int mipmap icon_delete_img 0x7f0e0018
int mipmap icon_delete_wallet 0x7f0e0019
int mipmap icon_edit 0x7f0e001a
int mipmap icon_end_time 0x7f0e001b
int mipmap icon_eye_close 0x7f0e001c
int mipmap icon_eye_open 0x7f0e001d
int mipmap icon_favorite 0x7f0e001e
int mipmap icon_fingerprint 0x7f0e001f
int mipmap icon_follow 0x7f0e0020
int mipmap icon_gold_vip 0x7f0e0021
int mipmap icon_governance 0x7f0e0022
int mipmap icon_gray_more 0x7f0e0023
int mipmap icon_heard_logo 0x7f0e0024
int mipmap icon_history 0x7f0e0025
int mipmap icon_home 0x7f0e0026
int mipmap icon_home_default 0x7f0e0027
int mipmap icon_hot 0x7f0e0028
int mipmap icon_hot_selected 0x7f0e0029
int mipmap icon_iv_bird 0x7f0e002a
int mipmap icon_kline_info 0x7f0e002b
int mipmap icon_like_post 0x7f0e002c
int mipmap icon_m 0x7f0e002d
int mipmap icon_menu 0x7f0e002e
int mipmap icon_mnemonics 0x7f0e002f
int mipmap icon_more 0x7f0e0030
int mipmap icon_network 0x7f0e0031
int mipmap icon_notice 0x7f0e0032
int mipmap icon_notifications 0x7f0e0033
int mipmap icon_notifications_selected 0x7f0e0034
int mipmap icon_pop_close 0x7f0e0035
int mipmap icon_post_comment 0x7f0e0036
int mipmap icon_post_forward 0x7f0e0037
int mipmap icon_post_like 0x7f0e0038
int mipmap icon_post_like_select 0x7f0e0039
int mipmap icon_post_save 0x7f0e003a
int mipmap icon_post_save_select 0x7f0e003b
int mipmap icon_post_share 0x7f0e003c
int mipmap icon_private 0x7f0e003d
int mipmap icon_proposal 0x7f0e003e
int mipmap icon_quote 0x7f0e003f
int mipmap icon_received 0x7f0e0040
int mipmap icon_red_close 0x7f0e0041
int mipmap icon_report 0x7f0e0042
int mipmap icon_repost 0x7f0e0043
int mipmap icon_reset_time 0x7f0e0044
int mipmap icon_right 0x7f0e0045
int mipmap icon_right_gray 0x7f0e0046
int mipmap icon_round_del 0x7f0e0047
int mipmap icon_scan 0x7f0e0048
int mipmap icon_scan_white 0x7f0e0049
int mipmap icon_search 0x7f0e004a
int mipmap icon_search_deep_gray 0x7f0e004b
int mipmap icon_select_black 0x7f0e004c
int mipmap icon_select_vote 0x7f0e004d
int mipmap icon_send 0x7f0e004e
int mipmap icon_send_token 0x7f0e004f
int mipmap icon_setting 0x7f0e0050
int mipmap icon_setting_proposal 0x7f0e0051
int mipmap icon_share_pic 0x7f0e0052
int mipmap icon_share_post 0x7f0e0053
int mipmap icon_show_more 0x7f0e0054
int mipmap icon_status_at 0x7f0e0055
int mipmap icon_status_callback 0x7f0e0056
int mipmap icon_status_follow 0x7f0e0057
int mipmap icon_status_like 0x7f0e0058
int mipmap icon_status_save 0x7f0e0059
int mipmap icon_successful 0x7f0e005a
int mipmap icon_tg 0x7f0e005b
int mipmap icon_think_face 0x7f0e005c
int mipmap icon_tok 0x7f0e005d
int mipmap icon_topic 0x7f0e005e
int mipmap icon_topic0 0x7f0e005f
int mipmap icon_topic1 0x7f0e0060
int mipmap icon_topic2 0x7f0e0061
int mipmap icon_topic3 0x7f0e0062
int mipmap icon_topic4 0x7f0e0063
int mipmap icon_topic5 0x7f0e0064
int mipmap icon_topic6 0x7f0e0065
int mipmap icon_topic7 0x7f0e0066
int mipmap icon_topic_list 0x7f0e0067
int mipmap icon_topic_list_default 0x7f0e0068
int mipmap icon_topic_select 0x7f0e0069
int mipmap icon_total_token 0x7f0e006a
int mipmap icon_transaction 0x7f0e006b
int mipmap icon_user 0x7f0e006c
int mipmap icon_user_selected 0x7f0e006d
int mipmap icon_wallet 0x7f0e006e
int mipmap icon_warning 0x7f0e006f
int mipmap icon_x 0x7f0e0070
int mipmap img_wallet 0x7f0e0071
int mipmap index 0x7f0e0072
int mipmap refresh 0x7f0e0073
int mipmap tlk_round 0x7f0e0074
int mipmap topic_heard_default 0x7f0e0075
int plurals mtrl_badge_content_description 0x7f0f0000
int raw beep 0x7f100000
int raw data 0x7f100001
int raw trust_js 0x7f100002
int raw user_info 0x7f100003
int raw user_name_heard 0x7f100004
int raw wallet_data 0x7f100005
int raw word_list 0x7f100006
int string After 0x7f110000
int string Backup 0x7f110001
int string Backup_mnemonics 0x7f110002
int string Backup_mnemonics_or 0x7f110003
int string Cancel 0x7f110004
int string Chinese 0x7f110005
int string Contain_illegal_character 0x7f110006
int string Copy_url 0x7f110007
int string Count 0x7f110008
int string English 0x7f110009
int string In_minute 0x7f11000a
int string Miners_fee 0x7f11000b
int string Mnemonic 0x7f11000c
int string My_qrCode 0x7f11000d
int string Network_Unavailable 0x7f11000e
int string New_version_found 0x7f11000f
int string Please_later 0x7f110010
int string Price 0x7f110011
int string Private_Key 0x7f110012
int string Reenter_password 0x7f110013
int string Refresh 0x7f110014
int string Renew 0x7f110015
int string Search 0x7f110016
int string So_Fast 0x7f110017
int string Successful_operation 0x7f110018
int string Telegram 0x7f110019
int string Updating 0x7f11001a
int string Very_fast 0x7f11001b
int string Wallet 0x7f11001c
int string abc_action_bar_home_description 0x7f11001d
int string abc_action_bar_up_description 0x7f11001e
int string abc_action_menu_overflow_description 0x7f11001f
int string abc_action_mode_done 0x7f110020
int string abc_activity_chooser_view_see_all 0x7f110021
int string abc_activitychooserview_choose_application 0x7f110022
int string abc_capital_off 0x7f110023
int string abc_capital_on 0x7f110024
int string abc_menu_alt_shortcut_label 0x7f110025
int string abc_menu_ctrl_shortcut_label 0x7f110026
int string abc_menu_delete_shortcut_label 0x7f110027
int string abc_menu_enter_shortcut_label 0x7f110028
int string abc_menu_function_shortcut_label 0x7f110029
int string abc_menu_meta_shortcut_label 0x7f11002a
int string abc_menu_shift_shortcut_label 0x7f11002b
int string abc_menu_space_shortcut_label 0x7f11002c
int string abc_menu_sym_shortcut_label 0x7f11002d
int string abc_prepend_shortcut_label 0x7f11002e
int string abc_search_hint 0x7f11002f
int string abc_searchview_description_clear 0x7f110030
int string abc_searchview_description_query 0x7f110031
int string abc_searchview_description_search 0x7f110032
int string abc_searchview_description_submit 0x7f110033
int string abc_searchview_description_voice 0x7f110034
int string abc_shareactionprovider_share_with 0x7f110035
int string abc_shareactionprovider_share_with_application 0x7f110036
int string abc_toolbar_collapse_description 0x7f110037
int string addLiquidity 0x7f110038
int string address_error 0x7f110039
int string androidx_startup 0x7f11003a
int string announcement 0x7f11003b
int string app_name 0x7f11003c
int string appbar_scrolling_view_behavior 0x7f11003d
int string approve 0x7f11003e
int string backup_key 0x7f11003f
int string backup_now 0x7f110040
int string banner_adapter_null_error 0x7f110041
int string banner_adapter_use_error 0x7f110042
int string bottom_sheet_behavior 0x7f110043
int string bottomsheet_action_expand_halfway 0x7f110044
int string browser_error 0x7f110045
int string button_ok 0x7f110046
int string call_notification_answer_action 0x7f110047
int string call_notification_answer_video_action 0x7f110048
int string call_notification_decline_action 0x7f110049
int string call_notification_hang_up_action 0x7f11004a
int string call_notification_incoming_text 0x7f11004b
int string call_notification_ongoing_text 0x7f11004c
int string call_notification_screening_text 0x7f11004d
int string cancle 0x7f11004e
int string cannot_load_image 0x7f11004f
int string change_password 0x7f110050
int string character_counter_content_description 0x7f110051
int string character_counter_overflowed_content_description 0x7f110052
int string character_counter_pattern 0x7f110053
int string check_finger 0x7f110054
int string check_success 0x7f110055
int string check_success_tip 0x7f110056
int string checkpwd_tip 0x7f110057
int string chip_text 0x7f110058
int string clear_text_end_icon_content_description 0x7f110059
int string close_flash 0x7f11005a
int string comfirm 0x7f11005b
int string coming_soon 0x7f11005c
int string common 0x7f11005d
int string confirm 0x7f11005e
int string confirm_del 0x7f11005f
int string copy 0x7f110060
int string copy_address 0x7f110061
int string copy_to 0x7f110062
int string create_or_input_wallet 0x7f110063
int string create_wallet 0x7f110064
int string custom 0x7f110065
int string day_ago 0x7f110066
int string default_filedownloader_notification_content 0x7f110067
int string default_filedownloader_notification_title 0x7f110068
int string delete_confirm 0x7f110069
int string delete_success 0x7f11006a
int string delete_wallet 0x7f11006b
int string dialog_division 0x7f11006c
int string dialog_title 0x7f11006d
int string digits_needed 0x7f11006e
int string edit_Name 0x7f11006f
int string error_icon_content_description 0x7f110070
int string error_pwd 0x7f110071
int string export_Privatekey 0x7f110072
int string exposed_dropdown_menu_content_description 0x7f110073
int string fab_transformation_scrim_behavior 0x7f110074
int string fab_transformation_sheet_behavior 0x7f110075
int string fail 0x7f110076
int string fast 0x7f110077
int string fgh_mask_bottom 0x7f110078
int string fgh_mask_top_pull 0x7f110079
int string fgh_mask_top_release 0x7f11007a
int string fgh_text_game_over 0x7f11007b
int string fgh_text_loading 0x7f11007c
int string fgh_text_loading_failed 0x7f11007d
int string fgh_text_loading_finish 0x7f11007e
int string gallery 0x7f11007f
int string gravity_center 0x7f110080
int string gravity_left 0x7f110081
int string gravity_right 0x7f110082
int string have_wallet_name 0x7f110083
int string hide_bottom_view_on_scroll_behavior 0x7f110084
int string hour_ago 0x7f110085
int string icon_content_description 0x7f110086
int string indicator_color_error 0x7f110087
int string indicator_null_error 0x7f110088
int string input_check_pwd 0x7f110089
int string input_observe 0x7f11008a
int string input_wallet 0x7f11008b
int string input_wallet_name 0x7f11008c
int string item_view_role_description 0x7f11008d
int string know 0x7f11008e
int string less_1 0x7f11008f
int string less_2 0x7f110090
int string less_3 0x7f110091
int string less_5 0x7f110092
int string material_clock_display_divider 0x7f110093
int string material_clock_toggle_content_description 0x7f110094
int string material_hour_selection 0x7f110095
int string material_hour_suffix 0x7f110096
int string material_minute_selection 0x7f110097
int string material_minute_suffix 0x7f110098
int string material_motion_easing_accelerated 0x7f110099
int string material_motion_easing_decelerated 0x7f11009a
int string material_motion_easing_emphasized 0x7f11009b
int string material_motion_easing_linear 0x7f11009c
int string material_motion_easing_standard 0x7f11009d
int string material_slider_range_end 0x7f11009e
int string material_slider_range_start 0x7f11009f
int string material_timepicker_am 0x7f1100a0
int string material_timepicker_clock_mode_description 0x7f1100a1
int string material_timepicker_hour 0x7f1100a2
int string material_timepicker_minute 0x7f1100a3
int string material_timepicker_pm 0x7f1100a4
int string material_timepicker_select_time 0x7f1100a5
int string material_timepicker_text_input_mode_description 0x7f1100a6
int string min_ago 0x7f1100a7
int string mnemonics 0x7f1100a8
int string mnemonics_error 0x7f1100a9
int string msg_camera_framework_bug 0x7f1100aa
int string mtrl_badge_numberless_content_description 0x7f1100ab
int string mtrl_chip_close_icon_content_description 0x7f1100ac
int string mtrl_exceed_max_badge_number_content_description 0x7f1100ad
int string mtrl_exceed_max_badge_number_suffix 0x7f1100ae
int string mtrl_picker_a11y_next_month 0x7f1100af
int string mtrl_picker_a11y_prev_month 0x7f1100b0
int string mtrl_picker_announce_current_selection 0x7f1100b1
int string mtrl_picker_cancel 0x7f1100b2
int string mtrl_picker_confirm 0x7f1100b3
int string mtrl_picker_date_header_selected 0x7f1100b4
int string mtrl_picker_date_header_title 0x7f1100b5
int string mtrl_picker_date_header_unselected 0x7f1100b6
int string mtrl_picker_day_of_week_column_header 0x7f1100b7
int string mtrl_picker_invalid_format 0x7f1100b8
int string mtrl_picker_invalid_format_example 0x7f1100b9
int string mtrl_picker_invalid_format_use 0x7f1100ba
int string mtrl_picker_invalid_range 0x7f1100bb
int string mtrl_picker_navigate_to_year_description 0x7f1100bc
int string mtrl_picker_out_of_range 0x7f1100bd
int string mtrl_picker_range_header_only_end_selected 0x7f1100be
int string mtrl_picker_range_header_only_start_selected 0x7f1100bf
int string mtrl_picker_range_header_selected 0x7f1100c0
int string mtrl_picker_range_header_title 0x7f1100c1
int string mtrl_picker_range_header_unselected 0x7f1100c2
int string mtrl_picker_save 0x7f1100c3
int string mtrl_picker_text_input_date_hint 0x7f1100c4
int string mtrl_picker_text_input_date_range_end_hint 0x7f1100c5
int string mtrl_picker_text_input_date_range_start_hint 0x7f1100c6
int string mtrl_picker_text_input_day_abbr 0x7f1100c7
int string mtrl_picker_text_input_month_abbr 0x7f1100c8
int string mtrl_picker_text_input_year_abbr 0x7f1100c9
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f1100ca
int string mtrl_picker_toggle_to_day_selection 0x7f1100cb
int string mtrl_picker_toggle_to_text_input_mode 0x7f1100cc
int string mtrl_picker_toggle_to_year_selection 0x7f1100cd
int string next 0x7f1100ce
int string no_data 0x7f1100cf
int string no_sdcard 0x7f1100d0
int string no_space 0x7f1100d1
int string no_space_address 0x7f1100d2
int string not_wifi_tip 0x7f1100d3
int string notnull 0x7f1100d4
int string old_password 0x7f1100d5
int string open_flash 0x7f1100d6
int string password_toggle_content_description 0x7f1100d7
int string passworld_disaffinity 0x7f1100d8
int string passworld_must 0x7f1100d9
int string path_password_eye 0x7f1100da
int string path_password_eye_mask_strike_through 0x7f1100db
int string path_password_eye_mask_visible 0x7f1100dc
int string path_password_strike_through 0x7f1100dd
int string pickerview_cancel 0x7f1100de
int string pickerview_day 0x7f1100df
int string pickerview_hours 0x7f1100e0
int string pickerview_minutes 0x7f1100e1
int string pickerview_month 0x7f1100e2
int string pickerview_seconds 0x7f1100e3
int string pickerview_submit 0x7f1100e4
int string pickerview_year 0x7f1100e5
int string private_key_error 0x7f1100e6
int string rank 0x7f1100e7
int string reset_password 0x7f1100e8
int string reset_wallet_name 0x7f1100e9
int string save 0x7f1100ea
int string save_rank 0x7f1100eb
int string scan_code 0x7f1100ec
int string scan_failed_tip 0x7f1100ed
int string search_menu_title 0x7f1100ee
int string select_browser 0x7f1100ef
int string select_image 0x7f1100f0
int string share_qr 0x7f1100f1
int string srl_component_falsify 0x7f1100f2
int string srl_content_empty 0x7f1100f3
int string srl_footer_failed 0x7f1100f4
int string srl_footer_finish 0x7f1100f5
int string srl_footer_loading 0x7f1100f6
int string srl_footer_nothing 0x7f1100f7
int string srl_footer_pulling 0x7f1100f8
int string srl_footer_refreshing 0x7f1100f9
int string srl_footer_release 0x7f1100fa
int string srl_header_failed 0x7f1100fb
int string srl_header_finish 0x7f1100fc
int string srl_header_loading 0x7f1100fd
int string srl_header_pulling 0x7f1100fe
int string srl_header_refreshing 0x7f1100ff
int string srl_header_release 0x7f110100
int string srl_header_secondary 0x7f110101
int string srl_header_update 0x7f110102
int string status_bar_notification_info_overflow 0x7f110103
int string success 0x7f110104
int string text_back_up 0x7f110105
int string to_backup_first 0x7f110106
int string url_error 0x7f110107
int string wallet_address 0x7f110108
int string wallet_already_exists 0x7f110109
int string wallet_info 0x7f11010a
int string wallet_list 0x7f11010b
int string wallet_name 0x7f11010c
int string wallet_nickname_exists 0x7f11010d
int string with_space 0x7f11010e
int string world_must_list 0x7f11010f
int string xpopup_cancel 0x7f110110
int string xpopup_image_not_exist 0x7f110111
int string xpopup_ok 0x7f110112
int string xpopup_save 0x7f110113
int string xpopup_saved_fail 0x7f110114
int string xpopup_saved_to_gallery 0x7f110115
int string zxing_app_name 0x7f110116
int style AlertDialog_AppCompat 0x7f120000
int style AlertDialog_AppCompat_Light 0x7f120001
int style AndroidThemeColorAccentYellow 0x7f120002
int style Animation_AppCompat_Dialog 0x7f120003
int style Animation_AppCompat_DropDownUp 0x7f120004
int style Animation_AppCompat_Tooltip 0x7f120005
int style Animation_Design_BottomSheetDialog 0x7f120006
int style Animation_MaterialComponents_BottomSheetDialog 0x7f120007
int style AppTheme 0x7f120008
int style Base_AlertDialog_AppCompat 0x7f120009
int style Base_AlertDialog_AppCompat_Light 0x7f12000a
int style Base_Animation_AppCompat_Dialog 0x7f12000b
int style Base_Animation_AppCompat_DropDownUp 0x7f12000c
int style Base_Animation_AppCompat_Tooltip 0x7f12000d
int style Base_CardView 0x7f12000e
int style Base_DialogWindowTitle_AppCompat 0x7f12000f
int style Base_DialogWindowTitleBackground_AppCompat 0x7f120010
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f120011
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f120012
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f120013
int style Base_TextAppearance_AppCompat 0x7f120014
int style Base_TextAppearance_AppCompat_Body1 0x7f120015
int style Base_TextAppearance_AppCompat_Body2 0x7f120016
int style Base_TextAppearance_AppCompat_Button 0x7f120017
int style Base_TextAppearance_AppCompat_Caption 0x7f120018
int style Base_TextAppearance_AppCompat_Display1 0x7f120019
int style Base_TextAppearance_AppCompat_Display2 0x7f12001a
int style Base_TextAppearance_AppCompat_Display3 0x7f12001b
int style Base_TextAppearance_AppCompat_Display4 0x7f12001c
int style Base_TextAppearance_AppCompat_Headline 0x7f12001d
int style Base_TextAppearance_AppCompat_Inverse 0x7f12001e
int style Base_TextAppearance_AppCompat_Large 0x7f12001f
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f120020
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f120021
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f120022
int style Base_TextAppearance_AppCompat_Medium 0x7f120023
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f120024
int style Base_TextAppearance_AppCompat_Menu 0x7f120025
int style Base_TextAppearance_AppCompat_SearchResult 0x7f120026
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f120027
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f120028
int style Base_TextAppearance_AppCompat_Small 0x7f120029
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f12002a
int style Base_TextAppearance_AppCompat_Subhead 0x7f12002b
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f12002c
int style Base_TextAppearance_AppCompat_Title 0x7f12002d
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f12002e
int style Base_TextAppearance_AppCompat_Tooltip 0x7f12002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f120030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f120031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f120032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f120033
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f120034
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f120035
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f120036
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f120037
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f120038
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f120039
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f12003a
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f12003b
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f12003c
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f12003d
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f12003e
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f12003f
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f120040
int style Base_TextAppearance_MaterialComponents_Badge 0x7f120041
int style Base_TextAppearance_MaterialComponents_Button 0x7f120042
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f120043
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f120044
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f120045
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f120046
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f120047
int style Base_Theme_AppCompat 0x7f120048
int style Base_Theme_AppCompat_CompactMenu 0x7f120049
int style Base_Theme_AppCompat_Dialog 0x7f12004a
int style Base_Theme_AppCompat_Dialog_Alert 0x7f12004b
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f12004c
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f12004d
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f12004e
int style Base_Theme_AppCompat_Light 0x7f12004f
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f120050
int style Base_Theme_AppCompat_Light_Dialog 0x7f120051
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f120052
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f120053
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f120054
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f120055
int style Base_Theme_MaterialComponents 0x7f120056
int style Base_Theme_MaterialComponents_Bridge 0x7f120057
int style Base_Theme_MaterialComponents_CompactMenu 0x7f120058
int style Base_Theme_MaterialComponents_Dialog 0x7f120059
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f12005a
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f12005b
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f12005c
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f12005d
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f12005e
int style Base_Theme_MaterialComponents_Light 0x7f12005f
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f120060
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f120061
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f120062
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f120063
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f120064
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f120065
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f120066
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f120067
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f120068
int style Base_ThemeOverlay_AppCompat 0x7f120069
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f12006a
int style Base_ThemeOverlay_AppCompat_Dark 0x7f12006b
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f12006c
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f12006d
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f12006e
int style Base_ThemeOverlay_AppCompat_Light 0x7f12006f
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f120070
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f120071
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f120072
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f120073
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f120074
int style Base_V14_Theme_MaterialComponents 0x7f120075
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f120076
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f120077
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f120078
int style Base_V14_Theme_MaterialComponents_Light 0x7f120079
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f12007a
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f12007b
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f12007c
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f12007d
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f12007e
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f12007f
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f120080
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f120081
int style Base_V21_Theme_AppCompat 0x7f120082
int style Base_V21_Theme_AppCompat_Dialog 0x7f120083
int style Base_V21_Theme_AppCompat_Light 0x7f120084
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f120085
int style Base_V21_Theme_MaterialComponents 0x7f120086
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f120087
int style Base_V21_Theme_MaterialComponents_Light 0x7f120088
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f120089
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f12008a
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f12008b
int style Base_V22_Theme_AppCompat 0x7f12008c
int style Base_V22_Theme_AppCompat_Light 0x7f12008d
int style Base_V23_Theme_AppCompat 0x7f12008e
int style Base_V23_Theme_AppCompat_Light 0x7f12008f
int style Base_V26_Theme_AppCompat 0x7f120090
int style Base_V26_Theme_AppCompat_Light 0x7f120091
int style Base_V26_Widget_AppCompat_Toolbar 0x7f120092
int style Base_V28_Theme_AppCompat 0x7f120093
int style Base_V28_Theme_AppCompat_Light 0x7f120094
int style Base_V7_Theme_AppCompat 0x7f120095
int style Base_V7_Theme_AppCompat_Dialog 0x7f120096
int style Base_V7_Theme_AppCompat_Light 0x7f120097
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f120098
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f120099
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f12009a
int style Base_V7_Widget_AppCompat_EditText 0x7f12009b
int style Base_V7_Widget_AppCompat_Toolbar 0x7f12009c
int style Base_Widget_AppCompat_ActionBar 0x7f12009d
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f12009e
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f12009f
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f1200a0
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1200a1
int style Base_Widget_AppCompat_ActionButton 0x7f1200a2
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1200a3
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1200a4
int style Base_Widget_AppCompat_ActionMode 0x7f1200a5
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1200a6
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1200a7
int style Base_Widget_AppCompat_Button 0x7f1200a8
int style Base_Widget_AppCompat_Button_Borderless 0x7f1200a9
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1200aa
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1200ab
int style Base_Widget_AppCompat_Button_Colored 0x7f1200ac
int style Base_Widget_AppCompat_Button_Small 0x7f1200ad
int style Base_Widget_AppCompat_ButtonBar 0x7f1200ae
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1200af
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1200b0
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1200b1
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1200b2
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1200b3
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1200b4
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1200b5
int style Base_Widget_AppCompat_EditText 0x7f1200b6
int style Base_Widget_AppCompat_ImageButton 0x7f1200b7
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1200b8
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1200b9
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1200ba
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1200bb
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1200bc
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1200bd
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1200be
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1200bf
int style Base_Widget_AppCompat_ListMenuView 0x7f1200c0
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1200c1
int style Base_Widget_AppCompat_ListView 0x7f1200c2
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1200c3
int style Base_Widget_AppCompat_ListView_Menu 0x7f1200c4
int style Base_Widget_AppCompat_PopupMenu 0x7f1200c5
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1200c6
int style Base_Widget_AppCompat_PopupWindow 0x7f1200c7
int style Base_Widget_AppCompat_ProgressBar 0x7f1200c8
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1200c9
int style Base_Widget_AppCompat_RatingBar 0x7f1200ca
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1200cb
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1200cc
int style Base_Widget_AppCompat_SearchView 0x7f1200cd
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1200ce
int style Base_Widget_AppCompat_SeekBar 0x7f1200cf
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1200d0
int style Base_Widget_AppCompat_Spinner 0x7f1200d1
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1200d2
int style Base_Widget_AppCompat_TextView 0x7f1200d3
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1200d4
int style Base_Widget_AppCompat_Toolbar 0x7f1200d5
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1200d6
int style Base_Widget_Design_TabLayout 0x7f1200d7
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f1200d8
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f1200d9
int style Base_Widget_MaterialComponents_Chip 0x7f1200da
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f1200db
int style Base_Widget_MaterialComponents_PopupMenu 0x7f1200dc
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f1200dd
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f1200de
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f1200df
int style Base_Widget_MaterialComponents_Slider 0x7f1200e0
int style Base_Widget_MaterialComponents_Snackbar 0x7f1200e1
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f1200e2
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f1200e3
int style Base_Widget_MaterialComponents_TextView 0x7f1200e4
int style CardView 0x7f1200e5
int style CardView_Dark 0x7f1200e6
int style CardView_Light 0x7f1200e7
int style DialogAnim 0x7f1200e8
int style DragSortDialog 0x7f1200e9
int style EmptyTheme 0x7f1200ea
int style Holo_ActionBar 0x7f1200eb
int style MaterialAlertDialog_MaterialComponents 0x7f1200ec
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f1200ed
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f1200ee
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f1200ef
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f1200f0
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f1200f1
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f1200f2
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f1200f3
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f1200f4
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f1200f5
int style MyEditText 0x7f1200f6
int style Platform_AppCompat 0x7f1200f7
int style Platform_AppCompat_Light 0x7f1200f8
int style Platform_MaterialComponents 0x7f1200f9
int style Platform_MaterialComponents_Dialog 0x7f1200fa
int style Platform_MaterialComponents_Light 0x7f1200fb
int style Platform_MaterialComponents_Light_Dialog 0x7f1200fc
int style Platform_ThemeOverlay_AppCompat 0x7f1200fd
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f1200fe
int style Platform_ThemeOverlay_AppCompat_Light 0x7f1200ff
int style Platform_V21_AppCompat 0x7f120100
int style Platform_V21_AppCompat_Light 0x7f120101
int style Platform_V25_AppCompat 0x7f120102
int style Platform_V25_AppCompat_Light 0x7f120103
int style Platform_Widget_AppCompat_Spinner 0x7f120104
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f120105
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f120106
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f120107
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f120108
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f120109
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f12010a
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f12010b
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f12010c
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f12010d
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f12010e
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f12010f
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f120110
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f120111
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f120112
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f120113
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f120114
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f120115
int style ShapeAppearance_MaterialComponents 0x7f120116
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f120117
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f120118
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f120119
int style ShapeAppearance_MaterialComponents_Test 0x7f12011a
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f12011b
int style ShapeAppearanceOverlay 0x7f12011c
int style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize 0x7f12011d
int style ShapeAppearanceOverlay_BottomRightCut 0x7f12011e
int style ShapeAppearanceOverlay_Cut 0x7f12011f
int style ShapeAppearanceOverlay_DifferentCornerSize 0x7f120120
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f120121
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f120122
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f120123
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f120124
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f120125
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f120126
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f120127
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f120128
int style ShapeAppearanceOverlay_TopLeftCut 0x7f120129
int style ShapeAppearanceOverlay_TopRightDifferentCornerSize 0x7f12012a
int style SplashTheme 0x7f12012b
int style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f12012c
int style Test_Theme_MaterialComponents_MaterialCalendar 0x7f12012d
int style Test_Widget_MaterialComponents_MaterialCalendar 0x7f12012e
int style Test_Widget_MaterialComponents_MaterialCalendar_Day 0x7f12012f
int style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f120130
int style TestStyleWithLineHeight 0x7f120131
int style TestStyleWithLineHeightAppearance 0x7f120132
int style TestStyleWithThemeLineHeightAttribute 0x7f120133
int style TestStyleWithoutLineHeight 0x7f120134
int style TestThemeWithLineHeight 0x7f120135
int style TestThemeWithLineHeightDisabled 0x7f120136
int style TextAppearance_AppCompat 0x7f120137
int style TextAppearance_AppCompat_Body1 0x7f120138
int style TextAppearance_AppCompat_Body2 0x7f120139
int style TextAppearance_AppCompat_Button 0x7f12013a
int style TextAppearance_AppCompat_Caption 0x7f12013b
int style TextAppearance_AppCompat_Display1 0x7f12013c
int style TextAppearance_AppCompat_Display2 0x7f12013d
int style TextAppearance_AppCompat_Display3 0x7f12013e
int style TextAppearance_AppCompat_Display4 0x7f12013f
int style TextAppearance_AppCompat_Headline 0x7f120140
int style TextAppearance_AppCompat_Inverse 0x7f120141
int style TextAppearance_AppCompat_Large 0x7f120142
int style TextAppearance_AppCompat_Large_Inverse 0x7f120143
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f120144
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f120145
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f120146
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f120147
int style TextAppearance_AppCompat_Medium 0x7f120148
int style TextAppearance_AppCompat_Medium_Inverse 0x7f120149
int style TextAppearance_AppCompat_Menu 0x7f12014a
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f12014b
int style TextAppearance_AppCompat_SearchResult_Title 0x7f12014c
int style TextAppearance_AppCompat_Small 0x7f12014d
int style TextAppearance_AppCompat_Small_Inverse 0x7f12014e
int style TextAppearance_AppCompat_Subhead 0x7f12014f
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f120150
int style TextAppearance_AppCompat_Title 0x7f120151
int style TextAppearance_AppCompat_Title_Inverse 0x7f120152
int style TextAppearance_AppCompat_Tooltip 0x7f120153
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f120154
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f120155
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f120156
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f120157
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f120158
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f120159
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f12015a
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f12015b
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f12015c
int style TextAppearance_AppCompat_Widget_Button 0x7f12015d
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f12015e
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f12015f
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f120160
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f120161
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f120162
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f120163
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f120164
int style TextAppearance_AppCompat_Widget_Switch 0x7f120165
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f120166
int style TextAppearance_Compat_Notification 0x7f120167
int style TextAppearance_Compat_Notification_Info 0x7f120168
int style TextAppearance_Compat_Notification_Info_Media 0x7f120169
int style TextAppearance_Compat_Notification_Line2 0x7f12016a
int style TextAppearance_Compat_Notification_Line2_Media 0x7f12016b
int style TextAppearance_Compat_Notification_Media 0x7f12016c
int style TextAppearance_Compat_Notification_Time 0x7f12016d
int style TextAppearance_Compat_Notification_Time_Media 0x7f12016e
int style TextAppearance_Compat_Notification_Title 0x7f12016f
int style TextAppearance_Compat_Notification_Title_Media 0x7f120170
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f120171
int style TextAppearance_Design_Counter 0x7f120172
int style TextAppearance_Design_Counter_Overflow 0x7f120173
int style TextAppearance_Design_Error 0x7f120174
int style TextAppearance_Design_HelperText 0x7f120175
int style TextAppearance_Design_Hint 0x7f120176
int style TextAppearance_Design_Placeholder 0x7f120177
int style TextAppearance_Design_Prefix 0x7f120178
int style TextAppearance_Design_Snackbar_Message 0x7f120179
int style TextAppearance_Design_Suffix 0x7f12017a
int style TextAppearance_Design_Tab 0x7f12017b
int style TextAppearance_MaterialComponents_Badge 0x7f12017c
int style TextAppearance_MaterialComponents_Body1 0x7f12017d
int style TextAppearance_MaterialComponents_Body2 0x7f12017e
int style TextAppearance_MaterialComponents_Button 0x7f12017f
int style TextAppearance_MaterialComponents_Caption 0x7f120180
int style TextAppearance_MaterialComponents_Chip 0x7f120181
int style TextAppearance_MaterialComponents_Headline1 0x7f120182
int style TextAppearance_MaterialComponents_Headline2 0x7f120183
int style TextAppearance_MaterialComponents_Headline3 0x7f120184
int style TextAppearance_MaterialComponents_Headline4 0x7f120185
int style TextAppearance_MaterialComponents_Headline5 0x7f120186
int style TextAppearance_MaterialComponents_Headline6 0x7f120187
int style TextAppearance_MaterialComponents_Overline 0x7f120188
int style TextAppearance_MaterialComponents_Subtitle1 0x7f120189
int style TextAppearance_MaterialComponents_Subtitle2 0x7f12018a
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f12018b
int style TextAppearance_MaterialComponents_Tooltip 0x7f12018c
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f12018d
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f12018e
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f12018f
int style Theme_AppCompat 0x7f120190
int style Theme_AppCompat_CompactMenu 0x7f120191
int style Theme_AppCompat_DayNight 0x7f120192
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f120193
int style Theme_AppCompat_DayNight_Dialog 0x7f120194
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f120195
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f120196
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f120197
int style Theme_AppCompat_DayNight_NoActionBar 0x7f120198
int style Theme_AppCompat_Dialog 0x7f120199
int style Theme_AppCompat_Dialog_Alert 0x7f12019a
int style Theme_AppCompat_Dialog_MinWidth 0x7f12019b
int style Theme_AppCompat_DialogWhenLarge 0x7f12019c
int style Theme_AppCompat_Empty 0x7f12019d
int style Theme_AppCompat_Light 0x7f12019e
int style Theme_AppCompat_Light_DarkActionBar 0x7f12019f
int style Theme_AppCompat_Light_Dialog 0x7f1201a0
int style Theme_AppCompat_Light_Dialog_Alert 0x7f1201a1
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f1201a2
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f1201a3
int style Theme_AppCompat_Light_NoActionBar 0x7f1201a4
int style Theme_AppCompat_NoActionBar 0x7f1201a5
int style Theme_Crop 0x7f1201a6
int style Theme_Design 0x7f1201a7
int style Theme_Design_BottomSheetDialog 0x7f1201a8
int style Theme_Design_Light 0x7f1201a9
int style Theme_Design_Light_BottomSheetDialog 0x7f1201aa
int style Theme_Design_Light_NoActionBar 0x7f1201ab
int style Theme_Design_NoActionBar 0x7f1201ac
int style Theme_MaterialComponents 0x7f1201ad
int style Theme_MaterialComponents_BottomSheetDialog 0x7f1201ae
int style Theme_MaterialComponents_Bridge 0x7f1201af
int style Theme_MaterialComponents_CompactMenu 0x7f1201b0
int style Theme_MaterialComponents_DayNight 0x7f1201b1
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f1201b2
int style Theme_MaterialComponents_DayNight_Bridge 0x7f1201b3
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f1201b4
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f1201b5
int style Theme_MaterialComponents_DayNight_Dialog 0x7f1201b6
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f1201b7
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f1201b8
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f1201b9
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f1201ba
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f1201bb
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f1201bc
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f1201bd
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f1201be
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f1201bf
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f1201c0
int style Theme_MaterialComponents_Dialog 0x7f1201c1
int style Theme_MaterialComponents_Dialog_Alert 0x7f1201c2
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f1201c3
int style Theme_MaterialComponents_Dialog_Bridge 0x7f1201c4
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f1201c5
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f1201c6
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f1201c7
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f1201c8
int style Theme_MaterialComponents_DialogWhenLarge 0x7f1201c9
int style Theme_MaterialComponents_Light 0x7f1201ca
int style Theme_MaterialComponents_Light_BarSize 0x7f1201cb
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f1201cc
int style Theme_MaterialComponents_Light_Bridge 0x7f1201cd
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f1201ce
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f1201cf
int style Theme_MaterialComponents_Light_Dialog 0x7f1201d0
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f1201d1
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f1201d2
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f1201d3
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f1201d4
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f1201d5
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f1201d6
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f1201d7
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f1201d8
int style Theme_MaterialComponents_Light_LargeTouch 0x7f1201d9
int style Theme_MaterialComponents_Light_NoActionBar 0x7f1201da
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f1201db
int style Theme_MaterialComponents_NoActionBar 0x7f1201dc
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f1201dd
int style ThemeOverlay_AppCompat 0x7f1201de
int style ThemeOverlay_AppCompat_ActionBar 0x7f1201df
int style ThemeOverlay_AppCompat_Dark 0x7f1201e0
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f1201e1
int style ThemeOverlay_AppCompat_DayNight 0x7f1201e2
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f1201e3
int style ThemeOverlay_AppCompat_Dialog 0x7f1201e4
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f1201e5
int style ThemeOverlay_AppCompat_Light 0x7f1201e6
int style ThemeOverlay_Design_TextInputEditText 0x7f1201e7
int style ThemeOverlay_MaterialComponents 0x7f1201e8
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f1201e9
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f1201ea
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f1201eb
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f1201ec
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f1201ed
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f1201ee
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f1201ef
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f1201f0
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f1201f1
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f1201f2
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1201f3
int style ThemeOverlay_MaterialComponents_Dark 0x7f1201f4
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f1201f5
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f1201f6
int style ThemeOverlay_MaterialComponents_Dialog 0x7f1201f7
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f1201f8
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f1201f9
int style ThemeOverlay_MaterialComponents_Light 0x7f1201fa
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f1201fb
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f1201fc
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f1201fd
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f1201fe
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f1201ff
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f120200
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f120201
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f120202
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f120203
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f120204
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f120205
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f120206
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f120207
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f120208
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f120209
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f12020a
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f12020b
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f12020c
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f12020d
int style ThemeOverlayColorAccentRed 0x7f12020e
int style Widget_AppCompat_ActionBar 0x7f12020f
int style Widget_AppCompat_ActionBar_Solid 0x7f120210
int style Widget_AppCompat_ActionBar_TabBar 0x7f120211
int style Widget_AppCompat_ActionBar_TabText 0x7f120212
int style Widget_AppCompat_ActionBar_TabView 0x7f120213
int style Widget_AppCompat_ActionButton 0x7f120214
int style Widget_AppCompat_ActionButton_CloseMode 0x7f120215
int style Widget_AppCompat_ActionButton_Overflow 0x7f120216
int style Widget_AppCompat_ActionMode 0x7f120217
int style Widget_AppCompat_ActivityChooserView 0x7f120218
int style Widget_AppCompat_AutoCompleteTextView 0x7f120219
int style Widget_AppCompat_Button 0x7f12021a
int style Widget_AppCompat_Button_Borderless 0x7f12021b
int style Widget_AppCompat_Button_Borderless_Colored 0x7f12021c
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f12021d
int style Widget_AppCompat_Button_Colored 0x7f12021e
int style Widget_AppCompat_Button_Small 0x7f12021f
int style Widget_AppCompat_ButtonBar 0x7f120220
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f120221
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f120222
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f120223
int style Widget_AppCompat_CompoundButton_Switch 0x7f120224
int style Widget_AppCompat_DrawerArrowToggle 0x7f120225
int style Widget_AppCompat_DropDownItem_Spinner 0x7f120226
int style Widget_AppCompat_EditText 0x7f120227
int style Widget_AppCompat_ImageButton 0x7f120228
int style Widget_AppCompat_Light_ActionBar 0x7f120229
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f12022a
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f12022b
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f12022c
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f12022d
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f12022e
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f12022f
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f120230
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f120231
int style Widget_AppCompat_Light_ActionButton 0x7f120232
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f120233
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f120234
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f120235
int style Widget_AppCompat_Light_ActivityChooserView 0x7f120236
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f120237
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f120238
int style Widget_AppCompat_Light_ListPopupWindow 0x7f120239
int style Widget_AppCompat_Light_ListView_DropDown 0x7f12023a
int style Widget_AppCompat_Light_PopupMenu 0x7f12023b
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f12023c
int style Widget_AppCompat_Light_SearchView 0x7f12023d
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f12023e
int style Widget_AppCompat_ListMenuView 0x7f12023f
int style Widget_AppCompat_ListPopupWindow 0x7f120240
int style Widget_AppCompat_ListView 0x7f120241
int style Widget_AppCompat_ListView_DropDown 0x7f120242
int style Widget_AppCompat_ListView_Menu 0x7f120243
int style Widget_AppCompat_PopupMenu 0x7f120244
int style Widget_AppCompat_PopupMenu_Overflow 0x7f120245
int style Widget_AppCompat_PopupWindow 0x7f120246
int style Widget_AppCompat_ProgressBar 0x7f120247
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f120248
int style Widget_AppCompat_RatingBar 0x7f120249
int style Widget_AppCompat_RatingBar_Indicator 0x7f12024a
int style Widget_AppCompat_RatingBar_Small 0x7f12024b
int style Widget_AppCompat_SearchView 0x7f12024c
int style Widget_AppCompat_SearchView_ActionBar 0x7f12024d
int style Widget_AppCompat_SeekBar 0x7f12024e
int style Widget_AppCompat_SeekBar_Discrete 0x7f12024f
int style Widget_AppCompat_Spinner 0x7f120250
int style Widget_AppCompat_Spinner_DropDown 0x7f120251
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f120252
int style Widget_AppCompat_Spinner_Underlined 0x7f120253
int style Widget_AppCompat_TextView 0x7f120254
int style Widget_AppCompat_TextView_SpinnerItem 0x7f120255
int style Widget_AppCompat_Toolbar 0x7f120256
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f120257
int style Widget_Compat_NotificationActionContainer 0x7f120258
int style Widget_Compat_NotificationActionText 0x7f120259
int style Widget_Design_AppBarLayout 0x7f12025a
int style Widget_Design_BottomNavigationView 0x7f12025b
int style Widget_Design_BottomSheet_Modal 0x7f12025c
int style Widget_Design_CollapsingToolbar 0x7f12025d
int style Widget_Design_FloatingActionButton 0x7f12025e
int style Widget_Design_NavigationView 0x7f12025f
int style Widget_Design_ScrimInsetsFrameLayout 0x7f120260
int style Widget_Design_Snackbar 0x7f120261
int style Widget_Design_TabLayout 0x7f120262
int style Widget_Design_TextInputEditText 0x7f120263
int style Widget_Design_TextInputLayout 0x7f120264
int style Widget_MaterialComponents_ActionBar_Primary 0x7f120265
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f120266
int style Widget_MaterialComponents_ActionBar_Solid 0x7f120267
int style Widget_MaterialComponents_ActionBar_Surface 0x7f120268
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f120269
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f12026a
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f12026b
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f12026c
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f12026d
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f12026e
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f12026f
int style Widget_MaterialComponents_Badge 0x7f120270
int style Widget_MaterialComponents_BottomAppBar 0x7f120271
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f120272
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f120273
int style Widget_MaterialComponents_BottomNavigationView 0x7f120274
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f120275
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f120276
int style Widget_MaterialComponents_BottomSheet 0x7f120277
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f120278
int style Widget_MaterialComponents_Button 0x7f120279
int style Widget_MaterialComponents_Button_Icon 0x7f12027a
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f12027b
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f12027c
int style Widget_MaterialComponents_Button_TextButton 0x7f12027d
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f12027e
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f12027f
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f120280
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f120281
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f120282
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f120283
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f120284
int style Widget_MaterialComponents_CardView 0x7f120285
int style Widget_MaterialComponents_CheckedTextView 0x7f120286
int style Widget_MaterialComponents_Chip_Action 0x7f120287
int style Widget_MaterialComponents_Chip_Choice 0x7f120288
int style Widget_MaterialComponents_Chip_Entry 0x7f120289
int style Widget_MaterialComponents_Chip_Filter 0x7f12028a
int style Widget_MaterialComponents_ChipGroup 0x7f12028b
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f12028c
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f12028d
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f12028e
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f12028f
int style Widget_MaterialComponents_CollapsingToolbar 0x7f120290
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f120291
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f120292
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f120293
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f120294
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f120295
int style Widget_MaterialComponents_FloatingActionButton 0x7f120296
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f120297
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f120298
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f120299
int style Widget_MaterialComponents_MaterialCalendar 0x7f12029a
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f12029b
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f12029c
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f12029d
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f12029e
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f12029f
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f1202a0
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f1202a1
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f1202a2
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f1202a3
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f1202a4
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f1202a5
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f1202a6
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f1202a7
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f1202a8
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f1202a9
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f1202aa
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f1202ab
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f1202ac
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f1202ad
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f1202ae
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f1202af
int style Widget_MaterialComponents_NavigationRailView 0x7f1202b0
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f1202b1
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f1202b2
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f1202b3
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f1202b4
int style Widget_MaterialComponents_NavigationView 0x7f1202b5
int style Widget_MaterialComponents_PopupMenu 0x7f1202b6
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f1202b7
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f1202b8
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f1202b9
int style Widget_MaterialComponents_ProgressIndicator 0x7f1202ba
int style Widget_MaterialComponents_ShapeableImageView 0x7f1202bb
int style Widget_MaterialComponents_Slider 0x7f1202bc
int style Widget_MaterialComponents_Snackbar 0x7f1202bd
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f1202be
int style Widget_MaterialComponents_Snackbar_TextView 0x7f1202bf
int style Widget_MaterialComponents_TabLayout 0x7f1202c0
int style Widget_MaterialComponents_TabLayout_Colored 0x7f1202c1
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f1202c2
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f1202c3
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f1202c4
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f1202c5
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f1202c6
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f1202c7
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f1202c8
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f1202c9
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f1202ca
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f1202cb
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f1202cc
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f1202cd
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f1202ce
int style Widget_MaterialComponents_TextView 0x7f1202cf
int style Widget_MaterialComponents_TimePicker 0x7f1202d0
int style Widget_MaterialComponents_TimePicker_Button 0x7f1202d1
int style Widget_MaterialComponents_TimePicker_Clock 0x7f1202d2
int style Widget_MaterialComponents_TimePicker_Display 0x7f1202d3
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f1202d4
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f1202d5
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f1202d6
int style Widget_MaterialComponents_Toolbar 0x7f1202d7
int style Widget_MaterialComponents_Toolbar_Primary 0x7f1202d8
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f1202d9
int style Widget_MaterialComponents_Toolbar_Surface 0x7f1202da
int style Widget_MaterialComponents_Tooltip 0x7f1202db
int style Widget_Support_CoordinatorLayout 0x7f1202dc
int style ZxingTheme 0x7f1202dd
int style _XPopup_TransparentDialog 0x7f1202de
int style custom_dialog2 0x7f1202df
int style custom_dialog_style 0x7f1202e0
int style deep_gray_horizontal_line_view 0x7f1202e1
int style edit_input_text 0x7f1202e2
int style edit_profile_text 0x7f1202e3
int style edit_text 0x7f1202e4
int style edit_text_proposal 0x7f1202e5
int style gray_horizontal_line_view 0x7f1202e6
int style picker_view_scale_anim 0x7f1202e7
int style picker_view_slide_anim 0x7f1202e8
int style text_style 0x7f1202e9
int style update_dialog 0x7f1202ea
int[] styleable ActionBar { 0x7f03003f, 0x7f030046, 0x7f030047, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f03013a, 0x7f03015a, 0x7f03015b, 0x7f030174, 0x7f0301df, 0x7f0301e6, 0x7f0301ef, 0x7f0301f0, 0x7f0301f3, 0x7f030200, 0x7f03023d, 0x7f0302b9, 0x7f030315, 0x7f03034a, 0x7f030351, 0x7f030352, 0x7f0303ea, 0x7f0303ee, 0x7f030450, 0x7f03045c }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f03003f, 0x7f030046, 0x7f0300ea, 0x7f0301df, 0x7f0303ee, 0x7f03045c }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f030189, 0x7f03020a }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f0300af, 0x7f0300b0, 0x7f0302ae, 0x7f0302af, 0x7f030310, 0x7f030388, 0x7f03038a }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f030174, 0x7f03018a, 0x7f0302a3, 0x7f0302a4, 0x7f0303e3 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollTargetViewId 6
int styleable AppBarLayout_statusBarForeground 7
int[] styleable AppBarLayoutStates { 0x7f0303dd, 0x7f0303de, 0x7f0303e0, 0x7f0303e1 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f03029b, 0x7f03029c }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatImageView { 0x01010119, 0x7f030398, 0x7f03044e, 0x7f03044f }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f030449, 0x7f03044a, 0x7f03044b }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f030039, 0x7f03003a, 0x7f03003b, 0x7f03003c, 0x7f03003d, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f0301b2, 0x7f0301c9, 0x7f0301d3, 0x7f030254, 0x7f0302a6, 0x7f03041a, 0x7f030436 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_firstBaselineToTopHeight 14
int styleable AppCompatTextView_fontFamily 15
int styleable AppCompatTextView_fontVariationSettings 16
int styleable AppCompatTextView_lastBaselineToBottomHeight 17
int styleable AppCompatTextView_lineHeight 18
int styleable AppCompatTextView_textAllCaps 19
int styleable AppCompatTextView_textLocale 20
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030000, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030024, 0x7f030026, 0x7f030027, 0x7f030028, 0x7f030029, 0x7f030038, 0x7f030071, 0x7f0300a8, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300b1, 0x7f0300b2, 0x7f0300be, 0x7f0300c7, 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f0300f5, 0x7f0300f6, 0x7f0300f7, 0x7f0300f8, 0x7f0300ff, 0x7f030100, 0x7f030106, 0x7f03011e, 0x7f030156, 0x7f030157, 0x7f030158, 0x7f03015c, 0x7f03015e, 0x7f03016e, 0x7f03016f, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f0301ef, 0x7f0301fe, 0x7f0302aa, 0x7f0302ab, 0x7f0302ac, 0x7f0302ad, 0x7f0302b0, 0x7f0302b1, 0x7f0302b2, 0x7f0302b3, 0x7f0302b4, 0x7f0302b5, 0x7f0302b6, 0x7f0302b7, 0x7f0302b8, 0x7f030332, 0x7f030333, 0x7f030334, 0x7f030349, 0x7f03034b, 0x7f030357, 0x7f030359, 0x7f03035a, 0x7f03035b, 0x7f03036f, 0x7f030371, 0x7f030373, 0x7f030374, 0x7f030394, 0x7f030395, 0x7f0303f5, 0x7f030425, 0x7f030427, 0x7f030428, 0x7f030429, 0x7f03042b, 0x7f03042c, 0x7f03042d, 0x7f03042e, 0x7f030431, 0x7f030432, 0x7f030464, 0x7f030465, 0x7f030466, 0x7f030467, 0x7f03048c, 0x7f03049d, 0x7f03049e, 0x7f03049f, 0x7f0304a0, 0x7f0304a1, 0x7f0304a2, 0x7f0304a3, 0x7f0304a4, 0x7f0304a5, 0x7f0304a6 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable Badge { 0x7f030040, 0x7f03004a, 0x7f03004c, 0x7f0301f1, 0x7f0302dc, 0x7f03031b, 0x7f03048b }
int styleable Badge_backgroundColor 0
int styleable Badge_badgeGravity 1
int styleable Badge_badgeTextColor 2
int styleable Badge_horizontalOffset 3
int styleable Badge_maxCharacterCount 4
int styleable Badge_number 5
int styleable Badge_verticalOffset 6
int[] styleable BallPulseFooter { 0x7f03039a, 0x7f03039b, 0x7f0303c9 }
int styleable BallPulseFooter_srlAnimatingColor 0
int styleable BallPulseFooter_srlClassicsSpinnerStyle 1
int styleable BallPulseFooter_srlNormalColor 2
int[] styleable Banner { 0x7f03004d, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030051, 0x7f030052, 0x7f030053, 0x7f030054, 0x7f030055, 0x7f030056, 0x7f030057, 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e }
int styleable Banner_banner_auto_loop 0
int styleable Banner_banner_indicator_gravity 1
int styleable Banner_banner_indicator_height 2
int styleable Banner_banner_indicator_margin 3
int styleable Banner_banner_indicator_marginBottom 4
int styleable Banner_banner_indicator_marginLeft 5
int styleable Banner_banner_indicator_marginRight 6
int styleable Banner_banner_indicator_marginTop 7
int styleable Banner_banner_indicator_normal_color 8
int styleable Banner_banner_indicator_normal_width 9
int styleable Banner_banner_indicator_radius 10
int styleable Banner_banner_indicator_selected_color 11
int styleable Banner_banner_indicator_selected_width 12
int styleable Banner_banner_indicator_space 13
int styleable Banner_banner_infinite_loop 14
int styleable Banner_banner_loop_time 15
int styleable Banner_banner_orientation 16
int styleable Banner_banner_radius 17
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f0301e4, 0x7f030201, 0x7f0302ee, 0x7f030381, 0x7f030383, 0x7f030472, 0x7f030475, 0x7f030477 }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_minHideDelay 3
int styleable BaseProgressIndicator_showAnimationBehavior 4
int styleable BaseProgressIndicator_showDelay 5
int styleable BaseProgressIndicator_trackColor 6
int styleable BaseProgressIndicator_trackCornerRadius 7
int styleable BaseProgressIndicator_trackThickness 8
int[] styleable BezierRadarHeader { 0x7f030399, 0x7f0303ab, 0x7f0303ca }
int styleable BezierRadarHeader_srlAccentColor 0
int styleable BezierRadarHeader_srlEnableHorizontalDrag 1
int styleable BezierRadarHeader_srlPrimaryColor 2
int[] styleable BottomAppBar { 0x7f030048, 0x7f030174, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f030199, 0x7f03019a, 0x7f0301e7, 0x7f03032a, 0x7f03032c, 0x7f03032d }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_elevation 1
int styleable BottomAppBar_fabAlignmentMode 2
int styleable BottomAppBar_fabAnimationMode 3
int styleable BottomAppBar_fabCradleMargin 4
int styleable BottomAppBar_fabCradleRoundedCornerRadius 5
int styleable BottomAppBar_fabCradleVerticalOffset 6
int styleable BottomAppBar_hideOnScroll 7
int styleable BottomAppBar_paddingBottomSystemWindowInsets 8
int styleable BottomAppBar_paddingLeftSystemWindowInsets 9
int styleable BottomAppBar_paddingRightSystemWindowInsets 10
int[] styleable BottomNavigationView { 0x7f030238 }
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 0
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010440, 0x7f030048, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f03006d, 0x7f03006e, 0x7f03006f, 0x7f0301d9, 0x7f03032a, 0x7f03032c, 0x7f03032d, 0x7f030330, 0x7f030377, 0x7f03037a }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_elevation 1
int styleable BottomSheetBehavior_Layout_backgroundTint 2
int styleable BottomSheetBehavior_Layout_behavior_draggable 3
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 4
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 5
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 6
int styleable BottomSheetBehavior_Layout_behavior_hideable 7
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 8
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 9
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 10
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 11
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 12
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 13
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_shapeAppearance 16
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 17
int[] styleable BubbleSeekBar { 0x0101000e, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f030088, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03008c, 0x7f03008d, 0x7f03008e, 0x7f03008f, 0x7f030090, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f030096, 0x7f030097, 0x7f030098, 0x7f030099, 0x7f03009a, 0x7f03009b, 0x7f03009c, 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f0300a0, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6, 0x7f0300a7 }
int styleable BubbleSeekBar_android_enabled 0
int styleable BubbleSeekBar_bsb_always_show_bubble 1
int styleable BubbleSeekBar_bsb_always_show_bubble_delay 2
int styleable BubbleSeekBar_bsb_anim_duration 3
int styleable BubbleSeekBar_bsb_auto_adjust_section_mark 4
int styleable BubbleSeekBar_bsb_bubble_color 5
int styleable BubbleSeekBar_bsb_bubble_text_color 6
int styleable BubbleSeekBar_bsb_bubble_text_size 7
int styleable BubbleSeekBar_bsb_hide_bubble 8
int styleable BubbleSeekBar_bsb_is_float_type 9
int styleable BubbleSeekBar_bsb_is_touch_auto 10
int styleable BubbleSeekBar_bsb_max 11
int styleable BubbleSeekBar_bsb_min 12
int styleable BubbleSeekBar_bsb_progress 13
int styleable BubbleSeekBar_bsb_rtl 14
int styleable BubbleSeekBar_bsb_second_track_color 15
int styleable BubbleSeekBar_bsb_second_track_size 16
int styleable BubbleSeekBar_bsb_section_count 17
int styleable BubbleSeekBar_bsb_section_text_color 18
int styleable BubbleSeekBar_bsb_section_text_interval 19
int styleable BubbleSeekBar_bsb_section_text_position 20
int styleable BubbleSeekBar_bsb_section_text_size 21
int styleable BubbleSeekBar_bsb_seek_by_section 22
int styleable BubbleSeekBar_bsb_seek_step_section 23
int styleable BubbleSeekBar_bsb_show_progress_in_float 24
int styleable BubbleSeekBar_bsb_show_section_mark 25
int styleable BubbleSeekBar_bsb_show_section_text 26
int styleable BubbleSeekBar_bsb_show_thumb_text 27
int styleable BubbleSeekBar_bsb_thumb_color 28
int styleable BubbleSeekBar_bsb_thumb_radius 29
int styleable BubbleSeekBar_bsb_thumb_radius_on_dragging 30
int styleable BubbleSeekBar_bsb_thumb_text_color 31
int styleable BubbleSeekBar_bsb_thumb_text_size 32
int styleable BubbleSeekBar_bsb_touch_to_seek 33
int styleable BubbleSeekBar_bsb_track_color 34
int styleable BubbleSeekBar_bsb_track_size 35
int[] styleable ButtonBarLayout { 0x7f03002a }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f030355, 0x7f030380 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f0300b5, 0x7f0300b6, 0x7f0300b7, 0x7f0300b9, 0x7f0300ba, 0x7f0300bb, 0x7f030115, 0x7f030116, 0x7f030118, 0x7f030119, 0x7f03011b }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable CardViewShadow { 0x7f030178, 0x7f0303d6, 0x7f03046a }
int styleable CardViewShadow_endColor 0
int styleable CardViewShadow_startColor 1
int styleable CardViewShadow_topDelta 2
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0300c1, 0x7f0300c2, 0x7f0300c5, 0x7f0300c6, 0x7f0300c8, 0x7f0300c9, 0x7f0300ca, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300db, 0x7f0300e3, 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f030181, 0x7f0301e5, 0x7f0301f4, 0x7f0301f8, 0x7f030366, 0x7f030377, 0x7f03037a, 0x7f030385, 0x7f030433, 0x7f030437 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f0300c0, 0x7f0300d3, 0x7f0300d4, 0x7f0300d5, 0x7f030375, 0x7f03038b, 0x7f03038c }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x7f030202, 0x7f030204, 0x7f030205 }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClassicsFooter { 0x7f030399, 0x7f03039b, 0x7f03039f, 0x7f0303a0, 0x7f0303a1, 0x7f0303a2, 0x7f0303a3, 0x7f0303a4, 0x7f0303b9, 0x7f0303ca, 0x7f0303d2 }
int styleable ClassicsFooter_srlAccentColor 0
int styleable ClassicsFooter_srlClassicsSpinnerStyle 1
int styleable ClassicsFooter_srlDrawableArrow 2
int styleable ClassicsFooter_srlDrawableArrowSize 3
int styleable ClassicsFooter_srlDrawableMarginRight 4
int styleable ClassicsFooter_srlDrawableProgress 5
int styleable ClassicsFooter_srlDrawableProgressSize 6
int styleable ClassicsFooter_srlDrawableSize 7
int styleable ClassicsFooter_srlFinishDuration 8
int styleable ClassicsFooter_srlPrimaryColor 9
int styleable ClassicsFooter_srlTextSizeTitle 10
int[] styleable ClassicsHeader { 0x7f030399, 0x7f03039b, 0x7f03039f, 0x7f0303a0, 0x7f0303a1, 0x7f0303a2, 0x7f0303a3, 0x7f0303a4, 0x7f0303ac, 0x7f0303b9, 0x7f0303ca, 0x7f0303d1, 0x7f0303d2, 0x7f0303d3 }
int styleable ClassicsHeader_srlAccentColor 0
int styleable ClassicsHeader_srlClassicsSpinnerStyle 1
int styleable ClassicsHeader_srlDrawableArrow 2
int styleable ClassicsHeader_srlDrawableArrowSize 3
int styleable ClassicsHeader_srlDrawableMarginRight 4
int styleable ClassicsHeader_srlDrawableProgress 5
int styleable ClassicsHeader_srlDrawableProgressSize 6
int styleable ClassicsHeader_srlDrawableSize 7
int styleable ClassicsHeader_srlEnableLastTime 8
int styleable ClassicsHeader_srlFinishDuration 9
int styleable ClassicsHeader_srlPrimaryColor 10
int styleable ClassicsHeader_srlTextSizeTime 11
int styleable ClassicsHeader_srlTextSizeTitle 12
int styleable ClassicsHeader_srlTextTimeMarginTop 13
int[] styleable ClockFaceView { 0x7f0300df, 0x7f0300e2 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0300e0, 0x7f0302d4, 0x7f030376 }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0300ee, 0x7f0300ef, 0x7f03011c, 0x7f03018c, 0x7f03018d, 0x7f03018e, 0x7f03018f, 0x7f030190, 0x7f030191, 0x7f030192, 0x7f030195, 0x7f0301d5, 0x7f0302df, 0x7f03036a, 0x7f03036c, 0x7f0303e4, 0x7f030450, 0x7f030452, 0x7f030453, 0x7f030463 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 10
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 11
int styleable CollapsingToolbarLayout_maxLines 12
int styleable CollapsingToolbarLayout_scrimAnimationDuration 13
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 14
int styleable CollapsingToolbarLayout_statusBarScrim 15
int styleable CollapsingToolbarLayout_title 16
int styleable CollapsingToolbarLayout_titleCollapseMode 17
int styleable CollapsingToolbarLayout_titleEnabled 18
int styleable CollapsingToolbarLayout_toolbarId 19
int[] styleable CollapsingToolbarLayout_Layout { 0x7f03025d, 0x7f03025e }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f03002b, 0x7f030250 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f0300ad, 0x7f0300b3, 0x7f0300b4 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable ConsecutiveScrollerLayout { 0x7f030025, 0x7f030037, 0x7f030159, 0x7f03020e, 0x7f030323, 0x7f030324, 0x7f030325, 0x7f030326, 0x7f0303e5 }
int styleable ConsecutiveScrollerLayout_adjustHeightOffset 0
int styleable ConsecutiveScrollerLayout_autoAdjustHeightAtBottomView 1
int styleable ConsecutiveScrollerLayout_disableChildHorizontalScroll 2
int styleable ConsecutiveScrollerLayout_isPermanent 3
int styleable ConsecutiveScrollerLayout_overDragMaxDistanceOfBottom 4
int styleable ConsecutiveScrollerLayout_overDragMaxDistanceOfTop 5
int styleable ConsecutiveScrollerLayout_overDragMode 6
int styleable ConsecutiveScrollerLayout_overDragRate 7
int styleable ConsecutiveScrollerLayout_stickyOffset 8
int[] styleable ConsecutiveScrollerLayout_Layout { 0x7f030259, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f030297, 0x7f03029a }
int styleable ConsecutiveScrollerLayout_Layout_layout_align 0
int styleable ConsecutiveScrollerLayout_Layout_layout_isConsecutive 1
int styleable ConsecutiveScrollerLayout_Layout_layout_isNestedScroll 2
int styleable ConsecutiveScrollerLayout_Layout_layout_isSink 3
int styleable ConsecutiveScrollerLayout_Layout_layout_isSticky 4
int styleable ConsecutiveScrollerLayout_Layout_layout_isTriggerScroll 5
int styleable ConsecutiveScrollerLayout_Layout_layout_scrollChild 6
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002e, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f0300bd, 0x7f03010b, 0x7f030163, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301be, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c3, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301c7, 0x7f03025f, 0x7f030260, 0x7f030261, 0x7f030262, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f030266, 0x7f030267, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030308, 0x7f030309, 0x7f03033a, 0x7f030343, 0x7f03047b, 0x7f03047d, 0x7f03048d }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animate_relativeTo 27
int styleable Constraint_barrierAllowsGoneWidgets 28
int styleable Constraint_barrierDirection 29
int styleable Constraint_barrierMargin 30
int styleable Constraint_chainUseRtl 31
int styleable Constraint_constraint_referenced_ids 32
int styleable Constraint_drawPath 33
int styleable Constraint_flow_firstHorizontalBias 34
int styleable Constraint_flow_firstHorizontalStyle 35
int styleable Constraint_flow_firstVerticalBias 36
int styleable Constraint_flow_firstVerticalStyle 37
int styleable Constraint_flow_horizontalAlign 38
int styleable Constraint_flow_horizontalBias 39
int styleable Constraint_flow_horizontalGap 40
int styleable Constraint_flow_horizontalStyle 41
int styleable Constraint_flow_lastHorizontalBias 42
int styleable Constraint_flow_lastHorizontalStyle 43
int styleable Constraint_flow_lastVerticalBias 44
int styleable Constraint_flow_lastVerticalStyle 45
int styleable Constraint_flow_maxElementsWrap 46
int styleable Constraint_flow_verticalAlign 47
int styleable Constraint_flow_verticalBias 48
int styleable Constraint_flow_verticalGap 49
int styleable Constraint_flow_verticalStyle 50
int styleable Constraint_flow_wrapMode 51
int styleable Constraint_layout_constrainedHeight 52
int styleable Constraint_layout_constrainedWidth 53
int styleable Constraint_layout_constraintBaseline_creator 54
int styleable Constraint_layout_constraintBaseline_toBaselineOf 55
int styleable Constraint_layout_constraintBottom_creator 56
int styleable Constraint_layout_constraintBottom_toBottomOf 57
int styleable Constraint_layout_constraintBottom_toTopOf 58
int styleable Constraint_layout_constraintCircle 59
int styleable Constraint_layout_constraintCircleAngle 60
int styleable Constraint_layout_constraintCircleRadius 61
int styleable Constraint_layout_constraintDimensionRatio 62
int styleable Constraint_layout_constraintEnd_toEndOf 63
int styleable Constraint_layout_constraintEnd_toStartOf 64
int styleable Constraint_layout_constraintGuide_begin 65
int styleable Constraint_layout_constraintGuide_end 66
int styleable Constraint_layout_constraintGuide_percent 67
int styleable Constraint_layout_constraintHeight_default 68
int styleable Constraint_layout_constraintHeight_max 69
int styleable Constraint_layout_constraintHeight_min 70
int styleable Constraint_layout_constraintHeight_percent 71
int styleable Constraint_layout_constraintHorizontal_bias 72
int styleable Constraint_layout_constraintHorizontal_chainStyle 73
int styleable Constraint_layout_constraintHorizontal_weight 74
int styleable Constraint_layout_constraintLeft_creator 75
int styleable Constraint_layout_constraintLeft_toLeftOf 76
int styleable Constraint_layout_constraintLeft_toRightOf 77
int styleable Constraint_layout_constraintRight_creator 78
int styleable Constraint_layout_constraintRight_toLeftOf 79
int styleable Constraint_layout_constraintRight_toRightOf 80
int styleable Constraint_layout_constraintStart_toEndOf 81
int styleable Constraint_layout_constraintStart_toStartOf 82
int styleable Constraint_layout_constraintTag 83
int styleable Constraint_layout_constraintTop_creator 84
int styleable Constraint_layout_constraintTop_toBottomOf 85
int styleable Constraint_layout_constraintTop_toTopOf 86
int styleable Constraint_layout_constraintVertical_bias 87
int styleable Constraint_layout_constraintVertical_chainStyle 88
int styleable Constraint_layout_constraintVertical_weight 89
int styleable Constraint_layout_constraintWidth_default 90
int styleable Constraint_layout_constraintWidth_max 91
int styleable Constraint_layout_constraintWidth_min 92
int styleable Constraint_layout_constraintWidth_percent 93
int styleable Constraint_layout_editor_absoluteX 94
int styleable Constraint_layout_editor_absoluteY 95
int styleable Constraint_layout_goneMarginBottom 96
int styleable Constraint_layout_goneMarginEnd 97
int styleable Constraint_layout_goneMarginLeft 98
int styleable Constraint_layout_goneMarginRight 99
int styleable Constraint_layout_goneMarginStart 100
int styleable Constraint_layout_goneMarginTop 101
int styleable Constraint_motionProgress 102
int styleable Constraint_motionStagger 103
int styleable Constraint_pathMotionArc 104
int styleable Constraint_pivotAnchor 105
int styleable Constraint_transitionEasing 106
int styleable Constraint_transitionPathRotate 107
int styleable Constraint_visibilityMode 108
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x01010440, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f0300bd, 0x7f030108, 0x7f03010b, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301be, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c3, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301c7, 0x7f030256, 0x7f03025f, 0x7f030260, 0x7f030261, 0x7f030262, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f030266, 0x7f030267, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030299 }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_maxWidth 7
int styleable ConstraintLayout_Layout_android_maxHeight 8
int styleable ConstraintLayout_Layout_android_minWidth 9
int styleable ConstraintLayout_Layout_android_minHeight 10
int styleable ConstraintLayout_Layout_android_paddingStart 11
int styleable ConstraintLayout_Layout_android_paddingEnd 12
int styleable ConstraintLayout_Layout_android_elevation 13
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 14
int styleable ConstraintLayout_Layout_barrierDirection 15
int styleable ConstraintLayout_Layout_barrierMargin 16
int styleable ConstraintLayout_Layout_chainUseRtl 17
int styleable ConstraintLayout_Layout_constraintSet 18
int styleable ConstraintLayout_Layout_constraint_referenced_ids 19
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 20
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 21
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 22
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 23
int styleable ConstraintLayout_Layout_flow_horizontalAlign 24
int styleable ConstraintLayout_Layout_flow_horizontalBias 25
int styleable ConstraintLayout_Layout_flow_horizontalGap 26
int styleable ConstraintLayout_Layout_flow_horizontalStyle 27
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 28
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 29
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 30
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 31
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 32
int styleable ConstraintLayout_Layout_flow_verticalAlign 33
int styleable ConstraintLayout_Layout_flow_verticalBias 34
int styleable ConstraintLayout_Layout_flow_verticalGap 35
int styleable ConstraintLayout_Layout_flow_verticalStyle 36
int styleable ConstraintLayout_Layout_flow_wrapMode 37
int styleable ConstraintLayout_Layout_layoutDescription 38
int styleable ConstraintLayout_Layout_layout_constrainedHeight 39
int styleable ConstraintLayout_Layout_layout_constrainedWidth 40
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 41
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 42
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 43
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 44
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 45
int styleable ConstraintLayout_Layout_layout_constraintCircle 46
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 47
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 48
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 49
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 50
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 51
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 52
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 53
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 54
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 55
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 56
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 57
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 58
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 59
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 60
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 61
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 62
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 63
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 64
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 65
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 66
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 67
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 68
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 69
int styleable ConstraintLayout_Layout_layout_constraintTag 70
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 71
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 72
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 73
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 74
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 75
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 76
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 77
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 78
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 79
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 80
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 81
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 82
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 83
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 84
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 85
int styleable ConstraintLayout_Layout_layout_goneMarginRight 86
int styleable ConstraintLayout_Layout_layout_goneMarginStart 87
int styleable ConstraintLayout_Layout_layout_goneMarginTop 88
int styleable ConstraintLayout_Layout_layout_optimizationLevel 89
int[] styleable ConstraintLayout_placeholder { 0x7f03010d, 0x7f030347 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002e, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f0300bd, 0x7f03010b, 0x7f030152, 0x7f030163, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301be, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c3, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301c7, 0x7f03025f, 0x7f030260, 0x7f030261, 0x7f030262, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f030266, 0x7f030267, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f030308, 0x7f030309, 0x7f03033a, 0x7f030343, 0x7f03047b, 0x7f03047d }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animate_relativeTo 29
int styleable ConstraintSet_barrierAllowsGoneWidgets 30
int styleable ConstraintSet_barrierDirection 31
int styleable ConstraintSet_barrierMargin 32
int styleable ConstraintSet_chainUseRtl 33
int styleable ConstraintSet_constraint_referenced_ids 34
int styleable ConstraintSet_deriveConstraintsFrom 35
int styleable ConstraintSet_drawPath 36
int styleable ConstraintSet_flow_firstHorizontalBias 37
int styleable ConstraintSet_flow_firstHorizontalStyle 38
int styleable ConstraintSet_flow_firstVerticalBias 39
int styleable ConstraintSet_flow_firstVerticalStyle 40
int styleable ConstraintSet_flow_horizontalAlign 41
int styleable ConstraintSet_flow_horizontalBias 42
int styleable ConstraintSet_flow_horizontalGap 43
int styleable ConstraintSet_flow_horizontalStyle 44
int styleable ConstraintSet_flow_lastHorizontalBias 45
int styleable ConstraintSet_flow_lastHorizontalStyle 46
int styleable ConstraintSet_flow_lastVerticalBias 47
int styleable ConstraintSet_flow_lastVerticalStyle 48
int styleable ConstraintSet_flow_maxElementsWrap 49
int styleable ConstraintSet_flow_verticalAlign 50
int styleable ConstraintSet_flow_verticalBias 51
int styleable ConstraintSet_flow_verticalGap 52
int styleable ConstraintSet_flow_verticalStyle 53
int styleable ConstraintSet_flow_wrapMode 54
int styleable ConstraintSet_layout_constrainedHeight 55
int styleable ConstraintSet_layout_constrainedWidth 56
int styleable ConstraintSet_layout_constraintBaseline_creator 57
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 58
int styleable ConstraintSet_layout_constraintBottom_creator 59
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 60
int styleable ConstraintSet_layout_constraintBottom_toTopOf 61
int styleable ConstraintSet_layout_constraintCircle 62
int styleable ConstraintSet_layout_constraintCircleAngle 63
int styleable ConstraintSet_layout_constraintCircleRadius 64
int styleable ConstraintSet_layout_constraintDimensionRatio 65
int styleable ConstraintSet_layout_constraintEnd_toEndOf 66
int styleable ConstraintSet_layout_constraintEnd_toStartOf 67
int styleable ConstraintSet_layout_constraintGuide_begin 68
int styleable ConstraintSet_layout_constraintGuide_end 69
int styleable ConstraintSet_layout_constraintGuide_percent 70
int styleable ConstraintSet_layout_constraintHeight_default 71
int styleable ConstraintSet_layout_constraintHeight_max 72
int styleable ConstraintSet_layout_constraintHeight_min 73
int styleable ConstraintSet_layout_constraintHeight_percent 74
int styleable ConstraintSet_layout_constraintHorizontal_bias 75
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 76
int styleable ConstraintSet_layout_constraintHorizontal_weight 77
int styleable ConstraintSet_layout_constraintLeft_creator 78
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 79
int styleable ConstraintSet_layout_constraintLeft_toRightOf 80
int styleable ConstraintSet_layout_constraintRight_creator 81
int styleable ConstraintSet_layout_constraintRight_toLeftOf 82
int styleable ConstraintSet_layout_constraintRight_toRightOf 83
int styleable ConstraintSet_layout_constraintStart_toEndOf 84
int styleable ConstraintSet_layout_constraintStart_toStartOf 85
int styleable ConstraintSet_layout_constraintTag 86
int styleable ConstraintSet_layout_constraintTop_creator 87
int styleable ConstraintSet_layout_constraintTop_toBottomOf 88
int styleable ConstraintSet_layout_constraintTop_toTopOf 89
int styleable ConstraintSet_layout_constraintVertical_bias 90
int styleable ConstraintSet_layout_constraintVertical_chainStyle 91
int styleable ConstraintSet_layout_constraintVertical_weight 92
int styleable ConstraintSet_layout_constraintWidth_default 93
int styleable ConstraintSet_layout_constraintWidth_max 94
int styleable ConstraintSet_layout_constraintWidth_min 95
int styleable ConstraintSet_layout_constraintWidth_percent 96
int styleable ConstraintSet_layout_editor_absoluteX 97
int styleable ConstraintSet_layout_editor_absoluteY 98
int styleable ConstraintSet_layout_goneMarginBottom 99
int styleable ConstraintSet_layout_goneMarginEnd 100
int styleable ConstraintSet_layout_goneMarginLeft 101
int styleable ConstraintSet_layout_goneMarginRight 102
int styleable ConstraintSet_layout_goneMarginStart 103
int styleable ConstraintSet_layout_goneMarginTop 104
int styleable ConstraintSet_motionProgress 105
int styleable ConstraintSet_motionStagger 106
int styleable ConstraintSet_pathMotionArc 107
int styleable ConstraintSet_pivotAnchor 108
int styleable ConstraintSet_transitionEasing 109
int styleable ConstraintSet_transitionPathRotate 110
int[] styleable CoordinatorLayout { 0x7f03024f, 0x7f0303e2 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f03025a, 0x7f03025b, 0x7f03025c, 0x7f030289, 0x7f030292, 0x7f030298 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f030036, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013b, 0x7f03013c }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customStringValue 8
int[] styleable CustomEditBox { 0x7f0301ed, 0x7f0301ee, 0x7f030438, 0x7f03045d, 0x7f030483, 0x7f030486 }
int styleable CustomEditBox_hint_color 0
int styleable CustomEditBox_hint_text 1
int styleable CustomEditBox_text_color 2
int styleable CustomEditBox_title_color 3
int styleable CustomEditBox_tv_size 4
int styleable CustomEditBox_tv_title 5
int[] styleable CustomInputBox { 0x7f03014e, 0x7f03014f, 0x7f0301dd, 0x7f0301e8, 0x7f0301ec, 0x7f0302e3, 0x7f030439, 0x7f030490 }
int styleable CustomInputBox_default_watch 0
int styleable CustomInputBox_del_show 1
int styleable CustomInputBox_has_del 2
int styleable CustomInputBox_hint 3
int styleable CustomInputBox_hintTextColor 4
int styleable CustomInputBox_max_text 5
int styleable CustomInputBox_text_size 6
int styleable CustomInputBox_watch_show 7
int[] styleable CustomLineText { 0x7f030450, 0x7f03045e }
int styleable CustomLineText_title 0
int styleable CustomLineText_title_icon 1
int[] styleable CustomNavBar { 0x7f0301fb, 0x7f0301fc, 0x7f030311, 0x7f03045f, 0x7f030460, 0x7f030461, 0x7f030462 }
int styleable CustomNavBar_icon_left 0
int styleable CustomNavBar_icon_right 1
int styleable CustomNavBar_nav_bar_color_bg 2
int styleable CustomNavBar_title_left 3
int styleable CustomNavBar_title_mid 4
int styleable CustomNavBar_title_right 5
int styleable CustomNavBar_title_right_color 6
int[] styleable CustomProgressBar { 0x7f0304af, 0x7f0304b0, 0x7f0304b1, 0x7f0304b2 }
int styleable CustomProgressBar_zpb_backgroundColor 0
int styleable CustomProgressBar_zpb_duration 1
int styleable CustomProgressBar_zpb_progressColor 2
int styleable CustomProgressBar_zpb_radius 3
int[] styleable DrawableTextView { 0x7f030073, 0x7f030074, 0x7f030075, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f030362, 0x7f030363, 0x7f030364, 0x7f03046b, 0x7f03046c, 0x7f03046d }
int styleable DrawableTextView_bottomDrawable 0
int styleable DrawableTextView_bottomDrawableHeight 1
int styleable DrawableTextView_bottomDrawableWidth 2
int styleable DrawableTextView_leftDrawable 3
int styleable DrawableTextView_leftDrawableHeight 4
int styleable DrawableTextView_leftDrawableWidth 5
int styleable DrawableTextView_rightDrawable 6
int styleable DrawableTextView_rightDrawableHeight 7
int styleable DrawableTextView_rightDrawableWidth 8
int styleable DrawableTextView_topDrawable 9
int styleable DrawableTextView_topDrawableHeight 10
int styleable DrawableTextView_topDrawableWidth 11
int[] styleable DrawerArrowToggle { 0x7f030033, 0x7f030034, 0x7f030060, 0x7f0300f1, 0x7f030168, 0x7f0301d8, 0x7f030393, 0x7f03043d }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DropBoxHeader { 0x7f030153, 0x7f030154, 0x7f030155 }
int styleable DropBoxHeader_dhDrawable1 0
int styleable DropBoxHeader_dhDrawable2 1
int styleable DropBoxHeader_dhDrawable3 2
int[] styleable ExtendedFloatingActionButton { 0x7f0300ed, 0x7f030174, 0x7f030193, 0x7f0301e5, 0x7f030385, 0x7f030389 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_hideMotionSpec 3
int styleable ExtendedFloatingActionButton_showMotionSpec 4
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 5
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f030065, 0x7f030066 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f030048, 0x7f030049, 0x7f030070, 0x7f030174, 0x7f030181, 0x7f03019b, 0x7f03019c, 0x7f0301e5, 0x7f0301f2, 0x7f0302de, 0x7f030350, 0x7f030366, 0x7f030377, 0x7f03037a, 0x7f030385, 0x7f030487 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f030065 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f030246, 0x7f0302a7 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f0301ca, 0x7f0301cb, 0x7f0301cc, 0x7f0301cd, 0x7f0301ce, 0x7f0301cf, 0x7f0301d0 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0301c8, 0x7f0301d1, 0x7f0301d3, 0x7f0301d4, 0x7f030482 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable FontTextView { 0x7f0301d2 }
int styleable FontTextView_fontType 0
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f0301d6 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable FunGameHitBlockHeader { 0x7f0301a3, 0x7f0301a4 }
int styleable FunGameHitBlockHeader_fghBallSpeed 0
int styleable FunGameHitBlockHeader_fghBlockHorizontalNum 1
int[] styleable FunGameView { 0x7f0301a2, 0x7f0301a5, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8, 0x7f0301a9, 0x7f0301aa, 0x7f0301ab, 0x7f0301ac, 0x7f0301ad, 0x7f0301ae, 0x7f0301af, 0x7f0301b0, 0x7f0301b1 }
int styleable FunGameView_fghBackColor 0
int styleable FunGameView_fghLeftColor 1
int styleable FunGameView_fghMaskTextBottom 2
int styleable FunGameView_fghMaskTextSizeBottom 3
int styleable FunGameView_fghMaskTextSizeTop 4
int styleable FunGameView_fghMaskTextTop 5
int styleable FunGameView_fghMaskTextTopPull 6
int styleable FunGameView_fghMaskTextTopRelease 7
int styleable FunGameView_fghMiddleColor 8
int styleable FunGameView_fghRightColor 9
int styleable FunGameView_fghTextGameOver 10
int styleable FunGameView_fghTextLoading 11
int styleable FunGameView_fghTextLoadingFailed 12
int styleable FunGameView_fghTextLoadingFinished 13
int[] styleable GasSelectView { 0x7f030484, 0x7f030485, 0x7f030489 }
int styleable GasSelectView_tv_tile 0
int styleable GasSelectView_tv_time 1
int styleable GasSelectView_value 2
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f03002d, 0x7f030084, 0x7f03011d, 0x7f030131, 0x7f030328, 0x7f030367, 0x7f030368, 0x7f030369, 0x7f03048f }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_brightness 1
int styleable ImageFilterView_contrast 2
int styleable ImageFilterView_crossfade 3
int styleable ImageFilterView_overlay 4
int styleable ImageFilterView_round 5
int styleable ImageFilterView_roundPercent 6
int styleable ImageFilterView_saturation 7
int styleable ImageFilterView_warmth 8
int[] styleable IndicatorLineView { 0x7f03013d, 0x7f03013e, 0x7f03013f, 0x7f030145, 0x7f030146 }
int styleable IndicatorLineView_cy_color_indicator 0
int styleable IndicatorLineView_cy_height_indicator 1
int styleable IndicatorLineView_cy_radius_indicator 2
int styleable IndicatorLineView_cy_width_indicator_max 3
int styleable IndicatorLineView_cy_width_indicator_selected 4
int[] styleable IndicatorSeekBar { 0x7f03020f, 0x7f030210, 0x7f030211, 0x7f030212, 0x7f030213, 0x7f030214, 0x7f030215, 0x7f030216, 0x7f030217, 0x7f030218, 0x7f030219, 0x7f03021a, 0x7f03021b, 0x7f03021c, 0x7f03021d, 0x7f03021e, 0x7f03021f, 0x7f030220, 0x7f030221, 0x7f030222, 0x7f030223, 0x7f030224, 0x7f030225, 0x7f030226, 0x7f030227, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230, 0x7f030231, 0x7f030232, 0x7f030233, 0x7f030234 }
int styleable IndicatorSeekBar_isb_clear_default_padding 0
int styleable IndicatorSeekBar_isb_indicator_color 1
int styleable IndicatorSeekBar_isb_indicator_content_layout 2
int styleable IndicatorSeekBar_isb_indicator_text_color 3
int styleable IndicatorSeekBar_isb_indicator_text_size 4
int styleable IndicatorSeekBar_isb_indicator_top_content_layout 5
int styleable IndicatorSeekBar_isb_max 6
int styleable IndicatorSeekBar_isb_min 7
int styleable IndicatorSeekBar_isb_only_thumb_draggable 8
int styleable IndicatorSeekBar_isb_progress 9
int styleable IndicatorSeekBar_isb_progress_value_float 10
int styleable IndicatorSeekBar_isb_r2l 11
int styleable IndicatorSeekBar_isb_seek_smoothly 12
int styleable IndicatorSeekBar_isb_show_indicator 13
int styleable IndicatorSeekBar_isb_show_thumb_text 14
int styleable IndicatorSeekBar_isb_show_tick_marks_type 15
int styleable IndicatorSeekBar_isb_show_tick_texts 16
int styleable IndicatorSeekBar_isb_thumb_adjust_auto 17
int styleable IndicatorSeekBar_isb_thumb_color 18
int styleable IndicatorSeekBar_isb_thumb_drawable 19
int styleable IndicatorSeekBar_isb_thumb_size 20
int styleable IndicatorSeekBar_isb_thumb_text_color 21
int styleable IndicatorSeekBar_isb_tick_marks_color 22
int styleable IndicatorSeekBar_isb_tick_marks_drawable 23
int styleable IndicatorSeekBar_isb_tick_marks_ends_hide 24
int styleable IndicatorSeekBar_isb_tick_marks_size 25
int styleable IndicatorSeekBar_isb_tick_marks_swept_hide 26
int styleable IndicatorSeekBar_isb_tick_texts_array 27
int styleable IndicatorSeekBar_isb_tick_texts_color 28
int styleable IndicatorSeekBar_isb_tick_texts_size 29
int styleable IndicatorSeekBar_isb_tick_texts_typeface 30
int styleable IndicatorSeekBar_isb_ticks_count 31
int styleable IndicatorSeekBar_isb_track_background_color 32
int styleable IndicatorSeekBar_isb_track_background_size 33
int styleable IndicatorSeekBar_isb_track_progress_color 34
int styleable IndicatorSeekBar_isb_track_progress_size 35
int styleable IndicatorSeekBar_isb_track_rounded_corners 36
int styleable IndicatorSeekBar_isb_user_seekable 37
int[] styleable IndicatorTriangleView { 0x7f03013d, 0x7f03013e, 0x7f030145, 0x7f030146 }
int styleable IndicatorTriangleView_cy_color_indicator 0
int styleable IndicatorTriangleView_cy_height_indicator 1
int styleable IndicatorTriangleView_cy_width_indicator_max 2
int styleable IndicatorTriangleView_cy_width_indicator_selected 3
int[] styleable Insets { 0x7f03032a, 0x7f03032c, 0x7f03032d, 0x7f030330 }
int styleable Insets_paddingBottomSystemWindowInsets 0
int styleable Insets_paddingLeftSystemWindowInsets 1
int styleable Insets_paddingRightSystemWindowInsets 2
int styleable Insets_paddingTopSystemWindowInsets 3
int[] styleable IntervalSeekBar { 0x7f03005f, 0x7f030061, 0x7f0302a2, 0x7f0302a8, 0x7f0302bb, 0x7f0302bc, 0x7f030365, 0x7f030370, 0x7f030372 }
int styleable IntervalSeekBar_barColor 0
int styleable IntervalSeekBar_barRadius 1
int styleable IntervalSeekBar_leftProgress 2
int styleable IntervalSeekBar_lineWidth 3
int styleable IntervalSeekBar_marginHorizontal 4
int styleable IntervalSeekBar_marginVertical 5
int styleable IntervalSeekBar_rightProgress 6
int styleable IntervalSeekBar_seekBackgroundColor 7
int styleable IntervalSeekBar_seekProgressColor 8
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030133, 0x7f0301d7, 0x7f030308, 0x7f03030a, 0x7f03047b, 0x7f03047d }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transitionEasing 16
int styleable KeyAttribute_transitionPathRotate 17
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030133, 0x7f0301d7, 0x7f030308, 0x7f03030a, 0x7f03047b, 0x7f03047d, 0x7f030492, 0x7f030493, 0x7f030494, 0x7f030495 }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_waveShape 18
int styleable KeyCycle_waveVariesBy 19
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f030133, 0x7f030163, 0x7f0301d7, 0x7f03024d, 0x7f03030a, 0x7f03033a, 0x7f03033c, 0x7f03033d, 0x7f03033e, 0x7f03033f, 0x7f03038d, 0x7f03047b }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030133, 0x7f0301d7, 0x7f030308, 0x7f03030a, 0x7f03047b, 0x7f03047d, 0x7f030491, 0x7f030492, 0x7f030493, 0x7f030494 }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_waveShape 19
int[] styleable KeyTrigger { 0x7f0301d7, 0x7f03030a, 0x7f03030b, 0x7f03030c, 0x7f03031d, 0x7f03031f, 0x7f030320, 0x7f03047f, 0x7f030480, 0x7f030481 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f0300bd, 0x7f03010b, 0x7f03025f, 0x7f030260, 0x7f030261, 0x7f030262, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f030266, 0x7f030267, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030276, 0x7f030277, 0x7f030278, 0x7f030279, 0x7f03027a, 0x7f03027b, 0x7f03027c, 0x7f03027d, 0x7f03027f, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f030284, 0x7f030285, 0x7f030286, 0x7f030287, 0x7f030288, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f0302dd, 0x7f0302e1, 0x7f0302ed, 0x7f0302f1 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_layout_constrainedHeight 14
int styleable Layout_layout_constrainedWidth 15
int styleable Layout_layout_constraintBaseline_creator 16
int styleable Layout_layout_constraintBaseline_toBaselineOf 17
int styleable Layout_layout_constraintBottom_creator 18
int styleable Layout_layout_constraintBottom_toBottomOf 19
int styleable Layout_layout_constraintBottom_toTopOf 20
int styleable Layout_layout_constraintCircle 21
int styleable Layout_layout_constraintCircleAngle 22
int styleable Layout_layout_constraintCircleRadius 23
int styleable Layout_layout_constraintDimensionRatio 24
int styleable Layout_layout_constraintEnd_toEndOf 25
int styleable Layout_layout_constraintEnd_toStartOf 26
int styleable Layout_layout_constraintGuide_begin 27
int styleable Layout_layout_constraintGuide_end 28
int styleable Layout_layout_constraintGuide_percent 29
int styleable Layout_layout_constraintHeight_default 30
int styleable Layout_layout_constraintHeight_max 31
int styleable Layout_layout_constraintHeight_min 32
int styleable Layout_layout_constraintHeight_percent 33
int styleable Layout_layout_constraintHorizontal_bias 34
int styleable Layout_layout_constraintHorizontal_chainStyle 35
int styleable Layout_layout_constraintHorizontal_weight 36
int styleable Layout_layout_constraintLeft_creator 37
int styleable Layout_layout_constraintLeft_toLeftOf 38
int styleable Layout_layout_constraintLeft_toRightOf 39
int styleable Layout_layout_constraintRight_creator 40
int styleable Layout_layout_constraintRight_toLeftOf 41
int styleable Layout_layout_constraintRight_toRightOf 42
int styleable Layout_layout_constraintStart_toEndOf 43
int styleable Layout_layout_constraintStart_toStartOf 44
int styleable Layout_layout_constraintTop_creator 45
int styleable Layout_layout_constraintTop_toBottomOf 46
int styleable Layout_layout_constraintTop_toTopOf 47
int styleable Layout_layout_constraintVertical_bias 48
int styleable Layout_layout_constraintVertical_chainStyle 49
int styleable Layout_layout_constraintVertical_weight 50
int styleable Layout_layout_constraintWidth_default 51
int styleable Layout_layout_constraintWidth_max 52
int styleable Layout_layout_constraintWidth_min 53
int styleable Layout_layout_constraintWidth_percent 54
int styleable Layout_layout_editor_absoluteX 55
int styleable Layout_layout_editor_absoluteY 56
int styleable Layout_layout_goneMarginBottom 57
int styleable Layout_layout_goneMarginEnd 58
int styleable Layout_layout_goneMarginLeft 59
int styleable Layout_layout_goneMarginRight 60
int styleable Layout_layout_goneMarginStart 61
int styleable Layout_layout_goneMarginTop 62
int styleable Layout_maxHeight 63
int styleable Layout_maxWidth 64
int styleable Layout_minHeight 65
int styleable Layout_minWidth 66
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f03015b, 0x7f03015d, 0x7f0302e4, 0x7f030384 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f0301ff, 0x7f030203 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialAlertDialog { 0x7f030041, 0x7f030042, 0x7f030043, 0x7f030044 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int[] styleable MaterialAlertDialogTheme { 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 4
int[] styleable MaterialAutoCompleteTextView { 0x01010220 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f030048, 0x7f030049, 0x7f030125, 0x7f030174, 0x7f0301f3, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f9, 0x7f0301fa, 0x7f030366, 0x7f030377, 0x7f03037a, 0x7f0303e6, 0x7f0303e7 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int[] styleable MaterialButtonToggleGroup { 0x7f0300bf, 0x7f030375, 0x7f03038c }
int styleable MaterialButtonToggleGroup_checkedButton 0
int styleable MaterialButtonToggleGroup_selectionRequired 1
int styleable MaterialButtonToggleGroup_singleSelection 2
int[] styleable MaterialCalendar { 0x0101020d, 0x7f030147, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f03031a, 0x7f030358, 0x7f0304ab, 0x7f0304ac, 0x7f0304ad }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_dayInvalidStyle 1
int styleable MaterialCalendar_daySelectedStyle 2
int styleable MaterialCalendar_dayStyle 3
int styleable MaterialCalendar_dayTodayStyle 4
int styleable MaterialCalendar_nestedScrollable 5
int styleable MaterialCalendar_rangeFillColor 6
int styleable MaterialCalendar_yearSelectedStyle 7
int styleable MaterialCalendar_yearStyle 8
int styleable MaterialCalendar_yearTodayStyle 9
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f030236, 0x7f03023f, 0x7f030240, 0x7f030247, 0x7f030248, 0x7f03024c }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f0300b8, 0x7f0300c1, 0x7f0300c3, 0x7f0300c4, 0x7f0300c5, 0x7f030366, 0x7f030377, 0x7f03037a, 0x7f0303df, 0x7f0303e6, 0x7f0303e7 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconMargin 3
int styleable MaterialCardView_checkedIconSize 4
int styleable MaterialCardView_checkedIconTint 5
int styleable MaterialCardView_rippleColor 6
int styleable MaterialCardView_shapeAppearance 7
int styleable MaterialCardView_shapeAppearanceOverlay 8
int styleable MaterialCardView_state_dragged 9
int styleable MaterialCardView_strokeColor 10
int styleable MaterialCardView_strokeWidth 11
int[] styleable MaterialCheckBox { 0x7f0300b3, 0x7f030488 }
int styleable MaterialCheckBox_buttonTint 0
int styleable MaterialCheckBox_useMaterialThemeColors 1
int[] styleable MaterialHeader { 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f0302ea, 0x7f0302eb, 0x7f0303ca, 0x7f0303cd, 0x7f0303ce, 0x7f0303cf, 0x7f0303d0 }
int styleable MaterialHeader_mhPrimaryColor 0
int styleable MaterialHeader_mhScrollableWhenRefreshing 1
int styleable MaterialHeader_mhShadowColor 2
int styleable MaterialHeader_mhShadowRadius 3
int styleable MaterialHeader_mhShowBezierWave 4
int styleable MaterialHeader_srlPrimaryColor 5
int styleable MaterialHeader_srlScrollableWhenRefreshing 6
int styleable MaterialHeader_srlShadowColor 7
int styleable MaterialHeader_srlShadowRadius 8
int styleable MaterialHeader_srlShowBezierWave 9
int[] styleable MaterialRadioButton { 0x7f0300b3, 0x7f030488 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f030377, 0x7f03037a }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f0302a6 }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f0302a6 }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f0300e1, 0x7f03024e }
int styleable MaterialTimePicker_clockIcon 0
int styleable MaterialTimePicker_keyboardIcon 1
int[] styleable MaterialToolbar { 0x7f030314, 0x7f0303eb, 0x7f030451 }
int styleable MaterialToolbar_navigationIconTint 0
int styleable MaterialToolbar_subtitleCentered 1
int styleable MaterialToolbar_titleCentered 2
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000d, 0x7f030021, 0x7f030023, 0x7f03002c, 0x7f03010e, 0x7f0301f9, 0x7f0301fa, 0x7f03031c, 0x7f030382, 0x7f030469 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f03034f, 0x7f0303e8 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f0302f2, 0x7f0302f3, 0x7f0302f4, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7 }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f03002e, 0x7f030163, 0x7f030307, 0x7f030309, 0x7f03033a, 0x7f03047b }
int styleable Motion_animate_relativeTo 0
int styleable Motion_drawPath 1
int styleable Motion_motionPathRotate 2
int styleable Motion_motionStagger 3
int styleable Motion_pathMotionArc 4
int styleable Motion_transitionEasing 5
int[] styleable MotionHelper { 0x7f03031e, 0x7f030321 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLayout { 0x7f030031, 0x7f030132, 0x7f030256, 0x7f0302f9, 0x7f030308, 0x7f030386 }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f03014b, 0x7f030257 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f030417, 0x7f030418, 0x7f030419 }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable MountainSceneView { 0x7f03030e, 0x7f03030f }
int styleable MountainSceneView_msvPrimaryColor 0
int styleable MountainSceneView_msvViewportHeight 1
int[] styleable NavigationBarView { 0x7f030048, 0x7f030174, 0x7f030235, 0x7f03023a, 0x7f03023b, 0x7f03023e, 0x7f03024a, 0x7f03024b, 0x7f03024c, 0x7f030253, 0x7f0302e5 }
int styleable NavigationBarView_backgroundTint 0
int styleable NavigationBarView_elevation 1
int styleable NavigationBarView_itemBackground 2
int styleable NavigationBarView_itemIconSize 3
int styleable NavigationBarView_itemIconTint 4
int styleable NavigationBarView_itemRippleColor 5
int styleable NavigationBarView_itemTextAppearanceActive 6
int styleable NavigationBarView_itemTextAppearanceInactive 7
int styleable NavigationBarView_itemTextColor 8
int styleable NavigationBarView_labelVisibilityMode 9
int styleable NavigationBarView_menu 10
int[] styleable NavigationRailView { 0x7f0301de, 0x7f0302e6 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_menuGravity 1
int[] styleable NavigationView { 0x010100d4, 0x010100dd, 0x0101011f, 0x7f030174, 0x7f0301de, 0x7f030235, 0x7f030237, 0x7f030239, 0x7f03023a, 0x7f03023b, 0x7f03023c, 0x7f03023f, 0x7f030240, 0x7f030241, 0x7f030242, 0x7f030243, 0x7f030244, 0x7f030245, 0x7f030249, 0x7f03024c, 0x7f0302e5, 0x7f030377, 0x7f03037a }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconSize 8
int styleable NavigationView_itemIconTint 9
int styleable NavigationView_itemMaxLines 10
int styleable NavigationView_itemShapeAppearance 11
int styleable NavigationView_itemShapeAppearanceOverlay 12
int styleable NavigationView_itemShapeFillColor 13
int styleable NavigationView_itemShapeInsetBottom 14
int styleable NavigationView_itemShapeInsetEnd 15
int styleable NavigationView_itemShapeInsetStart 16
int styleable NavigationView_itemShapeInsetTop 17
int styleable NavigationView_itemTextAppearance 18
int styleable NavigationView_itemTextColor 19
int styleable NavigationView_menu 20
int styleable NavigationView_shapeAppearance 21
int styleable NavigationView_shapeAppearanceOverlay 22
int[] styleable OnClick { 0x7f0300de, 0x7f030416 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f03015f, 0x7f030161, 0x7f030162, 0x7f0302a5, 0x7f0302d9, 0x7f0302e0, 0x7f03030d, 0x7f030318, 0x7f030322, 0x7f03046e, 0x7f03046f, 0x7f030470 }
int styleable OnSwipe_dragDirection 0
int styleable OnSwipe_dragScale 1
int styleable OnSwipe_dragThreshold 2
int styleable OnSwipe_limitBoundsTo 3
int styleable OnSwipe_maxAcceleration 4
int styleable OnSwipe_maxVelocity 5
int styleable OnSwipe_moveWhenScrollAtTop 6
int styleable OnSwipe_nestedScrollFlags 7
int styleable OnSwipe_onTouchUp 8
int styleable OnSwipe_touchAnchorId 9
int styleable OnSwipe_touchAnchorSide 10
int styleable OnSwipe_touchRegionId 11
int[] styleable PhoenixHeader { 0x7f030341, 0x7f030342 }
int styleable PhoenixHeader_phAccentColor 0
int styleable PhoenixHeader_phPrimaryColor 1
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f030327 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0303dc }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f03027e, 0x7f030308, 0x7f03048d }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f0302d4 }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f0302ef, 0x7f03048a }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f030329, 0x7f03032f }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f03019d, 0x7f03019e, 0x7f03019f, 0x7f0301a0, 0x7f0301a1, 0x7f030258, 0x7f030361, 0x7f030392, 0x7f0303d4 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f03020b }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f03006c }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f0300e3, 0x7f030107, 0x7f03014c, 0x7f0301da, 0x7f0301fd, 0x7f030255, 0x7f030353, 0x7f030354, 0x7f03036d, 0x7f03036e, 0x7f0303e9, 0x7f0303f2, 0x7f03048e }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable ShapeAppearance { 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030126, 0x7f030127, 0x7f030128, 0x7f030129, 0x7f03012a }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f030377, 0x7f03037a, 0x7f0303e6, 0x7f0303e7 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f0301db, 0x7f0301dc, 0x7f030251, 0x7f030252, 0x7f03043e, 0x7f03043f, 0x7f030440, 0x7f030441, 0x7f030442, 0x7f030446, 0x7f030447, 0x7f030448, 0x7f03044c, 0x7f030472, 0x7f030473, 0x7f030474, 0x7f030476 }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_thumbColor 9
int styleable Slider_thumbElevation 10
int styleable Slider_thumbRadius 11
int styleable Slider_thumbStrokeColor 12
int styleable Slider_thumbStrokeWidth 13
int styleable Slider_tickColor 14
int styleable Slider_tickColorActive 15
int styleable Slider_tickColorInactive 16
int styleable Slider_tickVisible 17
int styleable Slider_trackColor 18
int styleable Slider_trackColorActive 19
int styleable Slider_trackColorInactive 20
int styleable Slider_trackHeight 21
int[] styleable SmartRefreshLayout { 0x7f030399, 0x7f03039c, 0x7f03039d, 0x7f03039e, 0x7f0303a5, 0x7f0303a6, 0x7f0303a7, 0x7f0303a8, 0x7f0303a9, 0x7f0303aa, 0x7f0303ad, 0x7f0303ae, 0x7f0303af, 0x7f0303b0, 0x7f0303b1, 0x7f0303b2, 0x7f0303b4, 0x7f0303b5, 0x7f0303b6, 0x7f0303b7, 0x7f0303ba, 0x7f0303bb, 0x7f0303be, 0x7f0303bf, 0x7f0303c0, 0x7f0303c1, 0x7f0303c2, 0x7f0303c3, 0x7f0303c4, 0x7f0303c5, 0x7f0303c6, 0x7f0303c7, 0x7f0303ca, 0x7f0303cb }
int styleable SmartRefreshLayout_srlAccentColor 0
int styleable SmartRefreshLayout_srlDisableContentWhenLoading 1
int styleable SmartRefreshLayout_srlDisableContentWhenRefresh 2
int styleable SmartRefreshLayout_srlDragRate 3
int styleable SmartRefreshLayout_srlEnableAutoLoadMore 4
int styleable SmartRefreshLayout_srlEnableClipFooterWhenFixedBehind 5
int styleable SmartRefreshLayout_srlEnableClipHeaderWhenFixedBehind 6
int styleable SmartRefreshLayout_srlEnableFooterFollowWhenLoadFinished 7
int styleable SmartRefreshLayout_srlEnableFooterTranslationContent 8
int styleable SmartRefreshLayout_srlEnableHeaderTranslationContent 9
int styleable SmartRefreshLayout_srlEnableLoadMore 10
int styleable SmartRefreshLayout_srlEnableLoadMoreWhenContentNotFull 11
int styleable SmartRefreshLayout_srlEnableNestedScrolling 12
int styleable SmartRefreshLayout_srlEnableOverScrollBounce 13
int styleable SmartRefreshLayout_srlEnableOverScrollDrag 14
int styleable SmartRefreshLayout_srlEnablePreviewInEditMode 15
int styleable SmartRefreshLayout_srlEnablePureScrollMode 16
int styleable SmartRefreshLayout_srlEnableRefresh 17
int styleable SmartRefreshLayout_srlEnableScrollContentWhenLoaded 18
int styleable SmartRefreshLayout_srlEnableScrollContentWhenRefreshed 19
int styleable SmartRefreshLayout_srlFixedFooterViewId 20
int styleable SmartRefreshLayout_srlFixedHeaderViewId 21
int styleable SmartRefreshLayout_srlFooterHeight 22
int styleable SmartRefreshLayout_srlFooterInsetStart 23
int styleable SmartRefreshLayout_srlFooterMaxDragRate 24
int styleable SmartRefreshLayout_srlFooterTranslationViewId 25
int styleable SmartRefreshLayout_srlFooterTriggerRate 26
int styleable SmartRefreshLayout_srlHeaderHeight 27
int styleable SmartRefreshLayout_srlHeaderInsetStart 28
int styleable SmartRefreshLayout_srlHeaderMaxDragRate 29
int styleable SmartRefreshLayout_srlHeaderTranslationViewId 30
int styleable SmartRefreshLayout_srlHeaderTriggerRate 31
int styleable SmartRefreshLayout_srlPrimaryColor 32
int styleable SmartRefreshLayout_srlReboundDuration 33
int[] styleable SmartRefreshLayout_Layout { 0x7f03029d, 0x7f03029e }
int styleable SmartRefreshLayout_Layout_layout_srlBackgroundColor 0
int styleable SmartRefreshLayout_Layout_layout_srlSpinnerStyle 1
int[] styleable Snackbar { 0x7f03038f, 0x7f030390, 0x7f030391 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f030022, 0x7f03002f, 0x7f030045, 0x7f030048, 0x7f030049, 0x7f030174, 0x7f0302da }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f03034a }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable State { 0x010100d0, 0x7f03010c }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f03014d }
int styleable StateSet_defaultState 0
int[] styleable StoreHouseHeader { 0x7f03037c, 0x7f03037d, 0x7f03037e, 0x7f03037f }
int styleable StoreHouseHeader_shhDropHeight 0
int styleable StoreHouseHeader_shhEnableFadeAnimation 1
int styleable StoreHouseHeader_shhLineWidth 2
int styleable StoreHouseHeader_shhText 3
int[] styleable SubsamplingScaleImageView { 0x7f030035, 0x7f030331, 0x7f030356, 0x7f030397, 0x7f03044d, 0x7f0304ae }
int styleable SubsamplingScaleImageView_assetName 0
int styleable SubsamplingScaleImageView_panEnabled 1
int styleable SubsamplingScaleImageView_quickScaleEnabled 2
int styleable SubsamplingScaleImageView_src 3
int styleable SubsamplingScaleImageView_tileBackgroundColor 4
int styleable SubsamplingScaleImageView_zoomEnabled 5
int[] styleable SwipeRevealLayout { 0x7f030160, 0x7f0301b3, 0x7f0302ec, 0x7f0302f8 }
int styleable SwipeRevealLayout_dragEdge 0
int styleable SwipeRevealLayout_flingVelocity 1
int styleable SwipeRevealLayout_minDistRequestDisallowParent 2
int styleable SwipeRevealLayout_mode 3
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f030387, 0x7f030396, 0x7f0303f3, 0x7f0303f4, 0x7f0303f6, 0x7f030443, 0x7f030444, 0x7f030445, 0x7f030471, 0x7f030478, 0x7f030479 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f030488 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabGradientTextView { 0x7f030143, 0x7f030144 }
int styleable TabGradientTextView_cy_textColorNormal 0
int styleable TabGradientTextView_cy_textColorSelected 1
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f0303f7, 0x7f0303f8, 0x7f0303f9, 0x7f0303fa, 0x7f0303fb, 0x7f0303fc, 0x7f0303fd, 0x7f0303fe, 0x7f0303ff, 0x7f030400, 0x7f030401, 0x7f030402, 0x7f030403, 0x7f030404, 0x7f030405, 0x7f030406, 0x7f030407, 0x7f030408, 0x7f030409, 0x7f03040a, 0x7f03040b, 0x7f03040c, 0x7f03040d, 0x7f03040f, 0x7f030410, 0x7f030411 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextColor 22
int styleable TabLayout_tabTextAppearance 23
int styleable TabLayout_tabTextColor 24
int styleable TabLayout_tabUnboundedRipple 25
int[] styleable TabLayoutMulti { 0x7f030140, 0x7f030141, 0x7f030142 }
int styleable TabLayoutMulti_cy_scrollable 0
int styleable TabLayoutMulti_cy_space_horizontal 1
int styleable TabLayoutMulti_cy_space_vertical 2
int[] styleable TabLayoutScroll { 0x7f030141, 0x7f030142 }
int styleable TabLayoutScroll_cy_space_horizontal 0
int styleable TabLayoutScroll_cy_space_vertical 1
int[] styleable TagFlowLayout { 0x7f0302e2, 0x7f030415 }
int styleable TagFlowLayout_max_select 0
int styleable TagFlowLayout_tag_gravity 1
int[] styleable TaurusHeader { 0x7f03043a }
int styleable TaurusHeader_thPrimaryColor 0
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f0301c9, 0x7f0301d3, 0x7f03041a, 0x7f030436 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextInputEditText { 0x7f030434 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x7f030079, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f03007d, 0x7f03007e, 0x7f03007f, 0x7f030080, 0x7f030081, 0x7f030082, 0x7f030083, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030179, 0x7f03017a, 0x7f03017b, 0x7f03017c, 0x7f03017d, 0x7f03017e, 0x7f030182, 0x7f030183, 0x7f030184, 0x7f030185, 0x7f030186, 0x7f030187, 0x7f030188, 0x7f03018b, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f030335, 0x7f030336, 0x7f030337, 0x7f030338, 0x7f030339, 0x7f030344, 0x7f030345, 0x7f030346, 0x7f03034c, 0x7f03034d, 0x7f03034e, 0x7f030377, 0x7f03037a, 0x7f0303d7, 0x7f0303d8, 0x7f0303d9, 0x7f0303da, 0x7f0303db, 0x7f0303ef, 0x7f0303f0, 0x7f0303f1 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_boxBackgroundColor 5
int styleable TextInputLayout_boxBackgroundMode 6
int styleable TextInputLayout_boxCollapsedPaddingTop 7
int styleable TextInputLayout_boxCornerRadiusBottomEnd 8
int styleable TextInputLayout_boxCornerRadiusBottomStart 9
int styleable TextInputLayout_boxCornerRadiusTopEnd 10
int styleable TextInputLayout_boxCornerRadiusTopStart 11
int styleable TextInputLayout_boxStrokeColor 12
int styleable TextInputLayout_boxStrokeErrorColor 13
int styleable TextInputLayout_boxStrokeWidth 14
int styleable TextInputLayout_boxStrokeWidthFocused 15
int styleable TextInputLayout_counterEnabled 16
int styleable TextInputLayout_counterMaxLength 17
int styleable TextInputLayout_counterOverflowTextAppearance 18
int styleable TextInputLayout_counterOverflowTextColor 19
int styleable TextInputLayout_counterTextAppearance 20
int styleable TextInputLayout_counterTextColor 21
int styleable TextInputLayout_endIconCheckable 22
int styleable TextInputLayout_endIconContentDescription 23
int styleable TextInputLayout_endIconDrawable 24
int styleable TextInputLayout_endIconMode 25
int styleable TextInputLayout_endIconTint 26
int styleable TextInputLayout_endIconTintMode 27
int styleable TextInputLayout_errorContentDescription 28
int styleable TextInputLayout_errorEnabled 29
int styleable TextInputLayout_errorIconDrawable 30
int styleable TextInputLayout_errorIconTint 31
int styleable TextInputLayout_errorIconTintMode 32
int styleable TextInputLayout_errorTextAppearance 33
int styleable TextInputLayout_errorTextColor 34
int styleable TextInputLayout_expandedHintEnabled 35
int styleable TextInputLayout_helperText 36
int styleable TextInputLayout_helperTextEnabled 37
int styleable TextInputLayout_helperTextTextAppearance 38
int styleable TextInputLayout_helperTextTextColor 39
int styleable TextInputLayout_hintAnimationEnabled 40
int styleable TextInputLayout_hintEnabled 41
int styleable TextInputLayout_hintTextAppearance 42
int styleable TextInputLayout_hintTextColor 43
int styleable TextInputLayout_passwordToggleContentDescription 44
int styleable TextInputLayout_passwordToggleDrawable 45
int styleable TextInputLayout_passwordToggleEnabled 46
int styleable TextInputLayout_passwordToggleTint 47
int styleable TextInputLayout_passwordToggleTintMode 48
int styleable TextInputLayout_placeholderText 49
int styleable TextInputLayout_placeholderTextAppearance 50
int styleable TextInputLayout_placeholderTextColor 51
int styleable TextInputLayout_prefixText 52
int styleable TextInputLayout_prefixTextAppearance 53
int styleable TextInputLayout_prefixTextColor 54
int styleable TextInputLayout_shapeAppearance 55
int styleable TextInputLayout_shapeAppearanceOverlay 56
int styleable TextInputLayout_startIconCheckable 57
int styleable TextInputLayout_startIconContentDescription 58
int styleable TextInputLayout_startIconDrawable 59
int styleable TextInputLayout_startIconTint 60
int styleable TextInputLayout_startIconTintMode 61
int styleable TextInputLayout_suffixText 62
int styleable TextInputLayout_suffixTextAppearance 63
int styleable TextInputLayout_suffixTextColor 64
int[] styleable ThemeEnforcement { 0x01010034, 0x7f03017f, 0x7f030180 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f0300ae, 0x7f0300eb, 0x7f0300ec, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f0302b9, 0x7f0302ba, 0x7f0302db, 0x7f0302e5, 0x7f030312, 0x7f030313, 0x7f03034a, 0x7f0303ea, 0x7f0303ec, 0x7f0303ed, 0x7f030450, 0x7f030454, 0x7f030455, 0x7f030456, 0x7f030457, 0x7f030458, 0x7f030459, 0x7f03045a, 0x7f03045b }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f030048 }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_padding 1
int styleable Tooltip_android_layout_margin 2
int styleable Tooltip_android_minWidth 3
int styleable Tooltip_android_minHeight 4
int styleable Tooltip_android_text 5
int styleable Tooltip_backgroundTint 6
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int[] styleable Transition { 0x010100d0, 0x7f03003e, 0x7f030109, 0x7f03010a, 0x7f030170, 0x7f030257, 0x7f030305, 0x7f03033a, 0x7f0303d5, 0x7f03047a, 0x7f03047c }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable TwoLevelHeader { 0x7f0303b3, 0x7f0303b8, 0x7f0303bc, 0x7f0303bd, 0x7f0303c8, 0x7f0303cc }
int styleable TwoLevelHeader_srlEnablePullToCloseTwoLevel 0
int styleable TwoLevelHeader_srlEnableTwoLevel 1
int styleable TwoLevelHeader_srlFloorDuration 2
int styleable TwoLevelHeader_srlFloorRage 3
int styleable TwoLevelHeader_srlMaxRage 4
int styleable TwoLevelHeader_srlRefreshRage 5
int[] styleable Variant { 0x7f03010c, 0x7f03035d, 0x7f03035e, 0x7f03035f, 0x7f030360 }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable VerticalTabLayout { 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f030412, 0x7f030413, 0x7f030414 }
int styleable VerticalTabLayout_indicator_color 0
int styleable VerticalTabLayout_indicator_corners 1
int styleable VerticalTabLayout_indicator_gravity 2
int styleable VerticalTabLayout_indicator_width 3
int styleable VerticalTabLayout_tab_height 4
int styleable VerticalTabLayout_tab_margin 5
int styleable VerticalTabLayout_tab_mode 6
int[] styleable View { 0x01010000, 0x010100da, 0x7f03032b, 0x7f03032e, 0x7f03043b }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030048, 0x7f030049 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable WaveSwipeHeader { 0x7f0304a7, 0x7f0304a8, 0x7f0304a9, 0x7f0304aa }
int styleable WaveSwipeHeader_wshAccentColor 0
int styleable WaveSwipeHeader_wshPrimaryColor 1
int styleable WaveSwipeHeader_wshShadowColor 2
int styleable WaveSwipeHeader_wshShadowRadius 3
int[] styleable pickerview { 0x7f030496, 0x7f030497, 0x7f030498, 0x7f030499, 0x7f03049a, 0x7f03049b, 0x7f03049c }
int styleable pickerview_wheelview_dividerColor 0
int styleable pickerview_wheelview_dividerWidth 1
int styleable pickerview_wheelview_gravity 2
int styleable pickerview_wheelview_lineSpacingMultiplier 3
int styleable pickerview_wheelview_textColorCenter 4
int styleable pickerview_wheelview_textColorOut 5
int styleable pickerview_wheelview_textSize 6
int xml provider_paths 0x7f140000
int xml standalone_badge 0x7f140001
int xml standalone_badge_gravity_bottom_end 0x7f140002
int xml standalone_badge_gravity_bottom_start 0x7f140003
int xml standalone_badge_gravity_top_start 0x7f140004
int xml standalone_badge_offset 0x7f140005
int xml take_file_path 0x7f140006
