// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TransferItemView_ViewBinding implements Unbinder {
  private TransferItemView target;

  @UiThread
  public TransferItemView_ViewBinding(TransferItemView target) {
    this(target, target);
  }

  @UiThread
  public TransferItemView_ViewBinding(TransferItemView target, View source) {
    this.target = target;

    target.mIvStatus = Utils.findRequiredViewAsType(source, R.id.iv_status, "field 'mIvStatus'", ImageView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    target.mTvAddress = Utils.findRequiredViewAsType(source, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    target.mTvAmount = Utils.findRequiredViewAsType(source, R.id.tv_amount, "field 'mTvAmount'", TextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    target.mLlRoot = Utils.findRequiredViewAsType(source, R.id.ll_root, "field 'mLlRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TransferItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvStatus = null;
    target.mTv1 = null;
    target.mTvAddress = null;
    target.mTvAmount = null;
    target.mTvTime = null;
    target.mLlRoot = null;
  }
}
