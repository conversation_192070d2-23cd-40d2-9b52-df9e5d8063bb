package com.Tlock.io.utils.cosmos;

import static com.Tlock.io.utils.wallet.WalletUtils.BIP44_COSMOS_ACCOUNT_ZERO_PATH;

import android.util.Log;

import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.JsonUtils;

import net.i2p.crypto.eddsa.Utils;

import org.bitcoinj.core.Bech32;
import org.bitcoinj.core.ECKey;
import org.bitcoinj.core.Sha256Hash;
import org.bitcoinj.crypto.ChildNumber;
import org.bitcoinj.crypto.DeterministicHierarchy;
import org.bitcoinj.crypto.DeterministicKey;
import org.bitcoinj.crypto.HDKeyDerivation;
import org.bitcoinj.crypto.MnemonicCode;
import org.bouncycastle.crypto.digests.RIPEMD160Digest;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Security;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;
import java.util.List;

public class CosmosWalletUtils {



    public static byte[] getPrivateKey(byte[] seed, List<ChildNumber> parentPaths, String lastPath) {
        DeterministicKey masterKey = HDKeyDerivation.createMasterPrivateKey(seed);
        DeterministicKey deterministicKey = new DeterministicHierarchy(masterKey).deriveChild(parentPaths, true, true, new ChildNumber(Integer.parseInt(lastPath)));
        return deterministicKey.getPrivKeyBytes();
    }

    public static String getAddressFromPubKey(byte[] pubKey) {
        try {
            String result = "";
            byte[] sha256Hash = Sha256Hash.hash(pubKey);
            byte[] ripemd160 = hashToRIPMD160(sha256Hash);
            byte[] converted = convertBits(ripemd160, 8, 5, true);
            result = Bech32.encode(Bech32.Encoding.BECH32, "tlock", converted);
            return result;
        } catch (Exception e) {
            String str = e.getMessage();
            return null;
        }

    }
    /**
     * 从字节数组加载PrivateKey对象。
     *
     * @param keyBytes 私钥字节数组（PKCS#8格式）
     * @return PrivateKey对象
     * @throws Exception 如果加载失败
     */
    public static PrivateKey loadPrivateKey(byte[] keyBytes) throws Exception {
        Security.addProvider(new BouncyCastleProvider());

        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        // 根据私钥的算法选择KeyFactory，例如RSA、EC等
        // 这里假设是RSA，如果是其他算法请修改
        KeyFactory keyFactory = KeyFactory.getInstance("RSA", "BC");
        return keyFactory.generatePrivate(keySpec);
    }

    public static byte[] convertBits(byte[] data, int fromBits, int toBits, boolean pad) throws Exception {
        int acc = 0;
        int bits = 0;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int maxv = (1 << toBits) - 1;
        for (int i = 0; i < data.length; i++) {
            int value = data[i] & 0xff;
            if ((value >>> fromBits) != 0) {
                throw new Exception("invalid data range: data[" + i + "]=" + value + " (fromBits=" + fromBits + ")");
            }
            acc = (acc << fromBits) | value;
            bits += fromBits;
            while (bits >= toBits) {
                bits -= toBits;
                baos.write((acc >>> bits) & maxv);
            }
        }
        if (pad) {
            if (bits > 0) {
                baos.write((acc << (toBits - bits)) & maxv);
            }
        } else if (bits >= fromBits) {
            throw new Exception("illegal zero padding");
        } else if (((acc << (toBits - bits)) & maxv) != 0) {
            throw new Exception("non-zero padding");
        }
        return baos.toByteArray();
    }


    public static ETHWallet getWallet(String mnemonic, int type) {
        try {
            ETHWallet ethWallet = new ETHWallet();
            if (type == 2) {
                //助记词
                String[] wordsArray = mnemonic.split(" ");
                List<String> wordsList = Arrays.asList(wordsArray);
                byte[] entropy = new MnemonicCode().toEntropy(wordsList);
                byte[] seed = MnemonicCode.toSeed(MnemonicCode.INSTANCE.toMnemonic(entropy), "");
                Log.e("COSMOS", "地址===" + JsonUtils.objectToJson(seed));
                DeterministicKey masterPrivateKey = HDKeyDerivation.createMasterPrivateKey(seed);
                DeterministicHierarchy deterministicHierarchy = new DeterministicHierarchy(masterPrivateKey);
                DeterministicKey deterministicKey = deterministicHierarchy.deriveChild(BIP44_COSMOS_ACCOUNT_ZERO_PATH, false, true, new ChildNumber(0));
                byte[] privKeyBytes = deterministicKey.getPrivKeyBytes();
                Log.e("COSMOS", "privKeyBytes===" + JsonUtils.objectToJson(privKeyBytes));

                String string = Utils.bytesToHex(privKeyBytes);
                if (string.toLowerCase().startsWith("0x")) {
                    string = string.substring(2);
                }
                ethWallet.setPrivateKey(string);
                String addressFromPrivateKry = getAddressFromPrivateKry(privKeyBytes);
                ethWallet.setAddress(addressFromPrivateKry);
                ethWallet.setMnemonic(mnemonic);
                return ethWallet;
            } else {
                //私钥
                ethWallet.setPrivateKey(mnemonic);
                if (mnemonic.toLowerCase().startsWith("0x")) {
                    mnemonic = mnemonic.substring(2);
                }
                byte[] bytes = Utils.hexToBytes(mnemonic);
                String addressFromPrivateKry = getAddressFromPrivateKry(bytes);
                ethWallet.setAddress(addressFromPrivateKry);
                return ethWallet;
            }
        } catch (Exception e) {
            Log.e("COSMOS", "错误===" + e.getMessage());

        }
        return null;
    }

    public static String getAddressFromPrivateKry(byte[] privKeyBytes) {
        Log.e("COSMOS", "私钥byte===" + JsonUtils.objectToJson(Utils.bytesToHex(privKeyBytes)));
        ECKey ecKey = ECKey.fromPrivate(privKeyBytes);
        byte[] pubKey = ecKey.getPubKey();
        Log.e("COSMOS", "pubKey===" + Utils.bytesToHex(pubKey));
        Log.e("COSMOS", "priKey===" + Utils.bytesToHex(ecKey.getPrivKeyBytes()));
        String address = getAddressFromPubKey(pubKey);
        Log.e("COSMOS", "addressFromPubKey===" + JsonUtils.objectToJson(address));
        return address;
    }

    public static byte[] hashToRIPMD160(byte[] sha256Hash) {
        RIPEMD160Digest rDigest = new RIPEMD160Digest();
        rDigest.update(sha256Hash, 0, sha256Hash.length);
        byte[] digestResult = new byte[rDigest.getDigestSize()];
        rDigest.doFinal(digestResult, 0);
        return digestResult;
    }
}
