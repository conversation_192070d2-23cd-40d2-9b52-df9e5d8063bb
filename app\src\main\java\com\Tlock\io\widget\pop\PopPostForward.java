package com.Tlock.io.widget.pop;

import android.content.Context;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.Tlock.io.R;
import com.lxj.xpopup.core.BottomPopupView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * @ClassName PopWhaleList
 * <AUTHOR>
 * @Data 2023/11/6 19:06
 * @Desc
 */

public class PopPostForward extends BottomPopupView {



    private String handle;
    private String id;

    public PopPostForward(@NonNull Context context) {
        super(context);
    }

    public PopPostForward(@NonNull Context context, String id, String handle) {
        super(context);
        this.id = id;
        this.handle = handle;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.pop_post_forward;
    }

    @Override
    protected void onCreate() {
        ButterKnife.bind(this);
        super.onCreate();
    }


    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    private CallBack callBack;

    @OnClick({R.id.tv_repost, R.id.tv_Quote})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_repost:
                if (callBack != null)
                    dismiss();
                callBack.repost(id);
                break;
            case R.id.tv_Quote:
                if (callBack != null)
                    callBack.quote(id);
                dismiss();
                break;
        }
    }

    public interface CallBack {
        void repost(String id);

        void quote(String id);

    }
}
