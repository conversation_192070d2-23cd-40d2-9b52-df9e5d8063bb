//package com.Tlock.io.adapter;
//
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//
//import androidx.annotation.NonNull;
//import androidx.viewpager.widget.PagerAdapter;
//
//import com.bumptech.glide.Glide;
//import com.lxj.xpopup.XPopup;
//import com.lxj.xpopup.core.ImageViewerPopupView;
//import com.lxj.xpopup.interfaces.OnSrcViewUpdateListener;
//import com.lxj.xpopup.util.SmartGlideImageLoader;
//
//import java.util.ArrayList;
//
//class ImagePagerAdapter extends PagerAdapter {
//    public static ArrayList<Object> list = new ArrayList<>();
//
//        @Override
//        public int getCount() {
//            return list.size();
//        }
//
//        @Override
//        public boolean isViewFromObject(@NonNull View view, @NonNull Object o) {
//            return view == o;
//        }
//
//        @NonNull
//        @Override
//        public Object instantiateItem(@NonNull ViewGroup container, final int position) {
//            final ImageView imageView = new ImageView(container.getContext());
//            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
//            container.addView(imageView);
//
//            //1. 加载图片
//            Glide.with(imageView).load(list.get(position)).into(imageView);
//
//            //2. 设置点击
//            imageView.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    new XPopup.Builder(getContext())
//                            .asImageViewer(imageView, position, list, true, false, -1, -1, -1, true, Color.BLACK, new OnSrcViewUpdateListener() {
//                                @Override
//                                public void onSrcViewUpdate(final ImageViewerPopupView popupView, final int position) {
//                                    //1.pager更新当前显示的图片
//                                    //当启用isInfinite时，position会无限增大，需要映射为当前ViewPager中的页
//                                    int realPosi = position % list.size();
//                                    pager.setCurrentItem(position, false);
//                                    //2.更新弹窗的srcView，注意这里的position是list中的position，上面ViewPager设置了pageLimit数量，
//                                    //保证能拿到child，如果不设置pageLimit，ViewPager默认最多维护3个page，会导致拿不到child
//                                    popupView.updateSrcView((ImageView) pager.getChildAt(position));
//                                }
//                            }, new SmartGlideImageLoader(), null)
//                            .show();
//                }
//            });
//            return imageView;
//        }
//
//        @Override
//        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
//            container.removeView((View) object);
//        }
//    }