<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".activity.cosmos.EditProfileActivity">

    <com.Tlock.io.widget.CustomNavBar
        android:id="@+id/custom_nav_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:title_mid="Edit Topic" />

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        android:layout_below="@id/custom_nav_bar"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/dp_24" />

    <ImageView
        android:id="@+id/iv_avatar1"
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_90"
        android:layout_below="@id/custom_nav_bar"
        android:layout_centerHorizontal="true"
        android:layout_margin="@dimen/dp_24"
        android:src="@mipmap/icon_heard_logo" />

    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_nike_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_avatar"
        app:text_color="@color/cosmos_black"
        app:tv_title="Title" />


    <RelativeLayout
        android:id="@+id/rl_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ed_nike_name"
        android:clickable="true"
        android:focusableInTouchMode="true"
        android:minHeight="@dimen/dp_54"
        android:paddingLeft="@dimen/dp_15"
        android:paddingTop="@dimen/dp_10"
        android:paddingRight="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_10">

        <TextView
            android:id="@+id/tv_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Category"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tv_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_2"
            android:layout_marginTop="@dimen/dp_6"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1.5dp"
            android:layout_marginTop="@dimen/dp_2"
            android:background="@color/cosmos_line_default"
            android:layout_below="@id/tv_3"/>

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_show_more" />

    </RelativeLayout>

    <com.Tlock.io.widget.CustomEditBox
        android:id="@+id/ed_bio"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_category"
        app:text_color="@color/cosmos_black"
        app:tv_title="Introduction" />


    <TextView
        android:id="@+id/tv_save"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp_24"
        android:background="@drawable/btn_black_60"
        android:gravity="center"
        android:padding="@dimen/dp_15"
        android:text="Save"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

</RelativeLayout>




