<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp_5"
    android:background="@drawable/btn_gray_6"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/dp_5"
    android:paddingTop="@dimen/dp_10"
    android:paddingRight="@dimen/dp_5"
    android:paddingBottom="@dimen/dp_10">

    <TextView
        android:id="@+id/tv_index"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:text="1"
        android:textColor="#CC536471"
        android:textSize="@dimen/sp_15"
        android:visibility="gone" />


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_10"
        android:layout_toRightOf="@id/tv_index"
        android:gravity="center"
        android:singleLine="true"
        android:text="交易所"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_15"
        android:textStyle="bold" />
</RelativeLayout>
