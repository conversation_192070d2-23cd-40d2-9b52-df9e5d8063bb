// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.RoundedImageView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ImageShowViewBean_ViewBinding implements Unbinder {
  private ImageShowViewBean target;

  private View view7f090151;

  private View view7f090143;

  private View view7f09025a;

  @UiThread
  public ImageShowViewBean_ViewBinding(ImageShowViewBean target) {
    this(target, target);
  }

  @UiThread
  public ImageShowViewBean_ViewBinding(final ImageShowViewBean target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_image, "field 'mIvImage' and method 'onBindClick'");
    target.mIvImage = Utils.castView(view, R.id.iv_image, "field 'mIvImage'", RoundedImageView.class);
    view7f090151 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_delete, "field 'mIvDelete' and method 'onBindClick'");
    target.mIvDelete = Utils.castView(view, R.id.iv_delete, "field 'mIvDelete'", ImageView.class);
    view7f090143 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.root, "field 'mRoot' and method 'onBindClick'");
    target.mRoot = Utils.castView(view, R.id.root, "field 'mRoot'", RelativeLayout.class);
    view7f09025a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    ImageShowViewBean target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvImage = null;
    target.mIvDelete = null;
    target.mRoot = null;

    view7f090151.setOnClickListener(null);
    view7f090151 = null;
    view7f090143.setOnClickListener(null);
    view7f090143 = null;
    view7f09025a.setOnClickListener(null);
    view7f09025a = null;
  }
}
