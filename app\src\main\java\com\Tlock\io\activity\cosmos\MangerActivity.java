package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.profile.ProfileQueryProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;

import butterknife.BindView;
import butterknife.OnClick;


public class MangerActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_name)
    TextView mTvName;
    @BindView(R.id.tv_handle)
    TextView mTvHandle;
    @BindView(R.id.tv_date)
    TextView mTvDate;
    @BindView(R.id.iv1)
    ImageView mIv1;
    @BindView(R.id.rl_task)
    RelativeLayout mRlTask;
    @BindView(R.id.iv2)
    ImageView mIv2;
    @BindView(R.id.rl_topic)
    RelativeLayout mRlTopic;
    @BindView(R.id.iv3)
    ImageView mIv3;
    @BindView(R.id.rl_member_list)
    RelativeLayout mRlMemberList;
    @BindView(R.id.main)
    RelativeLayout mMain;
    @BindView(R.id.tv_wallet_address)
    TextView mTvWalletAddress;
    private ProfileProto.Profile authInfo;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context
    ) {
        Intent intent = new Intent(context, MangerActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_manger;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mTvWalletAddress.setText(WalletDaoUtils.getCurrent().getAddress());

    }


    @Override
    protected void loadData() {
        getAuthInfo();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            ETHWallet current = WalletDaoUtils.getCurrent();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    authInfo = CosmosUtils.getAuthInfo(current.getAddress());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mTvName.setText(authInfo.getNickname());


                            if (authInfo.getAvatar().startsWith("http")) {
                                if (TextUtils.isEmpty(authInfo.getAvatar())) {
                                    TextAvatarDrawable a = new TextAvatarDrawable(authInfo.getUserHandle().substring(0, 1));

                                    mIvHeard.setImageDrawable(a);
                                    mTvHandle.setText("@" + authInfo.getUserHandle());
                                } else {
                                    Glide.with(getActivity()).load(authInfo.getAvatar()).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(authInfo.getAvatar()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                }

                            } else {
                                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authInfo.getAvatar());
                                Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                .centerCrop()
                                                .dontTransform())
                                        .apply(RequestOptions.circleCropTransform().circleCrop())
                                        .into(mIvHeard);
                            }

                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.tv_handle, R.id.rl_task, R.id.rl_topic, R.id.rl_member_list, R.id.tv_wallet_address})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_handle:
                CopyUtils.copyToClipboard(authInfo.getUserHandle());
                break;
            case R.id.rl_task:
                MTaskListActivity.start(getActivity());
                break;
            case R.id.rl_topic:
                MTopicSearchActivity.start(getActivity());
                break;
            case R.id.rl_member_list:
                MMemberListActivity.start(getActivity(), WalletDaoUtils.getCurrent().getAddress());
                break;
            case R.id.tv_wallet_address:
                CopyUtils.copyToClipboard(WalletDaoUtils.getCurrent().getAddress());

                break;
        }
    }
}