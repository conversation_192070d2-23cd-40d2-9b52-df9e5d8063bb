// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class Test1Activity_ViewBinding implements Unbinder {
  private Test1Activity target;

  private View view7f090137;

  private View view7f090365;

  @UiThread
  public Test1Activity_ViewBinding(Test1Activity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public Test1Activity_ViewBinding(final Test1Activity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    view = Utils.findRequiredView(source, R.id.iv_avatar, "field 'mIvAvatar' and method 'onBindClick'");
    target.mIvAvatar = Utils.castView(view, R.id.iv_avatar, "field 'mIvAvatar'", ImageView.class);
    view7f090137 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvAvatar1 = Utils.findRequiredViewAsType(source, R.id.iv_avatar1, "field 'mIvAvatar1'", ImageView.class);
    target.mSmallLabel1 = Utils.findRequiredViewAsType(source, R.id.smallLabel1, "field 'mSmallLabel1'", ImageView.class);
    target.mSmallLabel2 = Utils.findRequiredViewAsType(source, R.id.smallLabel2, "field 'mSmallLabel2'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.tv_save, "field 'mTvSave' and method 'onBindClick'");
    target.mTvSave = Utils.castView(view, R.id.tv_save, "field 'mTvSave'", TextView.class);
    view7f090365 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mRvImages = Utils.findRequiredViewAsType(source, R.id.rv_images, "field 'mRvImages'", RecyclerView.class);
    target.mPager2 = Utils.findRequiredViewAsType(source, R.id.pager2, "field 'mPager2'", ViewPager2.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    Test1Activity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIvAvatar = null;
    target.mIvAvatar1 = null;
    target.mSmallLabel1 = null;
    target.mSmallLabel2 = null;
    target.mTvSave = null;
    target.mRvImages = null;
    target.mPager2 = null;

    view7f090137.setOnClickListener(null);
    view7f090137 = null;
    view7f090365.setOnClickListener(null);
    view7f090365 = null;
  }
}
