package com.Tlock.io.adapter;

import static com.scwang.smartrefresh.layout.util.DensityUtil.dp2px;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

public class SpacesItemDecoration1 extends RecyclerView.ItemDecoration {
    private int space;
    private int spanCount;

    public SpacesItemDecoration1(int space, int spanCount) {
        this.space = space;
        this.spanCount = spanCount;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int column = position % spanCount;

        // 当 spanCount 为 1 时，不添加左右间距
        if (spanCount == 1) {
            outRect.left = 0;
            outRect.right = 0;
        } else {
            // 第一列：左边距为 0
            if (column == 0) {
                outRect.left = 0;
                outRect.right = space -dp2px(1);
            }
            // 最后一列：右边距为 0
            else if (column == spanCount - 1) {
                outRect.left = space -dp2px(1);
                outRect.right = 0;
            }
            // 中间列：左右间距均匀
            else {
                outRect.left = space -dp2px(2);
                outRect.right = space - dp2px(2);
            }
        }

        // 设置顶部间距（除了第一行）
        if (position >= spanCount) {
            outRect.top = space;
        } else {
            outRect.top = 0;
        }
        // outRect.bottom = space; // 已注释，保持不变
//        Log.e("SpacesItemDecoration", "Position: " + position + ", Column: " + column + ", Left: " + outRect.left + ", Right: " + outRect.right + ", Top: " + outRect.top + ", Bottom: " + outRect.bottom);
    }
}