package com.Tlock.io.fragment.cosmos;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.TopicDetailActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseFragment;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.Event;
import com.Tlock.io.itemBean.cosmos.TopicListItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

import butterknife.BindView;


public class TopicListFragment extends BaseFragment {

    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;
    @BindView(R.id.load_error)
    LoadErrorView mLoadError;
    @BindView(R.id.refresh_layout)
    SmartRefreshLayout mRefreshLayout;
    private BaseRecyclerViewAdapter<PostQueryProto.TopicResponse> adapter;
    private String id;
    private boolean isPost;

    public TopicListFragment(String id, boolean isPost) {
        this.id = id;
        this.isPost = isPost;
    }


    @Override
    protected int getContentViewId() {
        return R.layout.fragment_post_like;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRecycleView();
    }

    private void initRecycleView() {

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getContext(), new ArrayList<>(), new BaseRecyclerViewAdapter.Delegate<PostQueryProto.TopicResponse>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                TopicListItemView itemView = new TopicListItemView(getContext());
                return itemView;
            }

            @Override
            public void bindViewData(int position, PostQueryProto.TopicResponse data, View view) {
                ((TopicListItemView) view).setData(data);
                ((TopicListItemView) view).setCallback(new TopicListItemView.Callback() {
                    @Override
                    public void resetProfile(PostQueryProto.TopicResponse data) {
                        try {
                            adapter.getList().set(position, data);
                        } catch (Exception e) {
                        }
                    }
                });
            }
        });
        mRecyclerView.setAdapter(adapter);
        //点击切换
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<PostQueryProto.TopicResponse>() {
            @Override
            public void onItemClick(int position, PostQueryProto.TopicResponse data, View view) {
                if (isPost) {
                    EventBus.getDefault().postSticky(new Event("topic_callback",
                            data.getName().trim().startsWith("#") ? (data.getName() + " ").replace("#", " #") : (" #" + data.getName() + " ")
                    ));
                } else {
//                    EventBus.getDefault().post(new Event("topic_select", data));
                    TopicDetailActivity.start(getActivity(), JsonUtils.objectToJson(data));
                }

            }
        });
    }

    @Override
    protected void loadData() {
        getTopicList();

    }

    @Override
    protected void getData() {
        super.getData();
        getTopicList();

    }

    public void getListData(int position) {
        if (adapter != null && adapter.getList().isEmpty()) {
            getTopicData(adapter.getList().get(position).getId());
        }

    }

    public void getTopicList() {
        if (mRefreshLayout != null) {
            mRefreshLayout.setEnableLoadMore(true);
            mRefreshLayout.setEnableRefresh(true);
        }
//        Log.e(TAG, "getTopicList: " + "执行方法");
        //  修改成实际的
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                if (!TextUtils.isEmpty(id)) {
                    ArrayList<PostQueryProto.TopicResponse> topicsByCategory = new ArrayList<>();
                    if (id.equalsIgnoreCase("0")) {
                        topicsByCategory = CosmosUtils.QueryFollowingTopics();
                    } else {
                        topicsByCategory = CosmosUtils.getTopicsByCategory(id, page);
                    }

                    ArrayList<PostQueryProto.TopicResponse> finalTopicsByCategory = topicsByCategory;
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (adapter != null) {
                                adapter.addListNoChange(finalTopicsByCategory, page);
                            }
                            finishRefresh();
                        }
                    });
                }
            }
        });
    }

    public void getTopicData(String math) {
        mRefreshLayout.setEnableLoadMore(false);
        mRefreshLayout.setEnableRefresh(false);

        page = 1;

        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.TopicResponse> topic = CosmosUtils.getTopic(math);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (topic.size() != 0) {
                            adapter.addList(topic, page);
                        } else {
                            PostQueryProto.TopicResponse build = PostQueryProto.TopicResponse.newBuilder().setName("#" + math).build();
                            ArrayList<PostQueryProto.TopicResponse> objects = new ArrayList<>();
                            objects.add(build);
                            adapter.setList(objects);
                        }
                    }
                });
            }
        });
    }


}