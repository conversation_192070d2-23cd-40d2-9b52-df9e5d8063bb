package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.itemBean.cosmos.PostTopicItemView;
import com.Tlock.io.profile.ProfileQueryProto;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.MeasureRecyclerView;

import java.util.ArrayList;

import butterknife.BindView;

public class FollowingListActivity extends BaseActivity {

    @BindView(R.id.recyclerView_community)
    MeasureRecyclerView mRecyclerView;
    private ArrayList<ProfileProto.Profile> userList = new ArrayList<>();
    private BaseRecyclerViewAdapter<ProfileProto.Profile> adapter;

    /**
     * @param context
     * @param from    1 关注  2 粉丝
     */
    public static void start(Context context, int from) {
        Intent intent = new Intent(context, FollowingListActivity.class);
        intent.putExtra("from", from);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_community;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRecyclerView();
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initRecyclerView() {
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new BaseRecyclerViewAdapter<>(getActivity(), userList, new BaseRecyclerViewAdapter.Delegate<ProfileProto.Profile>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new PostTopicItemView(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, ProfileProto.Profile data, View view) {
                PostTopicItemView viewBean = (PostTopicItemView) view;
                viewBean.setData(data,true);
            }
        });
        adapter.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<ProfileProto.Profile>() {
            @Override
            public void onItemClick(int position, ProfileProto.Profile data, View view) {
                UserInfoActivity.start(getActivity(), data.getWalletAddress());
            }
        });
        mRecyclerView.setAdapter(adapter);
    }

    @Override
    protected void loadData() {
        params.clear();
        int from = getIntent().getIntExtra("from", 0);
        mCustomNavBar.setMidTitle(from == 1 ? "Following" : "Fans");
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<ProfileProto.Profile> followList = new ArrayList<>();
                if (from == 1) {
                    //关注人
                    followList = CosmosUtils.queryFollowing(WalletDaoUtils.getCurrent().getAddress());
                } else if (from == 2) {
                    //粉丝
                    followList = CosmosUtils.queryFollowers(WalletDaoUtils.getCurrent().getAddress());
                }
                ArrayList<ProfileProto.Profile> finalFollowList = followList;
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        adapter.setList(finalFollowList);

                    }
                });
            }
        });


    }
}