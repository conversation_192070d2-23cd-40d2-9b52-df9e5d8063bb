{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-en-rUS/values-en-rUS.xml", "map": [{"source": "D:\\cs\\tlk\\app\\src\\main\\res\\values-en-rUS\\strings.xml", "from": {"startLines": "13,116,113,117,21,30,96,49,17,31,36,70,62,72,44,12,20,16,63,29,50,14,18,85,138,33,15,84,57,133,97,19,3,131,109,108,151,28,134,125,126,115,40,46,82,69,122,42,74,41,58,59,86,38,120,119,118,92,24,124,25,87,83,146,37,106,61,60,102,111,78,79,80,81,35,143,144,110,45,9,90,91,10,99,27,147,148,95,137,26,114,163,150,73,88,5,121,145,75,94,135,76,103,93,89,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "945,5497,5278,5540,1310,1774,4418,2421,1115,1816,1960,3068,2876,3136,2250,880,1252,1074,2923,1711,2468,986,1154,3738,7089,1863,1027,3689,2591,6878,4499,1197,59,6833,5069,5018,7715,1621,6934,6142,6199,5405,2105,2364,3607,3023,5889,2209,3233,2152,2634,2707,3783,2058,5738,5679,5622,4116,1374,6088,1423,3826,3650,7374,2011,4888,2820,2764,4646,5167,3419,3466,3513,3560,1914,7215,7264,5128,2319,749,3978,4045,799,4558,1548,7432,7498,4353,7050,1489,5341,7962,7652,3186,3865,160,5827,7325,3288,4261,6995,3347,4711,4178,3910,7579", "endLines": "13,116,113,117,21,30,96,49,17,31,36,70,62,72,44,12,20,16,63,29,50,14,18,85,138,33,15,84,57,133,97,19,3,131,109,108,151,28,134,125,126,115,40,46,82,69,122,42,74,41,58,59,86,38,120,119,118,92,24,124,25,87,83,146,37,106,61,60,102,111,78,79,80,81,35,143,144,110,45,9,90,91,10,99,27,147,148,95,137,26,114,163,150,73,88,7,121,145,75,94,135,76,103,93,89,149", "endColumns": "39,41,61,80,41,40,79,45,37,43,49,46,45,48,67,63,56,39,51,61,43,39,41,43,69,47,45,47,41,54,55,53,40,41,57,49,62,88,59,55,109,90,45,51,41,43,60,37,53,55,71,55,41,43,87,57,55,60,47,52,64,37,37,56,45,58,54,54,63,39,45,45,45,45,44,47,59,37,43,48,65,69,77,63,71,64,79,63,37,57,62,55,61,45,43,111,60,47,57,90,51,51,51,81,66,71", "endOffsets": "980,5534,5335,5616,1347,1810,4493,2462,1148,1855,2005,3110,2917,3180,2313,939,1304,1109,2970,1768,2507,1021,1191,3777,7154,1906,1068,3732,2628,6928,4550,1246,95,6870,5122,5063,7773,1705,6989,6193,6304,5491,2146,2411,3644,3062,5945,2242,3282,2203,2701,2758,3820,2097,5821,5732,5673,4172,1417,6136,1483,3859,3683,7426,2052,4942,2870,2814,4705,5202,3460,3507,3554,3601,1954,7258,7319,5161,2358,793,4039,4110,872,4617,1615,7492,7573,4412,7083,1542,5399,8013,7709,3227,3904,741,5883,7368,3341,4347,7042,3394,4758,4255,3972,7646"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,137,199,280,322,363,443,489,527,571,621,668,714,763,831,895,952,992,1044,1106,1150,1190,1232,1276,1346,1394,1440,1488,1530,1585,1641,1695,1736,1778,1836,1886,1949,2038,2098,2154,2264,2355,2401,2453,2495,2539,2600,2638,2692,2748,2820,2876,2918,2962,3050,3108,3164,3225,3273,3326,3391,3429,3467,3524,3570,3629,3684,3739,3803,3843,3889,3935,3981,4027,4072,4120,4180,4218,4262,4311,4377,4447,4525,4589,4661,4726,4806,4870,4908,4966,5029,5085,5147,5193,5237,5821,5882,5930,5988,6079,6131,6183,6235,6317,6384", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "39,41,61,80,41,40,79,45,37,43,49,46,45,48,67,63,56,39,51,61,43,39,41,43,69,47,45,47,41,54,55,53,40,41,57,49,62,88,59,55,109,90,45,51,41,43,60,37,53,55,71,55,41,43,87,57,55,60,47,52,64,37,37,56,45,58,54,54,63,39,45,45,45,45,44,47,59,37,43,48,65,69,77,63,71,64,79,63,37,57,62,55,61,45,43,111,60,47,57,90,51,51,51,81,66,71", "endOffsets": "90,132,194,275,317,358,438,484,522,566,616,663,709,758,826,890,947,987,1039,1101,1145,1185,1227,1271,1341,1389,1435,1483,1525,1580,1636,1690,1731,1773,1831,1881,1944,2033,2093,2149,2259,2350,2396,2448,2490,2534,2595,2633,2687,2743,2815,2871,2913,2957,3045,3103,3159,3220,3268,3321,3386,3424,3462,3519,3565,3624,3679,3734,3798,3838,3884,3930,3976,4022,4067,4115,4175,4213,4257,4306,4372,4442,4520,4584,4656,4721,4801,4865,4903,4961,5024,5080,5142,5188,5232,5816,5877,5925,5983,6074,6126,6178,6230,6312,6379,6451"}}]}]}