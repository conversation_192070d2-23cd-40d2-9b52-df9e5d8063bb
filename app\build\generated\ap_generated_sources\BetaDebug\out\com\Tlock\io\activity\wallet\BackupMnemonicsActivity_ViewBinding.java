// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class BackupMnemonicsActivity_ViewBinding implements Unbinder {
  private BackupMnemonicsActivity target;

  private View view7f090346;

  @UiThread
  public BackupMnemonicsActivity_ViewBinding(BackupMnemonicsActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public BackupMnemonicsActivity_ViewBinding(final BackupMnemonicsActivity target, View source) {
    this.target = target;

    View view;
    target.mIvBack = Utils.findRequiredViewAsType(source, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    target.mRlToolbar = Utils.findRequiredViewAsType(source, R.id.rl_toolbar, "field 'mRlToolbar'", RelativeLayout.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    target.mTvTip = Utils.findRequiredViewAsType(source, R.id.tv_tip, "field 'mTvTip'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_next, "field 'mTvNext' and method 'onViewClicked'");
    target.mTvNext = Utils.castView(view, R.id.tv_next, "field 'mTvNext'", TextView.class);
    view7f090346 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    BackupMnemonicsActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mRlToolbar = null;
    target.mTv1 = null;
    target.mTvTip = null;
    target.mTvNext = null;

    view7f090346.setOnClickListener(null);
    view7f090346 = null;
  }
}
