package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.UserUtil.dip2px;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestOptions;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TopicListItemView extends BaseView {

    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_nike_name)
    TextView mTvNikeName;
    @BindView(R.id.tv_handle)
    TextView mTvHandle;
    @BindView(R.id.ll_root)
    RelativeLayout mLlRoot;
    private PostQueryProto.TopicResponse data;
    private String id = "";

    public TopicListItemView(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_post_topic;
    }

    public void setData(PostQueryProto.TopicResponse data) {
        if (id.equalsIgnoreCase(data.getId())) {
            return;
        }
        id = data.getId();
        this.data = data;
        mTvNikeName.setText(data.getName());

        if (data.getScore() == 0) {
            mTvHandle.setVisibility(GONE);
        } else {
            mTvHandle.setText(BigDecimalUtils.saveDecimals(data.getScore() + "",0));
            mTvHandle.setVisibility(VISIBLE);
        }
        mTvNikeName.setVisibility(VISIBLE);

        int radiusPx = dip2px(getContext(), 6);
        RequestOptions options = new RequestOptions().bitmapTransform(new RoundedCorners(radiusPx));

        if (!data.getImage().isEmpty()) {
            int radiusPx1 = dip2px(getContext(), 3);
            RequestOptions options1 = new RequestOptions().bitmapTransform(new RoundedCorners(radiusPx1));
            Bitmap bitmap1 = BitmapUtils.base64ToBitmap(data.getImage());
            Glide.with(getContext())
                    .load(bitmap1)
                    .apply(options1)
                    .into(mIvHeard);
        } else {
            Glide.with(getContext()).load(getResources().getDrawable(R.mipmap.topic_heard_default))
                    .apply(options)
                    .transition(DrawableTransitionOptions.withCrossFade(500))
                    .into(mIvHeard);
            getAuthInfo();
        }
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String authStr = CosmosUtils.getTopicHeard(data.getId());
                    mIvHeard.post(new Runnable() {
                        @Override
                        public void run() {
                            int radiusPx = dip2px(getContext(), 3);
                            RequestOptions options = new RequestOptions().bitmapTransform(new RoundedCorners(radiusPx));
                            if (!TextUtils.isEmpty(authStr)) {
                                if (callback != null) {
                                    PostQueryProto.TopicResponse build = data.toBuilder().setImage(authStr).build();
                                    callback.resetProfile(build);
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authStr);
                                    Glide.with(getContext()).load(bitmap1)
                                            .apply(options)
                                            .transition(DrawableTransitionOptions.withCrossFade(500))
                                            .into(mIvHeard);
                                }

                            }
                        }
                    });
                }
            });
        }
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void resetProfile(PostQueryProto.TopicResponse data);
    }

}
