package com.Tlock.io.utils;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;

import com.Tlock.io.app.AppApplication;

import org.greenrobot.greendao.annotation.NotNull;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @ClassName BigDecimalUtils
 * <AUTHOR>
 * @Data 2021/11/23 11:30
 * @Desc
 */

public class BigDecimalUtils {
    /**
     * wei转为 Gwei
     *
     * @param wei
     * @return
     */
    public static String weiToGwei(String wei) {
        return new BigDecimal(wei)
                .divide(new BigDecimal(10).pow(9))
                .toPlainString();
    }

    /**
     * Gwei转为 wei
     *
     * @param gwei
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String gweiTowei(String gwei) {
        String string = new BigDecimal(gwei)
                .multiply(new BigDecimal(10).pow(9)).setScale(0, BigDecimal.ROUND_DOWN)
                .toPlainString();
        return string;
    }

    /**
     * gwei转为 eth
     *
     * @param uTok
     * @return
     */
    public static String uTok2Tok(String uTok) {
        return new BigDecimal(uTok)
                .divide(new BigDecimal(10).pow(6))
                .toPlainString();
    }

    /**
     * eth转wei
     *
     * @param TOK
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String Tok2uTok(String TOK) {
        if (TextUtils.isEmpty(TOK)) return "0";
        return new BigDecimal(TOK)
                .multiply(new BigDecimal(10).pow(6)).setScale(0, BigDecimal.ROUND_DOWN)
                .toPlainString();
    }


    /**
     * gwei转为 eth
     *
     * @param gwei
     * @return
     */
    public static String gweiToEth(String gwei) {
        return new BigDecimal(gwei)
                .divide(new BigDecimal(10).pow(9))
                .toPlainString();
    }

    /**
     * eth转wei
     *
     * @param eth
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String ethToWei(String eth) {
        if (TextUtils.isEmpty(eth)) return "0";
        return new BigDecimal(eth)
                .multiply(new BigDecimal(10).pow(18)).setScale(0, BigDecimal.ROUND_DOWN)
                .toPlainString();
    }

    @SuppressWarnings("deprecation")
    public static String ethToGwei(String eth) {
        return new BigDecimal(eth)
                .multiply(new BigDecimal(10).pow(9)).setScale(0, BigDecimal.ROUND_DOWN)
                .toPlainString();
    }

    /**
     * wei转为 eth
     *
     * @param wei
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String weiToEth(String wei) {
        if (TextUtils.isEmpty(wei)) return "0";
        return new BigDecimal(wei)
                .divide(new BigDecimal(10).pow(18))
                .toPlainString();
    }

    /**
     * wei转为 eth 带精度
     *
     * @param gwei
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String weiToEthWithDecimal(String gwei, int decimal) {
        if (TextUtils.isEmpty(gwei) || gwei.equalsIgnoreCase("0") || gwei.equalsIgnoreCase("null") || TextUtils.isEmpty(gwei.trim()))
            return "0";
        if (decimal == 0) return gwei;
        return new BigDecimal(gwei)
                .divide(new BigDecimal(10).pow(decimal))
                .toPlainString();
    }

    /**
     * eth转wei 带精度
     *
     * @param eth
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String ethToWeiWithDecimal(String eth, int decimal) {
        if (TextUtils.isEmpty(eth)) eth = "0";
        return new BigDecimal(eth)
                .multiply(new BigDecimal(10).pow(decimal)).setScale(0, BigDecimal.ROUND_DOWN)
                .toPlainString();
    }


    /**
     * 转数字 加千分符
     *
     * @param num
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String toNumber(String num) {
        return saveDecimals(num, 6);
    }

    /**
     * 保留整数
     *
     * @param num
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String toNumber4(String num) {
        return saveDecimals(num, -1);
    }

    /**
     * 转数字 加千分符
     *
     * @param num
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String toNumber2(String num) {
        if (TextUtils.isEmpty(num)) return "0";
        return saveDecimals(num, 2);
    }

    /**
     * 转数字保留整数 加千分符
     *
     * @param num
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String toNumber3(String num) {
        BigDecimal bigDecimal = new BigDecimal(num);
        DecimalFormat decimalFormat = new DecimalFormat("###,###");
        String format = decimalFormat.format(bigDecimal);

        return format.equalsIgnoreCase(".") ? "0" : format;
    }

    /**
     * 转数字*美元 加千分符
     *
     * @param num
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String balanceToNumber(String num, String u) {
        if (TextUtils.isEmpty(num) || TextUtils.isEmpty(u)) return "0";
        BigDecimal bigDecimal = new BigDecimal(num);
        BigDecimal multiply = bigDecimal.multiply(new BigDecimal(u));
        DecimalFormat decimalFormat = new DecimalFormat("###,###.##");
        String format = decimalFormat.format(multiply);
        return format;
    }

    /**
     * 两数相乘 保留4位
     *
     * @param num
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String multiplication(String num, String u) {
        if (TextUtils.isEmpty(num) || TextUtils.isEmpty(u)) return "0";
        BigDecimal bigDecimal = new BigDecimal(num.replaceAll(",", ""));
        BigDecimal multiply = bigDecimal.multiply(new BigDecimal(u.replaceAll(",", "")));
        DecimalFormat decimalFormat = new DecimalFormat("######.####");
        String format = decimalFormat.format(multiply);
        return format;
    }


    /**
     * 两数相乘
     *
     * @param s1
     * @param s2
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String multipliy(String s1, String s2) {
        if (TextUtils.isEmpty(s1) || TextUtils.isEmpty(s2)) return "0";
        String num = unNumberFormat(s1);
        String u = unNumberFormat(s2);
        BigDecimal bigDecimal = new BigDecimal(num.replaceAll(",", ""));
        BigDecimal multiply = bigDecimal.multiply(new BigDecimal(u.replaceAll(",", "")));
        return multiply.toPlainString();
    }

    /**
     * 两数相乘
     *
     * @param s1
     * @param s2
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String multipliy(String s1, String s2, String s3) {
        if (TextUtils.isEmpty(s1) || TextUtils.isEmpty(s2) || TextUtils.isEmpty(s3)) return "0";
        String num1 = unNumberFormat(s1);
        String num2 = unNumberFormat(s2);
        String num3 = unNumberFormat(s3);
        BigDecimal bigDecimal = new BigDecimal(num1.replaceAll(",", ""));
        BigDecimal multiply = bigDecimal.multiply(new BigDecimal(num2.replaceAll(",", "")));
        BigDecimal multiply1 = multiply.multiply(new BigDecimal(num3.replaceAll(",", "")));
        return multiply1.toPlainString();
    }

    /**
     * 向上取整
     *
     * @param num
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String roundUp(String num) {
        BigDecimal bigDecimal = new BigDecimal(num).setScale(0, BigDecimal.ROUND_UP);
        return bigDecimal.toPlainString();
    }

    /**
     * 向上取整(小数位)
     *
     * @param num
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String roundUp(String num, int decimal) {
        BigDecimal bigDecimal = new BigDecimal(num).setScale(decimal, BigDecimal.ROUND_UP);
        return bigDecimal.toPlainString();
    }

    /**
     * 两数相除
     *
     * @param num
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String divide(String num, String u) {
        try {
            if (TextUtils.isEmpty(num) || TextUtils.isEmpty(u)) return "0";
            BigDecimal bigDecimal = new BigDecimal(num.replaceAll(",", ""));
            BigDecimal multiply = bigDecimal.divide(new BigDecimal(u.replaceAll(",", "")), 30, BigDecimal.ROUND_DOWN);
//            Log.e("TAG", "设置数值: " + multiply);
            return multiply.toPlainString();
        } catch (Exception e) {
            return "0";
        }

    }

    /**
     * 两数相除 保留4位
     *
     * @param num
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String division(String num, String u) {
        if (TextUtils.isEmpty(num) || TextUtils.isEmpty(u) || u.equalsIgnoreCase("0") || num.equalsIgnoreCase("0"))
            return "0";
        try {
            BigDecimal bigDecimal = new BigDecimal(num.replaceAll(",", ""));
            BigDecimal multiply = bigDecimal.divide(new BigDecimal(u.replaceAll(",", "")), 4, BigDecimal.ROUND_DOWN);
            DecimalFormat decimalFormat = new DecimalFormat("######.####");
            String format = decimalFormat.format(multiply);
            return format;
        } catch (Exception e) {
            return "0";
        }

    }


    /**
     * 两数相除 保留4位
     *
     * @param num
     * @return String
     */
    @SuppressWarnings("deprecation")
    public static String division(String num, String u, String Format) {
        if (TextUtils.isEmpty(num) || TextUtils.isEmpty(u) || u.equalsIgnoreCase("0") || num.equalsIgnoreCase("0"))
            return "0";
        try {
            BigDecimal bigDecimal = new BigDecimal(num.replaceAll(",", ""));
            BigDecimal multiply = bigDecimal.divide(new BigDecimal(u.replaceAll(",", "")), 4, BigDecimal.ROUND_DOWN);
            DecimalFormat decimalFormat = new DecimalFormat(Format);
            String format = decimalFormat.format(multiply);
            return format;
        } catch (Exception e) {
            return "0";
        }

    }


    /**
     * 可买
     *
     * @param num
     * @param u
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String canBuy(String num, String u, int decimal) {
        String format = "";
        try {
            BigDecimal bigDecimal = new BigDecimal(weiToEthWithDecimal(num, decimal));
            BigDecimal multiply = bigDecimal.divide(new BigDecimal(u), 4, BigDecimal.ROUND_DOWN);
            DecimalFormat decimalFormat = new DecimalFormat("###,###.####");
            format = decimalFormat.format(multiply);
            return numberFormat(format);
        } catch (Exception e) {

        }

        return format;
    }

    /**
     * 可卖
     *
     * @param num
     * @param u
     * @return
     */
    public static String canSell(String num, String u, int decimal) {
        BigDecimal bigDecimal = new BigDecimal(weiToEthWithDecimal(num.replaceAll(",", ""), decimal));
        BigDecimal multiply = bigDecimal.multiply(new BigDecimal(u.replaceAll(",", "")));
        DecimalFormat decimalFormat = new DecimalFormat("###,###.####");
        String format = decimalFormat.format(multiply);
        String s = numberFormat(format);
        return s;
    }

    /**
     * 计算滑点
     *
     * @param count 正常购买的数量
     * @param min   最小值百分比
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String getMin(String count, String min) {
        String subtract = "";
        try {
            BigDecimal countBig = new BigDecimal(count.replaceAll(",", ""));
            BigDecimal minBig = new BigDecimal(min.replaceAll(",", ""));
            BigDecimal divide = countBig.multiply(minBig).divide(new BigDecimal("100"));
            //减
            subtract = countBig.subtract(divide).toPlainString();

        } catch (Exception e) {

        }
        return subtract;
    }

    /**
     * 保留有效数字
     *
     * @param num   数字
     * @param count 保留位数
     * @return
     */
    public static String save4Valid(String num, @NotNull int count) {
        BigDecimal pi = new BigDecimal(num); // 将PI转换为BigDecimal类型
        MathContext mc = new MathContext(count, RoundingMode.DOWN); //设置精度为4。
        return pi.divide(BigDecimal.ONE, mc).stripTrailingZeros().toPlainString(); //BigDecimal.ONE为1，任何数除以1还是为它本身，即可保留mc个有效数字，并转为double类型
    }

    /**
     * 保留小数
     *
     * @param numb  数字
     * @param count 保留位数
     * @return 10000亿
     */
    public static String saveDecimals(String numb, @NotNull int count) {
        try {
            if (numb.isEmpty()) return "0";
            String num = numb.replaceAll(",", "");
            BigDecimal pi = new BigDecimal(num); // 将PI转换为BigDecimal类型
            String numStr = pi.stripTrailingZeros().toPlainString();
            if (pi.compareTo(BigDecimal.ZERO) == 0) return "0";
            String format = "###,###";
            switch (count) {
                case -1:
                    format = "######";
                    break;
                case 0:
                    format = "###,###";
                    break;
                case 1:
                    format = "###,##0.0";
                    break;
                case 2:
                    format = "###,##0.00";
                    break;
                case 3:
                    format = "###,##0.000";
                    break;
                case 4:
                    format = "###,##0.0000";
                    break;
                case 5:
                    format = "###,##0.00000";
                    break;
                case 6:
                    format = "###,##0.000000";
                    break;
                case 7:
                    format = "###,##0.0000000";
                    break;
                case 8:
                    format = "###,##0.00000000";
                    break;
                case 9:
                    format = "###,##0.000000000";
                    break;
                case 10:
                    format = "###,##0.0000000000";
                    break;


            }
            DecimalFormat decimalFormat = new DecimalFormat(format);
            return decimalFormat.format(new BigDecimal(numStr));
        } catch (Exception e) {
            return "0";
        }

    }

    /**
     * 保留小数
     *
     * @param numb     数字
     * @param count    保留位数
     * @param saveZero 保留0占位
     * @return 10000亿
     */
    public static String saveDecimals(String numb, @NotNull int count, boolean saveZero) {
        String num = numb.replaceAll(",", "");
        BigDecimal pi = new BigDecimal(num); // 将PI转换为BigDecimal类型
        String numStr = pi.stripTrailingZeros().toPlainString();
        if (pi.compareTo(BigDecimal.ZERO) == 0) return "0";
        String format = "###,###";
        switch (count) {
            case -1:
                format = "######";
                break;
            case 0:
                format = "###,###";
                break;
            case 1:
                format = "###,##0.0";
                break;
            case 2:
                format = "###,##0.00";
                break;
            case 3:
                format = "###,##0.000";
                break;
            case 4:
                format = "###,##0.0000";
                break;
            case 5:
                format = "###,##0.00000";
                break;
            case 6:
                format = "###,##0.000000";
                break;
            case 7:
                format = "###,##0.0000000";
                break;
            case 8:
                format = "###,##0.00000000";
                break;
            case 9:
                format = "###,##0.000000000";
                break;
            case 10:
                format = "###,##0.0000000000";
                break;


        }
        if (!saveZero) {
            format.replaceAll("0", "#");
        }
        DecimalFormat decimalFormat = new DecimalFormat(format);
        return decimalFormat.format(new BigDecimal(numStr));
    }

    /**
     * 数字格式化
     *
     * @param number
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String numberFormat(String number) {
        return numberFormatFunction(number);
    }

    /**
     * 数字格式化2  长数字简写
     *
     * @param numb
     * @return
     */
    public static String numberFormat2(String numb) {
        return priceFormatFunction(numb);
    }

    /**
     * 数字格式化2  长数字简写
     *
     * @param numb
     * @return
     */
    public static String numberFormat3(String numb) {
        //0.2435
//        numb="0.2435";
        if (TextUtils.isEmpty(numb)) return "0";
        String s1 = new BigDecimal(numb).toPlainString();
        String num = unNumberFormat(s1);
        if (num.startsWith("0.0")) {
            String[] split = num.split("\\.");
            if (split[1].length() > 6) {
                //小于1
                BigDecimal bigDecimal = new BigDecimal(split[1]);
                String str = bigDecimal.toPlainString();
                if (str.equals("0")) return "0";
                int i = split[1].length() - str.length();
                if (i <= 3) {
                    return save4Valid(num, 4);
                } else {
                    String s = save4Valid(str, 4);
                    return String.format("0.0(%s)%s", i, s.replaceAll("(0)+$", ""));
                }
            }
        } else {
            if (new BigDecimal(s1).compareTo(new BigDecimal("1000")) < 0) {
                return save4Valid(num, 4);
            } else {
                return saveDecimals(num, 0);
            }
        }
        return num;
    }

    /**
     * 数字格式化 0.0(10)1231 转 0.0000001231
     *
     * @param str
     * @return
     */
    public static String unNumberFormat(String str) {
        if (TextUtils.isEmpty(str)) return "0";
        str = str.replace("$", "").trim();
        if (TextUtils.isEmpty(str)) return "0";
        str = str.replaceAll(",", "");
        if (str.contains("(") && str.contains(")")) {
            int length = str.length();
            int index = str.indexOf("(");
            int index1 = str.indexOf(")");
            String num = str.substring(index1 + 1, length);
            if (index1 == length - 1) {
                return "0";
            }
            if (index + 1 == index1 || index == -1 || index1 == -1) {
                return "0." + num;
            }
            //获取0的数量
            String substring = str.substring(index + 1, index1);
            //获取数字
            return BigDecimalUtils.weiToEthWithDecimal(num, Integer.parseInt(substring) + num.length());
        } else {
            return str;
        }

    }


    /**
     * 钱包数字 加千分符 超过1w去掉小数  超过100w加m  超过10亿 加B
     *
     * @param num
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String walletCount(String num) {
        return numberFormatFunction(num);

    }

    /**
     * 两数相减 a-b
     *
     * @param a 正常购买的数量
     * @param b 最小值百分比
     * @return
     */
    public static String subtract(String a, String b) {
        String subtract = "";
        try {
            BigDecimal countBig = new BigDecimal(a);
            BigDecimal minBig = new BigDecimal(b);
            //减
            subtract = countBig.subtract(minBig).toPlainString();

        } catch (Exception e) {

        }
        return subtract;
    }

    /**
     * 两数相加 a+b
     *
     * @return
     */
    public static String add(String a, String b) {
        String add = "";
        try {
            BigDecimal countBig = new BigDecimal(a);
            BigDecimal minBig = new BigDecimal(b);
            //加
            add = countBig.add(minBig).toPlainString();

        } catch (Exception e) {

        }
        return add;
    }

    /**
     * 两数比较数值大小
     *
     * @param a
     * @param b
     * @return a是否大于等于b
     */
    public static boolean compare(String a, String b) {
        if (TextUtils.isEmpty(a)) a = "0";
        if (TextUtils.isEmpty(b)) b = "0";
        try {
            BigDecimal aBig = new BigDecimal(unNumberFormat(a));
            BigDecimal bBig = new BigDecimal(unNumberFormat(b));
            //减
            return aBig.compareTo(bBig) >= 0;
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * 根据价格规定数量
     *
     * @param count
     * @param price
     * @return
     */
    public static String formatCountByPrice(String count, String price) {
        String strCount = count.replaceAll(",", "");
        String strPrice = price.replaceAll(",", "");
        BigDecimal bigCount = new BigDecimal(strCount);
        BigDecimal bigPrice = new BigDecimal(strPrice);
        String countStr = "";
        if (bigPrice.compareTo(new BigDecimal("0.001")) < 0) {
            //价格小于0.001
            countStr = saveDecimals(bigCount.toPlainString(), 0);
        } else if (bigPrice.compareTo(new BigDecimal("0.01")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 1);
        } else if (bigPrice.compareTo(new BigDecimal("0.1")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 2);
        } else if (bigPrice.compareTo(new BigDecimal("1")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 3);
        } else if (bigPrice.compareTo(new BigDecimal("10")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 4);
        } else if (bigPrice.compareTo(new BigDecimal("100")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 5);
        } else if (bigPrice.compareTo(new BigDecimal("1000")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 6);
        } else if (bigPrice.compareTo(new BigDecimal("10000")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 7);
        } else if (bigPrice.compareTo(new BigDecimal("100000")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 8);
        } else if (bigPrice.compareTo(new BigDecimal("1000000")) < 0) {
            countStr = saveDecimals(bigCount.toPlainString(), 9);
        } else {
            countStr = bigCount.toPlainString();
        }
//        Log.e("MainActivity", "formatCountByPrice: " + countStr);
        if (countStr.contains("\\.")) {
            String[] split = countStr.split("\\.");
            String s = saveDecimals(split[0], 0);
            return s + split[1];
        } else {
            return countStr;
        }


    }

    /**
     * 方法一
     *
     * @param count
     * @param price
     * @return
     */
    public static String numberFormatFunction(String count, String price) {
        if (TextUtils.isEmpty(count)) return "0";
        if (TextUtils.isEmpty(price)) return "0";
        BigDecimal bigCount = new BigDecimal(count);
        BigDecimal bigPrice = new BigDecimal(price);
        if (bigPrice.compareTo(new BigDecimal(0)) == 0) {
            return numberFormatFunction(count);
        } else {
            if (bigCount.compareTo(new BigDecimal("1000000")) < 0) {
                //小于100万
                return formatCountByPrice(count, price);
            } else {
                //大于100万
                return numberFormatFunction(count);

            }
        }
    }

    /**
     * 中文方法二
     *
     * @param count
     * @return
     */
    public static String numberFormatFunction(String count) {
        if (TextUtils.isEmpty(count)) return "0";
        count = count.replaceAll(",", "");
        BigDecimal bigCount = new BigDecimal(count);
        if (bigCount.compareTo(new BigDecimal(0)) == 0) {
            return "0";
        } else {
            if (bigCount.compareTo(new BigDecimal("1000000")) < 0) {
                //小于100万
                return numberFormat3(bigCount.toPlainString());
            } else if (bigCount.compareTo(new BigDecimal("100000000")) < 0) {
                //小于1亿 展示1234.12万
                String division = division(bigCount.toPlainString(), "10000", "###.##");
                return division + "万";

            } else if (bigCount.compareTo(new BigDecimal("1000000000000")) < 0) {
                //小于1万亿 1234.23万亿
                String division = division(bigCount.toPlainString(), "100000000", "###.##");
                return division + "亿";

            } else if (bigCount.compareTo(new BigDecimal("100000000000000")) < 0) {
                //小于100万亿 1.23万亿
                String division = division(bigCount.toPlainString(), "1000000000000", "###.##");
                return division + "万亿";

            } else if (bigCount.compareTo(new BigDecimal("100000000000000")) >= 0) {
                //大于100万亿
                String division = division(bigCount.toPlainString(), "1000000000000", "###,###");
                return division + "万亿";

            }
        }
        return "0";
    }

    /**
     * 判断两个数字大小
     *
     * @param num1
     * @param num2
     * @return 0 相等   1 num1>num2  -1 num1<num2
     */
    public static int numberCompare(String num1, String num2) {
        String s1 = unNumberFormat(num1);
        String s2 = unNumberFormat(num2);
        return new BigDecimal(s1).compareTo(new BigDecimal(s2));

    }


    /**
     * 设置价格
     *
     * @param price
     * @return
     */
    public static String priceFormatFunction(String price) {
        if (TextUtils.isEmpty(price)) return "0";
        String s = price.replaceAll(",", "");
        String s1 = unNumberFormat(s);
        if (TextUtils.isEmpty(s1)) return "0";
        BigDecimal bigCount = new BigDecimal(s1);
        if (bigCount.compareTo(new BigDecimal("100")) < 0) {
            //price小于100
            return numberFormat3(s1);
        } else if (bigCount.compareTo(new BigDecimal("10000")) >= 0) {
            String division = division(bigCount.toPlainString(), "1", "###,###.#");
            return division;
        } else if (bigCount.compareTo(new BigDecimal("100")) >= 0) {
            String division = division(bigCount.toPlainString(), "1", "###,###.##");
            return division;
        }
        return "0";
    }


    public static String setCountFunction(String count, String price) {

        String appLanguage = LanguageUtils.getInstance().getLanguageName(AppApplication.getInstance());
//        Log.e("TAG", "initView: " + appLanguage);
        if (appLanguage.equals("简体中文")) {
            if (TextUtils.isEmpty(count)) return "0";
            String strCount = count.replaceAll(",", "");
            BigDecimal bigCount = new BigDecimal(strCount);
            if (TextUtils.isEmpty(price)) {
                return numberFormat3(bigCount.toPlainString());
            }
            String strPrice = price.replaceAll(",", "");
            BigDecimal bigPrice = new BigDecimal(strPrice);

            if (bigPrice.compareTo(new BigDecimal(0)) == 0) {
                return numberFormatFunction(bigCount.toPlainString());
            } else {
                if (bigPrice.compareTo(new BigDecimal("0.000000000001")) < 0) {
                    //小于100万亿 1.23万亿
                    String division = division(bigCount.toPlainString(), "1000000000000", "###,###");
                    return division + "万亿";

                } else if (bigPrice.compareTo(new BigDecimal("0.000000001")) < 0) {
                    //小于1万亿 1234.23万亿
                    String division = division(bigCount.toPlainString(), "100000000", "###,###");
                    return division + "亿";

                } else if (bigPrice.compareTo(new BigDecimal("0.00001")) < 0) {
                    //小于1亿 展示1234.12万
                    String division = division(bigCount.toPlainString(), "10000", "###,###");
                    return division + "万";
                } else if (bigPrice.compareTo(new BigDecimal("0.1")) < 0) {
                    //整数
                    return saveDecimals(bigCount.toPlainString(), 0);
                } else if (bigPrice.compareTo(new BigDecimal("1")) < 0) {
                    //1位
                    return saveDecimals(bigCount.toPlainString(), 1);
                } else if (bigPrice.compareTo(new BigDecimal("10")) < 0) {
                    //2位
                    return saveDecimals(bigCount.toPlainString(), 2);
                } else if (bigPrice.compareTo(new BigDecimal("100")) < 0) {
                    //3位
                    return saveDecimals(bigCount.toPlainString(), 3);
                } else if (bigPrice.compareTo(new BigDecimal("1000")) < 0) {
                    //4位
                    return saveDecimals(bigCount.toPlainString(), 4);
                } else if (bigPrice.compareTo(new BigDecimal("10000")) < 0) {
                    //5位
                    return saveDecimals(bigCount.toPlainString(), 5);
                } else if (bigPrice.compareTo(new BigDecimal("10000")) >= 0) {
                    //6位
                    return saveDecimals(bigCount.toPlainString(), 6);
                } else {
                    return saveDecimals(bigCount.toPlainString(), 0);
                }
            }
        } else {
            return numberFormatENFunction(count, price);
        }

    }

    /**
     * 英文方法二
     *
     * @param count
     * @return
     */
    public static String numberFormatENFunction(String count, String price) {
        if (TextUtils.isEmpty(count)) return "0";
        String strCount = count.replaceAll(",", "");
        BigDecimal bigCount = new BigDecimal(strCount);
        if (TextUtils.isEmpty(price)) {
            return numberFormat3(bigCount.toPlainString());
        }
        String strPrice = price.replaceAll(",", "");
        BigDecimal bigPrice = new BigDecimal(strPrice);

        if (bigPrice.compareTo(new BigDecimal(0)) == 0) {
            return numberFormat3(bigCount.toPlainString());
        } else {
            if (bigPrice.compareTo(new BigDecimal("0.00000000000001")) < 0) {
                //小于100万亿 1.23万亿
                String division = division(bigCount.toPlainString(), "1000000000000", "###,###");
                return division + "T";

            } else if (bigPrice.compareTo(new BigDecimal("0.00000000001")) < 0) {
                //小于1万亿 1234.23万亿
                String division = division(bigCount.toPlainString(), "1000000000", "###,###");
                return division + "B";

            } else if (bigPrice.compareTo(new BigDecimal("0.00000001")) < 0) {
                //小于1亿 展示1234.12万
                String division = division(bigCount.toPlainString(), "1000000", "###,###");
                return division + "M";
            } else if (bigPrice.compareTo(new BigDecimal("0.1")) < 0) {
                //整数
                return saveDecimals(bigCount.toPlainString(), 0);
            } else if (bigPrice.compareTo(new BigDecimal("1")) < 0) {
                //1位
                return saveDecimals(bigCount.toPlainString(), 1);
            } else if (bigPrice.compareTo(new BigDecimal("10")) < 0) {
                //2位
                return saveDecimals(bigCount.toPlainString(), 2);
            } else if (bigPrice.compareTo(new BigDecimal("100")) < 0) {
                //3位
                return saveDecimals(bigCount.toPlainString(), 3);
            } else if (bigPrice.compareTo(new BigDecimal("1000")) < 0) {
                //4位
                return saveDecimals(bigCount.toPlainString(), 4);
            } else if (bigPrice.compareTo(new BigDecimal("10000")) < 0) {
                //5位
                return saveDecimals(bigCount.toPlainString(), 5);
            } else if (bigPrice.compareTo(new BigDecimal("10000")) >= 0) {
                //6位
                return saveDecimals(bigCount.toPlainString(), 6);
            } else {
                return saveDecimals(bigCount.toPlainString(), 0);
            }
        }
    }

    public static SpannableString setUnitColor(String count, int color) {
        Log.e("TAG", "setUnitColor: " + count);
        if (TextUtils.isEmpty(count)) return new SpannableString("0");
        String strCount = count.replaceAll(",", "");
        if (strCount.contains("(") || strCount.contains(")")) {
            return new SpannableString(count);
        }
        try {
            double v = Double.parseDouble(strCount);
            return new SpannableString(count);
        } catch (Exception e) {

            SpannableString spannableString = new SpannableString(strCount);
            //字体颜色
            spannableString.setSpan(new ForegroundColorSpan(color),
                    strCount.length() - 1, strCount.length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spannableString;
        }
    }


    /**
     * 获取价格冲击百分比
     *
     * @return 百分比
     */
    public static String getPriceImpact(String inputCount, String poolCount) {
        String input = unNumberFormat(inputCount);
        String pool = unNumberFormat(poolCount);
        //价格冲击公式  价格影响百分比=购买数量/购买数量+该币种池子数量
        // p=ITC/ITC+PTC  p=百分比  ITC=购买token数量 PTC=池子Token数量
        return divide(input, add(pool, input));
    }

    /**
     * 获取买入/卖出 价格滑点  买入/卖出 税率+价格冲击
     *
     * @return
     */
    public static String getBuyImpact(String count, String poolCount, String tax, boolean isV3) {
        Log.e("测试", "---------------------------开始----------------------------");
        String add1 = isV3 ? tax : BigDecimalUtils.add(tax, "0.01");
        Log.e("测试", "购买数量token: " + count + "  池子数量" + poolCount);
        String person = BigDecimalUtils.getPriceImpact(count, poolCount);
        Log.e("测试", "购买数量占比: " + person);
        String multipliy = BigDecimalUtils.multipliy(add1 + "", "100");
        Log.e("测试", "百分比: " + multipliy);
        String s = BigDecimalUtils.roundUp(multipliy, 2);
        Log.e("测试", "购买数量: " + count);
        String add = BigDecimalUtils.add(BigDecimalUtils.multipliy(person, "100"), s);
        Log.e("测试", "测试滑点: " + add);
        Log.e("测试", "---------------------------结束----------------------------");
        return saveDecimals(add, 2);
    }
    //
//    private String getPrice(String goldCount,int goldDecimal,String tokenCount,String tokenDecimal){
//
//        return "";
//    }
}



