// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class NewWalletActivity_ViewBinding implements Unbinder {
  private NewWalletActivity target;

  private View view7f090308;

  private View view7f090315;

  private View view7f090334;

  @UiThread
  public NewWalletActivity_ViewBinding(NewWalletActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public NewWalletActivity_ViewBinding(final NewWalletActivity target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.tv_cancel, "field 'mTvCancel' and method 'onViewClicked'");
    target.mTvCancel = Utils.castView(view, R.id.tv_cancel, "field 'mTvCancel'", TextView.class);
    view7f090308 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIvLogo = Utils.findRequiredViewAsType(source, R.id.iv_logo, "field 'mIvLogo'", ImageView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv1, "field 'mTv1'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_create, "field 'mTvCreate' and method 'onViewClicked'");
    target.mTvCreate = Utils.castView(view, R.id.tv_create, "field 'mTvCreate'", TextView.class);
    view7f090315 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_input, "field 'mTvInput' and method 'onViewClicked'");
    target.mTvInput = Utils.castView(view, R.id.tv_input, "field 'mTvInput'", TextView.class);
    view7f090334 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    NewWalletActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvCancel = null;
    target.mCustomNavBar = null;
    target.mIvLogo = null;
    target.mTv1 = null;
    target.mTvCreate = null;
    target.mTvInput = null;

    view7f090308.setOnClickListener(null);
    view7f090308 = null;
    view7f090315.setOnClickListener(null);
    view7f090315 = null;
    view7f090334.setOnClickListener(null);
    view7f090334 = null;
  }
}
