// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ProposalItemView_ViewBinding implements Unbinder {
  private ProposalItemView target;

  private View view7f090310;

  private View view7f0901a6;

  private View view7f09019f;

  private View view7f09019c;

  private View view7f09018a;

  @UiThread
  public ProposalItemView_ViewBinding(ProposalItemView target) {
    this(target, target);
  }

  @UiThread
  public ProposalItemView_ViewBinding(final ProposalItemView target, View source) {
    this.target = target;

    View view;
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvAccountName = Utils.findRequiredViewAsType(source, R.id.tv_account_name, "field 'mTvAccountName'", FontTextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    target.mTvAccount = Utils.findRequiredViewAsType(source, R.id.tv_account, "field 'mTvAccount'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_content, "field 'mTvContent' and method 'onBindClick'");
    target.mTvContent = Utils.castView(view, R.id.tv_content, "field 'mTvContent'", FontTextView.class);
    view7f090310 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvShare = Utils.findRequiredViewAsType(source, R.id.tv_share, "field 'mTvShare'", TextView.class);
    target.mTvReview = Utils.findRequiredViewAsType(source, R.id.tv_review, "field 'mTvReview'", TextView.class);
    target.mTvPraise = Utils.findRequiredViewAsType(source, R.id.tv_praise, "field 'mTvPraise'", TextView.class);
    target.mTvCollect = Utils.findRequiredViewAsType(source, R.id.tv_Collect, "field 'mTvCollect'", TextView.class);
    target.mIvPost = Utils.findRequiredViewAsType(source, R.id.iv_post, "field 'mIvPost'", ImageView.class);
    target.mIvShare = Utils.findRequiredViewAsType(source, R.id.iv_share, "field 'mIvShare'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_share, "field 'mLlShare' and method 'onBindClick'");
    target.mLlShare = Utils.castView(view, R.id.ll_share, "field 'mLlShare'", RelativeLayout.class);
    view7f0901a6 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvReview = Utils.findRequiredViewAsType(source, R.id.iv_review, "field 'mIvReview'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_review, "field 'mLlReview' and method 'onBindClick'");
    target.mLlReview = Utils.castView(view, R.id.ll_review, "field 'mLlReview'", RelativeLayout.class);
    view7f09019f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvPraise = Utils.findRequiredViewAsType(source, R.id.iv_praise, "field 'mIvPraise'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_praise, "field 'mLlPraise' and method 'onBindClick'");
    target.mLlPraise = Utils.castView(view, R.id.ll_praise, "field 'mLlPraise'", RelativeLayout.class);
    view7f09019c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvCollect = Utils.findRequiredViewAsType(source, R.id.iv_Collect, "field 'mIvCollect'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_Collect, "field 'mLlCollect' and method 'onBindClick'");
    target.mLlCollect = Utils.castView(view, R.id.ll_Collect, "field 'mLlCollect'", RelativeLayout.class);
    view7f09018a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    ProposalItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvHeard = null;
    target.mTvAccountName = null;
    target.mTvTime = null;
    target.mTvAccount = null;
    target.mTvContent = null;
    target.mTvShare = null;
    target.mTvReview = null;
    target.mTvPraise = null;
    target.mTvCollect = null;
    target.mIvPost = null;
    target.mIvShare = null;
    target.mLlShare = null;
    target.mIvReview = null;
    target.mLlReview = null;
    target.mIvPraise = null;
    target.mLlPraise = null;
    target.mIvCollect = null;
    target.mLlCollect = null;

    view7f090310.setOnClickListener(null);
    view7f090310 = null;
    view7f0901a6.setOnClickListener(null);
    view7f0901a6 = null;
    view7f09019f.setOnClickListener(null);
    view7f09019f = null;
    view7f09019c.setOnClickListener(null);
    view7f09019c = null;
    view7f09018a.setOnClickListener(null);
    view7f09018a = null;
  }
}
