package com.Tlock.io.widget.pop;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.utils.ToastUtil;
import com.lxj.xpopup.core.BottomPopupView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * @ClassName PopWhaleList
 * <AUTHOR>
 * @Data 2023/11/6 19:06
 * @Desc
 */

public class PopPostShare extends BottomPopupView {



    public PopPostShare(@NonNull Context context) {
        super(context);
    }



    @Override
    protected int getImplLayoutId() {
        return R.layout.pop_post_share;
    }

    @Override
    protected void onCreate() {
        ButterKnife.bind(this);
        super.onCreate();
    }


    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    private CallBack callBack;

    @OnClick({R.id.tv_copy_link, R.id.tv_share_tg, R.id.tv_share_x})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.tv_copy_link:
                ToastUtil.toastShortCenter(AppApplication.getInstance(), "Coming soon");
                break;
            case R.id.tv_share_tg:
                ToastUtil.toastShortCenter(AppApplication.getInstance(), "Coming soon");
                break;
            case R.id.tv_share_x:
                ToastUtil.toastShortCenter(AppApplication.getInstance(), "Coming soon");
                break;
        }
    }


    public interface CallBack {

        void follow(String handle);

        void report(String id);

    }
}
