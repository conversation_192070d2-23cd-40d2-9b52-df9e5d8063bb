package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.QRCodeUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;

import butterknife.BindView;

public class ReceiveActivity extends BaseActivity {
    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_name)
    FontTextView mTvName;
    @BindView(R.id.tv_address)
    TextView mTvAddress;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.tv_tip)
    TextView mTvTip;
    @BindView(R.id.iv_qr_code)
    ImageView mIvQrCode;
    private ETHWallet current;
    private ProfileProto.Profile authInfo;

    /**
     * @param context
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, ReceiveActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_receive;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        current = WalletDaoUtils.getCurrent();
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        Bitmap qrCode = QRCodeUtils.createQRCode(current.getAddress(), 500, 500);
        mIvQrCode.setImageBitmap(qrCode);
        mTvAddress.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                CopyUtils.copyToClipboard(current.getAddress());
            }
        });
    }

    @Override
    protected void loadData() {
        getAuthInfo();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    authInfo = CosmosUtils.getAuthInfo(current.getAddress());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mTvAddress.setText(current.getAddress());
                            mCustomNavBar.setMidTitle(authInfo.getNickname());
                            if (TextUtils.isEmpty(authInfo.getAvatar())) {
                                TextAvatarDrawable a = new TextAvatarDrawable(authInfo.getUserHandle().substring(0, 1));
                                mIvHeard.setImageDrawable(a);
                            } else {
                                if (authInfo.getAvatar().startsWith("http")) {
                                    Glide.with(getActivity()).load(authInfo.getAvatar()).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(authInfo.getAvatar()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authInfo.getAvatar());
                                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                }
                            }
                        }
                    });
                }
            });
        }
    }
}