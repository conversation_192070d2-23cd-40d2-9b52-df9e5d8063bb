<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:id="@+id/root"
   >
    <com.Tlock.io.widget.RoundedImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/iv_image"
        />

    <ImageView
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:id="@+id/iv_delete"
        android:visibility="gone"
        android:src="@mipmap/icon_delete_img"
        android:layout_alignRight="@id/iv_image"
        android:padding="@dimen/dp_5"
        />

    <ImageView
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:src="@drawable/gif_loading"
        android:visibility="gone"
        android:layout_centerInParent="true"/>

</RelativeLayout>
