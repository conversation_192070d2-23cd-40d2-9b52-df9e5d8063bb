// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopPayer_ViewBinding implements Unbinder {
  private PopPayer target;

  private View view7f0902f6;

  private View view7f09037f;

  @UiThread
  public PopPayer_ViewBinding(PopPayer target) {
    this(target, target);
  }

  @UiThread
  public PopPayer_ViewBinding(final PopPayer target, View source) {
    this.target = target;

    View view;
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTvCaptureTip = Utils.findRequiredViewAsType(source, R.id.tv_capture_tip, "field 'mTvCaptureTip'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_Cancel, "field 'mTvCancel' and method 'onBindClick'");
    target.mTvCancel = Utils.castView(view, R.id.tv_Cancel, "field 'mTvCancel'", TextView.class);
    view7f0902f6 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_wallet, "field 'mTvWallet' and method 'onBindClick'");
    target.mTvWallet = Utils.castView(view, R.id.tv_wallet, "field 'mTvWallet'", TextView.class);
    view7f09037f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopPayer target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mTvCaptureTip = null;
    target.mTvCancel = null;
    target.mTvWallet = null;

    view7f0902f6.setOnClickListener(null);
    view7f0902f6 = null;
    view7f09037f.setOnClickListener(null);
    view7f09037f = null;
  }
}
