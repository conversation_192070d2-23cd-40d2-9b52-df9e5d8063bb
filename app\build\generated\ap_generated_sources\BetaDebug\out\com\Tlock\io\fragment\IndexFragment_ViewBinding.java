// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.fragment;

import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.itemBean.cosmos.TopicTabView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class IndexFragment_ViewBinding implements Unbinder {
  private IndexFragment target;

  @UiThread
  public IndexFragment_ViewBinding(IndexFragment target, View source) {
    this.target = target;

    target.mEdSearch = Utils.findRequiredViewAsType(source, R.id.ed_search, "field 'mEdSearch'", EditText.class);
    target.mVpSort = Utils.findRequiredViewAsType(source, R.id.vp_sort, "field 'mVpSort'", ViewPager2.class);
    target.mMain = Utils.findRequiredViewAsType(source, R.id.main, "field 'mMain'", RelativeLayout.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mTab1 = Utils.findRequiredViewAsType(source, R.id.tab1, "field 'mTab1'", TopicTabView.class);
    target.mTab2 = Utils.findRequiredViewAsType(source, R.id.tab2, "field 'mTab2'", TopicTabView.class);
    target.mTab3 = Utils.findRequiredViewAsType(source, R.id.tab3, "field 'mTab3'", TopicTabView.class);
    target.mTab4 = Utils.findRequiredViewAsType(source, R.id.tab4, "field 'mTab4'", TopicTabView.class);
    target.mTab5 = Utils.findRequiredViewAsType(source, R.id.tab5, "field 'mTab5'", TopicTabView.class);
    target.mTab6 = Utils.findRequiredViewAsType(source, R.id.tab6, "field 'mTab6'", TopicTabView.class);
    target.mTab7 = Utils.findRequiredViewAsType(source, R.id.tab7, "field 'mTab7'", TopicTabView.class);
    target.mTab8 = Utils.findRequiredViewAsType(source, R.id.tab8, "field 'mTab8'", TopicTabView.class);
    target.mLlTab = Utils.findRequiredViewAsType(source, R.id.ll_tab, "field 'mLlTab'", LinearLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    IndexFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mEdSearch = null;
    target.mVpSort = null;
    target.mMain = null;
    target.mLine1 = null;
    target.mTab1 = null;
    target.mTab2 = null;
    target.mTab3 = null;
    target.mTab4 = null;
    target.mTab5 = null;
    target.mTab6 = null;
    target.mTab7 = null;
    target.mTab8 = null;
    target.mLlTab = null;
  }
}
