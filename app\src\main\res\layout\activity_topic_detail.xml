<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:paddingTop="@dimen/dp_5"
    tools:context=".activity.cosmos.PostQuoteActivity">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:padding="@dimen/dp_10"
        android:src="@mipmap/icon_back_black" />

    <TextView
        android:id="@+id/tv_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_7"
        android:layout_marginRight="@dimen/dp_16"
        android:layout_toLeftOf="@id/tv_follow"
        android:visibility="gone"
        android:background="@drawable/btn_black_60"
        android:gravity="center"
        android:paddingLeft="@dimen/dp_18"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_18"
        android:paddingBottom="@dimen/dp_7"
        android:text="Edit"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14" />

    <TextView
        android:id="@+id/tv_follow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_7"
        android:layout_marginRight="@dimen/dp_16"
        android:background="@drawable/btn_black_60"
        android:gravity="center"
        android:paddingLeft="@dimen/dp_18"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_18"
        android:paddingBottom="@dimen/dp_7"
        android:text="Join"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14" />

    <ImageView
        android:id="@+id/iv_topic_avatar"
        android:layout_width="@dimen/dp_54"
        android:layout_height="@dimen/dp_54"
        android:layout_below="@id/iv_back"
        android:layout_alignTop="@id/ll_title"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/dp_16"
        android:adjustViewBounds="true"
        android:src="@mipmap/topic_heard_default"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_back"
        android:layout_marginTop="16dp"
        android:layout_toRightOf="@id/iv_topic_avatar"
        android:gravity="center">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:minHeight="@dimen/dp_54">

            <TextView
                android:id="@+id/tv_topic_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginRight="@dimen/dp_20"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_topic_hotness"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_topic_name"
                android:layout_marginLeft="@dimen/dp_15"
                android:textColor="@color/cosmos_default"
                android:textSize="@dimen/sp_12" />

            <TextView
                android:id="@+id/tv_topic_follow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_topic_name"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_toRightOf="@id/tv_topic_hotness"
                android:textColor="@color/cosmos_default"
                android:textSize="@dimen/sp_12" />

        </RelativeLayout>

    </LinearLayout>


    <View
        android:id="@+id/line1"
        style="@style/gray_horizontal_line_view"
        android:layout_height="@dimen/dp_1"
        android:layout_below="@id/ll_title"
        android:layout_marginTop="@dimen/dp_10" />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/ll_comment"
        android:layout_below="@id/line1"
        android:layout_marginBottom="@dimen/dp_10">

        <com.scwang.smartrefresh.layout.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:srlTextSizeTitle="@dimen/sp_14" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.Tlock.io.custom.LoadErrorView
                android:id="@+id/load_error"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />
        </RelativeLayout>

        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:srlTextSizeTitle="@dimen/sp_14" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <View
        android:id="@id/line2"
        style="@style/gray_horizontal_line_view"
        android:layout_above="@id/ll_comment" />

    <RelativeLayout
        android:id="@+id/ll_comment"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/dp_10"
        android:layout_marginRight="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_10"
        android:background="@drawable/bg_deep_gray_13"
        android:clickable="true"
        android:focusable="true"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_user_heard"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dp_7" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_30"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dp_13"
            android:layout_marginRight="@dimen/dp_15"
            android:layout_toRightOf="@id/iv_user_heard"
            android:gravity="center_vertical"
            android:text="Join the discussion"
            android:textColor="@color/hint_color"
            android:textSize="@dimen/sp_14" />

    </RelativeLayout>

</RelativeLayout>