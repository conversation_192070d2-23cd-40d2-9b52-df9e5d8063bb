package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.fragment.cosmos.MReportListFragment;
import com.Tlock.io.fragment.cosmos.MTopicListFragment;
import com.Tlock.io.fragment.cosmos.RecommendedFragment;
import com.cy.tablayoutniubility.FragPageAdapterVp2;
import com.cy.tablayoutniubility.TabAdapter;
import com.cy.tablayoutniubility.TabLayoutScroll;
import com.cy.tablayoutniubility.TabMediatorVp2;
import com.cy.tablayoutniubility.TabViewHolder;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;


public class MTaskListActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.tablayout)
    TabLayoutScroll mTablayout;
    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;
    @BindView(R.id.main)
    RelativeLayout mMain;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context
    ) {
        Intent intent = new Intent(context, MTaskListActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_m_task_list;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {

        FragPageAdapterVp2<String> fragmentPageAdapter = new FragPageAdapterVp2<String>(this) {

            @Override
            public Fragment createFragment(String bean, int position) {
                if (position == 0){
                    return new MReportListFragment(position);
                }else{
                    return new MTopicListFragment(position);
                }
            }

            @Override
            public void bindDataToTab(TabViewHolder holder, int position, String bean, boolean isSelected) {
                TextView textView = holder.getView(R.id.tv);
                if (isSelected) {
                    textView.setTextColor(getResources().getColor(R.color.bg_btn_blue));
                } else {
                    textView.setTextColor(getResources().getColor(R.color.bg_yellow_color));
                }
                textView.setText(bean);
            }

            @Override
            public int getTabLayoutID(int position, String bean) {
                return R.layout.item_home_tab;
            }
        };
        TabAdapter<String> tabAdapter = new TabMediatorVp2<String>(mTablayout, viewPager2).setAdapter(fragmentPageAdapter);

        List<String> list = new ArrayList<>();
        list.add("Report");
        list.add("Topic Grouping");
        fragmentPageAdapter.add(list);
        tabAdapter.add(list);


    }


    @Override
    protected void loadData() {

    }

    @OnClick({R.id.iv_back})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
        }
    }
}