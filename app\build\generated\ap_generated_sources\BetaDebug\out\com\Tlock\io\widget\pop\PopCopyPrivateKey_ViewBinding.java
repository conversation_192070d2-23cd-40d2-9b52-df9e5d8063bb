// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopCopyPrivateKey_ViewBinding implements Unbinder {
  private PopCopyPrivateKey target;

  private View view7f090194;

  private View view7f0901a2;

  private View view7f0901a6;

  private View view7f09030c;

  @UiThread
  public PopCopyPrivateKey_ViewBinding(PopCopyPrivateKey target) {
    this(target, target);
  }

  @UiThread
  public PopCopyPrivateKey_ViewBinding(final PopCopyPrivateKey target, View source) {
    this.target = target;

    View view;
    target.mTvListTitle = Utils.findRequiredViewAsType(source, R.id.tv_list_title, "field 'mTvListTitle'", TextView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv_1, "field 'mTv1'", TextView.class);
    target.mTvFirst = Utils.findRequiredViewAsType(source, R.id.tv_first, "field 'mTvFirst'", TextView.class);
    target.mIvCopy1 = Utils.findRequiredViewAsType(source, R.id.iv_copy_1, "field 'mIvCopy1'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_first, "field 'mLlFirst' and method 'onBindClick'");
    target.mLlFirst = Utils.castView(view, R.id.ll_first, "field 'mLlFirst'", RelativeLayout.class);
    view7f090194 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv_2, "field 'mTv2'", TextView.class);
    target.mTvSecond = Utils.findRequiredViewAsType(source, R.id.tv_second, "field 'mTvSecond'", TextView.class);
    target.mIvCopy2 = Utils.findRequiredViewAsType(source, R.id.iv_copy_2, "field 'mIvCopy2'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_second, "field 'mLlSecond' and method 'onBindClick'");
    target.mLlSecond = Utils.castView(view, R.id.ll_second, "field 'mLlSecond'", RelativeLayout.class);
    view7f0901a2 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTv3 = Utils.findRequiredViewAsType(source, R.id.tv_3, "field 'mTv3'", TextView.class);
    target.mTvThird = Utils.findRequiredViewAsType(source, R.id.tv_third, "field 'mTvThird'", TextView.class);
    target.mIvCopy3 = Utils.findRequiredViewAsType(source, R.id.iv_copy_3, "field 'mIvCopy3'", ImageView.class);
    view = Utils.findRequiredView(source, R.id.ll_third, "field 'mLlThird' and method 'onBindClick'");
    target.mLlThird = Utils.castView(view, R.id.ll_third, "field 'mLlThird'", RelativeLayout.class);
    view7f0901a6 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onBindClick'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f09030c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopCopyPrivateKey target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvListTitle = null;
    target.mTv1 = null;
    target.mTvFirst = null;
    target.mIvCopy1 = null;
    target.mLlFirst = null;
    target.mTv2 = null;
    target.mTvSecond = null;
    target.mIvCopy2 = null;
    target.mLlSecond = null;
    target.mTv3 = null;
    target.mTvThird = null;
    target.mIvCopy3 = null;
    target.mLlThird = null;
    target.mTvConfirm = null;

    view7f090194.setOnClickListener(null);
    view7f090194 = null;
    view7f0901a2.setOnClickListener(null);
    view7f0901a2 = null;
    view7f0901a6.setOnClickListener(null);
    view7f0901a6 = null;
    view7f09030c.setOnClickListener(null);
    view7f09030c = null;
  }
}
