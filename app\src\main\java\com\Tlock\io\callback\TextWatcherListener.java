package com.Tlock.io.callback;

import android.text.Editable;
import android.text.TextWatcher;


/**
 *  本类的主要功能是 :  文本框输入监听
 *
 */
public class TextWatcherListener implements TextWatcher {
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    /**
     * 附件备注使用
     * @param content
     * @param position
     */
    public void afterTextChanged(String content, int position) {

    }
}
