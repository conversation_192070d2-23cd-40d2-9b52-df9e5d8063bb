package com.Tlock.io.utils;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Environment;
import android.view.View;


import com.Tlock.io.R;

import java.io.File;
import java.io.FileOutputStream;

import static android.view.View.DRAWING_CACHE_QUALITY_AUTO;
import static android.view.View.DRAWING_CACHE_QUALITY_HIGH;
import static android.view.View.DRAWING_CACHE_QUALITY_LOW;

/**
 * @ClassName ScreenshotUtil
 * <AUTHOR>
 * @Data 2021/8/26 9:13
 * @Desc
 */

public class ScreenshotUtil {
    /**
     * 截图
     *
     * @param activity
     */
    public static Bitmap shotScreen(Activity activity) {
        //获取屏幕截图
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap first = getBitmap(activity);

//        Bitmap bitmap = addBitmap(first, ((BitmapDrawable) activity.getResources().getDrawable(R.mipmap.qr_code)).getBitmap());
        return first;
    }

    /**
     * 拼接bitmap
     *
     * @param first  截图
     * @param second 二维码
     * @return
     */
    public static Bitmap addBitmap(Bitmap first, Bitmap second) {
        int width1 = first.getWidth();
        int width2 = second.getWidth();
        int width = Math.max(first.getWidth(), second.getWidth());
        int height3 = first.getHeight();
        int height4 = second.getHeight();
        int height =  height3+ height4;
        float w1 = (float) (Math.round(first.getWidth() / second.getWidth()) / 100.0);
        float w2 = (float) (Math.round(second.getWidth() / first.getWidth()) / 100.0);
        float bigger = width1 > width2 ? w1 / w2 : w2 / w1;
        Matrix matrix = new Matrix();
        matrix.postScale(bigger, height);
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.drawBitmap(first, 0, 0, null);
        int height2 = first.getHeight();
        canvas.drawBitmap(second, 0, height2, null);
        return bitmap;
    }

    /**
     * 拼接bitmap
     *
     * @param model
     * @param qrCode 二维码
     * @return
     */
    public static Bitmap bitmapToBitmap(Bitmap model, Bitmap qrCode) {
        int width1 = model.getWidth();
        int height3 = model.getHeight();
        Bitmap bitmap = Bitmap.createBitmap(width1, height3, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.drawBitmap(model, 0, 0, null);
        canvas.drawBitmap(qrCode, width1/2-113, height3-460, null);
        return bitmap;

    }

    /**
     * 保存截图
     *
     * @param activity
     * @param bitmap
     */
    public static File saveBitmap(Activity activity, Bitmap bitmap) {
        try {
            String storePath = Environment.getExternalStorageDirectory()+"/DCIM/pic";
            File appDir = new File(storePath);
            if (!appDir.exists()) {
                appDir.mkdirs();
            }
            String fileName = System.currentTimeMillis() + ".jpg";
            File file = new File(appDir, fileName);
            FileOutputStream fos = new FileOutputStream(file);
            //通过io流的方式来压缩保存图片
            boolean isSuccess = bitmap.compress(Bitmap.CompressFormat.PNG, 80, fos);
            fos.flush();
            fos.close();

            //保存图片后发送广播通知更新数据库
            Uri uri = Uri.fromFile(file);
            activity.getApplicationContext().sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri));

            if (isSuccess) {
                ToastUtil.toastLongCenter(activity, "保存成功");
            } else {
                ToastUtil.toastLongCenter(activity, "保存失败");
            }
            if (file!=null)
                return file;
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return null;
    }

    public static Bitmap getBitmap(Activity activity) {
        Bitmap bitmap = null;
        View view = activity.getWindow().getDecorView();
        int width = view.getRight() - view.getLeft();
        int height = view.getBottom() - view.getTop();
        final boolean opaque = view.getDrawingCacheBackgroundColor() != 0 || view.isOpaque();
        Bitmap.Config quality;
        if (!opaque) {
            switch (view.getDrawingCacheQuality()) {
                case DRAWING_CACHE_QUALITY_AUTO:
                case DRAWING_CACHE_QUALITY_LOW:
                case DRAWING_CACHE_QUALITY_HIGH:
                default:
                    quality = Bitmap.Config.ARGB_8888;
                    break;
            }
        } else {
            quality = Bitmap.Config.ARGB_8888;
        }
//        if (opaque) bitmap.setHasAlpha(false);
        bitmap = Bitmap.createBitmap(view.getResources().getDisplayMetrics(),
                width, height, quality);
        bitmap.setDensity(view.getResources().getDisplayMetrics().densityDpi);
        boolean clear = view.getDrawingCacheBackgroundColor() != 0;
        Canvas canvas = new Canvas(bitmap);
        if (clear) {
            bitmap.eraseColor(view.getDrawingCacheBackgroundColor());
        }
        view.computeScroll();
        final int restoreCount = canvas.save();
        canvas.translate(-view.getScrollX(), -view.getScrollY());
        view.draw(canvas);
        canvas.restoreToCount(restoreCount);
        canvas.setBitmap(null);
        Bitmap resultingBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);

        Canvas comboCanvas = new Canvas(resultingBitmap);

        comboCanvas.drawBitmap(bitmap, 0, 0, null);
        Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, width, height, true);
        return scaledBitmap;
    }
}
