// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class AllowanceActivity_ViewBinding implements Unbinder {
  private AllowanceActivity target;

  @UiThread
  public AllowanceActivity_ViewBinding(AllowanceActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public AllowanceActivity_ViewBinding(AllowanceActivity target, View source) {
    this.target = target;

    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mTvCurrent = Utils.findRequiredViewAsType(source, R.id.tv_current, "field 'mTvCurrent'", TextView.class);
    target.mTvTotal = Utils.findRequiredViewAsType(source, R.id.tv_total, "field 'mTvTotal'", TextView.class);
    target.mTvResetTime = Utils.findRequiredViewAsType(source, R.id.tv_reset_time, "field 'mTvResetTime'", TextView.class);
    target.mTvEndTime = Utils.findRequiredViewAsType(source, R.id.tv_end_time, "field 'mTvEndTime'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    AllowanceActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mTvCurrent = null;
    target.mTvTotal = null;
    target.mTvResetTime = null;
    target.mTvEndTime = null;
  }
}
