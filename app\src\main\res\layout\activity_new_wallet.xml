<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".activity.wallet.NewWalletActivity">

    <com.Tlock.io.widget.CustomNavBar
        android:id="@+id/custom_nav_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:icon_left="@mipmap/icon_back_black" />

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:layout_below="@id/custom_nav_bar"
        android:layout_marginTop="@dimen/dp_20"
        android:src="@mipmap/img_wallet" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_logo"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="@dimen/dp_45"
        android:layout_marginRight="@dimen/dp_45"
        android:gravity="center"
        android:text="Welcome to Tlock!"
        app:fontFamily="@font/font_family"
        app:fontType="name"
        android:textStyle="bold"
        android:textColor="@color/cosmos_black"
        android:textSize="24sp" />


    <TextView
        android:id="@+id/tv_create"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/tv_input"
        android:layout_gravity="center_horizontal"
        android:layout_marginRight="@dimen/dp_22"
        android:layout_marginLeft="@dimen/dp_22"
        android:background="@drawable/btn_blue_6"
        android:gravity="center"
        android:textStyle="bold"
        android:padding="@dimen/dp_15"
        android:text="Create wallet"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <TextView
        android:id="@+id/tv_input"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:layout_marginRight="@dimen/dp_22"
        android:layout_marginLeft="@dimen/dp_22"
        android:layout_above="@id/tv_cancel"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/btn_black_6"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="@dimen/dp_15"
        android:text="Import wallet"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16" />

    <TextView
        android:id="@+id/tv_cancel"
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_60"
        android:text="@string/Cancel"
        android:textColor="@color/cosmos_default" />

</RelativeLayout>