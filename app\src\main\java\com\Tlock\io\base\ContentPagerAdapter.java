package com.Tlock.io.base;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.List;

public class ContentPagerAdapter extends FragmentStateAdapter {
    private List<Fragment> datas;

    public ContentPagerAdapter(@NonNull FragmentActivity fragmentActivity, List<Fragment> datas) {
        super(fragmentActivity);
        this.datas = datas;
    }

    public ContentPagerAdapter(@NonNull Fragment fragmentActivity, List<Fragment> datas) {
        super(fragmentActivity);
        this.datas = datas;
    }

    public ContentPagerAdapter(@NonNull FragmentManager fragmentManager,
                               @NonNull Lifecycle lifecycle, List<Fragment> datas) {
        super(fragmentManager, lifecycle);
        this.datas = datas;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        return datas.get(position);
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    public void setList(List<Fragment> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

} 