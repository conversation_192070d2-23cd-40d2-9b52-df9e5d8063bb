// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopTip_ViewBinding implements Unbinder {
  private PopTip target;

  @UiThread
  public PopTip_ViewBinding(PopTip target) {
    this(target, target);
  }

  @UiThread
  public PopTip_ViewBinding(PopTip target, View source) {
    this.target = target;

    target.mTvContent = Utils.findRequiredViewAsType(source, R.id.tv_content, "field 'mTvContent'", TextView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mTvMsg = Utils.findRequiredViewAsType(source, R.id.tv_msg, "field 'mTvMsg'", TextView.class);
    target.mTvConfirm = Utils.findRequiredViewAsType(source, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mIvTitle = Utils.findRequiredViewAsType(source, R.id.iv_title, "field 'mIvTitle'", ImageView.class);
    target.mLlTitle = Utils.findRequiredViewAsType(source, R.id.ll_title, "field 'mLlTitle'", LinearLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopTip target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvContent = null;
    target.mTvTitle = null;
    target.mTvMsg = null;
    target.mTvConfirm = null;
    target.mLine1 = null;
    target.mIvTitle = null;
    target.mLlTitle = null;
  }
}
