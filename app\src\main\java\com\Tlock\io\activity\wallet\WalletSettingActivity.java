package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.Tlock.io.widget.pop.PopChangePwd;
import com.Tlock.io.widget.wallet.PopCheckPwd;
import com.Tlock.io.widget.wallet.PopWalletEditName;
import com.Tlock.io.widget.wallet.PopWalletTip;
import com.lxj.xpopup.XPopup;

import butterknife.BindView;
import butterknife.OnClick;

public class WalletSettingActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.tv_name)
    TextView mTvName;

    @BindView(R.id.rl_info)
    RelativeLayout mRlInfo;
    @BindView(R.id.rl_out_private)
    RelativeLayout mRlOutPrivate;
    @BindView(R.id.rl_out_world)
    RelativeLayout mRlOutWorld;
    @BindView(R.id.tv_delete)
    TextView mTvDelete;
    @BindView(R.id.iv_notice2)
    ImageView mIvNotice2;
    @BindView(R.id.iv2)
    ImageView mIv2;
    @BindView(R.id.iv_notice1)
    ImageView mIvNotice1;
    @BindView(R.id.iv1)
    ImageView mIv1;
    @BindView(R.id.tv_change_pwd)
    TextView mTvChangePwd;
    @BindView(R.id.iv_dot)
    ImageView mIvDot;
    @BindView(R.id.rl_change_pwd)
    RelativeLayout mRlChangePwd;
    @BindView(R.id.tv_pwd_tip)
    TextView mTvPwdTip;
    private ETHWallet current;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, WalletSettingActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_wallet_setting;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        current = WalletDaoUtils.getCurrent();
        if (current == null) return;
        if (current.getType() == 1) {
            mRlOutWorld.setVisibility(View.GONE);
        } else if (current.getType() == 2) {
            mRlOutWorld.setVisibility(View.VISIBLE);
        } else if (current.getType() == 3) {
            mRlOutWorld.setVisibility(View.GONE);
            mRlOutPrivate.setVisibility(View.GONE);
            mRlChangePwd.setVisibility(View.GONE);
            mTvPwdTip.setVisibility(View.GONE);
        } else if (current.getType() == 4) {
            mIvNotice1.setVisibility(View.VISIBLE);
            mIvNotice2.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(current.getPassword())) {
                mTvPwdTip.setVisibility(View.VISIBLE);
                mIvDot.setVisibility(View.VISIBLE);
            }
        }
        mRlInfo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showWalletEditName();
            }
        });

    }

    @Override
    protected void loadData() {

    }

    @OnClick({R.id.rl_info, R.id.rl_out_private, R.id.rl_change_pwd, R.id.rl_out_world, R.id.tv_delete})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.rl_info:
                //详情
                checkPwd(1);
                break;
            case R.id.rl_out_private:
                if (WalletDaoUtils.getCurrent().getType() == 3) {
                    showToast("当前选中为观察钱包");
                    return;
                }
                //导出私钥
                checkPwd(4);
                break;
            case R.id.rl_out_world:
                if (WalletDaoUtils.getCurrent().getType() == 3) {
                    showToast("当前选中为观察钱包");
                    return;
                }
                //导出助记词
                checkPwd(5);
                break;
            case R.id.rl_change_pwd:
                //修改密码
                PopChangePwd popChangePwd = new PopChangePwd(getActivity(), current);
                popChangePwd.setCallBack(new PopChangePwd.CallBack() {
                    @Override
                    public void success() {
                        mTvPwdTip.setVisibility(View.GONE);
                        mIvDot.setVisibility(View.GONE);
                    }
                });
                new XPopup.Builder(getActivity()).asCustom(popChangePwd).show();
                break;
            case R.id.tv_delete:
                //删除钱包
                if (WalletDaoUtils.loadAllUserWallet().size() == 1 && current.getType() != 3) {
                    showToast("钱包至少保留一个用户钱包");
                    break;
                }
                if (TextUtils.isEmpty(current.getPassword())) {
                    showWalletDelete();
                } else {
                    checkPwd(3);
                }
                break;
        }
    }

    /**
     * 弹出确认删除
     */
    private void showWalletDelete() {
        PopWalletTip popWalletTip = new PopWalletTip(getActivity(), current);
        popWalletTip.setCallBack(new PopWalletTip.CallBack() {
            @Override
            public void toBackup(ETHWallet wallet) {
                BackupActivity.start(getActivity(), JsonUtils.objectToJson(wallet));
            }

            @Override
            public void delete() {
                finish();
            }
        });
        new XPopup.Builder(getActivity()).asCustom(popWalletTip).show();
    }


    private void checkPwd(int type) {
        PopCheckPwd popCheckPwd = new PopCheckPwd(getActivity(), current);
        popCheckPwd.setCallBack(new PopCheckPwd.CallBack() {
            @Override
            public void succeeded() {
                switch (type) {
                    case 1:
                        break;
                    case 2:
                        BackupActivity.start(getActivity(), JsonUtils.objectToJson(current));
                        break;
                    case 3:
                        showWalletDelete();
                        break;
                    case 4:
                        if (WalletDaoUtils.getCurrent().getType() == 4) {
                            ETHWallet current = WalletDaoUtils.getCurrent();
                            current.setType(2);
                            WalletDaoUtils.updateWallet(current);
                            mIvNotice1.setVisibility(View.GONE);
                            mIvNotice2.setVisibility(View.GONE);
                        }
                        BackupPrivateKeyActivity.start(getActivity(), JsonUtils.objectToJson(current));
                        break;
                    case 5:
                        if (WalletDaoUtils.getCurrent().getType() == 4) {
                            ETHWallet current = WalletDaoUtils.getCurrent();
                            current.setType(2);
                            WalletDaoUtils.updateWallet(current);
                            mIvNotice1.setVisibility(View.GONE);
                            mIvNotice2.setVisibility(View.GONE);
                        }
                        BackupMnemonicsActivity.start(getActivity(), JsonUtils.objectToJson(current), 2);
                        break;
                    case 6:
                        //指纹验证
                        break;
                }
            }

            @Override
            public void failed() {

            }
        });
        new XPopup.Builder(getActivity()).asCustom(popCheckPwd).show();
    }

    /**
     * 弹出修改名称
     */
    private void showWalletEditName() {
        PopWalletEditName popWalletEditName = new PopWalletEditName(getActivity(), current);
        popWalletEditName.setCallBack(new PopWalletEditName.CallBack() {
            @Override
            public void change(long id, String name) {

            }
        });
        new XPopup.Builder(getActivity()).asCustom(popWalletEditName).show();
    }
}