// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TopicListItemView_ViewBinding implements Unbinder {
  private TopicListItemView target;

  @UiThread
  public TopicListItemView_ViewBinding(TopicListItemView target) {
    this(target, target);
  }

  @UiThread
  public TopicListItemView_ViewBinding(TopicListItemView target, View source) {
    this.target = target;

    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvNikeName = Utils.findRequiredViewAsType(source, R.id.tv_nike_name, "field 'mTvNikeName'", TextView.class);
    target.mTvHandle = Utils.findRequiredViewAsType(source, R.id.tv_handle, "field 'mTvHandle'", TextView.class);
    target.mLlRoot = Utils.findRequiredViewAsType(source, R.id.ll_root, "field 'mLlRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TopicListItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mIvHeard = null;
    target.mTvNikeName = null;
    target.mTvHandle = null;
    target.mLlRoot = null;
  }
}
