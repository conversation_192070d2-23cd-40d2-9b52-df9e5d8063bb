package com.Tlock.io.entity.wallet;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TRANSACTION".
*/
public class TransactionDao extends AbstractDao<Transaction, Long> {

    public static final String TABLENAME = "TRANSACTION";

    /**
     * Properties of entity Transaction.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property WalletAddress = new Property(1, String.class, "walletAddress", false, "WALLET_ADDRESS");
        public final static Property Function = new Property(2, String.class, "function", false, "FUNCTION");
        public final static Property GasLimit = new Property(3, String.class, "gasLimit", false, "GAS_LIMIT");
        public final static Property Payer = new Property(4, String.class, "payer", false, "PAYER");
        public final static Property BlockHeight = new Property(5, String.class, "blockHeight", false, "BLOCK_HEIGHT");
        public final static Property Hash = new Property(6, String.class, "hash", false, "HASH");
        public final static Property Data = new Property(7, String.class, "data", false, "DATA");
        public final static Property GasWanted = new Property(8, String.class, "gasWanted", false, "GAS_WANTED");
        public final static Property GesUsed = new Property(9, String.class, "gesUsed", false, "GES_USED");
        public final static Property Time = new Property(10, String.class, "time", false, "TIME");
        public final static Property Sequence = new Property(11, String.class, "sequence", false, "SEQUENCE");
    }


    public TransactionDao(DaoConfig config) {
        super(config);
    }
    
    public TransactionDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TRANSACTION\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"WALLET_ADDRESS\" TEXT," + // 1: walletAddress
                "\"FUNCTION\" TEXT," + // 2: function
                "\"GAS_LIMIT\" TEXT," + // 3: gasLimit
                "\"PAYER\" TEXT," + // 4: payer
                "\"BLOCK_HEIGHT\" TEXT," + // 5: blockHeight
                "\"HASH\" TEXT," + // 6: hash
                "\"DATA\" TEXT," + // 7: data
                "\"GAS_WANTED\" TEXT," + // 8: gasWanted
                "\"GES_USED\" TEXT," + // 9: gesUsed
                "\"TIME\" TEXT," + // 10: time
                "\"SEQUENCE\" TEXT);"); // 11: sequence
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_TRANSACTION_WALLET_ADDRESS ON \"TRANSACTION\"" +
                " (\"WALLET_ADDRESS\" ASC);");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TRANSACTION\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, Transaction entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String walletAddress = entity.getWalletAddress();
        if (walletAddress != null) {
            stmt.bindString(2, walletAddress);
        }
 
        String function = entity.getFunction();
        if (function != null) {
            stmt.bindString(3, function);
        }
 
        String gasLimit = entity.getGasLimit();
        if (gasLimit != null) {
            stmt.bindString(4, gasLimit);
        }
 
        String payer = entity.getPayer();
        if (payer != null) {
            stmt.bindString(5, payer);
        }
 
        String blockHeight = entity.getBlockHeight();
        if (blockHeight != null) {
            stmt.bindString(6, blockHeight);
        }
 
        String hash = entity.getHash();
        if (hash != null) {
            stmt.bindString(7, hash);
        }
 
        String data = entity.getData();
        if (data != null) {
            stmt.bindString(8, data);
        }
 
        String gasWanted = entity.getGasWanted();
        if (gasWanted != null) {
            stmt.bindString(9, gasWanted);
        }
 
        String gesUsed = entity.getGesUsed();
        if (gesUsed != null) {
            stmt.bindString(10, gesUsed);
        }
 
        String time = entity.getTime();
        if (time != null) {
            stmt.bindString(11, time);
        }
 
        String sequence = entity.getSequence();
        if (sequence != null) {
            stmt.bindString(12, sequence);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, Transaction entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String walletAddress = entity.getWalletAddress();
        if (walletAddress != null) {
            stmt.bindString(2, walletAddress);
        }
 
        String function = entity.getFunction();
        if (function != null) {
            stmt.bindString(3, function);
        }
 
        String gasLimit = entity.getGasLimit();
        if (gasLimit != null) {
            stmt.bindString(4, gasLimit);
        }
 
        String payer = entity.getPayer();
        if (payer != null) {
            stmt.bindString(5, payer);
        }
 
        String blockHeight = entity.getBlockHeight();
        if (blockHeight != null) {
            stmt.bindString(6, blockHeight);
        }
 
        String hash = entity.getHash();
        if (hash != null) {
            stmt.bindString(7, hash);
        }
 
        String data = entity.getData();
        if (data != null) {
            stmt.bindString(8, data);
        }
 
        String gasWanted = entity.getGasWanted();
        if (gasWanted != null) {
            stmt.bindString(9, gasWanted);
        }
 
        String gesUsed = entity.getGesUsed();
        if (gesUsed != null) {
            stmt.bindString(10, gesUsed);
        }
 
        String time = entity.getTime();
        if (time != null) {
            stmt.bindString(11, time);
        }
 
        String sequence = entity.getSequence();
        if (sequence != null) {
            stmt.bindString(12, sequence);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public Transaction readEntity(Cursor cursor, int offset) {
        Transaction entity = new Transaction( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // walletAddress
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // function
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // gasLimit
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // payer
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // blockHeight
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // hash
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // data
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // gasWanted
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // gesUsed
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // time
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11) // sequence
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, Transaction entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setWalletAddress(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setFunction(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setGasLimit(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setPayer(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setBlockHeight(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setHash(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setData(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setGasWanted(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setGesUsed(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setTime(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setSequence(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(Transaction entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(Transaction entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(Transaction entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
