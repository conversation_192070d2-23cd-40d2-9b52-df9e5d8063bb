package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.wallet.WalletUtils;

import butterknife.BindView;
import butterknife.OnClick;
import wallet.core.jni.HDWallet;

public class BackupPrivateKeyActivity extends BaseActivity {

    @BindView(R.id.tv_tip)
    TextView mTvTip;

    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.rl_toolbar)
    RelativeLayout mRlToolbar;
    @BindView(R.id.tv1)
    TextView mTv1;
    @BindView(R.id.iv_copy_1)
    ImageView mIvCopy1;
    @BindView(R.id.rl1)
    RelativeLayout mRl1;
    @BindView(R.id.tv2)
    TextView mTv2;
    @BindView(R.id.iv_copy_2)
    ImageView mIvCopy2;
    @BindView(R.id.rl2)
    RelativeLayout mRl2;
    @BindView(R.id.tv3)
    TextView mTv3;
    @BindView(R.id.iv_copy_3)
    ImageView mIvCopy3;
    @BindView(R.id.tv_key_1)
    TextView mTvKey1;
    @BindView(R.id.tv_key_2)
    TextView mTvKey2;
    @BindView(R.id.tv_key_3)
    TextView mTvKey3;
    private String data;
    private ETHWallet walletInfo;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String data) {
        Intent intent = new Intent(context, BackupPrivateKeyActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_backup_private_key;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        data = getIntent().getStringExtra("data");
        ETHWallet ethWallet = JsonUtils.jsonToObject(data, ETHWallet.class);
        String privateKey = "";
        if (ethWallet.getType() == 1) {
            privateKey = ethWallet.getPrivateKey();
        } else {
            HDWallet hdWallet = new HDWallet(ethWallet.getMnemonic(), "");
            walletInfo = WalletUtils.getWalletInfo(hdWallet);
            privateKey = walletInfo.getPrivateKey();
        }
        setTextData(privateKey);
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {

    }

    private void setTextData(String str) {
        int a = str.length() / 3;
        mTvKey1.setText(str.subSequence(0, a));
        mTvKey2.setText(str.subSequence(a , 2 * a ));
        mTvKey3.setText(str.subSequence(2 * a , str.length()));
    }


    @OnClick({R.id.iv_copy_1, R.id.iv_copy_2, R.id.iv_copy_3})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_copy_1:
                //复制第一段
                CopyUtils.copyToClipboard(mTvKey1.getText().toString().trim());
                break;
            case R.id.iv_copy_2:
                //复制第二段
                CopyUtils.copyToClipboard(mTvKey2.getText().toString().trim());
                break;
            case R.id.iv_copy_3:
                //复制第三段
                CopyUtils.copyToClipboard(mTvKey3.getText().toString().trim());
                break;
        }
    }
}