package com.Tlock.io.activity.cosmos;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.callback.OnItemClickRecyclerListener;
import com.Tlock.io.entity.cosmos.SearchHistoryBean;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.itemBean.cosmos.SearchTopicItemView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;

public class SearchActivity extends BaseActivity {


    @BindView(R.id.iv_back)
    ImageView mIvBack;
    @BindView(R.id.ed_search)
    EditText mEdSearch;
    @BindView(R.id.iv_clear)
    ImageView mIvClear;

    @BindView(R.id.iv_clear_history)
    ImageView mIvClearHistory;
    @BindView(R.id.ll_history)
    RelativeLayout mLlHistory;
    @BindView(R.id.rv_history)
    RecyclerView mRvHistory;
    @BindView(R.id.rv_user)
    RecyclerView mRvUser;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.rv_topic)
    RecyclerView mRvTopic;

    private BaseRecyclerViewAdapter<ProfileProto.Profile> adapter_history;
    private BaseRecyclerViewAdapter<ProfileProto.Profile> adapter_user;
    private BaseRecyclerViewAdapter<ProfileProto.Profile> adapter_topic;
    private final ArrayList<ProfileProto.Profile> userDataList = new ArrayList<>();
    private final ArrayList<ProfileProto.Profile> topicDataList = new ArrayList<>();


    private BaseRecyclerViewAdapter<SearchHistoryBean> adapterHistory;
    private ArrayList<ProfileProto.Profile> historyDataList = new ArrayList<>();
    private ArrayList<SearchHistoryBean> historyList = new ArrayList<>();
    private ArrayList<PostQueryProto.TopicResponse> topicList;

    /**
     * @param context
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, SearchActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_search;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mRvUser.setVisibility(View.GONE);
        mRvTopic.setVisibility(View.GONE);
        mRvHistory.setVisibility(View.VISIBLE);
        mEdSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (mEdSearch.getText().toString().isEmpty()) {
                    mIvClear.setVisibility(View.GONE);
                    mLlHistory.setVisibility(View.VISIBLE);
                    mRvHistory.setVisibility(View.VISIBLE);
                    mRvUser.setVisibility(View.GONE);
                    mRvTopic.setVisibility(View.GONE);
                    mLine1.setVisibility(View.GONE);
                    loadData();
                } else {
                    mLlHistory.setVisibility(View.GONE);
                    mIvClear.setVisibility(View.VISIBLE);
                    mRvHistory.setVisibility(View.GONE);

                    mRvUser.setVisibility(View.VISIBLE);
                    mRvTopic.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {
                String string = editable.toString();
                getAtData(string);
                getTopicData(string);
            }
        });
        initRecyclerView();
    }


    private void initRecyclerView() {
        mRvHistory.setLayoutManager(new LinearLayoutManager(getActivity()));
        historyList = SpUtil.getSearchHistory();
        historyDataList = new ArrayList<>();
        for (SearchHistoryBean searchHistoryBean : historyList) {
            ProfileProto.Profile.Builder builder = ProfileProto.Profile.newBuilder();
            builder.setUserHandle(searchHistoryBean.getTitle());
            builder.setWalletAddress(searchHistoryBean.getId());
            historyDataList.add(builder.build());
        }

        adapter_history = new BaseRecyclerViewAdapter<>(getActivity(), historyDataList, new BaseRecyclerViewAdapter.Delegate<ProfileProto.Profile>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new SearchTopicItemView(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, ProfileProto.Profile data, View view) {
                SearchTopicItemView viewBean = (SearchTopicItemView) view;
                viewBean.setData(data);
            }
        });

        mRvHistory.setAdapter(adapter_history);
        adapter_history.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<ProfileProto.Profile>() {
            @Override
            public void onItemClick(int position, ProfileProto.Profile data, View view) {
                if (!mEdSearch.getText().toString().isEmpty()) {
                    SearchHistoryBean searchHistoryBean;
                    if (data.getWalletAddress().startsWith("tlock")) {
                        searchHistoryBean = new SearchHistoryBean(data.getNickname(), data.getWalletAddress(), "0");
                    } else {
                        searchHistoryBean = new SearchHistoryBean(data.getUserHandle(), data.getWalletAddress(), "1");
                    }
                    if (!historyList.contains(searchHistoryBean)) {
                        historyList.add(searchHistoryBean);
                        SpUtil.setSearchHistory(historyList);
                    }

                }
                if (data.getWalletAddress().startsWith("tlock")) {
                    UserInfoActivity.start(getActivity(), data.getWalletAddress());
                } else {
                    PostQueryProto.TopicResponse build = PostQueryProto.TopicResponse.newBuilder()
                            .setName(data.getUserHandle())
                            .setSummary(data.getBio())
                            .setId(data.getWalletAddress()).build();
                    TopicDetailActivity.start(getActivity(), JsonUtils.objectToJson(build));
                }
            }
        });


        mRvUser.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter_user = new BaseRecyclerViewAdapter<>(getActivity(), userDataList, new BaseRecyclerViewAdapter.Delegate<ProfileProto.Profile>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new SearchTopicItemView(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, ProfileProto.Profile data, View view) {
                SearchTopicItemView viewBean = (SearchTopicItemView) view;
                viewBean.setData(data, true);
            }
        });

        mRvUser.setAdapter(adapter_user);
        adapter_user.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<ProfileProto.Profile>() {
            @Override
            public void onItemClick(int position, ProfileProto.Profile data, View view) {
                if (!mEdSearch.getText().toString().isEmpty()) {
                    SearchHistoryBean searchHistoryBean;
                    if (data.getWalletAddress().startsWith("tlock")) {
                        searchHistoryBean = new SearchHistoryBean(data.getNickname(), data.getWalletAddress(), "0");
                    } else {
                        searchHistoryBean = new SearchHistoryBean(data.getUserHandle(), data.getWalletAddress(), "1");
                    }
                    if (!historyDataList.contains(searchHistoryBean)) {
                        historyList.add(searchHistoryBean);
                        SpUtil.setSearchHistory(historyList);
                    }

                }
                if (data.getWalletAddress().startsWith("tlock")) {
                    UserInfoActivity.start(getActivity(), data.getWalletAddress());
                } else {
                    PostQueryProto.TopicResponse build = PostQueryProto.TopicResponse.newBuilder()
                            .setName(data.getUserHandle())
                            .setSummary(data.getBio())
                            .setId(data.getWalletAddress()).build();
                    TopicDetailActivity.start(getActivity(), JsonUtils.objectToJson(build));
                }
            }
        });


        mRvTopic.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter_topic = new BaseRecyclerViewAdapter<>(getActivity(), topicDataList, new BaseRecyclerViewAdapter.Delegate<ProfileProto.Profile>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new SearchTopicItemView(getActivity());
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, ProfileProto.Profile data, View view) {
                SearchTopicItemView viewBean = (SearchTopicItemView) view;
                viewBean.setData(data, true);
            }
        });

        mRvTopic.setAdapter(adapter_topic);
        adapter_topic.setOnItemClickRecyclerListener(new OnItemClickRecyclerListener<ProfileProto.Profile>() {
            @Override
            public void onItemClick(int position, ProfileProto.Profile data, View view) {
                if (!mEdSearch.getText().toString().isEmpty()) {
                    SearchHistoryBean searchHistoryBean;
                    if (data.getWalletAddress().startsWith("tlock")) {
                        searchHistoryBean = new SearchHistoryBean(data.getNickname(), data.getWalletAddress(), "0");
                    } else {
                        searchHistoryBean = new SearchHistoryBean(data.getUserHandle(), data.getWalletAddress(), "1");
                    }
                    if (!historyDataList.contains(searchHistoryBean)) {
                        historyList.add(searchHistoryBean);
                        SpUtil.setSearchHistory(historyList);
                    }

                }
                if (data.getWalletAddress().startsWith("tlock")) {
                    UserInfoActivity.start(getActivity(), data.getWalletAddress());
                } else {
                    PostQueryProto.TopicResponse build = PostQueryProto.TopicResponse.newBuilder()
                            .setName(data.getUserHandle())
                            .setSummary(data.getBio())
                            .setId(data.getWalletAddress()).build();
                    TopicDetailActivity.start(getActivity(), JsonUtils.objectToJson(build));
                }
            }
        });


    }


    @Override
    protected void loadData() {
        params.clear();
        historyList = SpUtil.getSearchHistory();
        historyDataList = new ArrayList<>();
        for (SearchHistoryBean searchHistoryBean : historyList) {
            ProfileProto.Profile.Builder builder = ProfileProto.Profile.newBuilder();
            builder.setUserHandle(searchHistoryBean.getTitle());
            builder.setWalletAddress(searchHistoryBean.getId());
            historyDataList.add(builder.build());
        }
        mRvHistory.setVisibility(View.VISIBLE);
        if (historyDataList.size() == 0) {
            mLlHistory.setVisibility(View.GONE);
        } else {
            mLlHistory.setVisibility(View.VISIBLE);
        }
        adapter_history.setList(historyDataList);
    }

    private void getAtData(String math) {
//        Log.e(TAG, "math: " + math);
        if (math.isEmpty()) {
            return;
        }
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<ProfileProto.Profile> userList = CosmosUtils.getUserList(math);
                if (userList.size() > 7) {
                    userList = new ArrayList<>(userList.subList(0, 7));
                }
//                Log.e(TAG, "userList: " + JsonUtils.listToJson(userList));
                ArrayList<ProfileProto.Profile> finalUserList = userList;
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {

                        if (!mEdSearch.getText().toString().isEmpty()) {
                            adapter_user.setList(finalUserList);
                        }
                    }
                });
            }
        });
    }

    private void getTopicData(String math) {
//        Log.e(TAG, "math: " + math);
        if (math.isEmpty()) {
            return;
        }
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                topicList = CosmosUtils.getTopic(math);
//                Log.e(TAG, "topic: " + JsonUtils.listToJson(topicList));
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (topicList.size() == 0 || mEdSearch.getText().toString().isEmpty()) {
                            mLine1.setVisibility(View.GONE);
                        } else {
                            mLine1.setVisibility(View.VISIBLE);
                        }
                        if (topicList.size() > 3) {
                            topicList = new ArrayList<>(topicList.subList(0, 3));
                        }
                        ArrayList<ProfileProto.Profile> objects = new ArrayList<>();
                        for (PostQueryProto.TopicResponse data : topicList) {
                            if (data.getName().isEmpty()) continue;
                            ProfileProto.Profile.Builder builder = ProfileProto.Profile.newBuilder();
                            builder.setUserHandle(data.getName());
                            builder.setWalletAddress(data.getId());
                            builder.setBio(data.getSummary());
                            objects.add(builder.build());
                        }
                        adapter_topic.setList(objects);
                    }
                });
            }
        });
    }


    @OnClick({R.id.iv_back, R.id.iv_clear, R.id.iv_clear_history})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.iv_clear:
                mEdSearch.setText("");
                loadData();
                break;
            case R.id.iv_clear_history:
                SpUtil.setSearchHistory(new ArrayList<>());
                if (mEdSearch.getText().toString().isEmpty()) {
                    adapter_history.setList(new ArrayList<>());
                }
                showToast("Delete success");
                loadData();
                break;
        }
    }
}