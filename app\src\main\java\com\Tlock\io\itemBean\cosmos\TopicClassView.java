package com.Tlock.io.itemBean.cosmos;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.post.PostQueryProto;

import butterknife.BindView;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class TopicClassView extends BaseView {


    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.iv_1)
    ImageView mIv1;
    @BindView(R.id.iv_select)
    ImageView mIvSelect;


    public TopicClassView(Context context) {
        super(context);
    }

    public TopicClassView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TopicClassView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_topic_class_item;
    }

    public void setData(PostQueryProto.CategoryResponse data, int position) {
        mTvTitle.setText(data.getName());
        mIvSelect.setVisibility(data.getIsSelect() ? VISIBLE : GONE);
        switch (position) {
            case 0:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic1));
                break;
            case 1:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic2));
                break;
            case 2:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic3));
                break;
            case 3:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic4));
                break;
            case 4:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic5));
                break;
            case 5:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic6));
                break;
            case 6:
                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic7));
                break;
        }

    }

}
