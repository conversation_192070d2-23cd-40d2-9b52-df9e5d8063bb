// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CheckMnemonicsActivity_ViewBinding implements Unbinder {
  private CheckMnemonicsActivity target;

  private View view7f09034a;

  @UiThread
  public CheckMnemonicsActivity_ViewBinding(CheckMnemonicsActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public CheckMnemonicsActivity_ViewBinding(final CheckMnemonicsActivity target, View source) {
    this.target = target;

    View view;
    target.mIvBack = Utils.findRequiredViewAsType(source, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    target.mRlToolbar = Utils.findRequiredViewAsType(source, R.id.rl_toolbar, "field 'mRlToolbar'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_next, "field 'mTvNext' and method 'onViewClicked'");
    target.mTvNext = Utils.castView(view, R.id.tv_next, "field 'mTvNext'", TextView.class);
    view7f09034a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mTv1Title = Utils.findRequiredViewAsType(source, R.id.tv1_title, "field 'mTv1Title'", TextView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv1, "field 'mTv1'", TextView.class);
    target.mTv2Title = Utils.findRequiredViewAsType(source, R.id.tv2_title, "field 'mTv2Title'", TextView.class);
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv2, "field 'mTv2'", TextView.class);
    target.mTv3Title = Utils.findRequiredViewAsType(source, R.id.tv3_title, "field 'mTv3Title'", TextView.class);
    target.mTv3 = Utils.findRequiredViewAsType(source, R.id.tv3, "field 'mTv3'", TextView.class);
    target.mTv4Title = Utils.findRequiredViewAsType(source, R.id.tv4_title, "field 'mTv4Title'", TextView.class);
    target.mTv4 = Utils.findRequiredViewAsType(source, R.id.tv4, "field 'mTv4'", TextView.class);
    target.mLlCheck = Utils.findRequiredViewAsType(source, R.id.ll_check, "field 'mLlCheck'", LinearLayout.class);
    target.mRecylerView = Utils.findRequiredViewAsType(source, R.id.recyler_view, "field 'mRecylerView'", RecyclerView.class);
    target.mTvError = Utils.findRequiredViewAsType(source, R.id.tv_error, "field 'mTvError'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    CheckMnemonicsActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvBack = null;
    target.mRlToolbar = null;
    target.mTvNext = null;
    target.mTv1Title = null;
    target.mTv1 = null;
    target.mTv2Title = null;
    target.mTv2 = null;
    target.mTv3Title = null;
    target.mTv3 = null;
    target.mTv4Title = null;
    target.mTv4 = null;
    target.mLlCheck = null;
    target.mRecylerView = null;
    target.mTvError = null;

    view7f09034a.setOnClickListener(null);
    view7f09034a = null;
  }
}
