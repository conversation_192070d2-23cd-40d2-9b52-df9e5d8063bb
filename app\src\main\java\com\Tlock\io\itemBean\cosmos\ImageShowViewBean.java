package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.UserUtil.dip2px;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.widget.RoundedImageView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;

import java.io.File;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * @ClassName LPHistoryBean
 * <AUTHOR>
 * @Data 2022/3/24 15:41
 * @Desc
 */

public class ImageShowViewBean extends BaseView {

    @BindView(R.id.iv_image)
    RoundedImageView mIvImage;
    @BindView(R.id.iv_delete)
    ImageView mIvDelete;
    @BindView(R.id.root)
    RelativeLayout mRoot;

    private File file;
    private int from = 0;

    /**
     * @param context
     * @param form    0添加 1展示
     */
    public ImageShowViewBean(Context context, int form) {
        super(context);
        this.from = form;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.item_show_image;
    }

    public ImageShowViewBean(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ImageShowViewBean(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public void setData(File file, int count) {
        this.file = file;
        ViewGroup.LayoutParams params = mIvImage.getLayoutParams();
        if (count == 3) { // 3 张图片时，设置为正方形
            int width = mIvImage.getContext().getResources().getDisplayMetrics().widthPixels / count;
            params.width = dip2px(getContext(), 100);
            params.height = dip2px(getContext(), 100); // 高度等于宽度，形成正方形
            mIvImage.setAdjustViewBounds(false);
        } else if (count == 1) { // 1 张图片时，占满宽度，高度固定
            mIvImage.setAdjustViewBounds(true);
        } else { // 2 张图片时，宽度均分，高度固定
            int width = mIvImage.getContext().getResources().getDisplayMetrics().widthPixels / count;
            params.width = width;
            params.height = dip2px(getContext(), 150); // 你可以调整这个高度
            mIvImage.setAdjustViewBounds(false);

        }
        mIvImage.setLayoutParams(params);
        mIvImage.setMaxHeight(dip2px(getContext(), 222));
        mIvImage.setMaxWidth(dip2px(getContext(), 222));
        from = 1;
        if (file == null) {
            Glide.with(mIvImage).load(R.mipmap.icon_add_image)
                    .centerCrop()
                    .into(mIvImage);
            mIvDelete.setVisibility(GONE);
        } else {
            RequestOptions options = new RequestOptions()
                    .transform(new MultiTransformation<>(
                            new CenterCrop()
                    ));

            Glide.with(mIvImage).load(file)
                    .apply(options)
                    .into(mIvImage);
            mIvDelete.setVisibility(VISIBLE);

        }

    }

    public void setData(String url, int count, boolean isInfo, String postId) {
        mIvImage.setVisibility(GONE);
        // 动态设置宽高
        ViewGroup.LayoutParams params = mIvImage.getLayoutParams();
        if (count == 3) { // 3 张图片时，设置为正方形
            int width = mIvImage.getContext().getResources().getDisplayMetrics().widthPixels / count;
            params.width = width;
            params.height = width; // 高度等于宽度，形成正方形
            mIvImage.setAdjustViewBounds(false);
        } else if (count == 1) { // 1 张图片时，占满宽度，高度固定
            mIvImage.setAdjustViewBounds(true);
        } else { // 2 张图片时，宽度均分，高度固定
            int width = mIvImage.getContext().getResources().getDisplayMetrics().widthPixels / count;
            params.width = width;
            params.height = dip2px(getContext(), 150); // 你可以调整这个高度
            mIvImage.setAdjustViewBounds(false);

        }
        if (!isInfo) {
            mIvImage.setLayoutParams(params);
            mIvImage.setMaxHeight(dip2px(getContext(), 222));
            mIvImage.setMaxWidth(dip2px(getContext(), 222));
        }
        from = 1;
        if (url.startsWith("http")) {
            RequestOptions options = new RequestOptions()
                    .placeholder(getResources().getDrawable(R.drawable.btn_gray_6))
                    .transform(new MultiTransformation<>(
                            new CenterCrop()
                    ));
            RequestBuilder<Drawable> load = Glide.with(mIvImage).load(url);
            if (count != 1) {
                load.apply(options);
            }

            load.listener(new RequestListener<Drawable>() {
                @Override
                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                    // 检查当前Tag是否与加载时一致
                    if (mIvImage.getTag(R.id.iv_image).equals(postId)) {
                        mIvImage.setImageDrawable(resource);
                    }
                    return false;
                }

                @Override
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                    return false;
                }
            }).into(mIvImage);
            mIvDelete.setVisibility(GONE);
        } else {
            RequestOptions options = new RequestOptions()
                    .placeholder(getResources().getDrawable(R.drawable.btn_gray_6))
                    .transform(new MultiTransformation<>(
                            new CenterCrop()
                    ));
            Bitmap bitmap1 = BitmapUtils.base64ToBitmap(url);
            RequestBuilder<Drawable> load = Glide.with(mIvImage).load(bitmap1);
            if (count != 1) {
                load.apply(options);
            }
            load.listener(new RequestListener<Drawable>() {
                @Override
                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                    // 检查当前Tag是否与加载时一致
                    if (mIvImage.getTag(R.id.iv_image).equals(postId)) {
                        mIvImage.setImageDrawable(resource);
                    }
                    return false;
                }

                @Override
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                    return false;
                }
            }).into(mIvImage);
            mIvDelete.setVisibility(GONE);
        }
        mIvImage.setVisibility(VISIBLE);

    }


    @OnClick({R.id.iv_image, R.id.iv_delete, R.id.root})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_image:
                if (file == null && from == 0) {
                    if (callback != null) {
                        callback.onAdd();
                    }
                    return;
                }
                if (callback != null) {
                    callback.onSelect(mIvImage);
                }
                break;
            case R.id.iv_delete:
                if (callback != null) {
                    callback.onDelete(file);
                }
                break;
            case R.id.root:
                if (callback != null) {
                    callback.outSide();
                }
                break;

        }
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public void setGlideTag(String id) {
        mIvImage.setTag(R.id.iv_image, id);

    }

    public interface Callback {
        void onDelete(File file);

        void onAdd();

        void onSelect(ImageView mIvImage);

        void outSide();

    }
}
