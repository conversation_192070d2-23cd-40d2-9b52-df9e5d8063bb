// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.transfer;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.wallet.GasSelectView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopTransferConfirm_ViewBinding implements Unbinder {
  private PopTransferConfirm target;

  private View view7f090373;

  private View view7f090374;

  private View view7f090375;

  private View view7f090376;

  private View view7f09034a;

  @UiThread
  public PopTransferConfirm_ViewBinding(PopTransferConfirm target) {
    this(target, target);
  }

  @UiThread
  public PopTransferConfirm_ViewBinding(final PopTransferConfirm target, View source) {
    this.target = target;

    View view;
    target.mTvPrice = Utils.findRequiredViewAsType(source, R.id.tv_price, "field 'mTvPrice'", TextView.class);
    target.mView1 = Utils.findRequiredView(source, R.id.view1, "field 'mView1'");
    target.mTvGasPrice = Utils.findRequiredViewAsType(source, R.id.tv_gasPrice, "field 'mTvGasPrice'", TextView.class);
    target.mRlGas = Utils.findRequiredViewAsType(source, R.id.rl_gas, "field 'mRlGas'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_time_1, "field 'mTvTime1' and method 'onViewClicked'");
    target.mTvTime1 = Utils.castView(view, R.id.tv_time_1, "field 'mTvTime1'", GasSelectView.class);
    view7f090373 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_time_2, "field 'mTvTime2' and method 'onViewClicked'");
    target.mTvTime2 = Utils.castView(view, R.id.tv_time_2, "field 'mTvTime2'", GasSelectView.class);
    view7f090374 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_time_3, "field 'mTvTime3' and method 'onViewClicked'");
    target.mTvTime3 = Utils.castView(view, R.id.tv_time_3, "field 'mTvTime3'", GasSelectView.class);
    view7f090375 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_time_4, "field 'mTvTime4' and method 'onViewClicked'");
    target.mTvTime4 = Utils.castView(view, R.id.tv_time_4, "field 'mTvTime4'", GasSelectView.class);
    view7f090376 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mLlSelect = Utils.findRequiredViewAsType(source, R.id.ll_select, "field 'mLlSelect'", LinearLayout.class);
    target.mTvCustom = Utils.findRequiredViewAsType(source, R.id.tv_custom, "field 'mTvCustom'", TextView.class);
    target.mEdGwei = Utils.findRequiredViewAsType(source, R.id.ed_Gwei, "field 'mEdGwei'", EditText.class);
    target.mLlGwei = Utils.findRequiredViewAsType(source, R.id.ll_Gwei, "field 'mLlGwei'", LinearLayout.class);
    target.mEdLimit = Utils.findRequiredViewAsType(source, R.id.ed_limit, "field 'mEdLimit'", EditText.class);
    target.mLlCustom = Utils.findRequiredViewAsType(source, R.id.ll_custom, "field 'mLlCustom'", LinearLayout.class);
    target.mLlRoot = Utils.findRequiredViewAsType(source, R.id.ll_root, "field 'mLlRoot'", LinearLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_next, "field 'mTvNext' and method 'onViewClicked'");
    target.mTvNext = Utils.castView(view, R.id.tv_next, "field 'mTvNext'", TextView.class);
    view7f09034a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mTvPriceNow = Utils.findRequiredViewAsType(source, R.id.tv_price_now, "field 'mTvPriceNow'", TextView.class);
    target.mTvBuyCount = Utils.findRequiredViewAsType(source, R.id.tv_buyCount, "field 'mTvBuyCount'", TextView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mIvClose = Utils.findRequiredViewAsType(source, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    target.mLlPrice = Utils.findRequiredViewAsType(source, R.id.ll_price, "field 'mLlPrice'", LinearLayout.class);
    target.mLlCount = Utils.findRequiredViewAsType(source, R.id.ll_count, "field 'mLlCount'", LinearLayout.class);
    target.mTvToken = Utils.findRequiredViewAsType(source, R.id.tv_token, "field 'mTvToken'", TextView.class);
    target.mTvGold = Utils.findRequiredViewAsType(source, R.id.tv_gold, "field 'mTvGold'", TextView.class);
    target.mTvBnb = Utils.findRequiredViewAsType(source, R.id.tv_bnb, "field 'mTvBnb'", TextView.class);
    target.mTvTip = Utils.findRequiredViewAsType(source, R.id.tv_tip, "field 'mTvTip'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PopTransferConfirm target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvPrice = null;
    target.mView1 = null;
    target.mTvGasPrice = null;
    target.mRlGas = null;
    target.mTvTime1 = null;
    target.mTvTime2 = null;
    target.mTvTime3 = null;
    target.mTvTime4 = null;
    target.mLlSelect = null;
    target.mTvCustom = null;
    target.mEdGwei = null;
    target.mLlGwei = null;
    target.mEdLimit = null;
    target.mLlCustom = null;
    target.mLlRoot = null;
    target.mTvNext = null;
    target.mTvPriceNow = null;
    target.mTvBuyCount = null;
    target.mTvTitle = null;
    target.mIvClose = null;
    target.mLlPrice = null;
    target.mLlCount = null;
    target.mTvToken = null;
    target.mTvGold = null;
    target.mTvBnb = null;
    target.mTvTip = null;

    view7f090373.setOnClickListener(null);
    view7f090373 = null;
    view7f090374.setOnClickListener(null);
    view7f090374 = null;
    view7f090375.setOnClickListener(null);
    view7f090375 = null;
    view7f090376.setOnClickListener(null);
    view7f090376 = null;
    view7f09034a.setOnClickListener(null);
    view7f09034a = null;
  }
}
