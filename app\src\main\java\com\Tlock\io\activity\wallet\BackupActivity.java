package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.JsonUtils;

import butterknife.BindView;
import butterknife.OnClick;

public class BackupActivity extends BaseActivity {


    @BindView(R.id.rl_mnemonic)
    RelativeLayout mRlMnemonic;
    @BindView(R.id.rl_key)
    RelativeLayout mRlKey;
    @BindView(R.id.iv_back)
    ImageView mIvBack;
    private String data;
    private ETHWallet ethWallet;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String data) {
        Intent intent = new Intent(context, BackupActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_backup;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        data = getIntent().getStringExtra("data");
        ethWallet = JsonUtils.jsonToObject(data, ETHWallet.class);
        if (ethWallet == null) return;
        if (ethWallet.getType() == 1) {
            mRlMnemonic.setVisibility(View.GONE);
        }
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {

    }

    @OnClick({R.id.rl_mnemonic, R.id.rl_key})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.rl_mnemonic:
                BackupMnemonicsActivity.start(getActivity(), JsonUtils.objectToJson(ethWallet), 2);
                break;
            case R.id.rl_key:
                BackupPrivateKeyActivity.start(getActivity(), JsonUtils.objectToJson(ethWallet));
                break;
        }
    }
}