// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TransactionInfoActivity_ViewBinding implements Unbinder {
  private TransactionInfoActivity target;

  @UiThread
  public TransactionInfoActivity_ViewBinding(TransactionInfoActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public TransactionInfoActivity_ViewBinding(TransactionInfoActivity target, View source) {
    this.target = target;

    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mTvMethod = Utils.findRequiredViewAsType(source, R.id.tv_method, "field 'mTvMethod'", TextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    target.mTvLimit = Utils.findRequiredViewAsType(source, R.id.tv_limit, "field 'mTvLimit'", TextView.class);
    target.mTvUsed = Utils.findRequiredViewAsType(source, R.id.tv_used, "field 'mTvUsed'", TextView.class);
    target.mTvHeight = Utils.findRequiredViewAsType(source, R.id.tv_height, "field 'mTvHeight'", TextView.class);
    target.mTvWalletAddress = Utils.findRequiredViewAsType(source, R.id.tv_wallet_address, "field 'mTvWalletAddress'", TextView.class);
    target.mTvPayer = Utils.findRequiredViewAsType(source, R.id.tv_payer, "field 'mTvPayer'", TextView.class);
    target.mTvHash = Utils.findRequiredViewAsType(source, R.id.tv_hash, "field 'mTvHash'", TextView.class);
    target.mTvData = Utils.findRequiredViewAsType(source, R.id.tv_data, "field 'mTvData'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TransactionInfoActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mTvMethod = null;
    target.mTvTime = null;
    target.mTvLimit = null;
    target.mTvUsed = null;
    target.mTvHeight = null;
    target.mTvWalletAddress = null;
    target.mTvPayer = null;
    target.mTvHash = null;
    target.mTvData = null;
  }
}
