// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.utils;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TwoButtonPopUtils_ViewBinding implements Unbinder {
  private TwoButtonPopUtils target;

  private View view7f09030c;

  private View view7f09036e;

  @UiThread
  public TwoButtonPopUtils_ViewBinding(final TwoButtonPopUtils target, View source) {
    this.target = target;

    View view;
    target.mTvPopTitle = Utils.findRequiredViewAsType(source, R.id.tv_pop_title, "field 'mTvPopTitle'", TextView.class);
    target.mTvPopContent = Utils.findRequiredViewAsType(source, R.id.tv_pop_content, "field 'mTvPopContent'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_cancel, "field 'mTvCancel' and method 'onViewClicked'");
    target.mTvCancel = Utils.castView(view, R.id.tv_cancel, "field 'mTvCancel'", TextView.class);
    view7f09030c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mViewLine = Utils.findRequiredView(source, R.id.view_line, "field 'mViewLine'");
    view = Utils.findRequiredView(source, R.id.tv_sure, "field 'mTvSure' and method 'onViewClicked'");
    target.mTvSure = Utils.castView(view, R.id.tv_sure, "field 'mTvSure'", TextView.class);
    view7f09036e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mLlContainer = Utils.findRequiredViewAsType(source, R.id.ll_container, "field 'mLlContainer'", LinearLayout.class);
    target.mLlRootPop = Utils.findRequiredViewAsType(source, R.id.ll_root_pop, "field 'mLlRootPop'", LinearLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TwoButtonPopUtils target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvPopTitle = null;
    target.mTvPopContent = null;
    target.mTvCancel = null;
    target.mViewLine = null;
    target.mTvSure = null;
    target.mLlContainer = null;
    target.mLlRootPop = null;

    view7f09030c.setOnClickListener(null);
    view7f09030c = null;
    view7f09036e.setOnClickListener(null);
    view7f09036e = null;
  }
}
