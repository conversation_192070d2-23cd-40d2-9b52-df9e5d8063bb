// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopSelectList_ViewBinding implements Unbinder {
  private PopSelectList target;

  private View view7f09030c;

  @UiThread
  public PopSelectList_ViewBinding(PopSelectList target) {
    this(target, target);
  }

  @UiThread
  public PopSelectList_ViewBinding(final PopSelectList target, View source) {
    this.target = target;

    View view;
    target.mRvWallet = Utils.findRequiredViewAsType(source, R.id.rv_wallet, "field 'mRvWallet'", RecyclerView.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onBindClick'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f09030c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopSelectList target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mRvWallet = null;
    target.mLine1 = null;
    target.mTvTitle = null;
    target.mTvConfirm = null;

    view7f09030c.setOnClickListener(null);
    view7f09030c = null;
  }
}
