<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_wallet"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginLeft="@dimen/dp_6"
        android:layout_marginTop="@dimen/dp_15"
        android:drawableRight="@mipmap/icon_black_down"
        android:drawablePadding="@dimen/dp_5"
        android:minWidth="@dimen/dp_20"
        android:padding="@dimen/dp_10"
        android:src="@mipmap/icon_menu"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_16" />

    <TextView
        android:id="@+id/btn_updata"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/iv_wallet"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_3"
        android:background="@drawable/shape_deep_gray_60"
        android:gravity="center"
        android:paddingLeft="@dimen/dp_18"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_18"
        android:paddingBottom="@dimen/dp_7"
        android:text="Edit profile"
        android:textColor="@color/black" />

    <RelativeLayout
        android:id="@+id/rl_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/btn_updata"
        android:layout_marginTop="@dimen/dp_5"
        android:padding="@dimen/dp_15">

        <ImageView
            android:id="@+id/iv_heard"
            android:layout_width="@dimen/dp_66"
            android:layout_height="@dimen/dp_66"
            android:layout_marginRight="@dimen/dp_10" />

        <ImageView
            android:id="@+id/iv_manger"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignRight="@id/iv_heard"
            android:layout_alignBottom="@id/iv_heard"
            android:layout_marginRight="@dimen/dp_6"
            android:src="@mipmap/icon_m"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/ll_name"
            android:layout_toRightOf="@id/iv_heard"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxEms="15"
                android:maxLines="1"
                android:textColor="@color/cosmos_black"
                android:textSize="@dimen/dp_14"
                android:textStyle="bold" />

            <com.Tlock.io.widget.FontTextView
                android:id="@+id/tv_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_alignTop="@id/tv_name"
                android:layout_marginLeft="5dp"
                android:layout_toRightOf="@id/tv_name"
                android:background="@drawable/btn_black_60"
                android:fontFamily="@font/font_family"
                android:paddingLeft="@dimen/dp_7"
                android:paddingRight="@dimen/dp_7"
                android:paddingBottom="@dimen/dp_1"
                android:text="Lv1"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10"
                android:textStyle="bold"
                app:fontType="walletname" />

            <ImageView
                android:id="@+id/iv_vip"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_alignBottom="@id/tv_name"
                android:layout_marginLeft="@dimen/dp_3"
                android:layout_toRightOf="@id/tv_level"
                android:src="@mipmap/icon_gold_vip"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/icon_check"
                android:layout_gravity="center_vertical"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_marginLeft="@dimen/dp_3"
                android:src="@mipmap/icon_check" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ll_name"
            android:layout_marginTop="@dimen/dp_2"
            android:layout_toRightOf="@id/iv_heard"
            android:text="_"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/dp_12" />

        <TextView
            android:id="@+id/tv_wallet_address"
            android:layout_width="@dimen/dp_150"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_handle"
            android:layout_marginTop="@dimen/dp_2"
            android:layout_toRightOf="@id/iv_heard"
            android:drawableRight="@mipmap/icon_copy_gray"
            android:ellipsize="middle"
            android:maxLines="1"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/dp_12" />

        <TextView
            android:id="@+id/review_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_wallet_address"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_toRightOf="@id/iv_heard"
            android:text="0"
            android:textColor="@color/black"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/review_count"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_toRightOf="@id/review_count"
            android:text="following"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/fans_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/review_count"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_toRightOf="@id/tv_1"
            android:text="0"
            android:textColor="@color/black"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/review_count"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_toRightOf="@id/fans_count"
            android:text="followers"
            android:textColor="@color/cosmos_default"
            android:textSize="@dimen/sp_12" />

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />
    </RelativeLayout>

    <View
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_6"
        android:layout_below="@id/rl_info"
        android:background="#EFF3F4" />

    <RelativeLayout
        android:id="@+id/rl_balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/line1"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_balance"
            android:layout_width="@dimen/dp_16"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_wallet" />

        <TextView
            android:id="@+id/tv_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/iv_balance"
            android:text="@string/Wallet"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <TextView
            android:id="@+id/tv_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_10"
            android:layout_toLeftOf="@id/iv_right"
            android:text="0"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_free"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_balance"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_free"
            android:layout_width="@dimen/dp_16"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_allowance" />

        <TextView
            android:id="@+id/tv_free"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/iv_free"
            android:text="Periodic allowance"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <TextView
            android:id="@+id/tv_free_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_10"
            android:layout_toLeftOf="@id/iv_free_1"
            android:text="0"
            android:textColor="@color/cosmos_black"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_free_1"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />


    </RelativeLayout>

    <View
        android:id="@+id/line2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_6"
        android:layout_below="@id/rl_free"
        android:background="#EFF3F4" />

    <RelativeLayout
        android:id="@+id/rl_transaction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/line2"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_transaction"
            android:layout_width="@dimen/dp_18"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_transaction" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_transaction"
            android:text="Transactions"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_favorite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_transaction"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_favorite"
            android:layout_width="@dimen/dp_18"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_favorite" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_favorite"
            android:text="Likes"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />

    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/rl_collect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_favorite"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_collect"
            android:layout_width="@dimen/dp_16"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_collect" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_collect"
            android:text="Bookmarks"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />

    </RelativeLayout>

    <View
        android:id="@+id/line3"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_6"
        android:layout_below="@id/rl_collect"
        android:background="#EFF3F4" />

    <RelativeLayout
        android:id="@+id/rl_governance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/line3"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_governance"
            android:layout_width="@dimen/dp_16"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_governance" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_governance"
            android:text="Proposal"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />

    </RelativeLayout>

    <View
        android:id="@+id/line4"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_6"
        android:layout_below="@id/rl_governance"
        android:background="#EFF3F4" />

    <RelativeLayout
        android:id="@+id/rl_Moderator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/line4"
        android:background="@color/white"
        android:padding="@dimen/dp_20">

        <ImageView
            android:id="@+id/iv_Moderator"
            android:layout_width="@dimen/dp_16"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_16"
            android:adjustViewBounds="true"
            android:src="@mipmap/icon_setting_proposal" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/iv_Moderator"
            android:text="Manager"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_15" />

        <ImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/icon_right_gray" />

    </RelativeLayout>

    <View
        android:id="@+id/line5"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_6"
        android:layout_below="@id/rl_Moderator"
        android:background="#EFF3F4" />

</RelativeLayout>
