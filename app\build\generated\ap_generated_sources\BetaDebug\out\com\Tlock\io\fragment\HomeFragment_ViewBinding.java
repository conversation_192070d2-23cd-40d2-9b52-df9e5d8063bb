// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.fragment;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.viewpager2.widget.ViewPager2;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.cy.tablayoutniubility.TabLayoutNoScroll;
import java.lang.IllegalStateException;
import java.lang.Override;

public class HomeFragment_ViewBinding implements Unbinder {
  private HomeFragment target;

  private View view7f09016a;

  @UiThread
  public HomeFragment_ViewBinding(final HomeFragment target, View source) {
    this.target = target;

    View view;
    target.tabLayoutLine = Utils.findRequiredViewAsType(source, R.id.tablayout, "field 'tabLayoutLine'", TabLayoutNoScroll.class);
    target.viewPager2 = Utils.findRequiredViewAsType(source, R.id.view_pager, "field 'viewPager2'", ViewPager2.class);
    view = Utils.findRequiredView(source, R.id.iv_search, "method 'onBindClick'");
    view7f09016a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    HomeFragment target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.tabLayoutLine = null;
    target.viewPager2 = null;

    view7f09016a.setOnClickListener(null);
    view7f09016a = null;
  }
}
