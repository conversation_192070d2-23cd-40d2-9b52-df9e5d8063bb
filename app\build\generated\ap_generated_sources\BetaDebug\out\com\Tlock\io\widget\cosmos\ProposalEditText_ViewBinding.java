// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ProposalEditText_ViewBinding implements Unbinder {
  private ProposalEditText target;

  @UiThread
  public ProposalEditText_ViewBinding(ProposalEditText target) {
    this(target, target);
  }

  @UiThread
  public ProposalEditText_ViewBinding(ProposalEditText target, View source) {
    this.target = target;

    target.mEditText = Utils.findRequiredViewAsType(source, R.id.editText, "field 'mEditText'", EditText.class);
    target.mIvAdd = Utils.findRequiredViewAsType(source, R.id.iv_add, "field 'mIvAdd'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    ProposalEditText target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mEditText = null;
    target.mIvAdd = null;
  }
}
