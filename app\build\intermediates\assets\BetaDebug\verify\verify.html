<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"><!--适配手机屏幕-->
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <title>dome</title><!--vconsole  js-->
    <script src="http://wechatfe.github.io/vconsole/lib/vconsole.min.js?v=3.2.0"></script>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <link rel="icon" href="favicon.png">
    <title>理想国</title>
    <style>
      * {
        margin: 0px;
        padding: 0px;
      }
      .mobContent {
        width: 100%;
        height: 100%;
        background: #171E26;
        overflow: hidden;
      }
      .verBox {
        width: 100%;
        margin: 0px auto;
      }

      .mobVerBox .nc-container #nc_1_wrapper {
        width: 100%!important;
      }
      .nc-container #nc_1_wrapper,.nc_scale {
        height: 52px !important;
        border-radius: 6px !important;
        box-shadow: 0 0 1px #1F2630 !important;
        background-color: #1F2630 !important;
      }
      .nc-container .nc_scale span {
        width: 52px!important;
        height: 52px!important;
        line-height: 52px!important;
        border-radius: 6px;
      }
      .nc-container .nc_scale span.nc-lang-cnt {
        width: 100%!important;
        line-height: 52px;
      }
      .nc-container .nc_scale .nc_ok, .nc-container .nc_scale .nc_bg {
        background: #FFC510!important;
        border-radius: 6px;
      }
    </style>
    <script type="text/javascript" charset="utf-8" src="https://g.alicdn.com/AWSC/AWSC/awsc.js"></script>
</head>
<body>
<script> init vConsolevar vConsole = new VConsole();console.log('Hello world');</script>
<noscript>
    <strong>We're sorry but forcoins doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
</noscript>
<div class="mobContent">
    <div id="__nc" class="verBox mobVerBox">
        <div id="nc"></div>
    </div>
</div>

<script>
      AWSC.use('nc', (state, module) => {
        window.nc = module.init({
          appkey: 'FFFF0N0000000000B3B9',
          scene: 'nc_login_h5',
          renderTo: 'nc',
          success: data => {
            slideCallBack(data)
          },
          fail: failCode => {
            alert('获取验证码失败')
            window.console && console.log(failCode)
          },
          error: errorCode => {
            alert('获取验证码失败')
            window.console && console.log(errorCode)
          }
        })
        window.nc.reset()
      })
      function slideCallBack (data) {
        let result = {
          'nc_token': data.token,
          'sessionid': data.sessionId,
          'sig': data.sig,
          'scene': 'nc_login_h5'
        }
        // 绑定Java接口与JavaScript函数。
        window.testInterface.getSlideData(JSON.stringify(result))
      }
    </script>
<!-- built files will be auto injected -->
</body>
</html>