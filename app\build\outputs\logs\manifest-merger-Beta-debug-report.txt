-- Merging decision tree log ---
permission#com.Tlock.io.openadsdk.permission.TT_PANGOLIN
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:70:5-72:47
	android:protectionLevel
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:72:9-44
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:71:9-73
uses-permission#com.Tlock.io.openadsdk.permission.TT_PANGOLIN
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:74:5-89
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:74:22-86
permission#com.Tlock.io.push.permission.MESSAGE
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:135:5-138:36
	android:protectionLevel
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:137:9-44
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:138:9-33
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:136:9-64
uses-permission#com.Tlock.io.push.permission.MESSAGE
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:140:5-142:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:142:9-33
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:141:9-64
permission#com.Tlock.io.permission.C2D_MESSAGE
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:147:5-150:36
	android:protectionLevel
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:149:9-44
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:150:9-33
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:148:9-63
uses-permission#com.Tlock.io.permission.C2D_MESSAGE
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:152:5-154:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:154:9-33
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:153:9-63
permission#com.Tlock.io.permission.MIPUSH_RECEIVE
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:160:5-163:36
	android:protectionLevel
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:162:9-44
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:163:9-33
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:161:9-66
uses-permission#com.Tlock.io.permission.MIPUSH_RECEIVE
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:165:5-167:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:167:9-33
	android:name
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:166:9-66
provider#androidx.core.content.FileProvider
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:385:9-393:20
	android:grantUriPermissions
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:389:13-47
	android:authorities
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:387:13-64
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:388:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:386:13-62
manifest
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
MERGED from [com.github.AnJiaoDe:TabLayoutNiubility:V1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bc273b064940e5148e2dd819bd82111\transformed\jetified-TabLayoutNiubility-V1.3.1\AndroidManifest.xml:2:1-11:12
MERGED from [:proto] D:\cs\tlk\proto\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.wkp:DragGridView:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7d7d522cd658a74e2fd0ed4c43471ca\transformed\jetified-DragGridView-1.0.1\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0689f52c8d09f0e15ee5135366e3b8c\transformed\material-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:2:1-24:12
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:2:1-46:12
MERGED from [com.github.li-xiaojun:EasyAdapter:1.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\a141764184f3520f2520d485daba8133\transformed\jetified-EasyAdapter-1.2.8\AndroidManifest.xml:2:1-9:12
MERGED from [com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaab13da6bf9b59bcb67a2fbc6db35d9\transformed\jetified-subsampling-scale-image-view-androidx-3.10.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\182f7f156563cb96f7fc7a93fbb671d8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8960aa6e858281122249b12ced85f681\transformed\appcompat-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:2:1-16:12
MERGED from [io.noties.markwon:image-glide:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d0b84d648f7a5fc0b554a06164d4f55e\transformed\jetified-image-glide-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecd36bd943f1ed64ccb906a67b7a5e11\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.youth.banner:banner:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d7af008fffeff50bd39e243281e0ad7\transformed\jetified-banner-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32284c76bdfbeb6e8dcca7975c3343f5\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.chauthai.swipereveallayout:swipe-reveal-layout:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2fa7c824e387c5822aef829203a8f1b2\transformed\jetified-swipe-reveal-layout-1.4.1\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1468f29970f2712d0e98b120182edec8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\91709e0713fac362714c9349f7d1ce83\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\9b412801109f33e6dfbf373705b5fb8b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\13986cb14e2338c15b9651bb76db97a1\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.jakewharton:butterknife:10.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\377a463887b3d7d6a87b1a60321ab67e\transformed\jetified-butterknife-10.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.jakewharton:butterknife-runtime:10.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\ae9500df773a8acd02eb17e827face0b\transformed\jetified-butterknife-runtime-10.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e14b8e111e59202da42ff67a79c9719\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f18b2d51800fd0723fd2d891e76a516\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e08591c2a0cd67a82b0591336f06c08\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cd61a1b434f2c9ef6a0181731792847\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a55c04ed0b8b266163c947c5bb949a4f\transformed\jetified-appcompat-resources-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd5e38fb4d2f03df5df9472d0ead9776\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3349f477c947a262ac511503dc1c5971\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\241adab02557883c730c4d052544d34b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa6bed2e40ae8ad1ac8c2486742fbeae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c6ecafa87f845531d5f0592c23ea71d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\02d87dff3da90183c5324e5bf8509f2d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\257abaa2293977b0a0c962d6006c44ab\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53c8ddc09b3bed8593b00eceac1a1f65\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\96093cbadab35e65a1781be443e6a3a3\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c07390ef56dc6697b698de5e4ed81ac\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47da799b4f7714068fd34e862819e5c3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff55c4a018ffbfa710f47ef90eb07256\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8c2a0fcd9f7f806b16d31389347783b4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e488675d250508e2d28e35b9d8dc622\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ff5a9a7d03aa41a285e7ec5269ba9e8c\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\2c869b67198a961a151ad23f45b2161a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c489a767b7d02d5bfc03dce3b96e660b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\0585910deac312766da6ba1c9f006342\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:2:1-14:12
MERGED from [com.github.bigmanLau:CardviewFix:1.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\7232001893b8fd855a9050c84c5f81fc\transformed\jetified-CardviewFix-1.0.3\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\29c835fd9f60d2cac6cc876aab58e560\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fec2a9a58e6240597ac57894939cebb7\transformed\jetified-GreenDaoUpgradeHelper-v2.2.1\AndroidManifest.xml:2:1-16:12
MERGED from [io.noties.markwon:linkify:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bc3e1a108f38a05e2dd5de0637d22e4\transformed\jetified-linkify-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.noties.markwon:core:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c5e7f02446cf34b31ff124ab8161dedf\transformed\jetified-core-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.contrarywind:Android-PickerView:4.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c2715fd91e86f3c9b6a97aa30509a8a8\transformed\jetified-Android-PickerView-4.1.9\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a115c79be891815e6fef390e180a443\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac42345df0fde9d7214f6fd960e9b5c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e25e587af16a8e12a8ca36060b070f1f\transformed\exifinterface-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\059ced8ab92e9783a33f05c243886b5e\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.contrarywind:wheelview:4.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\30c3d77390feafaf3242c902bae1b2b2\transformed\jetified-wheelview-4.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\517c492ee2e7eba1a71c0c9bb474f740\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d000b537175726c02faf9d0b0b549af2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2844585aca0b2f9e972d583cddaccd4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\15cead8b21eace7778c0db27b9e01387\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91f549ed0fa3030f8314becf430eadde\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd4b5661d261566e42f83842de5c192\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\68210ff09168caf82545a96e10b80d16\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-6] C:\Users\<USER>\.gradle\caches\transforms-3\924afc56e026b49ad40635f04d8fbc7e\transformed\jetified-SmartRefreshLayout-1.1.0-alpha-6\AndroidManifest.xml:2:1-17:12
MERGED from [io.reactivex:rxandroid:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a8cdef720e297de67dcf990bddc6c4c7\transformed\jetified-rxandroid-1.0.1\AndroidManifest.xml:15:1-21:12
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:2:1-31:12
MERGED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:2:1-19:12
MERGED from [com.trustwallet:wallet-core:2.6.3] C:\Users\<USER>\.gradle\caches\transforms-3\b77d237395e2b42d086a421b053e6643\transformed\jetified-wallet-core-2.6.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:2:1-18:12
MERGED from [com.hyman:flowlayout-lib:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f6e87a1beca5b6273eb5df37b6788cdd\transformed\jetified-flowlayout-lib-1.1.2\AndroidManifest.xml:2:1-12:12
MERGED from [com.github.donkingliang:ConsecutiveScroller:4.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\8a27acb9e77f484805afc42353431a7c\transformed\jetified-ConsecutiveScroller-4.6.4\AndroidManifest.xml:2:1-9:12
MERGED from [com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-26] C:\Users\<USER>\.gradle\caches\transforms-3\e396990e4cedac72d7ee6505fbd880a1\transformed\jetified-SmartRefreshHeader-1.1.0-alpha-26\AndroidManifest.xml:2:1-17:12
MERGED from [com.crazysunj:mtrva:2.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\8093159662bfd4052a33ae82743f638e\transformed\jetified-mtrva-2.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.scwang90:refresh-header-material:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\eab26ff1e8133c533f88d284bc10c9c8\transformed\jetified-refresh-header-material-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.warkiz.widget:indicatorseekbar:2.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\d81ca3195213575aefabf73ca12ccfa0\transformed\jetified-indicatorseekbar-2.1.2\AndroidManifest.xml:2:1-11:12
MERGED from [q.rorbin:VerticalTabLayout:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ddd6860bb2e9e1d3218e8792ee8bc7a\transformed\jetified-VerticalTabLayout-1.2.5\AndroidManifest.xml:2:1-13:12
MERGED from [q.rorbin:badgeview:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f8d5eecf00cf80180942b100fc5a7307\transformed\jetified-badgeview-1.1.2\AndroidManifest.xml:2:1-13:12
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
	package
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:4:5-27
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:1-407:12
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BROADCAST_PACKAGE_ADDED
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:6:5-8:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:8:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:7:9-66
uses-permission#android.permission.BROADCAST_PACKAGE_CHANGED
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:9:5-11:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:11:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:10:9-68
uses-permission#android.permission.BROADCAST_PACKAGE_INSTALL
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:14:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:13:9-68
uses-permission#android.permission.BROADCAST_PACKAGE_REPLACED
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:17:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:16:9-69
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:18:5-84
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:18:22-81
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:19:5-21:36
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:18:5-68
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:18:5-68
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:21:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:20:9-52
uses-permission#android.permission.REORDER_TASKS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:22:5-24:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:24:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:23:9-56
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:27:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:26:9-59
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:30:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:29:9-62
uses-permission#android.permission.SYSTEM_OVERLAY_WINDOW
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:31:5-33:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:33:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:32:9-64
uses-permission#android.permission.INTERNET
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:34:5-36:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:36:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:35:9-51
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:37:5-39:36
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:13:5-79
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:13:5-79
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:39:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:38:9-63
uses-permission#android.permission.READ_PHONE_STATE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:40:5-42:36
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:14:5-75
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:14:5-75
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:42:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:41:9-59
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:43:5-81
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:11:5-81
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:11:5-81
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:20:5-81
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:20:5-81
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:43:22-78
uses-permission#android.permission.REQUEST_DELETE_PACKAGES
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:44:5-46:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:46:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:45:9-66
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:47:5-90
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:47:22-87
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:48:5-50:38
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:10:5-80
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:10:5-80
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:19:5-80
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:19:5-80
	android:maxSdkVersion
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:50:9-35
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:49:9-64
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:51:5-76
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:51:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:52:5-75
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:52:22-72
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:53:5-55:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:55:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:54:9-60
uses-permission#android.permission.READ_SETTINGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:59:5-61:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:61:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:60:9-56
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:62:5-64:36
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:12:5-81
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:12:5-81
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:64:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:63:9-65
uses-permission#android.permission.WRITE_SETTINGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:65:5-68:47
	tools:ignore
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:68:9-44
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:67:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:66:9-57
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:75:5-78:47
	tools:ignore
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:78:9-44
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:77:9-32
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:76:9-68
uses-permission#android.permission.GET_TASKS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:79:5-81:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:81:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:80:9-52
uses-permission#android.permission.READ_LOGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:82:5-85:47
	tools:ignore
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:85:9-44
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:84:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:83:9-52
uses-permission#android.permission.VIBRATE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:86:5-88:36
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:17:5-66
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:17:5-66
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:88:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:87:9-50
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:89:5-91:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:91:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:90:9-67
uses-permission#android.permission.BLUETOOTH
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:92:5-94:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:94:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:93:9-52
uses-feature#android.hardware.Camera
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:96:5-60
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:96:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:97:5-99:36
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:15:5-70
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:15:5-70
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:99:9-33
		MERGED from D:\cs\tlk\app\src\main\AndroidManifest.xml:99:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:98:9-57
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:101:5-103:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:103:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:102:9-63
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:104:5-106:36
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:12:5-79
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:12:5-79
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:106:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:105:9-63
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:107:5-109:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:109:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:108:9-60
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:110:5-112:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:112:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:111:9-65
uses-permission#com.android.launcher.permission.READ_SETTINGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:113:5-115:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:115:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:114:9-69
uses-permission#android.permission.BROADCAST_STICKY
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:116:5-118:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:118:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:117:9-59
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:119:5-121:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:121:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:120:9-79
uses-permission#com.meizu.flyme.push.permission.RECEIVE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:126:5-128:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:128:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:127:9-63
uses-permission#android.permission.CAMERA
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:132:5-65
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:9:5-65
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:9:5-65
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:11:5-65
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:11:5-65
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:132:22-62
uses-permission#android.permission.FLASHLIGHT
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:133:5-69
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:12:5-69
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:12:5-69
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:133:22-66
uses-permission#com.meizu.c2dm.permission.RECEIVE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:143:5-145:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:145:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:144:9-57
uses-permission#com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:156:5-158:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:158:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:157:9-70
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:170:5-172:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:172:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:171:9-75
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:173:5-175:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:175:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:174:9-70
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:176:5-178:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:178:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:177:9-71
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:179:5-181:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:181:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:180:9-66
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:182:5-184:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:184:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:183:9-67
uses-permission#android.permission.READ_APP_BADGE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:185:5-187:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:187:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:186:9-57
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:188:5-190:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:190:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:189:9-65
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:191:5-193:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:193:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:192:9-67
uses-permission#android.permission.ACCESS_LOCATION_EXTRA_COMMANDS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:194:5-196:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:196:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:195:9-73
uses-permission#android.permission.USE_FINGERPRINT
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:197:5-199:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:199:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:198:9-58
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:200:5-202:36
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:9:5-77
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:9:5-77
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:202:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:201:9-61
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:203:5-205:36
	android:required
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:205:9-33
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:204:9-66
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:206:5-208:53
	tools:ignore
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:208:9-50
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:207:9-61
application
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:210:5-405:19
MERGED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:10:5-18:19
MERGED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:10:5-18:19
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0689f52c8d09f0e15ee5135366e3b8c\transformed\material-1.4.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0689f52c8d09f0e15ee5135366e3b8c\transformed\material-1.4.0\AndroidManifest.xml:22:5-20
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:19:5-22:38
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:19:5-22:38
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:29:5-44:19
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:29:5-44:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\182f7f156563cb96f7fc7a93fbb671d8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\182f7f156563cb96f7fc7a93fbb671d8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecd36bd943f1ed64ccb906a67b7a5e11\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecd36bd943f1ed64ccb906a67b7a5e11\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.chauthai.swipereveallayout:swipe-reveal-layout:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2fa7c824e387c5822aef829203a8f1b2\transformed\jetified-swipe-reveal-layout-1.4.1\AndroidManifest.xml:12:5-20
MERGED from [com.chauthai.swipereveallayout:swipe-reveal-layout:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2fa7c824e387c5822aef829203a8f1b2\transformed\jetified-swipe-reveal-layout-1.4.1\AndroidManifest.xml:12:5-20
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:7:5-12:19
MERGED from [io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fec2a9a58e6240597ac57894939cebb7\transformed\jetified-GreenDaoUpgradeHelper-v2.2.1\AndroidManifest.xml:11:5-14:19
MERGED from [io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fec2a9a58e6240597ac57894939cebb7\transformed\jetified-GreenDaoUpgradeHelper-v2.2.1\AndroidManifest.xml:11:5-14:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac42345df0fde9d7214f6fd960e9b5c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac42345df0fde9d7214f6fd960e9b5c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d000b537175726c02faf9d0b0b549af2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d000b537175726c02faf9d0b0b549af2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-6] C:\Users\<USER>\.gradle\caches\transforms-3\924afc56e026b49ad40635f04d8fbc7e\transformed\jetified-SmartRefreshLayout-1.1.0-alpha-6\AndroidManifest.xml:11:5-15:19
MERGED from [com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-6] C:\Users\<USER>\.gradle\caches\transforms-3\924afc56e026b49ad40635f04d8fbc7e\transformed\jetified-SmartRefreshLayout-1.1.0-alpha-6\AndroidManifest.xml:11:5-15:19
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:22:5-29:19
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:22:5-29:19
MERGED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:11:5-17:19
MERGED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:11:5-17:19
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:11:5-16:19
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:11:5-16:19
MERGED from [com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-26] C:\Users\<USER>\.gradle\caches\transforms-3\e396990e4cedac72d7ee6505fbd880a1\transformed\jetified-SmartRefreshHeader-1.1.0-alpha-26\AndroidManifest.xml:11:5-15:19
MERGED from [com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-26] C:\Users\<USER>\.gradle\caches\transforms-3\e396990e4cedac72d7ee6505fbd880a1\transformed\jetified-SmartRefreshHeader-1.1.0-alpha-26\AndroidManifest.xml:11:5-15:19
MERGED from [q.rorbin:VerticalTabLayout:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ddd6860bb2e9e1d3218e8792ee8bc7a\transformed\jetified-VerticalTabLayout-1.2.5\AndroidManifest.xml:11:5-20
MERGED from [q.rorbin:VerticalTabLayout:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ddd6860bb2e9e1d3218e8792ee8bc7a\transformed\jetified-VerticalTabLayout-1.2.5\AndroidManifest.xml:11:5-20
MERGED from [q.rorbin:badgeview:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f8d5eecf00cf80180942b100fc5a7307\transformed\jetified-badgeview-1.1.2\AndroidManifest.xml:11:5-20
MERGED from [q.rorbin:badgeview:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f8d5eecf00cf80180942b100fc5a7307\transformed\jetified-badgeview-1.1.2\AndroidManifest.xml:11:5-20
	android:requestLegacyExternalStorage
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:217:9-52
	android:roundIcon
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:218:9-45
	android:largeHeap
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:216:9-33
	android:icon
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:214:9-40
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:219:9-35
	android:label
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:215:9-41
	android:hardwareAccelerated
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:213:9-43
	android:allowBackup
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:212:9-35
		REJECTED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:20:9-35
		REJECTED from [io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fec2a9a58e6240597ac57894939cebb7\transformed\jetified-GreenDaoUpgradeHelper-v2.2.1\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:220:9-40
	tools:replace
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:222:9-44
	android:usesCleartextTraffic
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:221:9-44
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:211:9-43
activity#com.Tlock.io.activity.cosmos.Test1Activity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:223:9-225:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:225:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:224:13-58
activity#com.Tlock.io.activity.cosmos.AllowanceActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:226:9-228:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:228:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:227:13-62
activity#com.Tlock.io.activity.cosmos.EditTopicActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:229:9-231:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:231:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:230:13-62
activity#com.Tlock.io.activity.cosmos.MangerActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:232:9-234:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:234:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:233:13-59
activity#com.Tlock.io.activity.cosmos.TransactionListActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:235:9-237:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:237:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:236:13-68
activity#com.Tlock.io.activity.cosmos.TransactionInfoActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:238:9-240:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:240:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:239:13-68
activity#com.Tlock.io.activity.cosmos.TransferInfoActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:241:9-243:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:243:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:242:13-65
activity#com.Tlock.io.activity.cosmos.ReceiveActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:244:9-246:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:246:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:245:13-60
activity#com.Tlock.io.activity.cosmos.TransferActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:247:9-249:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:249:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:248:13-61
activity#com.Tlock.io.activity.cosmos.TransferHistoryActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:250:9-252:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:252:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:251:13-68
activity#com.Tlock.io.activity.cosmos.WalletInfoActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:253:9-255:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:255:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:254:13-63
activity#com.Tlock.io.activity.cosmos.SearchActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:256:9-258:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:258:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:257:13-59
activity#com.Tlock.io.activity.cosmos.EditProfileActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:259:9-261:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:261:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:260:13-64
activity#com.Tlock.io.activity.cosmos.UserInfoActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:262:9-265:46
	android:launchMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:265:13-43
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:264:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:263:13-61
activity#com.Tlock.io.activity.cosmos.MTaskDetailActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:266:9-268:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:268:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:267:13-64
activity#com.Tlock.io.activity.cosmos.MTaskListActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:269:9-271:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:271:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:270:13-62
activity#com.Tlock.io.activity.cosmos.MTopicDetailActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:272:9-274:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:274:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:273:13-65
activity#com.Tlock.io.activity.cosmos.MTopicSearchActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:275:9-277:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:277:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:276:13-65
activity#com.Tlock.io.activity.cosmos.FollowingListActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:278:9-280:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:280:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:279:13-66
activity#com.Tlock.io.activity.cosmos.CollectListActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:281:9-283:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:283:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:282:13-64
activity#com.Tlock.io.activity.wallet.WalletOrderActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:284:9-286:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:286:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:285:13-64
activity#com.Tlock.io.activity.cosmos.MMemberListActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:287:9-290:46
	android:launchMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:290:13-43
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:289:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:288:13-64
activity#com.Tlock.io.activity.cosmos.TopicActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:291:9-293:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:293:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:292:13-58
activity#com.Tlock.io.activity.cosmos.PostQuoteActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:294:9-296:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:296:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:295:13-62
activity#com.Tlock.io.activity.cosmos.ContentInfoActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:297:9-300:46
	android:launchMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:300:13-43
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:299:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:298:13-64
activity#com.Tlock.io.activity.cosmos.TopicDetailActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:301:9-303:40
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:303:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:302:13-64
activity#com.Tlock.io.activity.wallet.WalletSettingActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:305:9-308:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:308:13-49
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:307:13-37
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:306:13-66
activity#com.Tlock.io.activity.wallet.NewWalletActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:309:9-317:20
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:313:13-49
	android:launchMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:312:13-44
	android:windowSoftInputMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:315:13-52
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:311:13-36
	android:theme
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:314:13-47
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:310:13-62
activity#com.Tlock.io.MainActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:319:9-330:20
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:323:13-49
	android:launchMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:322:13-44
	android:windowSoftInputMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:325:13-52
	android:exported
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:321:13-36
	android:theme
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:324:13-47
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:320:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:326:13-329:29
action#android.intent.action.MAIN
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:327:17-69
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:327:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:328:17-77
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:328:27-74
activity#com.Tlock.io.activity.cosmos.PostActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:333:9-336:58
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:335:13-49
	android:windowSoftInputMode
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:336:13-55
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:334:13-57
activity#com.Tlock.io.activity.wallet.AssetDetailsActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:345:9-347:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:347:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:346:13-65
activity#com.Tlock.io.activity.wallet.InputWalletActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:350:9-352:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:352:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:351:13-64
activity#com.Tlock.io.activity.wallet.BackupActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:353:9-355:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:355:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:354:13-59
activity#com.Tlock.io.activity.wallet.ToBackupMnemonicsActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:357:9-359:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:359:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:358:13-70
activity#com.Tlock.io.activity.wallet.BackupPrivateKeyActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:360:9-362:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:362:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:361:13-69
activity#com.Tlock.io.activity.wallet.CheckMnemonicsActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:363:9-365:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:365:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:364:13-67
activity#com.Tlock.io.activity.wallet.BackupMnemonicsActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:366:9-368:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:368:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:367:13-68
activity#com.Tlock.io.activity.wallet.CreateWalletActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:369:9-371:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:371:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:370:13-65
activity#com.Tlock.io.activity.wallet.ShareAddressActivity
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:373:9-375:52
	android:screenOrientation
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:375:13-49
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:374:13-65
meta-data#design_width_in_dp
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:395:9-397:35
	android:value
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:397:13-32
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:396:13-46
meta-data#design_height_in_dp
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:398:9-400:35
	android:value
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:400:13-32
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:399:13-47
meta-data#com.Tlock.io.widget.QiNiuModule
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:401:9-403:43
	android:value
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:403:13-40
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:402:13-59
uses-sdk
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
MERGED from [com.github.AnJiaoDe:TabLayoutNiubility:V1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bc273b064940e5148e2dd819bd82111\transformed\jetified-TabLayoutNiubility-V1.3.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.AnJiaoDe:TabLayoutNiubility:V1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bc273b064940e5148e2dd819bd82111\transformed\jetified-TabLayoutNiubility-V1.3.1\AndroidManifest.xml:7:5-9:41
MERGED from [:proto] D:\cs\tlk\proto\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:proto] D:\cs\tlk\proto\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.wkp:DragGridView:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7d7d522cd658a74e2fd0ed4c43471ca\transformed\jetified-DragGridView-1.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.wkp:DragGridView:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7d7d522cd658a74e2fd0ed4c43471ca\transformed\jetified-DragGridView-1.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0689f52c8d09f0e15ee5135366e3b8c\transformed\material-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0689f52c8d09f0e15ee5135366e3b8c\transformed\material-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:8:5-10:41
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.li-xiaojun:EasyAdapter:1.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\a141764184f3520f2520d485daba8133\transformed\jetified-EasyAdapter-1.2.8\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.li-xiaojun:EasyAdapter:1.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\a141764184f3520f2520d485daba8133\transformed\jetified-EasyAdapter-1.2.8\AndroidManifest.xml:5:5-7:41
MERGED from [com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaab13da6bf9b59bcb67a2fbc6db35d9\transformed\jetified-subsampling-scale-image-view-androidx-3.10.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaab13da6bf9b59bcb67a2fbc6db35d9\transformed\jetified-subsampling-scale-image-view-androidx-3.10.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\182f7f156563cb96f7fc7a93fbb671d8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\182f7f156563cb96f7fc7a93fbb671d8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8960aa6e858281122249b12ced85f681\transformed\appcompat-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\8960aa6e858281122249b12ced85f681\transformed\appcompat-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [io.noties.markwon:image-glide:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d0b84d648f7a5fc0b554a06164d4f55e\transformed\jetified-image-glide-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:image-glide:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d0b84d648f7a5fc0b554a06164d4f55e\transformed\jetified-image-glide-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecd36bd943f1ed64ccb906a67b7a5e11\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecd36bd943f1ed64ccb906a67b7a5e11\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.youth.banner:banner:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d7af008fffeff50bd39e243281e0ad7\transformed\jetified-banner-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.youth.banner:banner:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d7af008fffeff50bd39e243281e0ad7\transformed\jetified-banner-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32284c76bdfbeb6e8dcca7975c3343f5\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32284c76bdfbeb6e8dcca7975c3343f5\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.chauthai.swipereveallayout:swipe-reveal-layout:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2fa7c824e387c5822aef829203a8f1b2\transformed\jetified-swipe-reveal-layout-1.4.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.chauthai.swipereveallayout:swipe-reveal-layout:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2fa7c824e387c5822aef829203a8f1b2\transformed\jetified-swipe-reveal-layout-1.4.1\AndroidManifest.xml:8:5-10:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1468f29970f2712d0e98b120182edec8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1468f29970f2712d0e98b120182edec8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\91709e0713fac362714c9349f7d1ce83\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\91709e0713fac362714c9349f7d1ce83\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\9b412801109f33e6dfbf373705b5fb8b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\9b412801109f33e6dfbf373705b5fb8b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\13986cb14e2338c15b9651bb76db97a1\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\13986cb14e2338c15b9651bb76db97a1\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.jakewharton:butterknife:10.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\377a463887b3d7d6a87b1a60321ab67e\transformed\jetified-butterknife-10.2.3\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:butterknife:10.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\377a463887b3d7d6a87b1a60321ab67e\transformed\jetified-butterknife-10.2.3\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:butterknife-runtime:10.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\ae9500df773a8acd02eb17e827face0b\transformed\jetified-butterknife-runtime-10.2.3\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:butterknife-runtime:10.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\ae9500df773a8acd02eb17e827face0b\transformed\jetified-butterknife-runtime-10.2.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e14b8e111e59202da42ff67a79c9719\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e14b8e111e59202da42ff67a79c9719\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f18b2d51800fd0723fd2d891e76a516\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f18b2d51800fd0723fd2d891e76a516\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e08591c2a0cd67a82b0591336f06c08\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e08591c2a0cd67a82b0591336f06c08\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cd61a1b434f2c9ef6a0181731792847\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cd61a1b434f2c9ef6a0181731792847\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a55c04ed0b8b266163c947c5bb949a4f\transformed\jetified-appcompat-resources-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a55c04ed0b8b266163c947c5bb949a4f\transformed\jetified-appcompat-resources-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd5e38fb4d2f03df5df9472d0ead9776\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd5e38fb4d2f03df5df9472d0ead9776\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3349f477c947a262ac511503dc1c5971\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3349f477c947a262ac511503dc1c5971\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\241adab02557883c730c4d052544d34b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\241adab02557883c730c4d052544d34b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa6bed2e40ae8ad1ac8c2486742fbeae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa6bed2e40ae8ad1ac8c2486742fbeae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c6ecafa87f845531d5f0592c23ea71d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c6ecafa87f845531d5f0592c23ea71d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\02d87dff3da90183c5324e5bf8509f2d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\02d87dff3da90183c5324e5bf8509f2d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\257abaa2293977b0a0c962d6006c44ab\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\257abaa2293977b0a0c962d6006c44ab\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53c8ddc09b3bed8593b00eceac1a1f65\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53c8ddc09b3bed8593b00eceac1a1f65\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\96093cbadab35e65a1781be443e6a3a3\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\96093cbadab35e65a1781be443e6a3a3\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c07390ef56dc6697b698de5e4ed81ac\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c07390ef56dc6697b698de5e4ed81ac\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47da799b4f7714068fd34e862819e5c3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47da799b4f7714068fd34e862819e5c3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff55c4a018ffbfa710f47ef90eb07256\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff55c4a018ffbfa710f47ef90eb07256\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8c2a0fcd9f7f806b16d31389347783b4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8c2a0fcd9f7f806b16d31389347783b4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e488675d250508e2d28e35b9d8dc622\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e488675d250508e2d28e35b9d8dc622\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ff5a9a7d03aa41a285e7ec5269ba9e8c\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ff5a9a7d03aa41a285e7ec5269ba9e8c\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\2c869b67198a961a151ad23f45b2161a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\2c869b67198a961a151ad23f45b2161a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c489a767b7d02d5bfc03dce3b96e660b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c489a767b7d02d5bfc03dce3b96e660b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\0585910deac312766da6ba1c9f006342\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\0585910deac312766da6ba1c9f006342\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:5:5-44
MERGED from [com.github.bigmanLau:CardviewFix:1.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\7232001893b8fd855a9050c84c5f81fc\transformed\jetified-CardviewFix-1.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.bigmanLau:CardviewFix:1.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\7232001893b8fd855a9050c84c5f81fc\transformed\jetified-CardviewFix-1.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\29c835fd9f60d2cac6cc876aab58e560\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\29c835fd9f60d2cac6cc876aab58e560\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fec2a9a58e6240597ac57894939cebb7\transformed\jetified-GreenDaoUpgradeHelper-v2.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.yuweiguocn:GreenDaoUpgradeHelper:v2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fec2a9a58e6240597ac57894939cebb7\transformed\jetified-GreenDaoUpgradeHelper-v2.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:linkify:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bc3e1a108f38a05e2dd5de0637d22e4\transformed\jetified-linkify-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:linkify:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bc3e1a108f38a05e2dd5de0637d22e4\transformed\jetified-linkify-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:core:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c5e7f02446cf34b31ff124ab8161dedf\transformed\jetified-core-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:core:4.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c5e7f02446cf34b31ff124ab8161dedf\transformed\jetified-core-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.contrarywind:Android-PickerView:4.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c2715fd91e86f3c9b6a97aa30509a8a8\transformed\jetified-Android-PickerView-4.1.9\AndroidManifest.xml:7:5-9:41
MERGED from [com.contrarywind:Android-PickerView:4.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c2715fd91e86f3c9b6a97aa30509a8a8\transformed\jetified-Android-PickerView-4.1.9\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a115c79be891815e6fef390e180a443\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a115c79be891815e6fef390e180a443\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac42345df0fde9d7214f6fd960e9b5c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\cac42345df0fde9d7214f6fd960e9b5c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e25e587af16a8e12a8ca36060b070f1f\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e25e587af16a8e12a8ca36060b070f1f\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\059ced8ab92e9783a33f05c243886b5e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\059ced8ab92e9783a33f05c243886b5e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.contrarywind:wheelview:4.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\30c3d77390feafaf3242c902bae1b2b2\transformed\jetified-wheelview-4.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.contrarywind:wheelview:4.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\30c3d77390feafaf3242c902bae1b2b2\transformed\jetified-wheelview-4.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\517c492ee2e7eba1a71c0c9bb474f740\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\517c492ee2e7eba1a71c0c9bb474f740\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d000b537175726c02faf9d0b0b549af2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d000b537175726c02faf9d0b0b549af2\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2844585aca0b2f9e972d583cddaccd4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2844585aca0b2f9e972d583cddaccd4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\15cead8b21eace7778c0db27b9e01387\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\15cead8b21eace7778c0db27b9e01387\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91f549ed0fa3030f8314becf430eadde\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91f549ed0fa3030f8314becf430eadde\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd4b5661d261566e42f83842de5c192\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fd4b5661d261566e42f83842de5c192\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\68210ff09168caf82545a96e10b80d16\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\68210ff09168caf82545a96e10b80d16\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-6] C:\Users\<USER>\.gradle\caches\transforms-3\924afc56e026b49ad40635f04d8fbc7e\transformed\jetified-SmartRefreshLayout-1.1.0-alpha-6\AndroidManifest.xml:7:5-9:41
MERGED from [com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-6] C:\Users\<USER>\.gradle\caches\transforms-3\924afc56e026b49ad40635f04d8fbc7e\transformed\jetified-SmartRefreshLayout-1.1.0-alpha-6\AndroidManifest.xml:7:5-9:41
MERGED from [io.reactivex:rxandroid:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a8cdef720e297de67dcf990bddc6c4c7\transformed\jetified-rxandroid-1.0.1\AndroidManifest.xml:19:5-43
MERGED from [io.reactivex:rxandroid:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a8cdef720e297de67dcf990bddc6c4c7\transformed\jetified-rxandroid-1.0.1\AndroidManifest.xml:19:5-43
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.trustwallet:wallet-core:2.6.3] C:\Users\<USER>\.gradle\caches\transforms-3\b77d237395e2b42d086a421b053e6643\transformed\jetified-wallet-core-2.6.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.trustwallet:wallet-core:2.6.3] C:\Users\<USER>\.gradle\caches\transforms-3\b77d237395e2b42d086a421b053e6643\transformed\jetified-wallet-core-2.6.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:6:5-43
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:6:5-43
MERGED from [com.hyman:flowlayout-lib:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f6e87a1beca5b6273eb5df37b6788cdd\transformed\jetified-flowlayout-lib-1.1.2\AndroidManifest.xml:8:5-10:41
MERGED from [com.hyman:flowlayout-lib:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f6e87a1beca5b6273eb5df37b6788cdd\transformed\jetified-flowlayout-lib-1.1.2\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.donkingliang:ConsecutiveScroller:4.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\8a27acb9e77f484805afc42353431a7c\transformed\jetified-ConsecutiveScroller-4.6.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.donkingliang:ConsecutiveScroller:4.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\8a27acb9e77f484805afc42353431a7c\transformed\jetified-ConsecutiveScroller-4.6.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-26] C:\Users\<USER>\.gradle\caches\transforms-3\e396990e4cedac72d7ee6505fbd880a1\transformed\jetified-SmartRefreshHeader-1.1.0-alpha-26\AndroidManifest.xml:7:5-9:41
MERGED from [com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-26] C:\Users\<USER>\.gradle\caches\transforms-3\e396990e4cedac72d7ee6505fbd880a1\transformed\jetified-SmartRefreshHeader-1.1.0-alpha-26\AndroidManifest.xml:7:5-9:41
MERGED from [com.crazysunj:mtrva:2.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\8093159662bfd4052a33ae82743f638e\transformed\jetified-mtrva-2.5.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.crazysunj:mtrva:2.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\8093159662bfd4052a33ae82743f638e\transformed\jetified-mtrva-2.5.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.scwang90:refresh-header-material:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\eab26ff1e8133c533f88d284bc10c9c8\transformed\jetified-refresh-header-material-2.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-header-material:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\eab26ff1e8133c533f88d284bc10c9c8\transformed\jetified-refresh-header-material-2.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.warkiz.widget:indicatorseekbar:2.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\d81ca3195213575aefabf73ca12ccfa0\transformed\jetified-indicatorseekbar-2.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.warkiz.widget:indicatorseekbar:2.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\d81ca3195213575aefabf73ca12ccfa0\transformed\jetified-indicatorseekbar-2.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [q.rorbin:VerticalTabLayout:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ddd6860bb2e9e1d3218e8792ee8bc7a\transformed\jetified-VerticalTabLayout-1.2.5\AndroidManifest.xml:7:5-9:41
MERGED from [q.rorbin:VerticalTabLayout:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ddd6860bb2e9e1d3218e8792ee8bc7a\transformed\jetified-VerticalTabLayout-1.2.5\AndroidManifest.xml:7:5-9:41
MERGED from [q.rorbin:badgeview:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f8d5eecf00cf80180942b100fc5a7307\transformed\jetified-badgeview-1.1.2\AndroidManifest.xml:7:5-9:41
MERGED from [q.rorbin:badgeview:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f8d5eecf00cf80180942b100fc5a7307\transformed\jetified-badgeview-1.1.2\AndroidManifest.xml:7:5-9:41
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml
		INJECTED from D:\cs\tlk\app\src\main\AndroidManifest.xml
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:390:13-392:58
	android:resource
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:392:17-55
	android:name
		ADDED from D:\cs\tlk\app\src\main\AndroidManifest.xml:391:17-67
meta-data#android.notch_support
ADDED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:11:9-13:36
	android:value
		ADDED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:13:13-33
	android:name
		ADDED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:12:13-49
activity#com.lxj.xpopup.util.XPermission$PermissionActivity
ADDED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:15:9-17:75
	android:theme
		ADDED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:17:13-72
	android:name
		ADDED from [com.github.li-xiaojun:XPopup:2.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe0ffb90c4cc2173002321ae627b6d3\transformed\jetified-XPopup-2.10.0\AndroidManifest.xml:16:13-78
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:15:5-17:47
	tools:ignore
		ADDED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:17:9-44
	android:name
		ADDED from [com.github.z244370114:DeviceLibray:1.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\9f24728db114076a9bce5da8898ae0df\transformed\jetified-DeviceLibray-1.0.4\AndroidManifest.xml:16:9-70
queries
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:14:5-27:15
intent#action:name:android.intent.action.PICK
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:15:9-17:18
action#android.intent.action.PICK
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:16:13-65
	android:name
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:16:21-62
intent#action:name:android.intent.action.GET_CONTENT
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:18:9-20:18
action#android.intent.action.GET_CONTENT
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:19:13-72
	android:name
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:19:21-69
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:21:9-23:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:22:13-73
	android:name
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:22:21-70
intent#action:name:com.android.camera.action.CROP
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:24:9-26:18
action#com.android.camera.action.CROP
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:25:13-69
	android:name
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:25:21-66
provider#com.sl.utakephoto.TakePhotoProvider
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:30:9-38:20
	android:grantUriPermissions
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:34:13-47
	android:authorities
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:32:13-64
	android:exported
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:33:13-37
	android:name
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:31:13-63
activity#com.sl.utakephoto.crop.CropActivity
ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:40:9-43:49
	android:configChanges
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:42:13-74
	android:theme
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:43:13-46
	android:name
		ADDED from [io.github.songlonggithub:uTakePhoto:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5241836f7a80c7ffe906929d97cad36\transformed\jetified-uTakePhoto-1.2.0\AndroidManifest.xml:41:13-63
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7a31481da876c53081acd9a9aa860f8\transformed\jetified-okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.Tlock.io.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.Tlock.io.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\505b3688473c724d9c868193f5201a6b\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
provider#com.squareup.picasso.PicassoProvider
ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\transforms-3\1d99392eac8ccfe334b75f7aa3897ba2\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
provider#androidx.startup.InitializationProvider
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e7629931206877e7fff841b620f2896\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:25:13-67
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ab74f4ed7c5d4829063b8c48984a3266\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-feature#android.hardware.camera
ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:14:5-60
	android:name
		ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:14:19-57
activity#com.yzq.zxinglibrary.android.CaptureActivity
ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:23:9-28:67
	android:screenOrientation
		ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:26:13-49
	android:windowSoftInputMode
		ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:28:13-64
	android:configChanges
		ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:25:13-59
	android:theme
		ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:27:13-63
	android:name
		ADDED from [com.github.yuzhiqiang1993:zxing:2.2.8] C:\Users\<USER>\.gradle\caches\transforms-3\e2803939f49d234b1bb2c9c5c4d3bbd9\transformed\jetified-zxing-2.2.8\AndroidManifest.xml:24:13-72
provider#me.jessyan.autosize.InitProvider
ADDED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:12:9-16:43
	android:authorities
		ADDED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:14:13-74
	android:multiprocess
		ADDED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:16:13-40
	android:exported
		ADDED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:15:13-37
	android:name
		ADDED from [com.github.JessYanCoding:AndroidAutoSize:v1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac92c78bd87c4bcf456f598f9c92eb44\transformed\jetified-AndroidAutoSize-v1.2.1\AndroidManifest.xml:13:13-60
service#com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService
ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
	android:name
		ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
service#com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService
ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
	android:process
		ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
	android:name
		ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-3\363ef18c6e46fb3b1a8cbabd54565330\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
