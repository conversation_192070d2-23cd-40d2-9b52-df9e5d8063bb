package com.Tlock.io.fragment;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.TopicDetailActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.ContentPagerAdapter;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.cosmos.TopicTabBean;
import com.Tlock.io.fragment.cosmos.TopicListFragment;
import com.Tlock.io.itemBean.cosmos.TopicTabView;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

import butterknife.BindView;


public class IndexFragment extends LazyLoadBaseFragment {


    @BindView(R.id.ed_search)
    EditText mEdSearch;
    @BindView(R.id.vp_sort)
    ViewPager2 mVpSort;
    @BindView(R.id.main)
    RelativeLayout mMain;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.tab1)
    TopicTabView mTab1;
    @BindView(R.id.tab2)
    TopicTabView mTab2;
    @BindView(R.id.tab3)
    TopicTabView mTab3;
    @BindView(R.id.tab4)
    TopicTabView mTab4;
    @BindView(R.id.tab5)
    TopicTabView mTab5;
    @BindView(R.id.tab6)
    TopicTabView mTab6;
    @BindView(R.id.tab7)
    TopicTabView mTab7;
    @BindView(R.id.tab8)
    TopicTabView mTab8;
    @BindView(R.id.ll_tab)
    LinearLayout mLlTab;
    private ArrayList<Fragment> fragments = new ArrayList<>();
    private ArrayList<TopicTabBean> titles = new ArrayList<>();
    private BaseRecyclerViewAdapter<TopicTabBean> adapter;
    private int indexPosition = 1;

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_index;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        //初始化dex列表
//        initRecycleView();
        mEdSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String string = charSequence.toString();
                TopicListFragment fragment = (TopicListFragment) fragments.get(indexPosition);
                if (TextUtils.isEmpty(string)) {
                    mLlTab.setVisibility(View.VISIBLE);
//                    Log.e(TAG, "onTextChanged: 取消了搜索" );
                    ((TopicListFragment) fragments.get(indexPosition)).getTopicList();
                } else {
                    mLlTab.setVisibility(View.GONE);
//                    Log.e(TAG, "onTextChanged: 执行了搜索" );
                    fragment.getTopicData(string);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTokenChane(Event event) {
        if (event.getMessgae().equalsIgnoreCase("topic_select")) {
            if (isSupportVisible()) {
                PostQueryProto.TopicResponse data = (PostQueryProto.TopicResponse) event.getObj();
                TopicDetailActivity.start(getActivity(), JsonUtils.objectToJson(data));
            }
        }
    }

    private void initFragment() {
        fragments.add(new TopicListFragment("", false));
        ContentPagerAdapter contentPagerAdapter = new ContentPagerAdapter(getChildFragmentManager(), getLifecycle(), fragments);
        mVpSort.setAdapter(contentPagerAdapter);
        mVpSort.setCurrentItem(1);
        mVpSort.setUserInputEnabled(false);
        mVpSort.setOffscreenPageLimit(2);
    }

    @Override
    protected void loadData() {
        getTabList();
    }

    private void getTabList() {

        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.CategoryResponse> categories = CosmosUtils.getCategories();
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ArrayList<TopicTabBean> objects = new ArrayList<>();
                        objects.add(new TopicTabBean("Favorite", false, "0"));
                        TopicListFragment topicListFragment = new TopicListFragment("0", false);
                        fragments.add(topicListFragment);
                        for (int i = 0; i < categories.size(); i++) {
                            PostQueryProto.CategoryResponse category = categories.get(i);
                            TopicTabBean topicTabBean = new TopicTabBean(category.getName(), i == 0 ? true : false, category.getId());
                            objects.add(topicTabBean);
                            TopicListFragment topicListFragment1 = new TopicListFragment(category.getId(), false);
                            fragments.add(topicListFragment1);
                        }
//                        adapter.setList(objects);
                        setTab(objects);
                        initFragment();
                        SpUtil.setTopicClassify(categories);
                    }
                });
            }
        });
    }

    private void setTab(ArrayList<TopicTabBean> tabs) {
        int childCount = mLlTab.getChildCount();
        for (int i = 0; i < childCount; i++) {
            TopicTabView childAt = (TopicTabView) mLlTab.getChildAt(i);
            childAt.setData(tabs.get(i), i);
            int finalI = i;
            childAt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    TopicTabView lastView = (TopicTabView) mLlTab.getChildAt(indexPosition);
                    lastView.setSelect(false);

                    TopicTabView childAt = (TopicTabView) view;
                    childAt.setSelect(true);

                    mLlTab.requestFocus();
                    indexPosition = finalI;
                    mVpSort.setCurrentItem(finalI, false);
                    ((TopicListFragment) fragments.get(finalI)).getTopicList();
                }
            });
        }

    }
}