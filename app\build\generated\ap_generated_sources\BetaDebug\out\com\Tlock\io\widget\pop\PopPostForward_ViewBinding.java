// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopPostForward_ViewBinding implements Unbinder {
  private PopPostForward target;

  private View view7f090361;

  private View view7f0902ff;

  @UiThread
  public PopPostForward_ViewBinding(PopPostForward target) {
    this(target, target);
  }

  @UiThread
  public PopPostForward_ViewBinding(final PopPostForward target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.tv_repost, "method 'onBindClick'");
    view7f090361 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_Quote, "method 'onBindClick'");
    view7f0902ff = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    target = null;


    view7f090361.setOnClickListener(null);
    view7f090361 = null;
    view7f0902ff.setOnClickListener(null);
    view7f0902ff = null;
  }
}
