<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_comment"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_15">

    <ImageView
        android:id="@+id/iv_heard"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginLeft="@dimen/dp_4"
        android:layout_marginRight="@dimen/dp_8" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_account_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/iv_heard"
        android:layout_marginBottom="@dimen/dp_5"
        android:layout_toRightOf="@id/iv_heard"
        android:maxWidth="@dimen/dp_150"
        android:singleLine="true"
        android:text="name"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_14"
        app:fontType="name" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_account_name"
        android:layout_toRightOf="@id/iv_heard"
        android:lineHeight="@dimen/dp_19"
        android:textColor="@color/blue_text"
        android:textSize="@dimen/sp_13"
        app:fontFamily="@font/font_family"
        app:fontType="content" />

    <com.Tlock.io.widget.FontTextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_handle"
        android:layout_marginBottom="@dimen/dp_10"
        android:layout_toRightOf="@id/iv_heard"
        android:lineHeight="@dimen/dp_19"
        android:textColor="@color/cosmos_black"
        android:textSize="@dimen/sp_15"
        app:fontFamily="@font/font_family"
        app:fontType="content" />

    <ImageView
        android:id="@+id/iv_post"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_content"
        android:layout_marginLeft="@dimen/dp_60"
        android:layout_marginBottom="@dimen/dp_12"
        android:adjustViewBounds="true"
        android:maxHeight="@dimen/dp_200" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/constraint"
        android:layout_alignBottom="@id/constraint"
        android:layout_toRightOf="@id/iv_heard"
        android:text="time"
        android:textColor="@color/cosmos_default"
        android:textSize="@dimen/sp_12" />

    <TextView
        android:id="@+id/tv_isShow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_time"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_toRightOf="@id/iv_heard"
        android:includeFontPadding="true"
        android:text="19 Replies In Total >"
        android:textColor="@color/cosmos_topic"
        android:textSize="@dimen/sp_12" />

    <LinearLayout
        android:id="@+id/constraint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_post"
        android:layout_alignParentRight="true"
        android:gravity="right"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/ll_review"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/icon_share_post"
            android:gravity="center"
            android:paddingLeft="@dimen/dp_10"
            android:paddingRight="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/ll_share"
            app:layout_constraintRight_toLeftOf="@id/ll_praise"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_review"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_comment" />

            <TextView
                android:id="@+id/tv_review"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_review"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_praise"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/icon_share_post"
            android:gravity="center"
            android:paddingLeft="@dimen/dp_10"
            android:paddingRight="@dimen/dp_10"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/ll_review"
            app:layout_constraintRight_toLeftOf="@id/ll_Collect"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_praise"
                android:layout_width="@dimen/dp_15"
                android:layout_height="@dimen/dp_15"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_7"
                android:src="@mipmap/icon_post_like" />

            <TextView
                android:id="@+id/tv_praise"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@id/iv_praise"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_12" />

        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_comment_child"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_isShow"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_toRightOf="@id/iv_heard"
        android:orientation="vertical"
        android:visibility="gone" />

    <View
        android:id="@+id/line1"
        style="@style/deep_gray_horizontal_line_view"
        android:layout_below="@id/ll_comment_child"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="@dimen/dp_15" />


</RelativeLayout>

