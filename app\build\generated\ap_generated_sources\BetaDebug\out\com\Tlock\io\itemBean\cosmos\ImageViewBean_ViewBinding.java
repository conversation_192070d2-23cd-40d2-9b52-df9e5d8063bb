// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class ImageViewBean_ViewBinding implements Unbinder {
  private ImageViewBean target;

  private View view7f090155;

  private View view7f090147;

  @UiThread
  public ImageViewBean_ViewBinding(ImageViewBean target) {
    this(target, target);
  }

  @UiThread
  public ImageViewBean_ViewBinding(final ImageViewBean target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_image, "field 'mIvImage' and method 'onBindClick'");
    target.mIvImage = Utils.castView(view, R.id.iv_image, "field 'mIvImage'", ImageView.class);
    view7f090155 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.iv_delete, "field 'mIvDelete' and method 'onBindClick'");
    target.mIvDelete = Utils.castView(view, R.id.iv_delete, "field 'mIvDelete'", ImageView.class);
    view7f090147 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    ImageViewBean target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvImage = null;
    target.mIvDelete = null;

    view7f090155.setOnClickListener(null);
    view7f090155 = null;
    view7f090147.setOnClickListener(null);
    view7f090147 = null;
  }
}
