// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class WalletViewBean_ViewBinding implements Unbinder {
  private WalletViewBean target;

  private View view7f090300;

  @UiThread
  public WalletViewBean_ViewBinding(WalletViewBean target) {
    this(target, target);
  }

  @UiThread
  public WalletViewBean_ViewBinding(final WalletViewBean target, View source) {
    this.target = target;

    View view;
    target.mTvName = Utils.findRequiredViewAsType(source, R.id.tv_name, "field 'mTvName'", TextView.class);
    view = Utils.findRequiredView(source, R.id.tv_address, "field 'mTvAddress' and method 'onBindClick'");
    target.mTvAddress = Utils.castView(view, R.id.tv_address, "field 'mTvAddress'", TextView.class);
    view7f090300 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvSelect = Utils.findRequiredViewAsType(source, R.id.iv_select, "field 'mIvSelect'", ImageView.class);
    target.mTvBalance = Utils.findRequiredViewAsType(source, R.id.Tv_balance, "field 'mTvBalance'", TextView.class);
    target.mRlRoot = Utils.findRequiredViewAsType(source, R.id.rl_root, "field 'mRlRoot'", RelativeLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    WalletViewBean target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvName = null;
    target.mTvAddress = null;
    target.mIvSelect = null;
    target.mTvBalance = null;
    target.mRlRoot = null;

    view7f090300.setOnClickListener(null);
    view7f090300 = null;
  }
}
