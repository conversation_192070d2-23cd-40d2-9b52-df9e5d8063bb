package com.Tlock.io.utils;


import android.app.KeyguardManager;
import android.content.Context;
import android.hardware.fingerprint.FingerprintManager;
import android.os.Build;
import android.os.CancellationSignal;
import android.os.Handler;
import android.util.Log;
import android.widget.Toast;

import com.Tlock.io.app.AppApplication;

public class FingerprintUtils {
    private static FingerprintUtils mFingerprintUtils;
    private static FingerprintManager mFingerprintManager;

    public static FingerprintUtils getFingerprintUtils(Context context) {
        if (mFingerprintUtils == null) {
            synchronized (FingerprintUtils.class) {
                if (mFingerprintUtils == null) {
                    mFingerprintUtils = new FingerprintUtils();
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        mFingerprintManager = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);

                    }
                }
            }

        }
        return mFingerprintUtils;
    }

    public void authenticate(FingerprintManager.CryptoObject crypto, CancellationSignal cancel,
                             int flags, final Callback callback, Handler handler) {

        if (mFingerprintManager != null) {
            if (!mFingerprintManager.isHardwareDetected()) {
                Toast.makeText(AppApplication.getInstance(), "您手机没有指纹识别设备", Toast.LENGTH_SHORT).show();
                return;
            }
            //检测设备是否处于安全状态中
            KeyguardManager keyguardManager = (KeyguardManager) AppApplication.getInstance().getSystemService(Context.KEYGUARD_SERVICE);
            if (!keyguardManager.isKeyguardSecure()) {
                //如果不是处于安全状态中，跳转打开安全保护（锁屏等）
                return;
            }
            //检测系统中是否注册的指纹
            if (!mFingerprintManager.hasEnrolledFingerprints()) {
                //没有录入指纹，跳转到指纹录入
                Toast.makeText(AppApplication.getInstance(), "没有录入指纹！", Toast.LENGTH_SHORT).show();
                return;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (cancel == null) {
                    cancel = new CancellationSignal();
                }
                final CancellationSignal finalCancel = cancel;
                mFingerprintManager.authenticate(crypto, finalCancel, flags, new FingerprintManager.AuthenticationCallback() {
                    @Override
                    public void onAuthenticationError(int errorCode, CharSequence errString) {
                        super.onAuthenticationError(errorCode, errString);
                        Log.i("TAG", "error ");
                    }

                    @Override
                    public void onAuthenticationHelp(int helpCode, CharSequence helpString) {
                        super.onAuthenticationHelp(helpCode, helpString);
                        if (helpCode == 1021 || helpCode == 1022 || helpCode == 1023 || helpCode == 1001) {

                        } else {
                            callback.help(finalCancel);
                            Log.i("TAG", "help ");
                        }

                    }

                    @Override
                    public void onAuthenticationSucceeded(FingerprintManager.AuthenticationResult result) {
                        super.onAuthenticationSucceeded(result);
                        callback.succeeded();
                    }

                    @Override
                    public void onAuthenticationFailed() {
                        super.onAuthenticationFailed();
                        Log.i("TAG", "failed");
                    }
                }, handler);
            }
        }
    }

    public interface Callback {
        void succeeded();

        void failed();

        void help(CancellationSignal cancellationSigna);
    }

    private Callback callback;


}
