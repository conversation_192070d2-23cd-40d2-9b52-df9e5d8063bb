// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class MMemberListActivity_ViewBinding implements Unbinder {
  private MMemberListActivity target;

  private View view7f090303;

  @UiThread
  public MMemberListActivity_ViewBinding(MMemberListActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public MMemberListActivity_ViewBinding(final MMemberListActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mRvAccount = Utils.findRequiredViewAsType(source, R.id.rv_account, "field 'mRvAccount'", RecyclerView.class);
    view = Utils.findRequiredView(source, R.id.tv_add_account, "field 'mTvAddAccount' and method 'onBindClick'");
    target.mTvAddAccount = Utils.castView(view, R.id.tv_add_account, "field 'mTvAddAccount'", TextView.class);
    view7f090303 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    MMemberListActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mRvAccount = null;
    target.mTvAddAccount = null;

    view7f090303.setOnClickListener(null);
    view7f090303 = null;
  }
}
