package com.Tlock.io.fragment;

import android.content.ContentResolver;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.AllowanceActivity;
import com.Tlock.io.activity.cosmos.CollectListActivity;
import com.Tlock.io.activity.cosmos.EditProfileActivity;
import com.Tlock.io.activity.cosmos.FollowingListActivity;
import com.Tlock.io.activity.cosmos.MangerActivity;
import com.Tlock.io.activity.cosmos.TransactionListActivity;
import com.Tlock.io.activity.cosmos.UserInfoActivity;
import com.Tlock.io.activity.cosmos.WalletInfoActivity;
import com.Tlock.io.activity.wallet.BackupActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.LazyLoadBaseFragment;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.TokenInfo;
import com.Tlock.io.profile.ProfileTXProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.SpUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.wallet.PopCheckPwd;
import com.Tlock.io.widget.wallet.PopWalletEdit;
import com.Tlock.io.widget.wallet.PopWalletEditName;
import com.Tlock.io.widget.wallet.PopWalletList;
import com.Tlock.io.widget.wallet.PopWalletTip;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;
import com.sl.utakephoto.compress.CompressConfig;
import com.sl.utakephoto.crop.CropOptions;
import com.sl.utakephoto.exception.TakeException;
import com.sl.utakephoto.manager.ITakePhotoResult;
import com.sl.utakephoto.manager.UTakePhoto;

import org.greenrobot.eventbus.EventBus;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;


public class MineFragment extends LazyLoadBaseFragment {


    @BindView(R.id.btn_updata)
    TextView mBtnUpdata;
    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.iv_manger)
    ImageView mIvManger;
    @BindView(R.id.tv_name)
    TextView mTvName;
    @BindView(R.id.tv_handle)
    TextView mTvHandle;
    @BindView(R.id.review_count)
    TextView mReviewCount;
    @BindView(R.id.tv_1)
    TextView mTv1;
    @BindView(R.id.fans_count)
    TextView mFansCount;
    @BindView(R.id.tv_2)
    TextView mTv2;
    @BindView(R.id.rl_info)
    RelativeLayout mRlInfo;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.iv_balance)
    ImageView mIvBalance;
    @BindView(R.id.tv_balance)
    TextView mTvBalance;
    @BindView(R.id.tv_data)
    TextView mTvData;
    @BindView(R.id.iv_right)
    ImageView mIvRight;
    @BindView(R.id.rl_balance)
    RelativeLayout mRlBalance;
    @BindView(R.id.line2)
    View mLine2;
    @BindView(R.id.iv_favorite)
    ImageView mIvFavorite;
    @BindView(R.id.rl_favorite)
    RelativeLayout mRlFavorite;
    @BindView(R.id.iv_collect)
    ImageView mIvCollect;
    @BindView(R.id.rl_collect)
    RelativeLayout mRlCollect;
    @BindView(R.id.line3)
    View mLine3;
    @BindView(R.id.iv_governance)
    ImageView mIvGovernance;
    @BindView(R.id.rl_governance)
    RelativeLayout mRlGovernance;
    @BindView(R.id.line4)
    View mLine4;
    @BindView(R.id.iv_Moderator)
    ImageView mIvModerator;
    @BindView(R.id.rl_Moderator)
    RelativeLayout mRlModerator;
    @BindView(R.id.line5)
    View mLine5;
    @BindView(R.id.tv_wallet_address)
    TextView mTvWalletAddress;
    @BindView(R.id.tv_level)
    FontTextView mTvLevel;
    @BindView(R.id.iv_vip)
    ImageView mIvVip;
    @BindView(R.id.icon_check)
    ImageView mIconCheck;
    @BindView(R.id.iv_wallet)
    ImageView mIvWallet;
    @BindView(R.id.iv_free)
    ImageView mIvFree;
    @BindView(R.id.tv_free)
    TextView mTvFree;
    @BindView(R.id.tv_free_data)
    TextView mTvFreeData;
    @BindView(R.id.iv_free_1)
    ImageView mIvFree1;
    @BindView(R.id.rl_free)
    RelativeLayout mRlFree;
    @BindView(R.id.iv_transaction)
    ImageView mIvTransaction;
    @BindView(R.id.rl_transaction)
    RelativeLayout mRlTransaction;
    private ETHWallet seleteWallet;
    private ProfileProto.Profile authInfo;
    private String avatar = "";


    @Override
    protected int getContentViewId() {
        return R.layout.fragment_mine;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mTvWalletAddress.setText(WalletDaoUtils.getCurrent().getAddress());
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onResume() {
        super.onResume();
        resetAndChange();
    }

    private void getAuthInfo() {
        if (WalletDaoUtils.getCurrent() != null) {
            ETHWallet current = WalletDaoUtils.getCurrent();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    authInfo = CosmosUtils.getAuthInfo(current.getAddress());
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            SpUtil.setUserInfo(JsonUtils.objectToJson(authInfo));
                            mTvWalletAddress.setText(authInfo.getWalletAddress());
                            mTvName.setText(authInfo.getNickname().isEmpty() ? authInfo.getUserHandle() : authInfo.getNickname());
                            if (!TextUtils.isEmpty(authInfo.getAvatar())) {
                                current.setName(authInfo.getNickname());
                                WalletDaoUtils.updateWallet(current);
                            }
                            mTvHandle.setText("@" + authInfo.getUserHandle());
                            mReviewCount.setText(authInfo.getFollowing() + "");
                            mFansCount.setText(authInfo.getFollowers() + "");
                            mTvLevel.setText("LV " + authInfo.getLevel());
                            if (authInfo.getAdminLevel() == 0) {
                                mIvManger.setVisibility(View.GONE);
                            }
                            if (TextUtils.isEmpty(authInfo.getAvatar())) {
                                avatar = "";
                                TextAvatarDrawable a = new TextAvatarDrawable(authInfo.getUserHandle().substring(0, 1));
                                // 应用到 ImageView
                                mIvHeard.setImageDrawable(a);
                            } else {
                                if (authInfo.getAvatar().startsWith("http")) {
                                    avatar = authInfo.getAvatar();
                                    Glide.with(getActivity()).load(authInfo.getAvatar()).apply(new RequestOptions()
                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                    .signature(new ObjectKey(authInfo.getAvatar()))
                                                    .centerCrop()
                                                    .format(DecodeFormat.PREFER_RGB_565)
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                } else {
                                    if (avatar.equalsIgnoreCase(authInfo.getAvatar())) return;
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(authInfo.getAvatar());
                                    Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                                    .centerCrop()
                                                    .dontTransform())
                                            .apply(RequestOptions.circleCropTransform().circleCrop())
                                            .into(mIvHeard);
                                    avatar = authInfo.getAvatar();

                                }
                            }
                        }
                    });
                }
            });
        }
    }

    public void getBalance() {
        if (WalletDaoUtils.getCurrent() != null) {
            ETHWallet current = WalletDaoUtils.getCurrent();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @RequiresApi(api = Build.VERSION_CODES.O)
                @Override
                public void run() {
                    String baseBalance = CosmosUtils.getBaseBalance(current.getAddress());
                    long granteeBalance = CosmosUtils.getGranteeBalance();
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            String division = BigDecimalUtils.division(baseBalance, "1000000");
                            mTvData.setText(BigDecimalUtils.saveDecimals(division, 2) + " TOK");

                            mTvFreeData.setText(BigDecimalUtils.saveDecimals(BigDecimalUtils.division(String.valueOf(granteeBalance), "1000000"), 2) + " TOK");
                            TokenInfo tokenInfo = WalletDaoUtils.getToken();
                            tokenInfo.setWalletAddress(current.getAddress());
                            tokenInfo.setChainID(10889);
                            tokenInfo.setWalletId(current.getId());
                            tokenInfo.setBalanceOf(baseBalance);
                            tokenInfo.setSymbol("TOK");
                            WalletDaoUtils.updateToken(tokenInfo);
                        }
                    });
                }
            });
        }
    }

    @OnClick({R.id.iv_wallet, R.id.btn_updata, R.id.rl_free, R.id.rl_transaction, R.id.review_count, R.id.tv_1, R.id.iv_heard, R.id.fans_count, R.id.tv_2, R.id.rl_info, R.id.rl_balance, R.id.rl_collect, R.id.rl_governance, R.id.rl_Moderator, R.id.rl_favorite, R.id.tv_wallet_address})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_wallet:
                EventBus.getDefault().postSticky(new Event("SHOW_LIST"));
                break;
            case R.id.rl_free:
                AllowanceActivity.start(getActivity());
                break;
            case R.id.rl_transaction:
                TransactionListActivity.start(getActivity());
                break;
            case R.id.tv_wallet_address:
                CopyUtils.copyToClipboard(WalletDaoUtils.getCurrent().getAddress());
                break;
            case R.id.btn_updata:
                EditProfileActivity.start(getActivity());
                break;
            case R.id.review_count:
            case R.id.tv_1:
                // 关注列表
                FollowingListActivity.start(getActivity(), 1);
                break;
            case R.id.fans_count:
            case R.id.tv_2:
                // 粉丝列表
                FollowingListActivity.start(getActivity(), 2);
                break;
            case R.id.iv_heard:
            case R.id.rl_info:
                // 详情
                UserInfoActivity.start(getActivity(), WalletDaoUtils.getCurrent().getAddress());
                break;
            case R.id.rl_balance:
                WalletInfoActivity.start(getActivity());
                break;
            case R.id.rl_favorite:
                CollectListActivity.start(getActivity(), 1);
                break;
            case R.id.rl_collect:
                //收藏
                CollectListActivity.start(getActivity(), 2);
                break;
            case R.id.rl_governance:
                //投票
                break;
            case R.id.rl_Moderator:
                MangerActivity.start(getActivity());
                break;

        }
    }


    public void resetAndChange() {
        //重置数据并重新加载
        getAuthInfo();
        getBalance();
    }

    /**
     * 弹出钱包列表
     */
    private void showWalletList() {
        PopWalletList popWalletList = new PopWalletList(getActivity(), 2);
        popWalletList.setCallBack(new PopWalletList.CallBack() {
            @Override
            public void change(@NonNull String name, @NonNull String address) {

            }

            @Override
            public void change(ETHWallet wallet) {
                resetAndChange();

            }

            @Override
            public void edit(ETHWallet ethWallet) {
                seleteWallet = ethWallet;
                showWalletEdit();
            }
        });
        new XPopup.Builder(getActivity())
                .autoDismiss(true)
                .isDestroyOnDismiss(true)
                .asCustom(popWalletList).show();
    }

    /**
     * 弹出选择操作弹窗
     */
    private void showWalletEdit() {
        PopWalletEdit popWalletEdit = new PopWalletEdit(getActivity(), seleteWallet);
        popWalletEdit.setCallBack(new PopWalletEdit.CallBack() {
            @Override
            public void change(int type) {
                if (seleteWallet.getType() == 3) {
                    //观察钱包
                    showWalletDelete();
                } else {
                    //普通钱包
                    checkPwd(type);
                }
            }
        });
        new XPopup.Builder(getActivity())
                .autoDismiss(true)
                .asCustom(popWalletEdit).show();
    }

    /**
     * 弹出确认删除
     */
    private void showWalletDelete() {
        PopWalletTip popWalletTip = new PopWalletTip(getActivity(), seleteWallet);
        popWalletTip.setCallBack(new PopWalletTip.CallBack() {
            @Override
            public void toBackup(ETHWallet wallet) {
                BackupActivity.start(getActivity(), JsonUtils.objectToJson(wallet));
            }

            @Override
            public void delete() {

            }
        });
        new XPopup.Builder(getActivity()).asCustom(popWalletTip).show();
    }

    /**
     * 校验身份
     *
     * @param type 1编辑权限  2 备忘权限 3 删除权限
     * @return
     */
    private void checkPwd(int type) {
        PopCheckPwd popCheckPwd = new PopCheckPwd(getActivity(), seleteWallet);
        popCheckPwd.setCallBack(new PopCheckPwd.CallBack() {
            @Override
            public void succeeded() {
                switch (type) {
                    case 1:
                        showWalletEditName();
                        break;
                    case 2:
                        BackupActivity.start(getActivity(), JsonUtils.objectToJson(seleteWallet));
                        break;
                    case 3:
                        showWalletDelete();
                        break;
                }
            }

            @Override
            public void failed() {
            }
        });
        new XPopup.Builder(getActivity()).asCustom(popCheckPwd).show();
    }

    /**
     * 弹出修改名称
     */
    private void showWalletEditName() {
        PopWalletEditName popWalletEditName = new PopWalletEditName(getActivity(), seleteWallet);
        popWalletEditName.setCallBack(new PopWalletEditName.CallBack() {
            @Override
            public void change(long id, String name) {

            }
        });
        new XPopup.Builder(getActivity()).asCustom(popWalletEditName).show();
    }

    private void setHeard() {
        UTakePhoto.with(getActivity())
                .openAlbum()
                .setCrop(new CropOptions.Builder()
                        .setAspectX(1).setAspectY(1)
                        .setOutputX(300).setOutputY(300)
                        .setWithOwnCrop(false)//使用系统裁剪还是自带裁剪
                        .create())
                .setCompressConfig(new CompressConfig.Builder()
                        .setFocusAlpha(false)//是否支持透明度
                        .setLeastCompressSize(200)//最小压缩尺寸
                        .create())
                .build(new ITakePhotoResult() {
                    @Override
                    public void takeSuccess(List<Uri> uriList) {
                        Bitmap bitmap = getBitmapFromUri(uriList.get(0));
                        String base64String = BitmapUtils.bitmapToBase64(bitmap);
                        Bitmap bitmap1 = BitmapUtils.base64ToBitmap(base64String);
                        Glide.with(getActivity()).asBitmap().load(bitmap1).apply(new RequestOptions()
                                        .centerCrop()
                                        .dontTransform())
                                .apply(RequestOptions.circleCropTransform().circleCrop())
                                .into(mIvHeard);
                        ETHWallet current = WalletDaoUtils.getCurrent();
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                ProfileTXProto.ProfileOptions.Builder pro = ProfileTXProto.ProfileOptions.newBuilder()
                                        .setAvatar(base64String)
                                        .setBio(authInfo.getBio())
                                        .setLocation(authInfo.getLocation())
                                        .setWebsite(authInfo.getWebsite())
                                        .setNickname(authInfo.getNickname())
                                        .setUserHandle(authInfo.getUserHandle());
                                CosmosUtils.profileUpdata(pro.build(), current.getAddress());
                            }
                        });

                    }

                    @Override
                    public void takeFailure(TakeException ex) {
//                        showToast("fail");

                    }

                    @Override
                    public void takeCancel() {
                        showToast("cancel");

                    }
                });
    }

    private Bitmap getBitmapFromUri(Uri uri) {
        try {
            ContentResolver cr = getActivity().getContentResolver();
            InputStream is = cr.openInputStream(uri);
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inSampleSize = 2; // 压缩图片，避免内存溢出
            return BitmapFactory.decodeStream(is, null, options);
        } catch (FileNotFoundException e) {

        }
        return null;
    }

}