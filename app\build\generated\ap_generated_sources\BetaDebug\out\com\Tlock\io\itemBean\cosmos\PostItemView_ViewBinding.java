// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PostItemView_ViewBinding implements Unbinder {
  private PostItemView target;

  private View view7f090151;

  private View view7f09015f;

  private View view7f090314;

  private View view7f090197;

  private View view7f0901a0;

  private View view7f0901a2;

  private View view7f0901a3;

  private View view7f09018e;

  private View view7f0901aa;

  @UiThread
  public PostItemView_ViewBinding(PostItemView target) {
    this(target, target);
  }

  @UiThread
  public PostItemView_ViewBinding(final PostItemView target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.iv_heard, "field 'mIvHeard' and method 'onBindClick'");
    target.mIvHeard = Utils.castView(view, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    view7f090151 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvAccountName = Utils.findRequiredViewAsType(source, R.id.tv_account_name, "field 'mTvAccountName'", FontTextView.class);
    target.mTvTime = Utils.findRequiredViewAsType(source, R.id.tv_time, "field 'mTvTime'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_more, "field 'mIvMore' and method 'onBindClick'");
    target.mIvMore = Utils.castView(view, R.id.iv_more, "field 'mIvMore'", ImageView.class);
    view7f09015f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", FontTextView.class);
    view = Utils.findRequiredView(source, R.id.tv_content, "field 'mTvContent' and method 'onBindClick'");
    target.mTvContent = Utils.castView(view, R.id.tv_content, "field 'mTvContent'", FontTextView.class);
    view7f090314 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mRvImages = Utils.findRequiredViewAsType(source, R.id.rv_image, "field 'mRvImages'", RecyclerView.class);
    target.mTvQuoteTitle = Utils.findRequiredViewAsType(source, R.id.tv_quote_title, "field 'mTvQuoteTitle'", TextView.class);
    target.mTvQuoteContent = Utils.findRequiredViewAsType(source, R.id.tv_quote_content, "field 'mTvQuoteContent'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_content_quote, "field 'mLlContentQuote' and method 'onBindClick'");
    target.mLlContentQuote = Utils.castView(view, R.id.ll_content_quote, "field 'mLlContentQuote'", LinearLayout.class);
    view7f090197 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mLlVote = Utils.findRequiredViewAsType(source, R.id.ll_vote, "field 'mLlVote'", LinearLayout.class);
    target.mTvVoteTime = Utils.findRequiredViewAsType(source, R.id.tv_voteTime, "field 'mTvVoteTime'", TextView.class);
    target.mTvTime1 = Utils.findRequiredViewAsType(source, R.id.tv_time1, "field 'mTvTime1'", TextView.class);
    target.mIvPraise = Utils.findRequiredViewAsType(source, R.id.iv_praise, "field 'mIvPraise'", ImageView.class);
    target.mTvPraise = Utils.findRequiredViewAsType(source, R.id.tv_praise, "field 'mTvPraise'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_praise, "field 'mLlPraise' and method 'onBindClick'");
    target.mLlPraise = Utils.castView(view, R.id.ll_praise, "field 'mLlPraise'", LinearLayout.class);
    view7f0901a0 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvQuote = Utils.findRequiredViewAsType(source, R.id.iv_quote, "field 'mIvQuote'", ImageView.class);
    target.mTvQuote = Utils.findRequiredViewAsType(source, R.id.tv_quote, "field 'mTvQuote'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_quote, "field 'mLlQuote' and method 'onBindClick'");
    target.mLlQuote = Utils.castView(view, R.id.ll_quote, "field 'mLlQuote'", LinearLayout.class);
    view7f0901a2 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvReview = Utils.findRequiredViewAsType(source, R.id.iv_review, "field 'mIvReview'", ImageView.class);
    target.mTvReview = Utils.findRequiredViewAsType(source, R.id.tv_review, "field 'mTvReview'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_review, "field 'mLlReview' and method 'onBindClick'");
    target.mLlReview = Utils.castView(view, R.id.ll_review, "field 'mLlReview'", LinearLayout.class);
    view7f0901a3 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvCollect = Utils.findRequiredViewAsType(source, R.id.iv_Collect, "field 'mIvCollect'", ImageView.class);
    target.mTvCollect = Utils.findRequiredViewAsType(source, R.id.tv_Collect, "field 'mTvCollect'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_Collect, "field 'mLlCollect' and method 'onBindClick'");
    target.mLlCollect = Utils.castView(view, R.id.ll_Collect, "field 'mLlCollect'", LinearLayout.class);
    view7f09018e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mIvShare = Utils.findRequiredViewAsType(source, R.id.iv_share, "field 'mIvShare'", ImageView.class);
    target.mTvShare = Utils.findRequiredViewAsType(source, R.id.tv_share, "field 'mTvShare'", TextView.class);
    view = Utils.findRequiredView(source, R.id.ll_share, "field 'mLlShare' and method 'onBindClick'");
    target.mLlShare = Utils.castView(view, R.id.ll_share, "field 'mLlShare'", LinearLayout.class);
    view7f0901aa = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mConstraint = Utils.findRequiredViewAsType(source, R.id.constraint, "field 'mConstraint'", LinearLayout.class);
    target.mLine1 = Utils.findRequiredView(source, R.id.line1, "field 'mLine1'");
    target.mRoot = Utils.findRequiredViewAsType(source, R.id.root, "field 'mRoot'", ConstraintLayout.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    PostItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvHeard = null;
    target.mTvAccountName = null;
    target.mTvTime = null;
    target.mIvMore = null;
    target.mTvTitle = null;
    target.mTvContent = null;
    target.mRvImages = null;
    target.mTvQuoteTitle = null;
    target.mTvQuoteContent = null;
    target.mLlContentQuote = null;
    target.mLlVote = null;
    target.mTvVoteTime = null;
    target.mTvTime1 = null;
    target.mIvPraise = null;
    target.mTvPraise = null;
    target.mLlPraise = null;
    target.mIvQuote = null;
    target.mTvQuote = null;
    target.mLlQuote = null;
    target.mIvReview = null;
    target.mTvReview = null;
    target.mLlReview = null;
    target.mIvCollect = null;
    target.mTvCollect = null;
    target.mLlCollect = null;
    target.mIvShare = null;
    target.mTvShare = null;
    target.mLlShare = null;
    target.mConstraint = null;
    target.mLine1 = null;
    target.mRoot = null;

    view7f090151.setOnClickListener(null);
    view7f090151 = null;
    view7f09015f.setOnClickListener(null);
    view7f09015f = null;
    view7f090314.setOnClickListener(null);
    view7f090314 = null;
    view7f090197.setOnClickListener(null);
    view7f090197 = null;
    view7f0901a0.setOnClickListener(null);
    view7f0901a0 = null;
    view7f0901a2.setOnClickListener(null);
    view7f0901a2 = null;
    view7f0901a3.setOnClickListener(null);
    view7f0901a3 = null;
    view7f09018e.setOnClickListener(null);
    view7f09018e = null;
    view7f0901aa.setOnClickListener(null);
    view7f0901aa = null;
  }
}
