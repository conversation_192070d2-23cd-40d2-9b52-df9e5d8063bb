// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CustomInputBox_ViewBinding implements Unbinder {
  private CustomInputBox target;

  @UiThread
  public CustomInputBox_ViewBinding(CustomInputBox target) {
    this(target, target);
  }

  @UiThread
  public CustomInputBox_ViewBinding(CustomInputBox target, View source) {
    this.target = target;

    target.mEditText = Utils.findRequiredViewAsType(source, R.id.editText, "field 'mEditText'", EditText.class);
    target.mIvDel = Utils.findRequiredViewAsType(source, R.id.iv_del, "field 'mIvDel'", ImageView.class);
    target.mIvShow = Utils.findRequiredViewAsType(source, R.id.iv_show, "field 'mIvShow'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    CustomInputBox target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mEditText = null;
    target.mIvDel = null;
    target.mIvShow = null;
  }
}
