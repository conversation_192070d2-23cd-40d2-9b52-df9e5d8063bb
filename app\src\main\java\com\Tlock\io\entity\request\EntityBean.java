package com.Tlock.io.entity.request;

public class EntityBean {
    private String title;
    private String acParkId;
    private String customQuery;
    private String idBusinessAdministration;
    private String content;
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getIdBusinessAdministration() {
        return idBusinessAdministration;
    }

    public void setIdBusinessAdministration(String idBusinessAdministration) {
        this.idBusinessAdministration = idBusinessAdministration;
    }

    public String getCustomQuery() {
        return customQuery;
    }

    public void setCustomQuery(String customQuery) {
        this.customQuery = customQuery;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAcParkId() {
        return acParkId;
    }

    public void setAcParkId(String acParkId) {
        this.acParkId = acParkId;
    }
}
