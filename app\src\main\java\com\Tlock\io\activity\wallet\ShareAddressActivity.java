package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.utils.CopyUtils;
import com.Tlock.io.utils.ScreenshotUtil;
import com.Tlock.io.widget.CustomNavBar;
import com.yzq.zxinglibrary.encode.CodeCreator;

import butterknife.BindView;
import butterknife.OnClick;

public class ShareAddressActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.iv_address)
    ImageView mIvAddress;
    @BindView(R.id.tv_address)
    TextView mTvAddress;
    @BindView(R.id.ll_img)
    LinearLayout mLlImg;
    private String data;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context, String data) {
        Intent intent = new Intent(context, ShareAddressActivity.class);
        intent.putExtra("data", data);
        context.startActivity(intent);
    }


    @Override
    protected int getContentViewId() {
        return R.layout.activity_share_address;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setStatusColor(getColor(R.color.bg_gray_color));
        data = getIntent().getStringExtra("data");
        mTvAddress.setText(data);
        Bitmap qrCode = CodeCreator.createQRCode(data, 500, 500, null);
        mIvAddress.setImageBitmap(qrCode);
    }

    @Override
    protected void loadData() {

    }

    @OnClick({R.id.tv_copy_address, R.id.tv_share_address})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_copy_address:
                if (TextUtils.isEmpty(data)) return;
                CopyUtils.getInstance().copyToClipboard(data);
                break;

            case R.id.tv_share_address:
                if (TextUtils.isEmpty(data)) return;
                try {
                    Bitmap bitmap = ScreenshotUtil.shotScreen(getActivity());
                    ScreenshotUtil.saveBitmap(getActivity(), bitmap);
                    showToast("已保存到相册");
                } catch (Exception e) {
                }
                break;
        }
    }
}