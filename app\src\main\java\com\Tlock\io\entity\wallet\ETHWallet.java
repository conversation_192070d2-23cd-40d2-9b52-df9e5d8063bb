package com.Tlock.io.entity.wallet;


import org.greenrobot.greendao.DaoException;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.ToMany;

import java.util.List;

/**
 * 钱包账号实体类
 */

@Entity
public class ETHWallet {

    @Id(autoincrement = true)
    private Long id;
    private String chainId;
    private String address;
    private String name;
    private String password;
    private String mnemonic;
    private String privateKey;
    private String publicKey;
    @ToMany(referencedJoinProperty = "walletId")
    private List<TokenInfo> tokenInfoList;
    private String chainList;//chainlist
    private boolean isCurrent;
    private boolean isBackup;
    private Integer orderID = 1000;
    private int type;//1 私钥 2助记词 3观察钱包 4默认给用户钱包
    /** Used to resolve relations */
    @Generated(hash = 2040040024)
    private transient DaoSession daoSession;
    /** Used for active entity operations. */
    @Generated(hash = 1911286554)
    private transient ETHWalletDao myDao;

    @Generated(hash = 876863434)
    public ETHWallet(Long id, String chainId, String address, String name,
                     String password, String mnemonic, String privateKey, String publicKey,
                     String chainList, boolean isCurrent, boolean isBackup, Integer orderID,
                     int type) {
        this.id = id;
        this.chainId = chainId;
        this.address = address;
        this.name = name;
        this.password = password;
        this.mnemonic = mnemonic;
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        this.chainList = chainList;
        this.isCurrent = isCurrent;
        this.isBackup = isBackup;
        this.orderID = orderID;
        this.type = type;
    }
    @Generated(hash = 1963897189)
    public ETHWallet() {
    }
    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getChainId() {
        return this.chainId;
    }
    public void setChainId(String chainId) {
        this.chainId = chainId;
    }
    public String getAddress() {
        return this.address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    public String getName() {
        return this.name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getPassword() {
        return this.password;
    }
    public void setPassword(String password) {
        this.password = password;
    }
    public String getMnemonic() {
        return this.mnemonic;
    }
    public void setMnemonic(String mnemonic) {
        this.mnemonic = mnemonic;
    }
    public String getPrivateKey() {
        return this.privateKey;
    }
    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }
    public String getPublicKey() {
        return this.publicKey;
    }
    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }
    public String getChainList() {
        return this.chainList;
    }
    public void setChainList(String chainList) {
        this.chainList = chainList;
    }
    public boolean getIsCurrent() {
        return this.isCurrent;
    }

    public void setIsCurrent(boolean isCurrent) {
        this.isCurrent = isCurrent;
    }

    public boolean getIsBackup() {
        return this.isBackup;
    }

    public void setIsBackup(boolean isBackup) {
        this.isBackup = isBackup;
    }

    public Integer getOrderID() {
        return this.orderID;
    }

    public void setOrderID(Integer orderID) {
        this.orderID = orderID;
    }

    public int getType() {
        return this.type;
    }

    public void setType(int type) {
        this.type = type;
    }

    /**
     * To-many relationship, resolved on first access (and after reset).
     * Changes to to-many relations are not persisted, make changes to the target entity.
     */
    @Generated(hash = 1496046976)
    public List<TokenInfo> getTokenInfoList() {
        if (tokenInfoList == null) {
            final DaoSession daoSession = this.daoSession;
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TokenInfoDao targetDao = daoSession.getTokenInfoDao();
            List<TokenInfo> tokenInfoListNew = targetDao
                    ._queryETHWallet_TokenInfoList(id);
            synchronized (this) {
                if (tokenInfoList == null) {
                    tokenInfoList = tokenInfoListNew;
                }
            }
        }
        return tokenInfoList;
    }
    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    @Generated(hash = 853926481)
    public synchronized void resetTokenInfoList() {
        tokenInfoList = null;
    }
    /**
     * Convenient call for {@link org.greenrobot.greendao.AbstractDao#delete(Object)}.
     * Entity must attached to an entity context.
     */
    @Generated(hash = 128553479)
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }
        myDao.delete(this);
    }
    /**
     * Convenient call for {@link org.greenrobot.greendao.AbstractDao#refresh(Object)}.
     * Entity must attached to an entity context.
     */
    @Generated(hash = 1942392019)
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }
        myDao.refresh(this);
    }
    /**
     * Convenient call for {@link org.greenrobot.greendao.AbstractDao#update(Object)}.
     * Entity must attached to an entity context.
     */
    @Generated(hash = 713229351)
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }
        myDao.update(this);
    }
    /** called by internal mechanisms, do not call yourself. */
    @Generated(hash = 374890006)
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getETHWalletDao() : null;
    }


}
