{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,376,475,585,672,775,896,974,1050,1141,1234,1326,1420,1520,1613,1708,1802,1893,1984,2067,2171,2275,2375,2484,2593,2702,2864,8030", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "371,470,580,667,770,891,969,1045,1136,1229,1321,1415,1515,1608,1703,1797,1888,1979,2062,2166,2270,2370,2479,2588,2697,2859,2957,8109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,304,404,516,596,661,755,825,887,974,1039,1098,1163,1224,1281,1400,1458,1519,1576,1647,1777,1863,1941,2049,2124,2195,2292,2359,2425,2505,2595,2681,2760,2837,2907,2982,3070,3140,3240,3339,3413,3489,3596,3650,3723,3814,3910,3972,4036,4099,4198,4296,4388,4488", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "219,299,399,511,591,656,750,820,882,969,1034,1093,1158,1219,1276,1395,1453,1514,1571,1642,1772,1858,1936,2044,2119,2190,2287,2354,2420,2500,2590,2676,2755,2832,2902,2977,3065,3135,3235,3334,3408,3484,3591,3645,3718,3809,3905,3967,4031,4094,4193,4291,4383,4483,4566"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3763,3863,3975,4055,4120,4214,4284,4346,4433,4498,4557,4622,4683,4740,4859,4917,4978,5035,5106,5236,5322,5400,5508,5583,5654,5751,5818,5884,5964,6054,6140,6219,6296,6366,6441,6529,6599,6699,6798,6872,6948,7055,7109,7182,7273,7369,7431,7495,7558,7657,7755,7847,7947", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "269,3037,3858,3970,4050,4115,4209,4279,4341,4428,4493,4552,4617,4678,4735,4854,4912,4973,5030,5101,5231,5317,5395,5503,5578,5649,5746,5813,5879,5959,6049,6135,6214,6291,6361,6436,6524,6594,6694,6793,6867,6943,7050,7104,7177,7268,7364,7426,7490,7553,7652,7750,7842,7942,8025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3042,3137,3240,3338,3438,3539,3651,8114", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3132,3235,3333,3433,3534,3646,3758,8210"}}]}]}