// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.pop;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopSend_ViewBinding implements Unbinder {
  private PopSend target;

  private View view7f09013a;

  private View view7f09030c;

  @UiThread
  public PopSend_ViewBinding(PopSend target) {
    this(target, target);
  }

  @UiThread
  public PopSend_ViewBinding(final PopSend target, View source) {
    this.target = target;

    View view;
    target.mEdOldPwd = Utils.findRequiredViewAsType(source, R.id.ed_old_pwd, "field 'mEdOldPwd'", EditText.class);
    target.mEdNewPwd = Utils.findRequiredViewAsType(source, R.id.ed_new_pwd, "field 'mEdNewPwd'", EditText.class);
    target.mIvNewSet = Utils.findRequiredViewAsType(source, R.id.iv_new_set, "field 'mIvNewSet'", ImageView.class);
    target.mRlNew = Utils.findRequiredViewAsType(source, R.id.rl_new, "field 'mRlNew'", RelativeLayout.class);
    target.mEdCheckPwd = Utils.findRequiredViewAsType(source, R.id.ed_check_pwd, "field 'mEdCheckPwd'", EditText.class);
    target.mIvCheckSet = Utils.findRequiredViewAsType(source, R.id.iv_check_set, "field 'mIvCheckSet'", ImageView.class);
    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_close, "field 'mIvClose' and method 'onViewClicked'");
    target.mIvClose = Utils.castView(view, R.id.iv_close, "field 'mIvClose'", ImageView.class);
    view7f09013a = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mRlCheck = Utils.findRequiredViewAsType(source, R.id.rl_check, "field 'mRlCheck'", RelativeLayout.class);
    view = Utils.findRequiredView(source, R.id.tv_confirm, "field 'mTvConfirm' and method 'onViewClicked'");
    target.mTvConfirm = Utils.castView(view, R.id.tv_confirm, "field 'mTvConfirm'", TextView.class);
    view7f09030c = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    PopSend target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mEdOldPwd = null;
    target.mEdNewPwd = null;
    target.mIvNewSet = null;
    target.mRlNew = null;
    target.mEdCheckPwd = null;
    target.mIvCheckSet = null;
    target.mTvTitle = null;
    target.mIvClose = null;
    target.mRlCheck = null;
    target.mTvConfirm = null;

    view7f09013a.setOnClickListener(null);
    view7f09013a = null;
    view7f09030c.setOnClickListener(null);
    view7f09030c = null;
  }
}
