package com.Tlock.io.entity.cosmos;


import java.util.List;

public class JinseBean {


    private String date;
    private List<Lives> lives;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public List<Lives> getLives() {
        return lives;
    }

    public void setLives(List<Lives> lives) {
        this.lives = lives;
    }

    public static class Lives {
        private Integer id;
        private String content;
        private String contentPrefix;
        private String linkName;
        private String link;
        private Integer grade;
        private String sort;
        private Integer category;
        private String highlightColor;
        private List<?> images;
        private Integer createdAt;
        private String createdAtZh;
        private String attribute;
        private Integer upCounts;
        private Integer downCounts;
        private String zanStatus;
        private List<?> readings;
        private Integer extraType;
        private Object extra;
        private Object prev;
        private Object next;
        private List<?> wordBlocks;
        private Integer isShowComment;
        private Integer isForbidComment;
        private Integer commentCount;
        private Object analystUser;
        private String showSourceName;
        private Integer voteId;
        private Object vote;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getContentPrefix() {
            return contentPrefix;
        }

        public void setContentPrefix(String contentPrefix) {
            this.contentPrefix = contentPrefix;
        }

        public String getLinkName() {
            return linkName;
        }

        public void setLinkName(String linkName) {
            this.linkName = linkName;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public Integer getGrade() {
            return grade;
        }

        public void setGrade(Integer grade) {
            this.grade = grade;
        }

        public String getSort() {
            return sort;
        }

        public void setSort(String sort) {
            this.sort = sort;
        }

        public Integer getCategory() {
            return category;
        }

        public void setCategory(Integer category) {
            this.category = category;
        }

        public String getHighlightColor() {
            return highlightColor;
        }

        public void setHighlightColor(String highlightColor) {
            this.highlightColor = highlightColor;
        }

        public List<?> getImages() {
            return images;
        }

        public void setImages(List<?> images) {
            this.images = images;
        }

        public Integer getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(Integer createdAt) {
            this.createdAt = createdAt;
        }

        public String getCreatedAtZh() {
            return createdAtZh;
        }

        public void setCreatedAtZh(String createdAtZh) {
            this.createdAtZh = createdAtZh;
        }

        public String getAttribute() {
            return attribute;
        }

        public void setAttribute(String attribute) {
            this.attribute = attribute;
        }

        public Integer getUpCounts() {
            return upCounts;
        }

        public void setUpCounts(Integer upCounts) {
            this.upCounts = upCounts;
        }

        public Integer getDownCounts() {
            return downCounts;
        }

        public void setDownCounts(Integer downCounts) {
            this.downCounts = downCounts;
        }

        public String getZanStatus() {
            return zanStatus;
        }

        public void setZanStatus(String zanStatus) {
            this.zanStatus = zanStatus;
        }

        public List<?> getReadings() {
            return readings;
        }

        public void setReadings(List<?> readings) {
            this.readings = readings;
        }

        public Integer getExtraType() {
            return extraType;
        }

        public void setExtraType(Integer extraType) {
            this.extraType = extraType;
        }

        public Object getExtra() {
            return extra;
        }

        public void setExtra(Object extra) {
            this.extra = extra;
        }

        public Object getPrev() {
            return prev;
        }

        public void setPrev(Object prev) {
            this.prev = prev;
        }

        public Object getNext() {
            return next;
        }

        public void setNext(Object next) {
            this.next = next;
        }

        public List<?> getWordBlocks() {
            return wordBlocks;
        }

        public void setWordBlocks(List<?> wordBlocks) {
            this.wordBlocks = wordBlocks;
        }

        public Integer getIsShowComment() {
            return isShowComment;
        }

        public void setIsShowComment(Integer isShowComment) {
            this.isShowComment = isShowComment;
        }

        public Integer getIsForbidComment() {
            return isForbidComment;
        }

        public void setIsForbidComment(Integer isForbidComment) {
            this.isForbidComment = isForbidComment;
        }

        public Integer getCommentCount() {
            return commentCount;
        }

        public void setCommentCount(Integer commentCount) {
            this.commentCount = commentCount;
        }

        public Object getAnalystUser() {
            return analystUser;
        }

        public void setAnalystUser(Object analystUser) {
            this.analystUser = analystUser;
        }

        public String getShowSourceName() {
            return showSourceName;
        }

        public void setShowSourceName(String showSourceName) {
            this.showSourceName = showSourceName;
        }

        public Integer getVoteId() {
            return voteId;
        }

        public void setVoteId(Integer voteId) {
            this.voteId = voteId;
        }

        public Object getVote() {
            return vote;
        }

        public void setVote(Object vote) {
            this.vote = vote;
        }
    }
}
