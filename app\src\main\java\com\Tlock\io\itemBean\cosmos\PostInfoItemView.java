package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.DateUtil.formatDate;
import static com.Tlock.io.utils.TimeUtils.timestampToLocalDate2;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.vectordrawable.graphics.drawable.Animatable2Compat;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.ContentInfoActivity;
import com.Tlock.io.activity.cosmos.UserInfoActivity;
import com.Tlock.io.adapter.SpacesItemDecoration1;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.OperationBean;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.Tlock.io.widget.cosmos.ProposalOptionItemView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory;
import com.bumptech.glide.signature.ObjectKey;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.ImageViewerPopupView;
import com.lxj.xpopup.interfaces.OnSrcViewUpdateListener;
import com.lxj.xpopup.util.SmartGlideImageLoader;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class PostInfoItemView extends BaseView {


    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.iv_more)
    ImageView mIvMore;
    @BindView(R.id.tv_content)
    FontTextView mTvContent;

    @BindView(R.id.tv_quote_title)
    TextView mTvQuoteTitle;
    @BindView(R.id.tv_quote_content)
    TextView mTvQuoteContent;
    @BindView(R.id.ll_content_quote)
    LinearLayout mLlContentQuote;
    @BindView(R.id.iv_quote)
    ImageView mIvQuote;
    @BindView(R.id.tv_quote)
    TextView mTvQuote;
    @BindView(R.id.ll_quote)
    View mLlQuote;
    @BindView(R.id.iv_review)
    ImageView mIvReview;
    @BindView(R.id.tv_review)
    TextView mTvReview;
    @BindView(R.id.ll_review)
    View mLlReview;
    @BindView(R.id.iv_praise)
    ImageView mIvPraise;
    @BindView(R.id.tv_praise)
    TextView mTvPraise;
    @BindView(R.id.ll_praise)
    View mLlPraise;
    @BindView(R.id.iv_Collect)
    ImageView mIvCollect;
    @BindView(R.id.tv_Collect)
    TextView mTvCollect;
    @BindView(R.id.ll_Collect)
    View mLlCollect;
    @BindView(R.id.iv_share)
    ImageView mIvShare;
    @BindView(R.id.tv_share)
    TextView mTvShare;
    @BindView(R.id.ll_share)
    View mLlShare;
    @BindView(R.id.constraint)
    ConstraintLayout mConstraint;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.ll_vote)
    LinearLayout mLlVote;
    @BindView(R.id.tv_voteTime)
    TextView mTvVoteTime;
    @BindView(R.id.tv_title)
    FontTextView mTvTitle;
    @BindView(R.id.tv_handle)
    TextView mTvHandle;
    @BindView(R.id.rv_image)
    RecyclerView mRvImages;

    @BindView(R.id.line3)
    View mLine3;


    private GifDrawable gifDrawable;
    private OperationBean currentOperation;
    private String avatar = "";
    private BaseRecyclerViewAdapter<String> imageAdapter;

    public PostInfoItemView(Context context) {
        super(context);
    }

    public PostInfoItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public PostInfoItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public PostProto.Post post;
    public ProfileProto.Profile profile;
    public PostQueryProto.PostResponse data;
    public PostProto.Post data1;
    private String avatarAddress = "";

    @Override
    protected int getLayoutId() {
        return R.layout.item_post_info;
    }

    public void setData(PostQueryProto.PostResponse data) {
        this.data = data;
        this.post = data.getPost();
        setDefaultImg();
        PostProto.Poll poll = data.getPost().getPoll();
        mConstraint.setVisibility(VISIBLE);
        mLine1.setVisibility(VISIBLE);
        mLine3.setVisibility(VISIBLE);
        if (TextUtils.isEmpty(post.getTitle())) {
            mTvTitle.setVisibility(GONE);
        } else {
            mTvTitle.setVisibility(VISIBLE);
            mTvTitle.setText(post.getTitle());
        }
        if (!TextUtils.isEmpty(post.getQuote())) {
            mLlContentQuote.setVisibility(VISIBLE);
            mTvQuoteContent.setText(data.getQuotePost().getContent());
            mTvQuoteTitle.setVisibility(data.getQuotePost().getTitle().isEmpty() ? GONE : VISIBLE);
            mTvQuoteTitle.setText(data.getQuotePost().getTitle());
        } else {
            mLlContentQuote.setVisibility(GONE);
        }
//        Markwon markdwon = Markwon.builder(mTvAccountName.getContext())
//                .usePlugin(GlideImagesPlugin.create(mTvAccountName.getContext()))
//                .build();
//        markdwon.setMarkdown(mTvContent, post.getContent());
        mTvContent.setText(post.getContent());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(timestampToLocalDate2(post.getTimestamp() + "000"));
        }
        mTvVoteTime.setVisibility(GONE);
        mLlVote.setVisibility(GONE);
        if (post.getPostType() == PostProto.PostType.POLL) {
            getOption();
            mTvVoteTime.setVisibility(VISIBLE);
            mLlVote.setVisibility(VISIBLE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mTvVoteTime.setText(poll.getTotalVotes() + " votes · " + formatDate(poll.getVotingEnd(), "yyyy/MM/dd HH:mm:ss"));
            }
            long totalVotes = poll.getTotalVotes();
            mLlVote.removeAllViews();
            for (int i = 0; i < poll.getVoteList().size(); i++) {
                PostProto.Vote vote = poll.getVoteList().get(i);
                ProposalOptionItemView proposalOptionItemView = new ProposalOptionItemView(mLlVote.getContext(), vote.getOption(), BigDecimalUtils.multipliy(BigDecimalUtils.division(vote.getCount() + "", totalVotes + ""), "100"));
                int finalI = i;
                proposalOptionItemView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        for (int i1 = 0; i1 < mLlVote.getChildCount(); i1++) {
                            ProposalOptionItemView proposalOptionItemView1 = (ProposalOptionItemView) mLlVote.getChildAt(i1);
                            if (proposalOptionItemView1.isSelected()) {
                                return;
                            }
                            proposalOptionItemView1.setSelected(false);
                        }
                        proposalOptionItemView.setSelected(true);
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                PostTXProto.CastVoteOnPollRequest build = PostTXProto.CastVoteOnPollRequest.newBuilder()
                                        .setCreator(WalletDaoUtils.getCurrent().getAddress())
                                        .setId(data.getPost().getId())
                                        .setOptionId(finalI)
                                        .build();
                                CosmosUtils.sendPoll(build);
                            }
                        });

                    }
                });
                mLlVote.addView(proposalOptionItemView);
            }
        }


        mTvReview.setText(post.getCommentCount() != 0 ? post.getCommentCount() + "" : "");
        mTvPraise.setText(post.getLikeCount() != 0 ? post.getLikeCount() + "" : "");
//        mTvCollect.setText(post.getReportCount() != 0 ? post.getReportCount() + "" : "");
        if (post.getImageIdsCount() != 0) {
            ArrayList<String> files1 = new ArrayList<>();
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {

                    String postImages = CosmosUtils.getPostImages(post.getImageIds(0));
                    if (!postImages.isEmpty()) {
                        files1.add(postImages);
                    }
                    mRvImages.post(new Runnable() {
                        @Override
                        public void run() {
                            if (imageAdapter == null) {
                                int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
                                mRvImages.addItemDecoration(new SpacesItemDecoration1(spacingInPixels, 3));
                            }
                            initAdapter(files1);
                        }
                    });
                }
            });
        }
        if (post.getImagesUrlCount() != 0) {
            ArrayList<String> files1 = new ArrayList<>();
            if (post.getImagesUrlList() != null && post.getImagesUrlList().size() > 0) {
                files1.addAll(post.getImagesUrlList());
            }
            if (imageAdapter == null) {
                int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
                mRvImages.addItemDecoration(new SpacesItemDecoration1(spacingInPixels, 3));
            }
            initAdapter(files1);
        }

        if (avatarAddress.equalsIgnoreCase(post.getCreator())) {
            return;
        }
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {

                ProfileProto.Profile authInfo = CosmosUtils.getAuthInfo(post.getCreator());
                mIvPraise.post(new Runnable() {
                    @Override
                    public void run() {
                        PostInfoItemView.this.profile = authInfo;
                        setProfile();
                    }
                });
            }
        });
    }

    private void initAdapter(ArrayList<String> files1) {
        if (files1.size() == 0) {
            mRvImages.setAdapter(null); // 清空适配器
            return;
        }
        mRvImages.setVisibility(VISIBLE);
        int spanCount = files1.size() == 1 ? 1 : files1.size();
        mRvImages.setLayoutManager(new GridLayoutManager(getContext(), spanCount));
        int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
        mRvImages.addItemDecoration(new SpacesItemDecoration1(spacingInPixels, spanCount));
        imageAdapter = new BaseRecyclerViewAdapter<>(getContext(), files1, new BaseRecyclerViewAdapter.Delegate<String>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new ImageShowViewBean(getContext(), 0);
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, String url, View view) {
                ImageShowViewBean viewBean = (ImageShowViewBean) view;
                viewBean.setGlideTag(data.getPost().getId());
                viewBean.setData(url, files1.size(),true,data.getPost().getId());
                viewBean.setCallback(new ImageShowViewBean.Callback() {
                    @Override
                    public void onDelete(File file) {

                    }

                    @Override
                    public void onAdd() {

                    }

                    @Override
                    public void onSelect(ImageView mIvImage) {
                        ArrayList<Object> objects = new ArrayList<>();
                        if (post.getImageIdsCount() != 0) {
                            File tempImage = createTempBitmapFile(getContext(), BitmapUtils.base64ToBitmap(url), "temp_image");
                            new XPopup.Builder(getContext()).asImageViewer(mIvImage, tempImage.getAbsolutePath(), new SmartGlideImageLoader()).show();

                        } else if (post.getImagesUrlCount() != 0) {
                            for (int i = 0; i < post.getImagesUrlList().size(); i++) {
                                objects.add(post.getImagesUrlList().get(i));
                            }
                            new XPopup.Builder(getContext()).asImageViewer(mIvImage, position, objects, new OnSrcViewUpdateListener() {
                                @Override
                                public void onSrcViewUpdate(final ImageViewerPopupView popupView, final int position) {
                                }
                            }, new SmartGlideImageLoader()).show();
                        }
                    }

                    @Override
                    public void outSide() {

                    }
                });

            }
        });

        mRvImages.setAdapter(imageAdapter);

    }
    public File createTempBitmapFile(Context context, Bitmap bitmap, String fileName) {
        // 创建缓存目录（若不存在）
        File cacheDir = new File(context.getCacheDir(), "temp_images");
        File tempFile = null;
        try {
            if (!cacheDir.exists()) cacheDir.mkdirs();

            // 创建临时文件（前缀+时间戳保证唯一性）
            tempFile = File.createTempFile("TEMP_", ".jpg", cacheDir);

            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos); // 压缩质量80%
                fos.flush();
            }

        } catch (Exception e) {

        }

        return tempFile; // 返回临时文件引用
    }

    private void setDefaultImg() {
        currentOperation = WalletDaoUtils.getCurrentOperation(post.getId());
        currentOperation.setPostId(post.getId());
        currentOperation.setAddress(WalletDaoUtils.getCurrent().getAddress());
        if (currentOperation.getIsLike()) {
            Glide.with(mIvPraise)
                    .load(R.mipmap.icon_post_like_select)
                    .into(mIvPraise);
            mTvPraise.setTextColor(getResources().getColor(R.color.red_text));

        } else {
            Glide.with(mIvPraise)
                    .load(R.mipmap.icon_post_like)
                    .into(mIvPraise);
            mTvPraise.setTextColor(getResources().getColor(R.color.cosmos_black));
        }

        if (currentOperation.getIsCollect()) {
            Glide.with(mIvCollect)
                    .load(R.mipmap.icon_post_save_select)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(mIvCollect);
            mTvCollect.setTextColor(getResources().getColor(R.color.text_collect_blue));
        } else {
            Glide.with(mIvCollect)
                    .load(R.mipmap.icon_post_save)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(mIvCollect);
            mTvCollect.setTextColor(getResources().getColor(R.color.cosmos_black));
        }
    }

    private void setProfile() {
        mTvHandle.setText("@" + profile.getUserHandle());
        mTvAccountName.setText(profile.getNickname().isEmpty() ? profile.getUserHandle() : profile.getNickname());

        if (TextUtils.isEmpty(profile.getAvatar())) {
            TextAvatarDrawable a = new TextAvatarDrawable(profile.getUserHandle().substring(0, 1));
            mIvHeard.setImageDrawable(a);
        } else {
            if (profile.getAvatar().startsWith("http")) {
                Glide.with(getContext()).load(profile.getAvatar()).apply(new RequestOptions()
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .signature(new ObjectKey(profile.getAvatar()))
                                .centerCrop()
                                .format(DecodeFormat.PREFER_RGB_565)
                                .dontTransform())
                        .apply(RequestOptions.circleCropTransform().circleCrop())
                        .into(mIvHeard);
            } else {
                Bitmap bitmap1 = BitmapUtils.base64ToBitmap(profile.getAvatar());
                Glide.with(getContext()).load(bitmap1).apply(new RequestOptions()
                                .centerCrop()
                                .dontTransform())
                        .skipMemoryCache(true) // 跳过内存缓存
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .transition(DrawableTransitionOptions.withCrossFade(new DrawableCrossFadeFactory.Builder()
                                .setCrossFadeEnabled(true) // 解决透明图片残留问题
                                .build()))
                        .apply(RequestOptions.circleCropTransform().circleCrop())
                        .into(mIvHeard);
            }

        }

    }

    private void getOption() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                if (post.getPoll().getTotalVotes() != 0) {
                    String string = CosmosUtils.queryVoteOption(post.getId());
                    int i = Integer.parseInt(string);
                    mTvPraise.post(() -> {
                        if (i != -1) {
                            for (int i1 = 0; i1 < mLlVote.getChildCount(); i1++) {
                                ProposalOptionItemView proposalOptionItemView1 = (ProposalOptionItemView) mLlVote.getChildAt(i1);
                                proposalOptionItemView1.setSelected(false);
                            }
                            ProposalOptionItemView proposalOptionItemView1 = (ProposalOptionItemView) mLlVote.getChildAt(i);
                            proposalOptionItemView1.setSelected(true);
                        }
                    });
                }
            }
        });
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @OnClick({R.id.ll_share, R.id.ll_review, R.id.ll_praise, R.id.ll_Collect, R.id.tv_content, R.id.iv_more, R.id.ll_quote, R.id.iv_heard, R.id.ll_content_quote})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.ll_share:
                if (callback != null)
                    callback.share();
                break;
            case R.id.ll_content_quote:
                ContentInfoActivity.start(getContext(), data.getQuotePost().getId(), true);
                break;
            case R.id.iv_heard:
                UserInfoActivity.start(getContext(), profile.getWalletAddress());
                break;
            case R.id.tv_content:
                if (callback != null)
                    callback.info(data);
                break;
            case R.id.ll_review:
                if (callback != null)
                    callback.review(post.getId(), profile.getUserHandle());
                break;
            case R.id.ll_praise:
                Glide.with(getContext())
                        .asGif()
                        .load(R.drawable.heart_1) // 替换为你的GIF资源ID
                        .listener(new RequestListener<GifDrawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
                                resource.setLoopCount(1);
                                // 加载静态图片
                                resource.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {
                                    @Override
                                    public void onAnimationEnd(Drawable drawable) {
                                        // 加载静态图片
                                        Glide.with(mIvPraise)
                                                .load(R.mipmap.icon_post_like_select)
                                                .into(mIvPraise);
                                    }
                                });
                                return false;
                            }
                        })
                        .into(mIvPraise);
                String string = mTvPraise.getText().toString();
                if (!TextUtils.isEmpty(string) && TextUtils.isDigitsOnly(string)) {
                    int i = Integer.parseInt(string);
                    mTvPraise.setText((i + 1) + "");
                } else {
                    mTvPraise.setText("1");
                }
                if (callback != null)
                    callback.praise(post.getId());
                break;
            case R.id.ll_Collect:
                Glide.with(getContext())
                        .asGif()
                        .load(R.drawable.gif_collect) // 替换为你的GIF资源ID
                        .listener(new RequestListener<GifDrawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
                                resource.setLoopCount(1);
                                return false;
                            }
                        })
                        .into(mIvCollect);
                if (callback != null)
                    callback.collect(post.getId());
                break;
            case R.id.iv_more:
                if (callback != null)
                    callback.more(post.getId(), profile.getUserHandle(), profile.getWalletAddress());
                break;
            case R.id.ll_quote:
                if (callback != null)
                    callback.quote();
                break;
        }
    }

    public interface Callback {
        void quote();

        void share();

        void review(String id, String handle);

        void praise(String id);

        void collect(String id);

        void more(String data, String handle, String address);

        void info(PostQueryProto.PostResponse data);

    }
}
