// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TaskTopicItemView_ViewBinding implements Unbinder {
  private TaskTopicItemView target;

  @UiThread
  public TaskTopicItemView_ViewBinding(TaskTopicItemView target) {
    this(target, target);
  }

  @UiThread
  public TaskTopicItemView_ViewBinding(TaskTopicItemView target, View source) {
    this.target = target;

    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv1, "field 'mIv1'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TaskTopicItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mIv1 = null;
  }
}
