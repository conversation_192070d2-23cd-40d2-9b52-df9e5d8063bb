package com.Tlock.io.base;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.config.AppConstants;
import com.Tlock.io.custom.LoadErrorView;
import com.Tlock.io.entity.request.CondBean;
import com.Tlock.io.entity.request.PageableBean;
import com.Tlock.io.entity.request.RequestBean;
import com.Tlock.io.network.NetWorkConfig;
import com.Tlock.io.network.OKHttpManager;
import com.Tlock.io.utils.LoadingDialogUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.widget.CustomHeader;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import com.youth.banner.util.LogUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.ButterKnife;

public abstract class LazyLoadBaseFragment extends BaseLifeCircleFragment  implements OnLoadMoreListener, OnRefreshListener {

    protected View rootView = null;
    protected HashMap<String, Object> params = new HashMap<>();//装载网络请求参数
    private Dialog loadingDialog;
    protected LoadErrorView mErrorView; //加载错误页面
    protected SmartRefreshLayout mRefreshLayout; //下拉刷新上拉加载

    protected InputMethodManager inputMethodManager;
    protected int page = 1;
    protected RecyclerView mRecylerView;
    protected String TAG = getClass().getSimpleName();
    protected boolean isLoading = false; // 防止重复加载
    protected boolean isLastPage = false; // 标记是否为最后一页
    private boolean mIsFirstVisible = true;

    private boolean isViewCreated = false;

    private boolean currentVisibleState = false;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);

        rootView = inflater.inflate(getContentViewId(), container, false);
        ButterKnife.bind(this, rootView);
        initBase();
        initView(savedInstanceState);
        return rootView;
    }
    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        loadData();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        // 对于默认 tab 和 间隔 checked tab 需要等到 isViewCreated = true 后才可以通过此通知用户可见
        // 这种情况下第一次可见不是在这里通知 因为 isViewCreated = false 成立,等从别的界面回到这里后会使用 onFragmentResume 通知可见
        // 对于非默认 tab mIsFirstVisible = true 会一直保持到选择则这个 tab 的时候，因为在 onActivityCreated 会返回 false
        if (isViewCreated) {
            if (isVisibleToUser && !currentVisibleState) {
                dispatchUserVisibleHint(true);
            } else if (!isVisibleToUser && currentVisibleState) {
                dispatchUserVisibleHint(false);
            }
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        isViewCreated = true;
        // !isHidden() 默认为 true  在调用 hide show 的时候可以使用
        if (!isHidden() && getUserVisibleHint()) {
            // 这里的限制只能限制 A - > B 两层嵌套
            dispatchUserVisibleHint(true);
        }

    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        LogUtils.e(getClass().getSimpleName() + "  onHiddenChanged dispatchChildVisibleState  hidden " + hidden);

        if (hidden) {
            dispatchUserVisibleHint(false);
        } else {
            dispatchUserVisibleHint(true);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!mIsFirstVisible) {
            if (!isHidden() && !currentVisibleState && getUserVisibleHint()) {
                dispatchUserVisibleHint(true);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 当前 Fragment 包含子 Fragment 的时候 dispatchUserVisibleHint 内部本身就会通知子 Fragment 不可见
        // 子 fragment 走到这里的时候自身又会调用一遍 ？
        if (currentVisibleState && getUserVisibleHint()) {
            dispatchUserVisibleHint(false);
        }
    }

    private boolean isFragmentVisible(Fragment fragment) {
        return !fragment.isHidden() && fragment.getUserVisibleHint();
    }


    /**
     * 统一处理 显示隐藏
     *
     * @param visible
     */
    private void dispatchUserVisibleHint(boolean visible) {
        //当前 Fragment 是 child 时候 作为缓存 Fragment 的子 fragment getUserVisibleHint = true
        //但当父 fragment 不可见所以 currentVisibleState = false 直接 return 掉
//        LogUtils.e(getClass().getSimpleName() + "  dispatchUserVisibleHint isParentInvisible() " + isParentInvisible());
        // 这里限制则可以限制多层嵌套的时候子 Fragment 的分发
        if (visible && isParentInvisible()) return;
//        //此处是对子 Fragment 不可见的限制，因为 子 Fragment 先于父 Fragment回调本方法 currentVisibleState 置位 false
//        // 当父 dispatchChildVisibleState 的时候第二次回调本方法 visible = false 所以此处 visible 将直接返回
        if (currentVisibleState == visible) {
            return;
        }

        currentVisibleState = visible;

        if (visible) {
            if (mIsFirstVisible) {
                mIsFirstVisible = false;
                onFragmentFirstVisible();
            }
            onFragmentResume();
            dispatchChildVisibleState(true);
        } else {
            dispatchChildVisibleState(false);
            onFragmentPause();
        }
    }

    /**
     * 用于分发可见时间的时候父获取 fragment 是否隐藏
     *
     * @return true fragment 不可见， false 父 fragment 可见
     */
    public boolean isParentInvisible() {
        LazyLoadBaseFragment fragment = (LazyLoadBaseFragment) getParentFragment();
        return fragment != null && !fragment.isSupportVisible();

    }

    public boolean isSupportVisible() {
        return currentVisibleState;
    }

    /**
     * 当前 Fragment 是 child 时候 作为缓存 Fragment 的子 fragment 的唯一或者嵌套 VP 的第一 fragment 时 getUserVisibleHint = true
     * 但是由于父 Fragment 还进入可见状态所以自身也是不可见的， 这个方法可以存在是因为庆幸的是 父 fragment 的生命周期回调总是先于子 Fragment
     * 所以在父 fragment 设置完成当前不可见状态后，需要通知子 Fragment 我不可见，你也不可见，
     * <p>
     * 因为 dispatchUserVisibleHint 中判断了 isParentInvisible 所以当 子 fragment 走到了 onActivityCreated 的时候直接 return 掉了
     * <p>
     * 当真正的外部 Fragment 可见的时候，走 setVisibleHint (VP 中)或者 onActivityCreated (hide show) 的时候
     * 从对应的生命周期入口调用 dispatchChildVisibleState 通知子 Fragment 可见状态
     *
     * @param visible
     */
    private void dispatchChildVisibleState(boolean visible) {
        FragmentManager childFragmentManager = getChildFragmentManager();
        List<Fragment> fragments = childFragmentManager.getFragments();
        if (!fragments.isEmpty()) {
            for (Fragment child : fragments) {
                if (child instanceof LazyLoadBaseFragment && !child.isHidden() && child.getUserVisibleHint()) {
                    ((LazyLoadBaseFragment) child).dispatchUserVisibleHint(visible);
                }
            }
        }
    }

    public void onFragmentFirstVisible() {
        LogUtils.e(getClass().getSimpleName() + "  对用户第一次可见");

    }

    public void onFragmentResume() {
        LogUtils.e(getClass().getSimpleName() + "  对用户可见");
    }

    public void onFragmentPause() {
        LogUtils.e(getClass().getSimpleName() + "  对用户不可见");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        isViewCreated = false;
        mIsFirstVisible = true;
        OKHttpManager.cancelTag(this);

    }

    // 初始化base
    private void initBase() {
        //设置 RecylerView的布局管理器
        mRecylerView = rootView.findViewById(R.id.recyler_view);
        if (mRecylerView != null) {
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL, false);
            mRecylerView.setLayoutManager(linearLayoutManager);
        }
        //加载错误页面
        mErrorView = rootView.findViewById(R.id.load_error);
        // RefreshLayout
        mRefreshLayout = rootView.findViewById(R.id.refresh_layout);
        if (mRefreshLayout != null) {
            mRefreshLayout.setRefreshHeader(new CustomHeader(getActivity()));
            mRefreshLayout.setOnLoadMoreListener(this);
            mRefreshLayout.setOnRefreshListener(this);
        }
        //加载失败的点击
        if (mErrorView != null) {
            mErrorView.setOnClickListener(this::onErrorViewClick);
        }

        //请求参数
        initRequestParams();
    }

    /**************************************************************************************/
    /**
     * 显示加载错误页面
     *
     * @param message {@link NetWorkConfig#}
     */
    protected void showError(String message) {
        if (mErrorView != null) {
            mErrorView.setErrorMessage(message);
            mErrorView.setVisibility(View.VISIBLE);
        }
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    /**
     * 显示加载错误页面
     *
     * @param message
     * @param page
     */
    protected void showError(String message, int page) {
        if (page == 1) {
            showError(message);
        } else {
            showToast(message);
            finishRefresh();
        }
    }

    /**
     * 改变加载更多或者刷新状态
     */
    protected void finishRefresh() {
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    //隐藏加载错误页面
    protected void hideError() {
        if (mErrorView != null) {
            mErrorView.setVisibility(View.GONE);
        }
        if (mRefreshLayout != null) {
            mRefreshLayout.finishLoadMore();
            mRefreshLayout.finishRefresh();
        }
    }

    /**************************************************************************************/

    // 吐司
    protected void showToast(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        ToastUtil.toastShortCenter(getActivity(), msg);
    }


    //加载布局文件
    protected abstract int getContentViewId();

    //初始化Views
    protected abstract void initView(Bundle savedInstanceState);

    //加载数据
    protected abstract void loadData();


    /**
     * 初始化请求参数
     */
    protected RequestBean initRequestParams() {
        RequestBean mRequestBean = new RequestBean();
        CondBean condBean = new CondBean();
        condBean.setGroups(new ArrayList());
        condBean.setRules(new ArrayList());
        mRequestBean.setCond(condBean);
        PageableBean pageableBean = new PageableBean();
        mRequestBean.setPageable(pageableBean);
        return mRequestBean;
    }

    /**************************************************************************************/

    public void showLoading() {
        LoadingDialogUtils.getUtils().showProgressDialog(getActivity());
    }

    public void hideLoading() {
        LoadingDialogUtils.getUtils().dismissDialog();
    }

    /**************************************************************************************/
    /**
     * 失败点击重新弄加载
     */

    public void onErrorViewClick(View view) {
        refresh();
    }

    public void refresh() {
        if (mRefreshLayout != null) {
            mRefreshLayout.autoRefresh();
        }
    }

    /**
     * 上拉加载更多回调
     *
     * @param refreshLayout
     */
    @Override
    public void onLoadMore(RefreshLayout refreshLayout) {
        page++;
        getData(page);
    }

    /**
     * 下拉刷新回调
     *
     * @param refreshLayout
     */
    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        page = 1;
        isLastPage = false;
        isLoading = false;
        getData(page);
    }
    /**************************************************************************************/
    /**
     * 重写此方法即可实现带页码刷新
     *
     * @param page 页码
     */
    protected void getData(int page) {
        getData();
    }

    /**
     * 刷新数据执行
     */
    protected void getData() {

    }

    /**************************************************************************************/

    @Override
    public void onDestroy() {
        super.onDestroy();

        if (loadingDialog != null) {
            if (loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }
            loadingDialog = null;
        }

        OKHttpManager.cancelTag(this);
    }


    /**
     * 清理临时压缩的图片和语音
     */
    public void deleteCacheDir() {
        try {
            File file = new File(Environment.getExternalStorageDirectory(), AppConstants.SD_PATH);
            deleteDirWihtFile(file);
        } catch (Exception e) {
//            e.printStackTrace();
        }

    }

    private void deleteDirWihtFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
            else if (file.isDirectory())
                deleteDirWihtFile(file); // 递规的方式删除文件夹
        }
        dir.delete();// 删除目录本身
    }

    protected void hideSoftKeyboard() {
        if (getActivity().getWindow().getAttributes().softInputMode != WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN) {
            if (getActivity().getCurrentFocus() != null)
                inputMethodManager.hideSoftInputFromWindow(getActivity().getCurrentFocus().getWindowToken(),
                        InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    /**
     * 下拉刷新 上拉加载完成后的动作
     *
     * @param respSize  服务器返回的List长度
     * @param limitSize 设置的每次请求条数
     */
    public void loadOrRefreshComplete(int respSize, int limitSize) {
        if (mRefreshLayout != null) {
            //如果正在下拉刷新,停止刷新
            if (mRefreshLayout.getState() == RefreshState.Refreshing) {
                mRefreshLayout.finishRefresh();
            }
            //如果正在上拉加载,停止加载
            if (mRefreshLayout.getState() == RefreshState.Loading) {
                mRefreshLayout.finishLoadMore();
            }
            //如果服务器返回数据小于设置的请求数,说明此次请求已取出最后所有数据,不必再上拉加载
            if (respSize < limitSize) {
                mRefreshLayout.setEnableLoadMore(false);
            } else if (!mRefreshLayout.isEnableLoadMore()) {
                mRefreshLayout.setEnableLoadMore(true);
            }
        }
        if (respSize == 0 && page == 1 && mErrorView != null) {
            showError(getResources().getString(R.string.no_data));
        } else if (mErrorView != null && mErrorView.getVisibility() == View.VISIBLE) {
            mErrorView.setVisibility(View.GONE);
        }
    }

    /**
     * 取消加載
     */
    public void cancleLoad(int size){
        if (mRefreshLayout != null) {
            //如果正在下拉刷新,停止刷新
            if (mRefreshLayout.getState() == RefreshState.Refreshing) {
                mRefreshLayout.finishRefresh();
            }
            //如果正在上拉加载,停止加载
            if (mRefreshLayout.getState() == RefreshState.Loading) {
                mRefreshLayout.finishLoadMore();
            }
            if (size==0){
                mRefreshLayout.finishLoadMore();
                mRefreshLayout.finishRefresh();
            }
        }
    }

    /**
     * 根view获取焦点
     * 用于让其他的组件失去焦点
     */
    public void setRootViewFocus() {
        if (rootView == null) return;
        rootView.setClickable(true);
        rootView.setFocusable(true);
        rootView.setFocusableInTouchMode(true);
        rootView.requestFocus();
    }

    protected GridLayoutManager getGridlayoutManger(int clumns) {
        return new GridLayoutManager(getActivity(), clumns);
    }

//    /**
//     * 获取统一GridLayoutManger
//     *
//     * @return
//     */
//    protected GridLayoutManager getGridlayoutManger() {
//        boolean phone = LayoutUtil.isPhone();
//        boolean screenPortrait = LayoutUtil.isScreenPortrait();
//        int clumns = 1;
//        if (phone) {
//            return new GridLayoutManager(getActivity(), clumns);
//        }
//        return new GridLayoutManager(getActivity(), screenPortrait ? 2 : 3);
//
//    }


}