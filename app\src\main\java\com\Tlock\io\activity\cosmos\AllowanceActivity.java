package com.Tlock.io.activity.cosmos;

import static com.Tlock.io.utils.TimeUtils.timestampToLocalDate3;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.Tlock.io.R;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.entity.wallet.TokenInfo;
import com.Tlock.io.utils.BigDecimalUtils;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomNavBar;
import com.cosmos.feegrant.v1beta1.FeegrantProto;
import com.google.protobuf.Timestamp;

import java.time.Instant;

import butterknife.BindView;


public class AllowanceActivity extends BaseActivity {


    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.tv_current)
    TextView mTvCurrent;
    @BindView(R.id.tv_total)
    TextView mTvTotal;
    @BindView(R.id.tv_reset_time)
    TextView mTvResetTime;
    @BindView(R.id.tv_end_time)
    TextView mTvEndTime;


    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, AllowanceActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_allowance;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mCustomNavBar.setOnLelftClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }


    @Override
    protected void loadData() {
        if (WalletDaoUtils.getCurrent() != null) {
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @RequiresApi(api = Build.VERSION_CODES.O)
                @Override
                public void run() {
                    FeegrantProto.PeriodicAllowance basicAllowance = CosmosUtils.getGrantee();
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (basicAllowance == null) {
                                mTvCurrent.setText("-- TOK");
                                mTvTotal.setText("-- TOK");
                                mTvResetTime.setText("--");
                                mTvEndTime.setText("--");
                                return;
                            }
                            Timestamp periodReset = basicAllowance.getPeriodReset();

                            Instant instant = Instant.ofEpochSecond(periodReset.getSeconds(), periodReset.getNanos());
                            long epochSecond = instant.getEpochSecond();
                            long l = System.currentTimeMillis();
                            if (l / 1000 > epochSecond) {
                                mTvCurrent.setText(BigDecimalUtils.saveDecimals(BigDecimalUtils.division(String.valueOf(2000000), "1000000"), 2)+" TOK");

                            } else {
                                String amount = basicAllowance.getPeriodCanSpend(0).getAmount();
                                mTvCurrent.setText(BigDecimalUtils.saveDecimals(BigDecimalUtils.division(String.valueOf(amount), "1000000"), 2)+" TOK");
                            }
                            String amount = basicAllowance.getBasic().getSpendLimit(0).getAmount();
                            mTvTotal.setText(BigDecimalUtils.saveDecimals(BigDecimalUtils.division(String.valueOf(amount), "1000000"), 2)+" TOK");
                            String string = timestampToLocalDate3(epochSecond + "000", "MM-dd HH:mm");
                            mTvResetTime.setText(string);

                            Timestamp expiration = basicAllowance.getBasic().getExpiration();
                            Instant instant1 = Instant.ofEpochSecond(expiration.getSeconds(), expiration.getNanos());
                            long epochSecond1 = instant1.getEpochSecond();
                            String string1 = timestampToLocalDate3(epochSecond1 + "000", "yyyy-MM-dd HH:mm");
                            mTvEndTime.setText(string1);

                        }
                    });
                }
            });
        }
    }


}