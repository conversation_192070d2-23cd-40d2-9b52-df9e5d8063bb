<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":proto" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\cs\tlk\proto\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\cs\tlk\app\src\main\assets"><file name="fonts/chirp_heavy_800.otf" path="D:\cs\tlk\app\src\main\assets\fonts\chirp_heavy_800.otf"/><file name="fonts/chirp_medium_500.otf" path="D:\cs\tlk\app\src\main\assets\fonts\chirp_medium_500.otf"/><file name="fonts/chirp_regular_400.otf" path="D:\cs\tlk\app\src\main\assets\fonts\chirp_regular_400.otf"/><file name="fonts/ooo.ttf" path="D:\cs\tlk\app\src\main\assets\fonts\ooo.ttf"/><file name="fonts/Poppins-SemiBold.ttf" path="D:\cs\tlk\app\src\main\assets\fonts\Poppins-SemiBold.ttf"/><file name="fonts/Roboto-Medium-12.ttf" path="D:\cs\tlk\app\src\main\assets\fonts\Roboto-Medium-12.ttf"/><file name="fonts/Roboto-Medium.ttf" path="D:\cs\tlk\app\src\main\assets\fonts\Roboto-Medium.ttf"/><file name="fonts/roboto_medium_numbers.ttf" path="D:\cs\tlk\app\src\main\assets\fonts\roboto_medium_numbers.ttf"/><file name="fonts/xtt.ttf" path="D:\cs\tlk\app\src\main\assets\fonts\xtt.ttf"/><file name="verify/2.html" path="D:\cs\tlk\app\src\main\assets\verify\2.html"/><file name="verify/ibm.json" path="D:\cs\tlk\app\src\main\assets\verify\ibm.json"/><file name="verify/verify.html" path="D:\cs\tlk\app\src\main\assets\verify\verify.html"/></source><source path="D:\cs\tlk\app\build\intermediates\shader_assets\BetaDebug\out"/></dataSet><dataSet config="Beta" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\cs\tlk\app\src\Beta\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\cs\tlk\app\src\debug\assets"/></dataSet><dataSet config="BetaDebug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\cs\tlk\app\src\BetaDebug\assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\cs\tlk\app\src\BetaDebug\assets"/></dataSet></merger>