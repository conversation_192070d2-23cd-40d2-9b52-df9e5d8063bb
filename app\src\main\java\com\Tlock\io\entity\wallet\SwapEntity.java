package com.Tlock.io.entity.wallet;


/**
 * @ClassName SwapEntity
 * <AUTHOR>
 * @Data 2021/12/13 17:27
 * @Desc
 */
public class SwapEntity {
    private String value;
    private String router;
    private String amountIn;
    private String fee;
    private String amountOutMin;
    private String T0;
    private String T1;
    private String to;
    private String jumpToken;//多跳地址
    private String factoryAddress;//工厂地址
    private String deadline;
    private String gasPrice;
    private String gasLimit;
    private String wallsetAddress;
    private String approveAddress;
    private String hash;
    private String pairPrice;
    private String pairAddress;
    private String showCount;//展示数量
    private boolean isBuy;
    private boolean t0IsApprove;
    private boolean t1IsApprove;
    private int decimals0;
    private int decimals1;
    private int type;//1token->母币  2母币->token  3token->token 5添加流动性 6.移除流动性
    //-----------------流动性部分使用---------------------
    private String tokenMin;
    private String goldMin;
    private String tokenCount;
    private String goldCount;
    private String liquidity;//lp份数
    private String goldName;
    private String tokenName;
    //----------------------挂单部分----------------------------
    private String encodedFunction;
    private int planStatus;//-1 撤销 0未成交  1已成交
    private int chanID;
    private String chainNet;
    //----------------确认弹窗---------------
    private String msg;
    private boolean isShowTip;
    private  boolean isT0ETH;

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public boolean isT0ETH() {
        return isT0ETH;
    }

    public void setT0ETH(boolean t0ETH) {
        isT0ETH = t0ETH;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean isShowTip() {
        return isShowTip;
    }

    public void setShowTip(boolean showTip) {
        isShowTip = showTip;
    }

    public int getChanID() {
        return chanID;
    }

    public void setChanID(int chanID) {
        this.chanID = chanID;
    }

    public String getChainNet() {
        return chainNet;
    }

    public void setChainNet(String chainNet) {
        this.chainNet = chainNet;
    }

    public String getFactoryAddress() {
        return factoryAddress;
    }

    public void setFactoryAddress(String factoryAddress) {
        this.factoryAddress = factoryAddress;
    }

    public String getJumpToken() {
        return jumpToken;
    }

    public void setJumpToken(String jumpToken) {
        this.jumpToken = jumpToken;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getRouter() {
        return router;
    }

    public void setRouter(String router) {
        this.router = router;
    }

    public String getAmountIn() {
        return amountIn;
    }

    public void setAmountIn(String amountIn) {
        this.amountIn = amountIn;
    }

    public String getAmountOutMin() {
        return amountOutMin;
    }

    public void setAmountOutMin(String amountOutMin) {
        this.amountOutMin = amountOutMin;
    }

    public String getT0() {
        return T0;
    }

    public void setT0(String t0) {
        T0 = t0;
    }

    public String getT1() {
        return T1;
    }

    public void setT1(String t1) {
        T1 = t1;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getDeadline() {
        return deadline;
    }

    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }

    public String getGasPrice() {
        return gasPrice;
    }

    public void setGasPrice(String gasPrice) {
        this.gasPrice = gasPrice;
    }

    public String getGasLimit() {
        return gasLimit;
    }

    public void setGasLimit(String gasLimit) {
        this.gasLimit = gasLimit;
    }

    public String getWallsetAddress() {
        return wallsetAddress;
    }

    public void setWallsetAddress(String wallsetAddress) {
        this.wallsetAddress = wallsetAddress;
    }

    public String getApproveAddress() {
        return approveAddress;
    }

    public void setApproveAddress(String approveAddress) {
        this.approveAddress = approveAddress;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getPairPrice() {
        return pairPrice;
    }

    public void setPairPrice(String pairPrice) {
        this.pairPrice = pairPrice;
    }

    public String getPairAddress() {
        return pairAddress;
    }

    public void setPairAddress(String pairAddress) {
        this.pairAddress = pairAddress;
    }

    public String getShowCount() {
        return showCount;
    }

    public void setShowCount(String showCount) {
        this.showCount = showCount;
    }

    public boolean isBuy() {
        return isBuy;
    }

    public void setBuy(boolean buy) {
        isBuy = buy;
    }

    public boolean isT0IsApprove() {
        return t0IsApprove;
    }

    public void setT0IsApprove(boolean t0IsApprove) {
        this.t0IsApprove = t0IsApprove;
    }

    public boolean isT1IsApprove() {
        return t1IsApprove;
    }

    public void setT1IsApprove(boolean t1IsApprove) {
        this.t1IsApprove = t1IsApprove;
    }

    public int getDecimals0() {
        return decimals0;
    }

    public void setDecimals0(int decimals0) {
        this.decimals0 = decimals0;
    }

    public int getDecimals1() {
        return decimals1;
    }

    public void setDecimals1(int decimals1) {
        this.decimals1 = decimals1;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTokenMin() {
        return tokenMin;
    }

    public void setTokenMin(String tokenMin) {
        this.tokenMin = tokenMin;
    }

    public String getGoldMin() {
        return goldMin;
    }

    public void setGoldMin(String goldMin) {
        this.goldMin = goldMin;
    }

    public String getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(String tokenCount) {
        this.tokenCount = tokenCount;
    }

    public String getGoldCount() {
        return goldCount;
    }

    public void setGoldCount(String goldCount) {
        this.goldCount = goldCount;
    }

    public String getLiquidity() {
        return liquidity;
    }

    public void setLiquidity(String liquidity) {
        this.liquidity = liquidity;
    }

    public String getGoldName() {
        return goldName;
    }

    public void setGoldName(String goldName) {
        this.goldName = goldName;
    }

    public String getTokenName() {
        return tokenName;
    }

    public void setTokenName(String tokenName) {
        this.tokenName = tokenName;
    }

    public String getEncodedFunction() {
        return encodedFunction;
    }

    public void setEncodedFunction(String encodedFunction) {
        this.encodedFunction = encodedFunction;
    }

    public int getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(int planStatus) {
        this.planStatus = planStatus;
    }
}
