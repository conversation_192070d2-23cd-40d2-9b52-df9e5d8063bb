// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget.wallet;

import android.view.View;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class PopCheckFinger_ViewBinding implements Unbinder {
  private PopCheckFinger target;

  private View view7f090118;

  private View view7f090308;

  @UiThread
  public PopCheckFinger_ViewBinding(PopCheckFinger target) {
    this(target, target);
  }

  @UiThread
  public PopCheckFinger_ViewBinding(final PopCheckFinger target, View source) {
    this.target = target;

    View view;
    view = Utils.findRequiredView(source, R.id.img_finger, "method 'onViewClicked'");
    view7f090118 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.tv_cancel, "method 'onViewClicked'");
    view7f090308 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    target = null;


    view7f090118.setOnClickListener(null);
    view7f090118 = null;
    view7f090308.setOnClickListener(null);
    view7f090308 = null;
  }
}
