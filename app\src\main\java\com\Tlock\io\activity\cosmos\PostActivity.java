package com.Tlock.io.activity.cosmos;

import static android.Manifest.permission.READ_MEDIA_IMAGES;
import static android.Manifest.permission.READ_MEDIA_VIDEO;
import static com.Tlock.io.utils.FilePathUtil.convertUriToFile;

import android.Manifest;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.Spannable;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Tlock.io.R;
import com.Tlock.io.adapter.SpacesItemDecoration;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.base.BaseListBean;
import com.Tlock.io.base.BaseRecyclerViewAdapter;
import com.Tlock.io.entity.Event;
import com.Tlock.io.entity.post.PostProto;
import com.Tlock.io.entity.profile.ProfileProto;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.itemBean.cosmos.ImageViewBean;
import com.Tlock.io.network.OKHttpManager;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.post.PostTXProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.cosmos.ProposalEditText;
import com.Tlock.io.widget.pop.PopPostImageSelect;
import com.Tlock.io.widget.pop.PopSelectList;
import com.Tlock.io.widget.pop.PopWhaleList;
import com.bigkoo.pickerview.builder.OptionsPickerBuilder;
import com.bigkoo.pickerview.listener.OnOptionsSelectChangeListener;
import com.bigkoo.pickerview.listener.OnOptionsSelectListener;
import com.bigkoo.pickerview.view.OptionsPickerView;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BasePopupView;
import com.lxj.xpopup.core.ImageViewerPopupView;
import com.lxj.xpopup.enums.PopupAnimation;
import com.lxj.xpopup.interfaces.OnSrcViewUpdateListener;
import com.lxj.xpopup.util.SmartGlideImageLoader;
import com.sl.utakephoto.compress.CompressConfig;
import com.sl.utakephoto.compress.CompressImage;
import com.sl.utakephoto.compress.CompressImageImpl;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.OnClick;

public class PostActivity extends BaseActivity {


    @BindView(R.id.iv_close)
    ImageView mIvClose;
    @BindView(R.id.send_post)
    TextView mSendPost;
    @BindView(R.id.ed_title)
    EditText mEdTitle;
    @BindView(R.id.line2)
    View mLine2;
    @BindView(R.id.ed_content)
    EditText mEdContent;
    @BindView(R.id.main)
    RelativeLayout mMain;
    @BindView(R.id.iv_menu_at)
    ImageView mIvMenuAt;
    @BindView(R.id.iv_menu_topic)
    ImageView mIvMenuTopic;
    @BindView(R.id.ll_menu)
    LinearLayout mLlMenu;
    @BindView(R.id.iv_close_Proposal)
    ImageView mIvCloseProposal;
    @BindView(R.id.ed_proposal1)
    EditText mEdProposal1;
    @BindView(R.id.ll_vote)
    LinearLayout mLlVote;
    @BindView(R.id.rl_proposal)
    RelativeLayout mRlProposal;
    @BindView(R.id.iv_menu_list)
    ImageView mIvMenuList;
    @BindView(R.id.iv_proposal)
    ImageView mIvProposal;
    @BindView(R.id.time)
    TextView mTime;
    @BindView(R.id.scrollView)
    ScrollView mScrollView;

    @BindView(R.id.iv_classify)
    ImageView mIvClassify;
    @BindView(R.id.iv_1)
    ImageView mIv1;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.iv_select)
    ImageView mIvSelect;
    @BindView(R.id.rl_item)
    RelativeLayout mRlItem;
    @BindView(R.id.rv_images)
    RecyclerView mRvImages;
    @BindView(R.id.tv_1)
    TextView mTv1;
    @BindView(R.id.line3)
    View mLine3;


    private boolean isEditing = false;
    private int isupdata2Chain = 0;//0初始化 1上传服务器 2上传链
    private String previousText = "";
    private boolean isHashTag = false;
    private boolean isMention = false;
    private int tagStartPosition = -1;
    private static final String TAG = "YourTag";
    private Pattern hashtagPattern = Pattern.compile("#\\w+"); // 话题正则
    private Pattern mentionPattern = Pattern.compile("@\\w+"); // 提及正则
    private ArrayList<String> topicsList = new ArrayList<>();
    private ArrayList<String> mentionsList = new ArrayList<>();
//    private BaseRecyclerViewAdapter<ProfileProto.Profile> adapter;
//    private ArrayList<ProfileProto.Profile> pairList;

    private boolean isProposal = false;
    private ArrayList<String> day = new ArrayList<>();
    private ArrayList<String> hour = new ArrayList<>();
    private ArrayList<String> min = new ArrayList<>();
    private long endTime;
    private int from = 0;
    private String name = "";
    private PopWhaleList popWhaleList;
    private BasePopupView searchPop;
    private int keyboardHeight = 0;
    private ViewTreeObserver.OnGlobalLayoutListener onGlobalLayoutListener;

    private void initTime() {
        for (int i = 0; i < 60; i++) {
            if (i < 7) {
                day.add("" + i);
            }
            if (i < 24) {
                hour.add("" + i);
            }
            if (i < 60) {
                min.add("" + i);
            }
        }
    }

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context, int from, String name) {
        Intent intent = new Intent(context, PostActivity.class);
        intent.putExtra("from", from);
        intent.putExtra("name", name);
        context.startActivity(intent);
    }

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Activity context) {
        Intent intent = new Intent(context, PostActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected int getContentViewId() {
        return R.layout.activity_post;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
        initTime();
        Intent intent = getIntent();
        from = intent.getIntExtra("from", 0);
        name = intent.getStringExtra("name");
        setListner();
        initPop();
        initVote();
        initAdapter();

    }

    private void setPostBtn() {
        if (mEdContent.getText().toString().isEmpty() || files.size() > 3) {
            mSendPost.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
            mSendPost.setTextColor(getResources().getColor(R.color.cosmos_black));
        } else {
            mSendPost.setBackground(getResources().getDrawable(R.drawable.btn_black_60));
            mSendPost.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void initPop() {
        popWhaleList = new PopWhaleList(getActivity(), new ArrayList<>());
        popWhaleList.setCallBack(new PopWhaleList.CallBack() {
            @Override
            public void change(ProfileProto.Profile data) {
                String nickname = data.getUserHandle();
                replaceTag(nickname + " ");

            }
        });

        searchPop = new XPopup.Builder(getActivity())
                .atView(mLine3)
                .isViewMode(true)      //开启View实现
                .isRequestFocus(false) //不强制焦点
                .hasShadowBg(false)
                .hasBlurBg(false)
                .isClickThrough(true)
                .enableDrag(false)
                .popupAnimation(PopupAnimation.TranslateFromBottom)
                .asCustom(popWhaleList);
    }

    private void setListner() {
        mEdContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                previousText = charSequence.toString();
//                Log.e(TAG, "previousText: " + previousText);

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
//                Log.e(TAG, "charSequence: " + charSequence);
                setPostBtn();
            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable.toString().length() > 300) {
                    showToast("The content cannot exceed 300 characters");
                }

                if (editable.toString().isEmpty() && searchPop.isShow()) {
                    searchPop.dismiss();
                }
                if (editable.toString().endsWith(" ") && searchPop.isShow()) {
                    searchPop.dismiss();
                }

                if (isEditing) return;


                String currentText = editable.toString();
                int cursorPosition = mEdContent.getSelectionStart();

                // 1. 检测删除操作
                if (currentText.length() < previousText.length()) {
                    // 识别删除范围
                    int diff = previousText.length() - currentText.length();
                    // 确定删除的起始位置
                    int deleteStart = cursorPosition;
                    int deleteEnd = cursorPosition + diff;

                    // 获取删除后的文本中光标位置
                    // 分析删除的位置是否在话题或提及内
                    // 获取话题位置
                    ArrayList<int[]> hashtagSpans = getAllSpans(currentText, hashtagPattern);
                    ArrayList<int[]> mentionSpans = getAllSpans(currentText, mentionPattern);

                    boolean withinHashtag = false;
                    int targetHashtagStart = -1;
                    int targetHashtagEnd = -1;

                    for (int[] span : hashtagSpans) {
                        if (cursorPosition > span[0] && cursorPosition <= span[1]) {
                            withinHashtag = true;
                            targetHashtagStart = span[0];
                            targetHashtagEnd = span[1];
                            break;
                        }
                    }

                    boolean withinMention = false;
                    int targetMentionStart = -1;
                    int targetMentionEnd = -1;

                    if (!withinHashtag) { // 如果不在话题内，再检查提及
                        for (int[] span : mentionSpans) {
                            if (cursorPosition > span[0] && cursorPosition <= span[1]) {
                                withinMention = true;
                                targetMentionStart = span[0];
                                targetMentionEnd = span[1];
                                break;
                            }
                        }
                    }

                    if (withinHashtag) {
                        // 删除整个话题
                        Editable editableContent = mEdContent.getText();
//                        Log.e(TAG, "withinHashtag: " + targetHashtagStart + " " + targetHashtagEnd);
                        editableContent.delete(targetHashtagStart, targetHashtagEnd);
                        cursorPosition = targetHashtagStart;
                        mEdContent.setSelection(cursorPosition);

                        // 更新 currentText after deletion
                        currentText = editableContent.toString();
                    } else if (withinMention) {
                        // 删除整个提及
                        Editable editableContent = mEdContent.getText();
//                        Log.e(TAG, "withinMention: " + targetMentionStart + " " + targetMentionEnd);

                        editableContent.delete(targetMentionStart, targetMentionEnd);
                        cursorPosition = targetMentionStart;
                        mEdContent.setSelection(cursorPosition);

                        // 更新 currentText after deletion
                        currentText = editableContent.toString();
                    }
                }

                // 2. 自动在#和@前添加空格
                if (cursorPosition > 0 && (currentText.charAt(cursorPosition - 1) == '#' || currentText.charAt(cursorPosition - 1) == '@')) {
                    if (cursorPosition == 1 || currentText.charAt(cursorPosition - 2) != ' ') {
                        editable.replace(cursorPosition - 1, cursorPosition, " " + currentText.charAt(cursorPosition - 1));
                        cursorPosition += 1; // 调整光标位置
                        mEdContent.setSelection(cursorPosition);
                        currentText = editable.toString(); // 更新文本内容
                    }
                }

                // 3. 进行颜色高亮并收集话题和提及
                Matcher hashtagMatcher = hashtagPattern.matcher(currentText);
                Matcher mentionMatcher = mentionPattern.matcher(currentText);
                ArrayList<String> currentHashtags = new ArrayList<>();
                ArrayList<String> currentMentions = new ArrayList<>();

                // 处理话题
                while (hashtagMatcher.find()) {
                    String hashtag = hashtagMatcher.group(); // 获取匹配的 #标签
                    currentHashtags.add(hashtag.replace("#", "").trim()); // 添加到当前话题列表

                    // 设置字体颜色为蓝色
                    editable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.cosmos_topic)),
                            hashtagMatcher.start(),
                            hashtagMatcher.end(),
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                // 处理提及
                while (mentionMatcher.find()) {
                    String mention = mentionMatcher.group(); // 获取匹配的 @标签
                    currentMentions.add(mention.replace("@", "").trim()); // 添加到当前提及列表

                    // 设置字体颜色为绿色
                    editable.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.cosmos_topic)),
                            mentionMatcher.start(),
                            mentionMatcher.end(),
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                // 4. 更新 EditText 内容

                try {
                    mEdContent.setSelection(cursorPosition); // 保持光标位置
                    String str = mEdContent.getText().toString();
                    int i = str.lastIndexOf(' ', cursorPosition - 1);
                    String substring = str.substring(i, cursorPosition);
//                    Log.e(TAG, "这是截取的内容: " + substring + "   cursorPosition  " + cursorPosition + " i  " + i);

                    if (substring.contains("@")) {
                        //@
                        getAtData(substring.replace("@", "").trim());
                    } else if (substring.contains("#")) {
                        // #
                        getTopicData(substring.replace("#", "").trim());
                    }

                } catch (IndexOutOfBoundsException e) {
                    // 处理可能的异常，如光标位置超出范围
                }
                // 5. 更新话题和提及列表
                updateLists(currentHashtags, currentMentions);

                previousText = currentText; // 更新前一个文本状态


            }
        });
        mEdContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showSoftKeyboard();
            }
        });
    }

    int count = 3;

    private void initVote() {
        for (int i = 2; i < count; i++) {
            addView();
        }
    }

    private void addView() {
        ProposalEditText proposalEditText = new ProposalEditText(getActivity());
        proposalEditText.setHint("Choice");
        proposalEditText.setAdd(true);
        if (mLlVote.getChildCount() != 0) {
            ProposalEditText v = (ProposalEditText) mLlVote.getChildAt(mLlVote.getChildCount() - 1);
            v.setAdd(false);
        }
        proposalEditText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                boolean add = ((ProposalEditText) view).isAdd();
                if (add) {
                    //添加
                    if (mLlVote.getChildCount() == 3) {
                        ToastUtil.toastView("Maximum 4 options");
                        return;
                    }
                    addView();
                } else {
                    //移除
                    mLlVote.removeView(view);
                }
            }
        });
        mLlVote.addView(proposalEditText);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        mEdContent.getViewTreeObserver().removeOnGlobalLayoutListener(onGlobalLayoutListener);
        onGlobalLayoutListener = null;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTokenChane(Event event) {
        if (event.getMessgae().equalsIgnoreCase("topic_callback")) {
            mEdContent.append(event.getData());
        }
    }

    /**
     * 更新话题和提及列表并显示
     *
     * @param currentHashtags 当前文本中的话题列表
     * @param currentMentions 当前文本中的提及列表
     */
    private void updateLists(ArrayList<String> currentHashtags, ArrayList<String> currentMentions) {
        // 更新话题列表
        topicsList.clear();
        topicsList.addAll(currentHashtags);
        // 更新提及列表
        mentionsList.clear();
        mentionsList.addAll(currentMentions);
    }


    public void replaceTag(String selectedText) {
        Editable editable = mEdContent.getText();
        if (editable == null) return;

        int cursorPos = mEdContent.getSelectionStart();
        if (cursorPos <= 0) return;

        // 找到光标前的最近一个@或#

        int lastAt = editable.toString().lastIndexOf('@', cursorPos - 1);
        int lastHash = editable.toString().lastIndexOf('#', cursorPos - 1);
        int tagPos = Math.max(lastAt, lastHash);
        if (tagPos == -1) return;

        char symbol = editable.charAt(tagPos);
        if (symbol != '@' && symbol != '#') return;

        // 找到@或#后的空格或文本末尾
        cursorPos = editable.toString().indexOf(' ', tagPos);
        if (cursorPos == -1) {
            cursorPos = editable.length();
        }

        // 删除旧的内容
        editable.delete(tagPos + 1, cursorPos);

        // 插入新的内容和一个空格
        if (tagPos >= editable.length()) {
            editable.append(selectedText + " "); // 在文本末尾添加文本
        } else {
            editable.insert(tagPos + 1, selectedText);
        }
//        Log.e(TAG, "replaceTag: " + mEdContent.getText().toString() + "  " + mEdContent.getText().length());
        int a = tagPos + 1 + selectedText.length();
//        Log.e(TAG, "replaceTag: " + a);
        // 移动光标到新插入内容的末尾
        mEdContent.setSelection(a);

    }


    /**
     * 获取所有匹配的标记的起始和结束索引
     *
     * @param text    当前文本
     * @param pattern 正则表达式模式
     * @return ArrayList<int [ ]> 每个 int[] 包含 [start, end] 索引
     */
    private ArrayList<int[]> getAllSpans(String text, Pattern pattern) {
        ArrayList<int[]> spans = new ArrayList<>();
        Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            spans.add(new int[]{matcher.start(), matcher.end()});
        }
        return spans;
    }

    @Override
    protected void loadData() {
    }

    private void getBody(ArrayList<String> imgs) {
        if (from != 0) {
            topicsList.add(name);
        }
        PostTXProto.MsgCreatePost.Builder builderTX = PostTXProto.MsgCreatePost.newBuilder();
        ETHWallet current = WalletDaoUtils.getCurrent();
        builderTX.setCreator(current.getAddress());
        PostTXProto.PostDetail.Builder builder = PostTXProto.PostDetail.newBuilder();

        if (!mEdTitle.getText().toString().isEmpty()) {
            builder.setTitle(mEdTitle.getText().toString());
        }

        if (!mEdTitle.getText().toString().isEmpty()) {
            builder.setTitle(mEdTitle.getText().toString());
        }
        if (imgs.size() > 0) {
            if (isupdata2Chain == 2) {
                builder.addAllImagesBase64(imgs);
            } else {
                builder.addAllImagesUrl(imgs);
            }
        }
        if (mEdContent.getText().toString().isEmpty()) {
            ToastUtil.toastView("The content cannot be empty");
            return;
        } else {
            if (topicsList != null && topicsList.size() > 0) {
                builder.addAllTopic(topicsList);
            }
            if (mentionsList != null && mentionsList.size() > 0) {
                builder.addAllMention(mentionsList);
            }
            if (!classify.isEmpty()) {
                builder.setCategory(classify);
            }
            builder.setContent(mEdContent.getText().toString());
        }
        builderTX.setPostDetail(builder.build());
        CosmosUtils.sendPostBody(builderTX.build());
        runOnUiThread(
                new Runnable() {
                    @Override
                    public void run() {
                        showToast("Send post successfully");
                    }
                }
        );
        if (from != 0) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showToast("Send post successfully");
                    EventBus.getDefault().postSticky(new Event("refresh_topic"));

                }
            });
        }
    }

    private void getAtData(String math) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<ProfileProto.Profile> userList = new ArrayList<>();

                userList = CosmosUtils.getAtUserList(math);
//                Log.e(TAG, "userList: " + JsonUtils.listToJson(userList));
                ArrayList<ProfileProto.Profile> finalUserList = userList;
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        boolean dismiss = searchPop.isDismiss();
                        if (dismiss) {
                            searchPop.show();

                        }
                        popWhaleList.setBeans(finalUserList);
                    }
                });
            }
        });
    }


    private void getTopicData(String math) {
        Log.e(TAG, "math: " + math);
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                ArrayList<PostQueryProto.TopicResponse> topic;
                if (math.isEmpty()) {
                    topic = CosmosUtils.getTrendingKeywords(1);
                } else {
                    topic = CosmosUtils.getTopic(math);
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ArrayList<ProfileProto.Profile> objects = new ArrayList<>();
                        if (topic.size() == 0) {
                            ProfileProto.Profile.Builder builder = ProfileProto.Profile.newBuilder();
                            builder.setUserHandle(math);
                            objects.add(builder.build());
                        }
                        for (PostQueryProto.TopicResponse data : topic) {
                            ProfileProto.Profile.Builder builder = ProfileProto.Profile.newBuilder();
                            builder.setUserHandle(data.getName());
                            objects.add(builder.build());
                        }
                        if (searchPop.isDismiss()) {
                            searchPop.show();
                        }
                        popWhaleList.setBeans(objects);

                    }
                });
            }
        });
    }

    @OnClick({R.id.iv_close, R.id.send_post, R.id.iv_menu_at, R.id.iv_menu_topic, R.id.iv_classify, R.id.iv_menu_list, R.id.iv_proposal, R.id.iv_close_Proposal, R.id.time, R.id.iv_select})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_close:
                finish();
                break;
            case R.id.iv_classify:
                //分类
                PopSelectList popSelectList = new PopSelectList(getActivity());
                popSelectList.setCallBack(new PopSelectList.CallBack() {
                    @Override
                    public void Done(PostQueryProto.CategoryResponse data, int position) {
                        classify = data.getName();
                        mRlItem.setVisibility(View.VISIBLE);
                        mTvTitle.setText(data.getName());
                        switch (position) {
                            case 0:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic1));
                                break;
                            case 1:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic2));
                                break;
                            case 2:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic3));
                                break;
                            case 3:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic4));
                                break;
                            case 4:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic5));
                                break;
                            case 5:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic6));
                                break;
                            case 6:
                                mIv1.setImageDrawable(getResources().getDrawable(R.mipmap.icon_topic7));
                                break;
                        }
                    }
                });
                new XPopup.Builder(getActivity())
                        .hasShadowBg(true)
                        .isDestroyOnDismiss(true)
                        .isClickThrough(true)
                        .isRequestFocus(true)
                        .asCustom(popSelectList).show();
                break;
            case R.id.send_post:
                if (mEdContent.getText().toString().length() > 300) {
                    showToast("Content cannot exceed 300 characters");
                    return;
                }
                if (mEdContent.getText().toString().isEmpty()) {
                    showToast("The content cannot be empty");
                    return;
                }
                if (files.size() >1) {
                    showToast("Multiple selections aren't supported.");
                    return;
                }
                if (isProposal) {
                    //投票
                    if (mEdTitle.getText().toString().isEmpty()) {
                        ToastUtil.toastView("Please enter the poll title");
                        return;
                    }
                    if (mEdProposal1.getText().toString().isEmpty()) {
                        ToastUtil.toastView("The voting option cannot be empty");
                        return;
                    }
                    ArrayList<PostProto.Vote> votes = new ArrayList<>();
                    PostProto.Vote build1 = PostProto.Vote.newBuilder()
                            .setId(0)
                            .setOption(mEdProposal1.getText().toString())
                            .build();
                    votes.add(build1);
                    for (int i = 0; i < mLlVote.getChildCount(); i++) {
                        ProposalEditText childAt = (ProposalEditText) mLlVote.getChildAt(i);
                        String editText = childAt.getEditText();
                        if (editText.isEmpty()) {
                            ToastUtil.toastView("The voting option cannot be empty");
                            return;
                        }
                        PostProto.Vote build = PostProto.Vote.newBuilder()
                                .setId(i + 1)
                                .setOption(editText)
                                .build();
                        votes.add(build);
                    }
                    if (endTime == 0) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.DAY_OF_YEAR, 1);
                        long futureTimeStamp = calendar.getTimeInMillis();
                        endTime = futureTimeStamp;
                    }
                    finish();
                    sendProposal(votes);
                    return;
                } else {
                    finish();
                    if (isupdata2Chain == 2) {
                        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                            @Override
                            public void run() {
                                ArrayList<String> bitmaps = new ArrayList<>();
                                for (File file : files) {
                                    String string = compressImage(BitmapFactory.decodeFile(file.getAbsolutePath()));
                                    int length = string.length();
                                    bitmaps.add(string);
                                }
                                getBody(bitmaps);
                            }
                        });
                    } else {
                        uploadAvatar();
                    }
                }
                break;
            case R.id.iv_menu_at:
                // 获取光标当前位置
                int cursorPosition = mEdContent.getSelectionStart();
                Log.e(TAG, "iv_menu_at: " + cursorPosition);
                mEdContent.getText().insert(cursorPosition, " @");
                mEdContent.setSelection(cursorPosition + 2);
                showSoftKeyboard();
                break;
            case R.id.iv_menu_topic:
                int cursorPosition1 = mEdContent.getSelectionStart();
                Log.e(TAG, "iv_menu_at: " + cursorPosition1);
                mEdContent.getText().insert(cursorPosition1, " #");
                mEdContent.setSelection(cursorPosition1 + 2);
                showSoftKeyboard();
                break;
            case R.id.iv_menu_list:
                TopicActivity.start(getActivity());
                break;
            case R.id.iv_close_Proposal:
                isProposal = false;
                mLlMenu.setVisibility(View.VISIBLE);
                mEdContent.setVisibility(View.VISIBLE);
                mRvImages.setVisibility(View.VISIBLE);
                mLine2.setVisibility(View.VISIBLE);
                mLine3.setVisibility(View.VISIBLE);
                mRlProposal.setVisibility(View.GONE);
                mEdTitle.setText("");
                mEdTitle.setHint("Please enter the title");
                break;
            case R.id.iv_proposal:
                isProposal = true;
                mLlMenu.setVisibility(View.GONE);
                mEdContent.setVisibility(View.GONE);
                mRvImages.setVisibility(View.GONE);
                mRlProposal.setVisibility(View.VISIBLE);
                mLine2.setVisibility(View.GONE);
                mLine3.setVisibility(View.GONE);

                mEdTitle.setText("");
                mEdTitle.setHint("Voting Content…");
                break;
            case R.id.time:
                hideSoftKeyboard();
                //时间选择器
                OptionsPickerView pvNoLinkOptions = new OptionsPickerBuilder(this, new OnOptionsSelectListener() {
                    @Override
                    public void onOptionsSelect(int options1, int options2, int options3, View v) {
                        String strDay = day.get(options1);
                        String strHour = hour.get(options2);
                        String strMin = min.get(options3);
                        Calendar calendar = Calendar.getInstance();

                        calendar.add(Calendar.DAY_OF_YEAR, Integer.parseInt(strDay));
                        calendar.add(Calendar.HOUR_OF_DAY, Integer.parseInt(strHour));
                        calendar.add(Calendar.MINUTE, Integer.parseInt(strMin));

                        long futureTimeStamp = calendar.getTimeInMillis();
                        endTime = futureTimeStamp;
                        mTime.setText("Poll length: " + strDay + "Day " + strHour + "Hour " + strMin + "Minute");
                    }
                })
                        .isCenterLabel(false)
                        .setLabels("Day", "Hour", "Minute")
                        .setOptionsSelectChangeListener(new OnOptionsSelectChangeListener() {
                            @Override
                            public void onOptionsSelectChanged(int options1, int options2, int options3) {

                            }
                        })
                        .setTitleText("Poll length")
                        .setItemVisibleCount(3)
                        .build();
                pvNoLinkOptions.setNPicker(day, hour, min);
                pvNoLinkOptions.setSelectOptions(1, 0, 0);
                pvNoLinkOptions.show();
                break;
            case R.id.iv_select:
                classify = "";
                mRlItem.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 压缩文件
     *
     * @param bitmap
     * @return
     */
    public static String compressImage(Bitmap bitmap) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);//质量压缩方法，这里100表示不压缩，把压缩后的数据存放到baos中
        int options = 100;
        while (baos.toByteArray().length / 1024 > 300) {  //循环判断如果压缩后图片是否大于500kb,大于继续压缩
            baos.reset();//重置baos即清空baos
            options -= 10;//每次都减少10
            bitmap.compress(Bitmap.CompressFormat.JPEG, options, baos);//这里压缩options%，把压缩后的数据存放到baos中
            long length = baos.toByteArray().length;
//            Log.e("图片", "length: " + length);
        }

        return Base64.encodeToString(baos.toByteArray(), Base64.NO_WRAP);
    }


    private String classify = "";

    private void sendProposal(ArrayList<PostProto.Vote> data) {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                PostProto.Poll vote = PostProto.Poll.newBuilder()
                        .addAllVote(data)
                        .setVotingEnd(endTime)
                        .build();

                PostTXProto.PostDetail.Builder builder = PostTXProto.PostDetail.newBuilder()
                        .setPoll(vote)
                        .setContent(mEdTitle.getText().toString());
                PostTXProto.MsgCreatePost buildTX = PostTXProto.MsgCreatePost.newBuilder()
                        .setCreator(WalletDaoUtils.getCurrent().getAddress())
                        .setPostDetail(builder.build())
                        .build();
                CosmosUtils.sendPostBody(buildTX);
            }
        });
    }

    public void showSoftKeyboard() {
        mEdContent.requestFocus();
        InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.showSoftInput(mEdContent, InputMethodManager.SHOW_IMPLICIT);
        }
    }

    /******************************************添加图片开始*****************************************************/

    private static final int REQUEST_CODE_PERMISSIONS = 100;
    private final String[] REQUIRED_PERMISSIONS = {
            READ_MEDIA_IMAGES,
            READ_MEDIA_VIDEO,
            Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
    };
    private BaseRecyclerViewAdapter<File> imageAdapter;
    private ArrayList<File> files = new ArrayList<>();

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, REQUIRED_PERMISSIONS[0]) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS);
        } else {
            selectMultipleImages(); // 已有权限，直接打开相册
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    selectMultipleImages();
                }
            } else {
                selectMultipleImages();
            }

        }
    }

    private ActivityResultLauncher<Intent> galleryLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            this::onActivityResult);

    private void uploadAvatar() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                if (files.size() != 0) {
                    OKHttpManager.postAsynUploadFiles("http://************:8080/api/s3/uploadImages", params, "files", files, new OKHttpManager.ResultCallback<BaseListBean<String>>() {
                        @Override
                        public void onError(int code, String result, String message) {
                        }

                        @Override
                        public void onResponse(BaseListBean<String> response) {
                            showToast("Send post successfully");
                            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                                @Override
                                public void run() {
                                    getBody(response.getData());

                                }
                            });

                        }
                    });
                } else {
                    AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                        @Override
                        public void run() {
                            getBody(new ArrayList<>());
                        }
                    });
                }
            }
        });
        params.put("files", files);
    }

    private void selectMultipleImages() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        galleryLauncher.launch(Intent.createChooser(intent, "选择图片"));

    }

    private void onActivityResult(ActivityResult result) {
        if (result.getResultCode() == RESULT_OK && result.getData() != null) {
            Intent data = result.getData();
            // 处理多张图片
            if (data.getClipData() != null) {
                // 多选图片的情况
                for (int i = 0; i < data.getClipData().getItemCount(); i++) {
                    Uri imageUri = data.getClipData().getItemAt(i).getUri();
                    File file = convertUriToFile(getActivity(), imageUri);
                    files.add(file);
                }
            } else if (data.getData() != null) {
                // 单选图片的情况
                File file = convertUriToFile(getActivity(), data.getData());
                files.add(file);
            }
            // 显示选择图片数量
            ArrayList<File> files1 = new ArrayList<>(files);
            if (isupdata2Chain == 1) {
                if (files1.size() < 3) {
                    files1.add(null);
                }
            } else {
                if (files1.size() < 1) {
                    files1.add(null);
                }
            }
            setPostBtn();
            imageAdapter.setList(files1);
        }
    }

    private void setCompose(Uri uri, int position) {
        CompressImageImpl.of(getActivity(), new CompressConfig.Builder()
                .setFocusAlpha(false)//是否支持透明度
                .setLeastCompressSize(200)//最小压缩尺寸
                .create(), Collections.singletonList(uri), new CompressImage.CompressListener() {
            @Override
            public void onStart() {

            }

            @Override
            public void onSuccess(Uri uri) {
                Bitmap bitmap = getBitmapFromUri(uri);
                String base64String = BitmapUtils.bitmapToBase64(bitmap);

            }

            @Override
            public void onError(Throwable obj) {

            }
        }).compress();
    }

    private Bitmap getBitmapFromUri(Uri uri) {
        try {
            ContentResolver cr = getActivity().getContentResolver();
            InputStream is = cr.openInputStream(uri);
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inSampleSize = 2; // 压缩图片，避免内存溢出
            return BitmapFactory.decodeStream(is, null, options);
        } catch (FileNotFoundException e) {
        }
        return null;
    }

    private void initAdapter() {
        mRvImages.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        ArrayList<File> files1 = new ArrayList<>(files);
        if (isupdata2Chain == 1) {
            if (files1.size() < 3) {
                files1.add(null);
            }
        } else {
            if (files1.size() < 1) {
                files1.add(null);
            }
        }

        int spanCount = files1.size() == 1 ? 1 : files1.size();
        mRvImages.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.dp_3); // 定义在 res/values/dimens.xml
        mRvImages.addItemDecoration(new SpacesItemDecoration(spacingInPixels, spanCount));

        imageAdapter = new BaseRecyclerViewAdapter<>(getActivity(), files1, new BaseRecyclerViewAdapter.Delegate<File>() {
            @Override
            public View buildView(ViewGroup parent, int viewType) {
                return new ImageViewBean(getActivity(), 0);
            }

            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void bindViewData(int position, File file, View view) {
                ImageViewBean viewBean = (ImageViewBean) view;
                viewBean.setData(file);
                viewBean.setCallback(new ImageViewBean.Callback() {
                    @Override
                    public void onDelete(File file) {
                        files.remove(file);
                        if (files.size() == 0) isupdata2Chain = 0;
                        ArrayList<File> files1 = new ArrayList<>(files);
                        if (isupdata2Chain == 1) {
                            if (files1.size() < 3) {
                                files1.add(null);
                            }
                        } else {
                            if (files1.size() < 1) {
                                files1.add(null);
                            }
                        }

                        if (files.size() > 1 ) {
                            showToast("Multiple selections aren't supported.");
                            mSendPost.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
                            mSendPost.setTextColor(getResources().getColor(R.color.cosmos_black));
                        }else if(mEdContent.getText().toString().isEmpty()){
                            mSendPost.setBackground(getResources().getDrawable(R.drawable.shape_deep_gray_60));
                            mSendPost.setTextColor(getResources().getColor(R.color.cosmos_black));
                        } else {
                            mSendPost.setBackground(getResources().getDrawable(R.drawable.btn_black_60));
                            mSendPost.setTextColor(getResources().getColor(R.color.white));
                        }
                        imageAdapter.setList(files1);
                    }

                    @Override
                    public void onAdd() {
                        if (isupdata2Chain == 0) {
                            isupdata2Chain = 2;
                            checkPermissions();
//                            PopPostImageSelect popPostImageSelect = new PopPostImageSelect(getActivity());
//                            popPostImageSelect.setCallback(new PopPostImageSelect.Callback() {
//
//                                @Override
//                                public void chain() {
//                                    isupdata2Chain = 2;
//                                    checkPermissions();
//                                }
//
//                                @Override
//                                public void service() {
//                                    isupdata2Chain = 1;
//                                    checkPermissions();
//                                }
//                            });
//                            new XPopup.Builder(getActivity())
//                                    .asCustom(popPostImageSelect)
//                                    .show();
                        } else {
                            checkPermissions();
                        }
                    }

                    @Override
                    public void onSelect(ImageView mIvImage) {
                        ArrayList<Object> list = new ArrayList<>();
                        for (File file1 : files) {
                            list.add(file1);
                        }
                        new XPopup.Builder(getActivity()).asImageViewer(mIvImage, position, list,
                                        new OnSrcViewUpdateListener() {
                                            @Override
                                            public void onSrcViewUpdate(final ImageViewerPopupView popupView, final int position) {

                                            }
                                        }, new SmartGlideImageLoader())
                                .show();
                    }
                });

            }
        });
        mRvImages.setAdapter(imageAdapter);

    }
    /******************************************添加图片开始*****************************************************/


}