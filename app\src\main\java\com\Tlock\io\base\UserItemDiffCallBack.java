package com.Tlock.io.base;

import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DiffUtil;

import com.Tlock.io.post.PostQueryProto;

import java.util.ArrayList;

public class UserItemDiffCallBack<T> extends DiffUtil.Callback {

    //旧的数据集合
    private ArrayList<T> mOldUserList;
    //新的数据集合
    private ArrayList<T> mNewUserList;

    //构造方法 传入旧的数据结构和新的数据结构
    public UserItemDiffCallBack(ArrayList<T> oldUserList, ArrayList<T> newUserList) {
        this.mOldUserList = oldUserList;
        this.mNewUserList = newUserList;
    }

    //获取旧的数据量大小
    @Override
    public int getOldListSize() {
        return null == mOldUserList ? 0 : mOldUserList.size();
    }

    //获取新的数据量大小
    @Override
    public int getNewListSize() {
        return null == mNewUserList ? 0 : mNewUserList.size();
    }

    //判断两个条目是否是一致的
    //在真实的项目中，我们一般使用id或者index搜索来判断两条item是否一致
    //如果我们的id一样，在系统里面我就认为两个数据记录是一样的
    @Override
    public boolean areItemsTheSame(int oldPosition, int newPosition) {
        if (oldPosition == newPosition) {
            return TextUtils.equals(((PostQueryProto.PostResponse) mOldUserList.get(oldPosition)).getPost().getId(), ((PostQueryProto.PostResponse) mNewUserList.get(oldPosition)).getPost().getId());

        } else {
            return false;
        }
    }

    //这个需要areItemsTheSame 返回true时才调用
    //即使我们的id是一致的，我们在系统中是同一个对象，但是的name可能更新，或者图像可能更新了
    //这里可以填写自己的逻辑，如果图像是一致的，我就认为内容没有变化
    @Override
    public boolean areContentsTheSame(int oldPosition, int newPosition) {
        boolean like = ((PostQueryProto.PostResponse) mOldUserList.get(oldPosition)).getPost().getLikeCount() == ((PostQueryProto.PostResponse) mNewUserList.get(oldPosition)).getPost().getLikeCount();
        boolean comment = ((PostQueryProto.PostResponse) mOldUserList.get(oldPosition)).getPost().getCommentCount() == ((PostQueryProto.PostResponse) mNewUserList.get(oldPosition)).getPost().getCommentCount();
        return like && comment;
    }

    @Nullable
    @Override
    public Object getChangePayload(int oldItemPosition, int newItemPosition) {
        Bundle payload = new Bundle();
        payload.putBoolean("KEY_UP_DOWN", true);
        return payload;
    }
}