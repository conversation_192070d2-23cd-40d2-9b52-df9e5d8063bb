{"logs": [{"outputFile": "com.Tlock.io.app-mergeBetaDebugResources-51:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0689f52c8d09f0e15ee5135366e3b8c\\transformed\\material-1.4.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,306,413,526,611,674,768,834,896,999,1070,1129,1205,1270,1324,1437,1495,1556,1610,1689,1805,1888,1979,2091,2170,2249,2337,2404,2470,2550,2640,2724,2801,2878,2955,3024,3123,3200,3293,3388,3462,3543,3639,3690,3758,3844,3932,3995,4060,4123,4228,4331,4426,4531", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "219,301,408,521,606,669,763,829,891,994,1065,1124,1200,1265,1319,1432,1490,1551,1605,1684,1800,1883,1974,2086,2165,2244,2332,2399,2465,2545,2635,2719,2796,2873,2950,3019,3118,3195,3288,3383,3457,3538,3634,3685,3753,3839,3927,3990,4055,4118,4223,4326,4421,4526,4608"}, "to": {"startLines": "2,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3028,3855,3962,4075,4160,4223,4317,4383,4445,4548,4619,4678,4754,4819,4873,4986,5044,5105,5159,5238,5354,5437,5528,5640,5719,5798,5886,5953,6019,6099,6189,6273,6350,6427,6504,6573,6672,6749,6842,6937,7011,7092,7188,7239,7307,7393,7481,7544,7609,7672,7777,7880,7975,8080", "endLines": "5,33,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "269,3105,3957,4070,4155,4218,4312,4378,4440,4543,4614,4673,4749,4814,4868,4981,5039,5100,5154,5233,5349,5432,5523,5635,5714,5793,5881,5948,6014,6094,6184,6268,6345,6422,6499,6568,6667,6744,6837,6932,7006,7087,7183,7234,7302,7388,7476,7539,7604,7667,7772,7875,7970,8075,8157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\505b3688473c724d9c868193f5201a6b\\transformed\\core-1.13.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "34,35,36,37,38,39,40,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3110,3208,3311,3412,3518,3619,3727,8245", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3203,3306,3407,3513,3614,3722,3850,8341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8960aa6e858281122249b12ced85f681\\transformed\\appcompat-1.3.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,388,500,613,701,808,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2107,2223,2333,2432,2545,2650,2764,2928,8162", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "383,495,608,696,803,929,1007,1083,1174,1267,1362,1456,1556,1649,1744,1838,1929,2020,2102,2218,2328,2427,2540,2645,2759,2923,3023,8240"}}]}]}