// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.itemBean.cosmos;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.FontTextView;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TaskReportItemView_ViewBinding implements Unbinder {
  private TaskReportItemView target;

  @UiThread
  public TaskReportItemView_ViewBinding(TaskReportItemView target) {
    this(target, target);
  }

  @UiThread
  public TaskReportItemView_ViewBinding(TaskReportItemView target, View source) {
    this.target = target;

    target.mIvHeard = Utils.findRequiredViewAsType(source, R.id.iv_heard, "field 'mIvHeard'", ImageView.class);
    target.mTvAccountName = Utils.findRequiredViewAsType(source, R.id.tv_account_name, "field 'mTvAccountName'", FontTextView.class);
    target.mTvDanger = Utils.findRequiredViewAsType(source, R.id.tv_danger, "field 'mTvDanger'", TextView.class);
    target.mTvCount = Utils.findRequiredViewAsType(source, R.id.tv_count, "field 'mTvCount'", TextView.class);
    target.mTvAccount = Utils.findRequiredViewAsType(source, R.id.tv_account, "field 'mTvAccount'", TextView.class);
    target.mRlName = Utils.findRequiredViewAsType(source, R.id.rl_name, "field 'mRlName'", RelativeLayout.class);
    target.mTvContent = Utils.findRequiredViewAsType(source, R.id.tv_content, "field 'mTvContent'", FontTextView.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv1, "field 'mIv1'", ImageView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    TaskReportItemView target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mIvHeard = null;
    target.mTvAccountName = null;
    target.mTvDanger = null;
    target.mTvCount = null;
    target.mTvAccount = null;
    target.mRlName = null;
    target.mTvContent = null;
    target.mIv1 = null;
  }
}
