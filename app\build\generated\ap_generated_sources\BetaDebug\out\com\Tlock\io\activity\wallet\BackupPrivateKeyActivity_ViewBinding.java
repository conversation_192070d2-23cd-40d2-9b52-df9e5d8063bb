// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.wallet;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class BackupPrivateKeyActivity_ViewBinding implements Unbinder {
  private BackupPrivateKeyActivity target;

  private View view7f09013d;

  private View view7f09013e;

  private View view7f09013f;

  @UiThread
  public BackupPrivateKeyActivity_ViewBinding(BackupPrivateKeyActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public BackupPrivateKeyActivity_ViewBinding(final BackupPrivateKeyActivity target, View source) {
    this.target = target;

    View view;
    target.mTvTip = Utils.findRequiredViewAsType(source, R.id.tv_tip, "field 'mTvTip'", TextView.class);
    target.mIvBack = Utils.findRequiredViewAsType(source, R.id.iv_back, "field 'mIvBack'", ImageView.class);
    target.mRlToolbar = Utils.findRequiredViewAsType(source, R.id.rl_toolbar, "field 'mRlToolbar'", RelativeLayout.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv1, "field 'mTv1'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_copy_1, "field 'mIvCopy1' and method 'onViewClicked'");
    target.mIvCopy1 = Utils.castView(view, R.id.iv_copy_1, "field 'mIvCopy1'", ImageView.class);
    view7f09013d = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mRl1 = Utils.findRequiredViewAsType(source, R.id.rl1, "field 'mRl1'", RelativeLayout.class);
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv2, "field 'mTv2'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_copy_2, "field 'mIvCopy2' and method 'onViewClicked'");
    target.mIvCopy2 = Utils.castView(view, R.id.iv_copy_2, "field 'mIvCopy2'", ImageView.class);
    view7f09013e = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mRl2 = Utils.findRequiredViewAsType(source, R.id.rl2, "field 'mRl2'", RelativeLayout.class);
    target.mTv3 = Utils.findRequiredViewAsType(source, R.id.tv3, "field 'mTv3'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_copy_3, "field 'mIvCopy3' and method 'onViewClicked'");
    target.mIvCopy3 = Utils.castView(view, R.id.iv_copy_3, "field 'mIvCopy3'", ImageView.class);
    view7f09013f = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onViewClicked(p0);
      }
    });
    target.mTvKey1 = Utils.findRequiredViewAsType(source, R.id.tv_key_1, "field 'mTvKey1'", TextView.class);
    target.mTvKey2 = Utils.findRequiredViewAsType(source, R.id.tv_key_2, "field 'mTvKey2'", TextView.class);
    target.mTvKey3 = Utils.findRequiredViewAsType(source, R.id.tv_key_3, "field 'mTvKey3'", TextView.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    BackupPrivateKeyActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTip = null;
    target.mIvBack = null;
    target.mRlToolbar = null;
    target.mTv1 = null;
    target.mIvCopy1 = null;
    target.mRl1 = null;
    target.mTv2 = null;
    target.mIvCopy2 = null;
    target.mRl2 = null;
    target.mTv3 = null;
    target.mIvCopy3 = null;
    target.mTvKey1 = null;
    target.mTvKey2 = null;
    target.mTvKey3 = null;

    view7f09013d.setOnClickListener(null);
    view7f09013d = null;
    view7f09013e.setOnClickListener(null);
    view7f09013e = null;
    view7f09013f.setOnClickListener(null);
    view7f09013f = null;
  }
}
