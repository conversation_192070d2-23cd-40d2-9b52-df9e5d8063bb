package com.Tlock.io.itemBean.cosmos;

import static com.Tlock.io.utils.DateUtil.friendly_time_2;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.vectordrawable.graphics.drawable.Animatable2Compat;

import com.Tlock.io.R;
import com.Tlock.io.activity.cosmos.UserInfoActivity;
import com.Tlock.io.app.AppApplication;
import com.Tlock.io.base.BaseView;
import com.Tlock.io.entity.wallet.OperationBean;
import com.Tlock.io.post.PostQueryProto;
import com.Tlock.io.utils.BitmapUtils;
import com.Tlock.io.utils.ToastUtil;
import com.Tlock.io.utils.cosmos.CosmosUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.FontTextView;
import com.Tlock.io.widget.RoundedImageView;
import com.Tlock.io.widget.TextAvatarDrawable;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.signature.ObjectKey;

import butterknife.BindView;
import butterknife.OnClick;
import io.noties.markwon.Markwon;
import io.noties.markwon.image.glide.GlideImagesPlugin;

/**
 * @ClassName Group
 * <AUTHOR>
 * @Data 2023/12/27 11:53
 * @Desc
 */

public class MessageItemView extends BaseView {


    @BindView(R.id.iv_heard)
    ImageView mIvHeard;
    @BindView(R.id.iv_status)
    ImageView mIvStatus;
    @BindView(R.id.tv_account_name)
    FontTextView mTvAccountName;
    @BindView(R.id.tv_content)
    FontTextView mTvContent;
    @BindView(R.id.rl_name)
    RelativeLayout mRlName;
    @BindView(R.id.tv_quote_title)
    TextView mTvQuoteTitle;
    @BindView(R.id.tv_quote_content)
    TextView mTvQuoteContent;
    @BindView(R.id.tv_time)
    TextView mTvTime;
    @BindView(R.id.tv_reply)
    TextView mTvReply;
    @BindView(R.id.iv_like)
    ImageView mIvLike;
    @BindView(R.id.tv_follow)
    TextView mTvFollow;
    @BindView(R.id.line1)
    View mLine1;
    @BindView(R.id.iv_quote_heard)
    RoundedImageView mIvQuoteHeard;
    @BindView(R.id.rl_content_quote)
    RelativeLayout mRlContentQuote;
    private boolean isFollow = false;
    private OperationBean currentOperation;
    private String id = "";

    public MessageItemView(Context context) {
        super(context);
    }

    public MessageItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MessageItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public PostQueryProto.ActivitiesReceivedResponse data;

    @Override
    protected int getLayoutId() {
        return R.layout.item_message;
    }

    public void setData(PostQueryProto.ActivitiesReceivedResponse data) {
        if (id.equalsIgnoreCase(data.getTimestamp() + "")) {
            return;
        }
        this.id = data.getTimestamp() + "";
        this.data = data;
        mRlContentQuote.setVisibility(GONE);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mTvTime.setText(friendly_time_2(data.getTimestamp() + "000"));
        }
        //设置用户数据
        setUser();
        //设置消息类型
        setType();
    }

    private void setQuote() {
        mTvQuoteTitle.setText(TextUtils.isEmpty(data.getParentPost().getTitle()) ? "" : data.getParentPost().getTitle());
        mTvQuoteTitle.setVisibility(TextUtils.isEmpty(data.getParentPost().getTitle()) ? GONE : VISIBLE);
        mTvQuoteContent.setText(TextUtils.isEmpty(data.getParentPost().getContent()) ? "" : data.getParentPost().getContent());
        Glide.with(mIvQuoteHeard).load(getResources().getDrawable(R.drawable.btn_gray_6)).centerCrop().into(mIvQuoteHeard);

        if (data.getParentPost() != null && data.getParentPost().getImageIdsCount() > 0) {
            mIvQuoteHeard.setVisibility(VISIBLE);
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String postImages = CosmosUtils.getPostImages(data.getParentPost().getImageIdsList().get(0));
                    Bitmap bitmap = BitmapUtils.base64ToBitmap(postImages);
                    mIvQuoteHeard.post(new Runnable() {
                        @Override
                        public void run() {
                            Glide.with(mIvQuoteHeard).load(bitmap).centerCrop().listener(new RequestListener<Drawable>() {
                                @Override
                                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                                    // 检查当前Tag是否与加载时一致
                                    if (data.getParentPost().getImageIdsCount() > 0 && mIvQuoteHeard.getTag(R.id.iv_quote_heard).equals(data.getParentPost().getId())) {
                                        mIvQuoteHeard.setImageDrawable(resource);
                                    }
                                    return false;
                                }

                                @Override
                                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                                    return false;
                                }
                            }).into(mIvQuoteHeard);
                        }
                    });
                }
            });
        } else if (data.getParentPost() != null && data.getParentPost().getImagesUrlList().size() > 0) {
            mIvQuoteHeard.setVisibility(VISIBLE);
            Glide.with(mIvQuoteHeard).load(data.getParentPost().getImagesUrlList().get(0)).centerCrop().into(mIvQuoteHeard);
        } else {
            mIvQuoteHeard.setVisibility(GONE);
        }
    }

    private void setType() {
        Markwon markdwon = Markwon.builder(mTvContent.getContext()).usePlugin(GlideImagesPlugin.create(mTvContent.getContext())).build();

        switch (data.getActivitiesType().getNumber()) {
            case 0:
                //给我点赞
                mTvFollow.setVisibility(GONE);
                mRlContentQuote.setVisibility(VISIBLE);
                //设置转发消息
                setQuote();
                mTvReply.setVisibility(GONE);
                mIvLike.setVisibility(GONE);
                markdwon.setMarkdown(mTvContent, "Liked your post");
                mIvStatus.setImageDrawable(getResources().getDrawable(R.mipmap.icon_status_like));
                break;
            case 1:
                //收藏我文章
                mTvFollow.setVisibility(GONE);
                mRlContentQuote.setVisibility(VISIBLE);
                //设置转发消息
                setQuote();
                mTvReply.setVisibility(GONE);
                mIvLike.setVisibility(GONE);
                markdwon.setMarkdown(mTvContent, "Saved your post");
                mIvStatus.setImageDrawable(getResources().getDrawable(R.mipmap.icon_status_save));
                break;
            case 2:
                mIvStatus.setImageDrawable(getResources().getDrawable(R.mipmap.icon_status_callback));
                //评论我
                markdwon.setMarkdown(mTvContent, data.getContent());
                mTvFollow.setVisibility(GONE);
                mTvReply.setVisibility(VISIBLE);
                mIvLike.setVisibility(VISIBLE);
                mRlContentQuote.setVisibility(VISIBLE);
                //设置转发消息
                setQuote();

                break;
            case 3:
                //关注我
                mIvStatus.setImageDrawable(getResources().getDrawable(R.mipmap.icon_status_follow));
                markdwon.setMarkdown(mTvContent, "Followed you");
                getFlowing();
                mTvFollow.setVisibility(GONE);
                mTvReply.setVisibility(VISIBLE);
                mIvLike.setVisibility(VISIBLE);
                //设置转发消息
                setQuote();
                break;
            case 4:
                //@我
                mIvStatus.setImageDrawable(getResources().getDrawable(R.mipmap.icon_status_at));
                markdwon.setMarkdown(mTvContent, "Mentioned you");
                mTvFollow.setVisibility(GONE);
                mRlContentQuote.setVisibility(VISIBLE);
                //设置转发消息
                setQuote();
                mTvReply.setVisibility(GONE);
                mIvLike.setVisibility(GONE);
                break;
        }
    }

    private void setUser() {
        mTvAccountName.setText(data.getProfile().getNickname().isEmpty() ? data.getProfile().getUserHandle() : data.getProfile().getNickname());
        if (data.getProfile().getAvatar().isEmpty()) {
            Glide.with(getContext()).load(getResources().getDrawable(R.drawable.shape_transparent_60)).into(mIvHeard);
            AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    String heardStr = CosmosUtils.getAuthHeard(data.getAddress());
                    mIvHeard.post(new Runnable() {
                        @Override
                        public void run() {
                            if (TextUtils.isEmpty(heardStr)) {
                                TextAvatarDrawable a = new TextAvatarDrawable(data.getProfile().getUserHandle().substring(0, 1));
                                // 应用到 ImageView
                                mIvHeard.setBackground(a);
                            } else {
                                if (callback != null) {
                                    PostQueryProto.ActivitiesReceivedResponse.Builder builder = data.toBuilder();
                                    PostQueryProto.ProfileResponse.Builder builder1 = builder.getProfile().toBuilder().setAvatar(heardStr);
                                    builder.setProfile(builder1.build());
                                    callback.resetProfile(builder.build());
                                }
                                if (heardStr.startsWith("http")) {
                                    Glide.with(getContext()).load(heardStr).apply(new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(new ObjectKey(data.getAddress())).centerCrop().format(DecodeFormat.PREFER_RGB_565).dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).into(mIvHeard);
                                } else {
                                    Bitmap bitmap1 = BitmapUtils.base64ToBitmap(heardStr);
                                    Glide.with(getContext()).load(bitmap1).apply(new RequestOptions().centerCrop().dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).placeholder(getResources().getDrawable(R.drawable.shape_transparent_60)).transition(DrawableTransitionOptions.withCrossFade(500)).into(mIvHeard);
                                }

                            }
                        }
                    });
                }
            });

        } else {
            Bitmap bitmap1 = BitmapUtils.base64ToBitmap(data.getProfile().getAvatar());
            Glide.with(getContext()).asBitmap().load(bitmap1).apply(new RequestOptions().centerCrop().dontTransform()).apply(RequestOptions.circleCropTransform().circleCrop()).placeholder(getResources().getDrawable(R.drawable.shape_transparent_60)).into(mIvHeard);
        }
    }

    private void setDefaultImg() {
        currentOperation = WalletDaoUtils.getCurrentOperation(data.getCommentId());
        currentOperation.setPostId(data.getCommentId());
        currentOperation.setAddress(WalletDaoUtils.getCurrent().getAddress());
        if (currentOperation.getIsLike()) {
            Glide.with(mIvLike).load(R.mipmap.icon_post_like_select).into(mIvLike);
        } else {
            Glide.with(mIvLike).load(R.mipmap.icon_post_like).into(mIvLike);
        }

    }


    public void setFollow(int status) {
        switch (status) {
            case 2:
                mTvFollow.setVisibility(VISIBLE);
                mTvReply.setVisibility(GONE);
                mIvLike.setVisibility(GONE);
                break;
            case 3:
                mTvFollow.setVisibility(VISIBLE);
                mTvReply.setVisibility(GONE);
                mIvLike.setVisibility(GONE);
                mTvFollow.setText("Following");
                mTvFollow.setTextColor(getResources().getColor(R.color.cosmos_default));
                mTvFollow.setBackground(getResources().getDrawable(R.drawable.bg_gray_6));
                isFollow = true;
                break;

        }


    }

    Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @OnClick({R.id.tv_reply, R.id.iv_like, R.id.iv_heard, R.id.tv_follow, R.id.rl_content_quote})
    public void onBindClick(View view) {
        switch (view.getId()) {
            case R.id.iv_heard:
                UserInfoActivity.start(getContext(), data.getAddress());
                break;
            case R.id.tv_reply:
                if (callback != null) {
                    callback.reply(data.getCommentId());
                }
                break;
            case R.id.iv_like:
                Glide.with(getContext()).asGif().load(R.drawable.heart_1) // 替换为你的GIF资源ID
                        .listener(new RequestListener<GifDrawable>() {
                            @Override
                            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<GifDrawable> target, boolean isFirstResource) {
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(GifDrawable resource, Object model, Target<GifDrawable> target, DataSource dataSource, boolean isFirstResource) {
                                resource.setLoopCount(1);
                                resource.registerAnimationCallback(new Animatable2Compat.AnimationCallback() {
                                    @Override
                                    public void onAnimationEnd(Drawable drawable) {
                                        // 加载静态图片
                                        Glide.with(mIvLike).load(R.mipmap.icon_post_like_select).into(mIvLike);
                                    }
                                });
                                return false;
                            }
                        }).into(mIvLike);

                if (callback != null) {
                    callback.like(data.getCommentId());
                }
                break;
            case R.id.tv_follow:
                if (isFollow) {
                    return;
                }
                ToastUtil.toastShortCenter(mTvFollow.getContext(), "Following");
                mTvFollow.setText("Following");
                mTvFollow.setTextColor(getResources().getColor(R.color.cosmos_default));
                mTvFollow.setBackground(getResources().getDrawable(R.drawable.bg_gray_6));
                setFollowStatus();
                break;
            case R.id.rl_content_quote:
                if (callback != null) {
                    callback.click(data.getParentId());
                }
                break;
        }
    }

    private void setFollowStatus() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                CosmosUtils.follow(WalletDaoUtils.getCurrent().getAddress(), data.getAddress());
            }
        });
    }

    private void getFlowing() {
        AppApplication.getInstance().getThreadPoolExecutor().execute(new Runnable() {
            @Override
            public void run() {
                long l = CosmosUtils.queryFollowRelationship(data.getAddress());
                mIvStatus.post(new Runnable() {
                    @Override
                    public void run() {
                        setFollow(Integer.parseInt(l + ""));
                    }
                });
            }
        });
    }

    public void setGlideTag(String id) {
        mIvQuoteHeard.setTag(R.id.iv_quote_heard,id );

    }


    public interface Callback {
        void like(String id);

        void reply(String id);

        void click(String id);

        void resetProfile(PostQueryProto.ActivitiesReceivedResponse data);
    }
}
