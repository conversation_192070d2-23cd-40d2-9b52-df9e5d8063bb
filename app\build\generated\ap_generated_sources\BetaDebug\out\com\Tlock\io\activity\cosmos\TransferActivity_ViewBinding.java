// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.activity.cosmos;

import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.DebouncingOnClickListener;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import com.Tlock.io.widget.CustomNavBar;
import java.lang.IllegalStateException;
import java.lang.Override;

public class TransferActivity_ViewBinding implements Unbinder {
  private TransferActivity target;

  private View view7f090169;

  private View view7f0902ff;

  private View view7f090070;

  @UiThread
  public TransferActivity_ViewBinding(TransferActivity target) {
    this(target, target.getWindow().getDecorView());
  }

  @UiThread
  public TransferActivity_ViewBinding(final TransferActivity target, View source) {
    this.target = target;

    View view;
    target.mCustomNavBar = Utils.findRequiredViewAsType(source, R.id.custom_nav_bar, "field 'mCustomNavBar'", CustomNavBar.class);
    target.mIv1 = Utils.findRequiredViewAsType(source, R.id.iv1, "field 'mIv1'", ImageView.class);
    target.mTv1 = Utils.findRequiredViewAsType(source, R.id.tv1, "field 'mTv1'", TextView.class);
    view = Utils.findRequiredView(source, R.id.iv_scan, "field 'mIvScan' and method 'onBindClick'");
    target.mIvScan = Utils.castView(view, R.id.iv_scan, "field 'mIvScan'", ImageView.class);
    view7f090169 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    target.mEdAddress = Utils.findRequiredViewAsType(source, R.id.ed_address, "field 'mEdAddress'", EditText.class);
    target.mTv2 = Utils.findRequiredViewAsType(source, R.id.tv2, "field 'mTv2'", TextView.class);
    target.mTvBalance = Utils.findRequiredViewAsType(source, R.id.tv_balance, "field 'mTvBalance'", TextView.class);
    target.mEdCount = Utils.findRequiredViewAsType(source, R.id.ed_count, "field 'mEdCount'", EditText.class);
    view = Utils.findRequiredView(source, R.id.tv_all, "field 'mTvAll' and method 'onBindClick'");
    target.mTvAll = Utils.castView(view, R.id.tv_all, "field 'mTvAll'", TextView.class);
    view7f0902ff = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
    view = Utils.findRequiredView(source, R.id.btn_confirm, "field 'mBtnConfirm' and method 'onBindClick'");
    target.mBtnConfirm = Utils.castView(view, R.id.btn_confirm, "field 'mBtnConfirm'", TextView.class);
    view7f090070 = view;
    view.setOnClickListener(new DebouncingOnClickListener() {
      @Override
      public void doClick(View p0) {
        target.onBindClick(p0);
      }
    });
  }

  @Override
  @CallSuper
  public void unbind() {
    TransferActivity target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mCustomNavBar = null;
    target.mIv1 = null;
    target.mTv1 = null;
    target.mIvScan = null;
    target.mEdAddress = null;
    target.mTv2 = null;
    target.mTvBalance = null;
    target.mEdCount = null;
    target.mTvAll = null;
    target.mBtnConfirm = null;

    view7f090169.setOnClickListener(null);
    view7f090169 = null;
    view7f0902ff.setOnClickListener(null);
    view7f0902ff = null;
    view7f090070.setOnClickListener(null);
    view7f090070 = null;
  }
}
