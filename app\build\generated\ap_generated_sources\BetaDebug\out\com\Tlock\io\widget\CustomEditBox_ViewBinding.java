// Generated code from Butter Knife. Do not modify!
package com.Tlock.io.widget;

import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.CallSuper;
import androidx.annotation.UiThread;
import butterknife.Unbinder;
import butterknife.internal.Utils;
import com.Tlock.io.R;
import java.lang.IllegalStateException;
import java.lang.Override;

public class CustomEditBox_ViewBinding implements Unbinder {
  private CustomEditBox target;

  @UiThread
  public CustomEditBox_ViewBinding(CustomEditBox target) {
    this(target, target);
  }

  @UiThread
  public CustomEditBox_ViewBinding(CustomEditBox target, View source) {
    this.target = target;

    target.mTvTitle = Utils.findRequiredViewAsType(source, R.id.tv_title, "field 'mTvTitle'", TextView.class);
    target.mEditText = Utils.findRequiredViewAsType(source, R.id.editText, "field 'mEditText'", EditText.class);
  }

  @Override
  @CallSuper
  public void unbind() {
    CustomEditBox target = this.target;
    if (target == null) throw new IllegalStateException("Bindings already cleared.");
    this.target = null;

    target.mTvTitle = null;
    target.mEditText = null;
  }
}
