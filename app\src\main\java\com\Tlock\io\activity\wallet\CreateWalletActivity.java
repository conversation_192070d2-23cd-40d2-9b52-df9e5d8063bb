package com.Tlock.io.activity.wallet;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.Tlock.io.R;
import com.Tlock.io.base.BaseActivity;
import com.Tlock.io.entity.wallet.ETHWallet;
import com.Tlock.io.utils.JsonUtils;
import com.Tlock.io.utils.cosmos.CosmosWalletUtils;
import com.Tlock.io.utils.wallet.WalletDaoUtils;
import com.Tlock.io.widget.CustomInputBox;
import com.Tlock.io.widget.CustomNavBar;

import butterknife.BindView;
import butterknife.OnClick;
import wallet.core.jni.HDWallet;

/**
 * 创建钱包
 */
public class CreateWalletActivity extends BaseActivity {

    @BindView(R.id.custom_nav_bar)
    CustomNavBar mCustomNavBar;
    @BindView(R.id.et_wallet_name)
    CustomInputBox mEtWalletName;
    @BindView(R.id.et_pwd)
    CustomInputBox mEtPwd;
    @BindView(R.id.et_pwd_confirm)
    CustomInputBox mEtPwdConfirm;
    @BindView(R.id.tv_create)
    TextView mTvCreate;
    private boolean isCheck = false;
    private boolean isRecheck = false;

    /**
     * 跳转
     *
     * @param context 上下文
     */
    public static void start(Context context) {
        Intent intent = new Intent(context, CreateWalletActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_create_wallet;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        setAndroidNativeLightStatusBar(getActivity(), false);
        mCustomNavBar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }

    @Override
    protected void loadData() {
    }


    @OnClick(R.id.tv_create)
    public void onViewClicked() {
        //密码是否合规
        if (!checkPwd(mEtPwd.getEditText(), mEtPwdConfirm.getEditText())) return;
        //钱包重名
        if (WalletDaoUtils.walletNameChecking(mEtWalletName.getEditText())) {
            showToast(getResources().getString(R.string.have_wallet_name));
            return;
        }
        //创建钱包
        HDWallet hdWallet = new HDWallet(128, "");
        ETHWallet walletInfo = CosmosWalletUtils.getWallet(hdWallet.mnemonic(), 2);
        walletInfo.setName(walletInfo.getAddress().substring(walletInfo.getAddress().length()-6));

        walletInfo.setPassword(mEtPwdConfirm.getEditText());
        //传到下一步做校验
        ToBackupMnemonicsActivity.start(getActivity(), JsonUtils.objectToJson(walletInfo));
    }

    /**
     * 校验密码合规
     *
     * @return
     */
    private boolean checkPwd(String p1, String p2) {
        if (!p1.equals(p2)) {
            showToast(getResources().getString(R.string.passworld_disaffinity));
            return false;
        }
        if (p1.length() < 6) {
            showToast(getResources().getString(R.string.passworld_must));
            return false;
        }
        return true;

    }


}